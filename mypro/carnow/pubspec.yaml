name: carnow
description: "A new Flutter project."
publish_to: 'none'
version: 0.4.1

environment:
  sdk: '>=3.8.1 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  
  # State Management
  flutter_riverpod: ^2.6.1
  riverpod: ^2.6.1
  riverpod_annotation: ^2.6.1
  
  # Hooks
  flutter_hooks: ^0.21.2
  hooks_riverpod: ^2.6.1
  
  # Data Classes
  freezed_annotation: ^3.1.0
  json_annotation: ^4.9.0
  equatable: ^2.0.7
  
  # Supabase (Auth + Data)
  supabase_flutter: ^2.9.1
  
  # Google OAuth - Using stable version that works
  google_sign_in: ^6.3.0


  
  # Error Tracking
  sentry_flutter: ^9.4.1
  
  # Routing
  go_router: ^16.0.0
  
  # UI
  flutter_svg: ^2.2.0
  smooth_page_indicator: ^1.2.1
  cached_network_image: ^3.4.1
  shimmer: ^3.0.0
  intl: ^0.20.2
  flutter_vector_icons: ^2.0.0
  gap: ^3.0.1
  
  # Material 3 Expressive & Dynamic Color
  dynamic_color: ^1.7.0
  material_color_utilities: ^0.13.0
  
  # Utilities
  uuid: ^4.5.1
  url_launcher: ^6.3.2
  shared_preferences: ^2.5.3
  http: ^1.4.0
  dio: ^5.8.0+1
  flutter_dotenv: ^5.2.1
  timeago: ^3.7.1
  fl_chart: ^1.0.0
  app_links: ^6.4.0
  connectivity_plus: ^6.1.4
  path: ^1.9.1
  image_picker: ^1.1.2
  file_picker: ^10.2.0
  mime: ^2.0.0
  logger: ^2.6.1
  logging: ^1.3.0
  meta: ^1.16.0
  share_plus: ^11.0.0
  flutter_secure_storage: ^9.2.4
  local_auth: ^2.3.0
  mobile_scanner: ^7.0.1
  permission_handler: ^12.0.1
  csv: ^6.0.0
  geolocator: ^14.0.2
  collection: ^1.19.1

  # PDF and Email Services
  pdf: ^3.11.3
  mailer: ^6.4.1
  path_provider: ^2.1.5
  flutter_cache_manager: ^3.4.1
  syncfusion_flutter_charts: ^30.1.40
  hive: ^2.2.3
  hive_flutter: ^1.1.0

dev_dependencies:
  sqflite_common_ffi: ^2.3.6
  flutter_test:
    sdk: flutter
  flutter_lints: ^6.0.0
  
  # Testing
  mocktail: ^1.0.4
  mockito: ^5.4.6
  test: ^1.25.15
  integration_test:
    sdk: flutter
  
  # Code Generation
  build_runner: ^2.5.4
  riverpod_generator: ^2.6.5
  freezed: 3.1.0
  json_serializable: 6.9.5

  crypto: ^3.0.6

flutter:
  uses-material-design: true
  generate: true
  
  assets:
    - .env 
    - assets/images/
    - assets/icons/
    - assets/fonts/
    - assets/data/

  fonts:
    - family: Cairo
      fonts:
        - asset: fonts/Cairo/Cairo-200.ttf
          weight: 200
        - asset: fonts/Cairo/Cairo-300.ttf
          weight: 300
        - asset: fonts/Cairo/Cairo-regular.ttf
          weight: 400
        - asset: fonts/Cairo/Cairo-500.ttf
          weight: 500
        - asset: fonts/Cairo/Cairo-600.ttf
          weight: 600
        - asset: fonts/Cairo/Cairo-700.ttf
          weight: 700
        - asset: fonts/Cairo/Cairo-800.ttf
          weight: 800
        - asset: fonts/Cairo/Cairo-900.ttf
          weight: 900

# Minimal dependency overrides (Forever Plan: Keep it simple)
dependency_overrides:
  # Required for flutter_secure_storage compatibility
  flutter_secure_storage_platform_interface: 2.0.1
  
  # Required for material color consistency
  material_color_utilities: 0.13.0
