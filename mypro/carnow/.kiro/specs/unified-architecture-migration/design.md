# Design Document

## Overview

هذا التصميم يوضح كيفية تحويل مشروع CarNow من الاعتماد المباشر على Supabase في Flutter إلى تطبيق المعمارية الموحدة Forever Plan Architecture. التصميم يضمن فصل كامل بين الطبقات مع الحفاظ على الأداء والأمان.

**المعمارية المستهدفة:**
```
Flutter (UI Only) → Go API (Production-Ready) → Supabase (Data + Auth)
                     ↓
    Redis Cache + Monitoring + Security + Material 3
```

## Architecture

### Current State Analysis

**المشاكل الحالية:**
1. Flutter يحتوي على استدعاءات مباشرة لـ Supabase (مخالفة للمعمارية)
2. منطق الأعمال موزع بين Flutter و Go Backend
3. عدم وجود طبقة موحدة للتحكم في البيانات
4. نقص في التكامل بين أنظمة المصادقة
5. عدم الاستفادة الكاملة من Redis Cache

**الحالة المطلوبة:**
1. Flutter يحتوي على UI فقط مع SimpleApiClient
2. Go Backend يحتوي على جميع منطق الأعمال
3. Supabase يُستخدم للبيانات والمصادقة فقط
4. Redis Cache للأداء المحسن
5. نظام مصادقة موحد عبر JWT

### Target Architecture Components

#### 1. Flutter Layer (UI Only)
```dart
// ✅ CORRECT: Flutter UI Only Pattern
class ProductListScreen extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final productsAsync = ref.watch(productsProvider);
    
    return productsAsync.when(
      data: (products) => ProductListView(products: products),
      loading: () => const LoadingWidget(),
      error: (error, stack) => ErrorWidget(error: error),
    );
  }
}

// ✅ CORRECT: Provider using SimpleApiClient only
@riverpod
Future<List<Product>> products(Ref ref) async {
  final apiClient = ref.read(simpleApiClientProvider);
  final result = await apiClient.get<Map<String, dynamic>>('/products');
  
  return result.when(
    success: (data) => (data['products'] as List)
        .map((json) => Product.fromJson(json))
        .toList(),
    error: (error) => throw Exception(error),
  );
}
```

#### 2. Go Backend Layer (Business Logic)
```go
// ✅ CORRECT: Go Backend API Structure
type ProductHandler struct {
    db     *database.Database
    cache  cache.CacheProvider
    logger *zap.Logger
}

func (h *ProductHandler) GetProducts(c *gin.Context) {
    // 1. Validate request parameters
    params := &ProductQueryParams{}
    if err := c.ShouldBindQuery(params); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid parameters"})
        return
    }
    
    // 2. Check cache first
    cacheKey := fmt.Sprintf("products:%s", params.Hash())
    if cached, err := h.cache.Get(c.Request.Context(), cacheKey); err == nil {
        c.JSON(http.StatusOK, cached)
        return
    }
    
    // 3. Query database with business logic
    products, err := h.db.GetProductsWithFilters(c.Request.Context(), params)
    if err != nil {
        h.logger.Error("Failed to get products", zap.Error(err))
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error"})
        return
    }
    
    // 4. Apply business rules
    filteredProducts := h.applyBusinessRules(products, params)
    
    // 5. Cache result
    h.cache.Set(c.Request.Context(), cacheKey, filteredProducts, 5*time.Minute)
    
    // 6. Return response
    c.JSON(http.StatusOK, gin.H{
        "products": filteredProducts,
        "total":    len(filteredProducts),
        "page":     params.Page,
    })
}
```

#### 3. Database Layer (Supabase Integration)
```go
// ✅ CORRECT: Database abstraction layer
type Database struct {
    supabaseClient *supabase.Client
    pgxPool        *pgxpool.Pool
    logger         *zap.Logger
}

func (db *Database) GetProductsWithFilters(ctx context.Context, params *ProductQueryParams) ([]Product, error) {
    query := `
        SELECT p.*, c.name as category_name, s.name as seller_name
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.id
        LEFT JOIN sellers s ON p.seller_id = s.id
        WHERE p.status = 'active'
    `
    
    args := []interface{}{}
    argIndex := 1
    
    // Apply filters with proper SQL injection protection
    if params.CategoryID != "" {
        query += fmt.Sprintf(" AND p.category_id = $%d", argIndex)
        args = append(args, params.CategoryID)
        argIndex++
    }
    
    if params.MinPrice > 0 {
        query += fmt.Sprintf(" AND p.price >= $%d", argIndex)
        args = append(args, params.MinPrice)
        argIndex++
    }
    
    // Add pagination
    query += fmt.Sprintf(" ORDER BY p.created_at DESC LIMIT $%d OFFSET $%d", argIndex, argIndex+1)
    args = append(args, params.Limit, params.Offset)
    
    rows, err := db.pgxPool.Query(ctx, query, args...)
    if err != nil {
        return nil, fmt.Errorf("failed to query products: %w", err)
    }
    defer rows.Close()
    
    var products []Product
    for rows.Next() {
        var product Product
        err := rows.Scan(
            &product.ID, &product.Name, &product.Price,
            &product.CategoryName, &product.SellerName,
        )
        if err != nil {
            return nil, fmt.Errorf("failed to scan product: %w", err)
        }
        products = append(products, product)
    }
    
    return products, nil
}
```

## Components and Interfaces

### 1. API Client Interface (Flutter)
```dart
abstract class ApiClient {
  Future<ApiResponse<T>> get<T>(String endpoint, {Map<String, dynamic>? queryParameters});
  Future<ApiResponse<T>> post<T>(String endpoint, {Map<String, dynamic>? data});
  Future<ApiResponse<T>> put<T>(String endpoint, {Map<String, dynamic>? data});
  Future<ApiResponse<T>> delete<T>(String endpoint);
}

class SimpleApiClient implements ApiClient {
  final Dio _dio;
  final Ref _ref;
  
  // Implementation with JWT token handling
  // Error handling and retry logic
  // Network connectivity management
}
```

### 2. Backend API Interface (Go)
```go
type APIHandler interface {
    // Product endpoints
    GetProducts(c *gin.Context)
    GetProduct(c *gin.Context)
    CreateProduct(c *gin.Context)
    UpdateProduct(c *gin.Context)
    DeleteProduct(c *gin.Context)
    
    // Category endpoints
    GetCategories(c *gin.Context)
    GetCategory(c *gin.Context)
    
    // User endpoints
    GetUserProfile(c *gin.Context)
    UpdateUserProfile(c *gin.Context)
    
    // Authentication endpoints
    SignIn(c *gin.Context)
    SignUp(c *gin.Context)
    RefreshToken(c *gin.Context)
    SignOut(c *gin.Context)
}

type CleanAPI struct {
    db     *database.SimpleDB
    cache  cache.CacheProvider
    logger *zap.Logger
}
```

### 3. Database Interface (Supabase)
```go
type DatabaseInterface interface {
    // Product operations
    GetProducts(ctx context.Context, filters *ProductFilters) ([]Product, error)
    GetProductByID(ctx context.Context, id string) (*Product, error)
    CreateProduct(ctx context.Context, product *Product) error
    UpdateProduct(ctx context.Context, id string, updates *ProductUpdates) error
    DeleteProduct(ctx context.Context, id string) error
    
    // User operations
    GetUserByID(ctx context.Context, id string) (*User, error)
    UpdateUser(ctx context.Context, id string, updates *UserUpdates) error
    
    // Health check
    Health() error
}
```

### 4. Authentication System Interface
```go
type AuthService interface {
    ValidateJWT(token string) (*Claims, error)
    RefreshToken(refreshToken string) (*TokenPair, error)
    RevokeToken(token string) error
    GetUserFromToken(token string) (*User, error)
}

type SupabaseAuthService struct {
    client *supabase.Client
    jwtSecret string
}
```

## Data Models

### 1. API Response Models
```dart
// Flutter API Response Model
@freezed
class ApiResponse<T> with _$ApiResponse<T> {
  const factory ApiResponse.success(
    T data, {
    String? message,
  }) = ApiResponseSuccess<T>;
  
  const factory ApiResponse.error(
    String error, {
    String? message,
    int? statusCode,
  }) = ApiResponseError<T>;
}

// Product Model
@freezed
class Product with _$Product {
  const factory Product({
    required String id,
    required String name,
    required String description,
    required double price,
    required String categoryId,
    required String sellerId,
    required String status,
    required DateTime createdAt,
    required DateTime updatedAt,
    String? imageUrl,
    Map<String, dynamic>? metadata,
  }) = _Product;
  
  factory Product.fromJson(Map<String, dynamic> json) => _$ProductFromJson(json);
}
```

### 2. Go Backend Models
```go
// Product model for Go backend
type Product struct {
    ID          string                 `json:"id" db:"id"`
    Name        string                 `json:"name" db:"name" validate:"required,min=3,max=100"`
    Description string                 `json:"description" db:"description" validate:"required,min=10,max=1000"`
    Price       float64                `json:"price" db:"price" validate:"required,gt=0"`
    CategoryID  string                 `json:"category_id" db:"category_id" validate:"required,uuid"`
    SellerID    string                 `json:"seller_id" db:"seller_id" validate:"required,uuid"`
    Status      string                 `json:"status" db:"status" validate:"required,oneof=active inactive"`
    ImageURL    *string                `json:"image_url,omitempty" db:"image_url"`
    Metadata    map[string]interface{} `json:"metadata,omitempty" db:"metadata"`
    CreatedAt   time.Time              `json:"created_at" db:"created_at"`
    UpdatedAt   time.Time              `json:"updated_at" db:"updated_at"`
}

// API Response wrapper
type APIResponse struct {
    Success bool        `json:"success"`
    Data    interface{} `json:"data,omitempty"`
    Error   string      `json:"error,omitempty"`
    Message string      `json:"message,omitempty"`
}
```

### 3. Database Schema (Supabase)
```sql
-- Products table
CREATE TABLE products (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    description TEXT NOT NULL,
    price DECIMAL(10,2) NOT NULL CHECK (price > 0),
    category_id UUID NOT NULL REFERENCES categories(id),
    seller_id UUID NOT NULL REFERENCES auth.users(id),
    status VARCHAR(20) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
    image_url TEXT,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_products_category_id ON products(category_id);
CREATE INDEX idx_products_seller_id ON products(seller_id);
CREATE INDEX idx_products_status ON products(status);
CREATE INDEX idx_products_created_at ON products(created_at DESC);
CREATE INDEX idx_products_price ON products(price);

-- RLS policies
ALTER TABLE products ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Products are viewable by everyone" ON products
    FOR SELECT USING (status = 'active');

CREATE POLICY "Users can insert their own products" ON products
    FOR INSERT WITH CHECK (auth.uid() = seller_id);

CREATE POLICY "Users can update their own products" ON products
    FOR UPDATE USING (auth.uid() = seller_id);
```

## Error Handling

### 1. Flutter Error Handling
```dart
// Global error handler
@riverpod
class GlobalErrorHandler extends _$GlobalErrorHandler {
  @override
  ErrorState build() => const ErrorState.none();
  
  void handleError(Object error, StackTrace stackTrace) {
    if (error is ApiException) {
      _handleApiError(error);
    } else if (error is NetworkException) {
      _handleNetworkError(error);
    } else {
      _handleUnknownError(error, stackTrace);
    }
  }
  
  void _handleApiError(ApiException error) {
    state = ErrorState.api(
      message: error.message,
      statusCode: error.statusCode,
    );
  }
  
  void _handleNetworkError(NetworkException error) {
    state = ErrorState.network(message: error.message);
  }
}

// Error boundary widget
class ErrorBoundary extends ConsumerWidget {
  final Widget child;
  
  const ErrorBoundary({required this.child, super.key});
  
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final errorState = ref.watch(globalErrorHandlerProvider);
    
    return errorState.when(
      none: () => child,
      api: (message, statusCode) => ApiErrorWidget(
        message: message,
        statusCode: statusCode,
        onRetry: () => ref.read(globalErrorHandlerProvider.notifier).clearError(),
      ),
      network: (message) => NetworkErrorWidget(
        message: message,
        onRetry: () => ref.read(globalErrorHandlerProvider.notifier).clearError(),
      ),
    );
  }
}
```

### 2. Go Backend Error Handling
```go
// Error types
type APIError struct {
    Code    string `json:"code"`
    Message string `json:"message"`
    Details string `json:"details,omitempty"`
}

func (e APIError) Error() string {
    return e.Message
}

// Error middleware
func ErrorHandlerMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        c.Next()
        
        if len(c.Errors) > 0 {
            err := c.Errors.Last()
            
            switch e := err.Err.(type) {
            case *APIError:
                c.JSON(http.StatusBadRequest, gin.H{
                    "success": false,
                    "error":   e.Message,
                    "code":    e.Code,
                })
            case *ValidationError:
                c.JSON(http.StatusBadRequest, gin.H{
                    "success": false,
                    "error":   "Validation failed",
                    "details": e.Details,
                })
            default:
                c.JSON(http.StatusInternalServerError, gin.H{
                    "success": false,
                    "error":   "Internal server error",
                })
            }
        }
    }
}

// Database error handling
func HandleDatabaseError(err error) *APIError {
    if err == nil {
        return nil
    }
    
    if errors.Is(err, sql.ErrNoRows) {
        return &APIError{
            Code:    "NOT_FOUND",
            Message: "Resource not found",
        }
    }
    
    if strings.Contains(err.Error(), "duplicate key") {
        return &APIError{
            Code:    "DUPLICATE",
            Message: "Resource already exists",
        }
    }
    
    return &APIError{
        Code:    "DATABASE_ERROR",
        Message: "Database operation failed",
        Details: err.Error(),
    }
}
```

## Testing Strategy

### 1. Flutter Testing
```dart
// Unit tests for providers
void main() {
  group('ProductsProvider', () {
    late ProviderContainer container;
    late MockApiClient mockApiClient;
    
    setUp(() {
      mockApiClient = MockApiClient();
      container = ProviderContainer(
        overrides: [
          simpleApiClientProvider.overrideWithValue(mockApiClient),
        ],
      );
    });
    
    test('should return products when API call succeeds', () async {
      // Arrange
      final mockProducts = [
        Product(id: '1', name: 'Test Product', price: 100.0),
      ];
      when(() => mockApiClient.get<Map<String, dynamic>>('/products'))
          .thenAnswer((_) async => ApiResponse.success({
            'products': mockProducts.map((p) => p.toJson()).toList(),
          }));
      
      // Act
      final result = await container.read(productsProvider.future);
      
      // Assert
      expect(result, equals(mockProducts));
    });
    
    test('should throw exception when API call fails', () async {
      // Arrange
      when(() => mockApiClient.get<Map<String, dynamic>>('/products'))
          .thenAnswer((_) async => ApiResponse.error('Network error'));
      
      // Act & Assert
      expect(
        () => container.read(productsProvider.future),
        throwsA(isA<Exception>()),
      );
    });
  });
}

// Widget tests
void main() {
  group('ProductListScreen', () {
    testWidgets('should display products when loaded', (tester) async {
      // Arrange
      final mockProducts = [
        Product(id: '1', name: 'Test Product', price: 100.0),
      ];
      
      await tester.pumpWidget(
        ProviderScope(
          overrides: [
            productsProvider.overrideWith((ref) => mockProducts),
          ],
          child: MaterialApp(
            home: ProductListScreen(),
          ),
        ),
      );
      
      // Act
      await tester.pumpAndSettle();
      
      // Assert
      expect(find.text('Test Product'), findsOneWidget);
      expect(find.text('100.0'), findsOneWidget);
    });
  });
}
```

### 2. Go Backend Testing
```go
// Unit tests for handlers
func TestProductHandler_GetProducts(t *testing.T) {
    // Setup
    mockDB := &MockDatabase{}
    mockCache := &MockCache{}
    handler := &ProductHandler{
        db:    mockDB,
        cache: mockCache,
    }
    
    // Test cases
    tests := []struct {
        name           string
        queryParams    string
        mockProducts   []Product
        mockError      error
        expectedStatus int
        expectedBody   string
    }{
        {
            name:        "successful request",
            queryParams: "?page=1&limit=10",
            mockProducts: []Product{
                {ID: "1", Name: "Test Product", Price: 100.0},
            },
            expectedStatus: http.StatusOK,
        },
        {
            name:           "database error",
            queryParams:    "?page=1&limit=10",
            mockError:      errors.New("database connection failed"),
            expectedStatus: http.StatusInternalServerError,
        },
    }
    
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            // Setup mock expectations
            if tt.mockError != nil {
                mockDB.On("GetProductsWithFilters", mock.Anything, mock.Anything).
                    Return(nil, tt.mockError)
            } else {
                mockDB.On("GetProductsWithFilters", mock.Anything, mock.Anything).
                    Return(tt.mockProducts, nil)
            }
            
            // Create request
            req := httptest.NewRequest("GET", "/products"+tt.queryParams, nil)
            w := httptest.NewRecorder()
            c, _ := gin.CreateTestContext(w)
            c.Request = req
            
            // Execute
            handler.GetProducts(c)
            
            // Assert
            assert.Equal(t, tt.expectedStatus, w.Code)
            mockDB.AssertExpectations(t)
        })
    }
}

// Integration tests
func TestProductAPI_Integration(t *testing.T) {
    // Setup test database
    testDB := setupTestDatabase(t)
    defer cleanupTestDatabase(t, testDB)
    
    // Setup test server
    router := setupTestRouter(testDB)
    server := httptest.NewServer(router)
    defer server.Close()
    
    // Test product creation
    t.Run("create and retrieve product", func(t *testing.T) {
        // Create product
        productData := map[string]interface{}{
            "name":        "Integration Test Product",
            "description": "Test description",
            "price":       99.99,
            "category_id": "test-category-id",
        }
        
        createResp := makeRequest(t, server.URL, "POST", "/api/v1/products", productData)
        assert.Equal(t, http.StatusCreated, createResp.StatusCode)
        
        var createResult map[string]interface{}
        json.NewDecoder(createResp.Body).Decode(&createResult)
        productID := createResult["data"].(map[string]interface{})["id"].(string)
        
        // Retrieve product
        getResp := makeRequest(t, server.URL, "GET", "/api/v1/products/"+productID, nil)
        assert.Equal(t, http.StatusOK, getResp.StatusCode)
        
        var getResult map[string]interface{}
        json.NewDecoder(getResp.Body).Decode(&getResult)
        product := getResult["data"].(map[string]interface{})
        
        assert.Equal(t, "Integration Test Product", product["name"])
        assert.Equal(t, 99.99, product["price"])
    })
}
```

### 3. End-to-End Testing
```dart
// E2E tests using integration_test
void main() {
  group('Product Management E2E', () {
    testWidgets('complete product workflow', (tester) async {
      // Setup
      app.main();
      await tester.pumpAndSettle();
      
      // Login
      await tester.tap(find.byKey(Key('login_button')));
      await tester.pumpAndSettle();
      
      await tester.enterText(find.byKey(Key('email_field')), '<EMAIL>');
      await tester.enterText(find.byKey(Key('password_field')), 'password123');
      await tester.tap(find.byKey(Key('submit_login')));
      await tester.pumpAndSettle();
      
      // Navigate to products
      await tester.tap(find.byKey(Key('products_tab')));
      await tester.pumpAndSettle();
      
      // Verify products are loaded
      expect(find.byType(ProductCard), findsAtLeastNWidgets(1));
      
      // Test product details
      await tester.tap(find.byType(ProductCard).first);
      await tester.pumpAndSettle();
      
      expect(find.byKey(Key('product_details')), findsOneWidget);
      expect(find.byKey(Key('add_to_cart_button')), findsOneWidget);
      
      // Add to cart
      await tester.tap(find.byKey(Key('add_to_cart_button')));
      await tester.pumpAndSettle();
      
      // Verify cart update
      expect(find.text('Added to cart'), findsOneWidget);
    });
  });
}
```

## Performance Optimization

### 1. Caching Strategy
```go
// Redis caching implementation
type CacheService struct {
    client redis.Client
    logger *zap.Logger
}

func (c *CacheService) GetProducts(ctx context.Context, filters *ProductFilters) ([]Product, error) {
    // Generate cache key
    cacheKey := fmt.Sprintf("products:%s", filters.Hash())
    
    // Try cache first
    cached, err := c.client.Get(ctx, cacheKey).Result()
    if err == nil {
        var products []Product
        if err := json.Unmarshal([]byte(cached), &products); err == nil {
            return products, nil
        }
    }
    
    // Cache miss - get from database
    products, err := c.getProductsFromDB(ctx, filters)
    if err != nil {
        return nil, err
    }
    
    // Cache the result
    if data, err := json.Marshal(products); err == nil {
        c.client.Set(ctx, cacheKey, data, 5*time.Minute)
    }
    
    return products, nil
}

// Cache invalidation
func (c *CacheService) InvalidateProductCache(ctx context.Context, productID string) error {
    patterns := []string{
        "products:*",
        fmt.Sprintf("product:%s", productID),
        "categories:*", // Products affect category counts
    }
    
    for _, pattern := range patterns {
        keys, err := c.client.Keys(ctx, pattern).Result()
        if err != nil {
            continue
        }
        
        if len(keys) > 0 {
            c.client.Del(ctx, keys...)
        }
    }
    
    return nil
}
```

### 2. Database Optimization
```sql
-- Optimized queries with proper indexing
EXPLAIN ANALYZE
SELECT p.*, c.name as category_name, s.name as seller_name
FROM products p
LEFT JOIN categories c ON p.category_id = c.id
LEFT JOIN sellers s ON p.seller_id = s.id
WHERE p.status = 'active'
  AND p.category_id = $1
  AND p.price BETWEEN $2 AND $3
ORDER BY p.created_at DESC
LIMIT $4 OFFSET $5;

-- Materialized views for complex queries
CREATE MATERIALIZED VIEW product_analytics AS
SELECT 
    p.category_id,
    c.name as category_name,
    COUNT(*) as product_count,
    AVG(p.price) as avg_price,
    MIN(p.price) as min_price,
    MAX(p.price) as max_price
FROM products p
JOIN categories c ON p.category_id = c.id
WHERE p.status = 'active'
GROUP BY p.category_id, c.name;

-- Refresh materialized view periodically
CREATE OR REPLACE FUNCTION refresh_product_analytics()
RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY product_analytics;
END;
$$ LANGUAGE plpgsql;

-- Schedule refresh every hour
SELECT cron.schedule('refresh-analytics', '0 * * * *', 'SELECT refresh_product_analytics();');
```

### 3. Flutter Performance
```dart
// Optimized list rendering
class OptimizedProductList extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final productsAsync = ref.watch(productsProvider);
    
    return productsAsync.when(
      data: (products) => ListView.builder(
        itemCount: products.length,
        itemBuilder: (context, index) {
          // Use const constructors and keys for performance
          return ProductCard(
            key: ValueKey(products[index].id),
            product: products[index],
          );
        },
        // Add caching for better scroll performance
        cacheExtent: 1000,
      ),
      loading: () => const ShimmerProductList(),
      error: (error, stack) => ErrorRetryWidget(
        onRetry: () => ref.refresh(productsProvider),
      ),
    );
  }
}

// Image optimization
class OptimizedProductImage extends StatelessWidget {
  final String imageUrl;
  final double? width;
  final double? height;
  
  const OptimizedProductImage({
    required this.imageUrl,
    this.width,
    this.height,
    super.key,
  });
  
  @override
  Widget build(BuildContext context) {
    return CachedNetworkImage(
      imageUrl: imageUrl,
      width: width,
      height: height,
      fit: BoxFit.cover,
      placeholder: (context, url) => const ShimmerBox(),
      errorWidget: (context, url, error) => const PlaceholderImage(),
      // Memory and disk caching
      memCacheWidth: width?.toInt(),
      memCacheHeight: height?.toInt(),
      maxWidthDiskCache: 800,
      maxHeightDiskCache: 600,
    );
  }
}
```

## Security Considerations

### 1. Authentication & Authorization
```go
// JWT middleware with proper validation
func JWTMiddleware(secret string) gin.HandlerFunc {
    return func(c *gin.Context) {
        authHeader := c.GetHeader("Authorization")
        if authHeader == "" {
            c.JSON(http.StatusUnauthorized, gin.H{"error": "Authorization header required"})
            c.Abort()
            return
        }
        
        tokenString := strings.TrimPrefix(authHeader, "Bearer ")
        if tokenString == authHeader {
            c.JSON(http.StatusUnauthorized, gin.H{"error": "Bearer token required"})
            c.Abort()
            return
        }
        
        token, err := jwt.ParseWithClaims(tokenString, &Claims{}, func(token *jwt.Token) (interface{}, error) {
            if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
                return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
            }
            return []byte(secret), nil
        })
        
        if err != nil || !token.Valid {
            c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid token"})
            c.Abort()
            return
        }
        
        if claims, ok := token.Claims.(*Claims); ok {
            c.Set("user_id", claims.UserID)
            c.Set("user_role", claims.Role)
        }
        
        c.Next()
    }
}

// Input validation and sanitization
func ValidateProductInput(c *gin.Context) {
    var input ProductInput
    if err := c.ShouldBindJSON(&input); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid input format"})
        c.Abort()
        return
    }
    
    // Sanitize inputs
    input.Name = html.EscapeString(strings.TrimSpace(input.Name))
    input.Description = html.EscapeString(strings.TrimSpace(input.Description))
    
    // Validate business rules
    if len(input.Name) < 3 || len(input.Name) > 100 {
        c.JSON(http.StatusBadRequest, gin.H{"error": "Product name must be 3-100 characters"})
        c.Abort()
        return
    }
    
    if input.Price <= 0 {
        c.JSON(http.StatusBadRequest, gin.H{"error": "Price must be greater than 0"})
        c.Abort()
        return
    }
    
    c.Set("validated_input", input)
    c.Next()
}
```

### 2. Data Protection
```go
// SQL injection prevention
func (db *Database) GetProductsByCategory(ctx context.Context, categoryID string, userID string) ([]Product, error) {
    // Use parameterized queries
    query := `
        SELECT p.* FROM products p
        WHERE p.category_id = $1 
        AND p.status = 'active'
        AND (p.visibility = 'public' OR p.seller_id = $2)
        ORDER BY p.created_at DESC
    `
    
    rows, err := db.pool.Query(ctx, query, categoryID, userID)
    if err != nil {
        return nil, fmt.Errorf("failed to query products: %w", err)
    }
    defer rows.Close()
    
    // ... rest of implementation
}

// Rate limiting
func RateLimitMiddleware(requests int, window time.Duration) gin.HandlerFunc {
    limiter := rate.NewLimiter(rate.Every(window/time.Duration(requests)), requests)
    
    return func(c *gin.Context) {
        if !limiter.Allow() {
            c.JSON(http.StatusTooManyRequests, gin.H{
                "error": "Rate limit exceeded",
                "retry_after": window.Seconds(),
            })
            c.Abort()
            return
        }
        c.Next()
    }
}
```

## Migration Strategy

### Phase 1: Backend API Completion
1. Complete all missing API endpoints in Go backend
2. Implement proper error handling and validation
3. Add comprehensive logging and monitoring
4. Set up Redis caching for performance

### Phase 2: Flutter Layer Cleanup
1. Remove all direct Supabase calls from Flutter
2. Replace with SimpleApiClient calls to Go backend
3. Update state management to use API providers
4. Implement proper error handling in UI

### Phase 3: Authentication Unification
1. Ensure Supabase Auth generates proper JWT tokens
2. Implement JWT validation in Go backend
3. Update Flutter auth system to work with backend
4. Test authentication flow end-to-end

### Phase 4: Testing & Optimization
1. Add comprehensive test coverage
2. Performance testing and optimization
3. Security testing and hardening
4. Documentation and deployment guides

### Phase 5: Production Deployment
1. Set up production environment
2. Database migration and optimization
3. Monitoring and alerting setup
4. Gradual rollout with feature flags