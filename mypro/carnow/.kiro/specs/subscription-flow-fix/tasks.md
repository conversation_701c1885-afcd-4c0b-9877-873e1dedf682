# Implementation Plan

- [x] 1. Create enhanced subscription data models with proper validation
  - Implement SubscriptionRequest model with Freezed annotations and JSON serialization
  - Implement SubscriptionResponse model with proper error handling fields
  - Create SubscriptionError union type for comprehensive error handling
  - Add input validation methods to models with Arabic error messages
  - _Requirements: 1.1, 4.1, 4.4_

- [x] 2. Create core subscription service interface and implementation
  - Create SubscriptionService interface in lib/core/services/ using core models
  - Implement concrete SubscriptionService using SimpleApiClient exclusively
  - Add proper error handling for database connection failures using core SubscriptionError types
  - Implement retry logic with exponential backoff for failed requests
  - Add comprehensive logging for all subscription operations
  - _Requirements: 1.2, 1.3, 1.4, 4.2, 5.3_

- [x] 3. Create core subscription error handler with user-friendly dialogs
  - Create SubscriptionErrorHandler in lib/core/error/ with specific error type handling
  - Implement user-friendly error dialogs in Arabic using Material 3
  - Add network error handling with retry options using core SubscriptionError types
  - Create database error handling without mock data fallbacks
  - Implement validation error display with field-specific messages
  - _Requirements: 1.5, 3.2, 3.3, 3.4, 4.2_

- [x] 4. Fix navigation management for subscription flow
  - Create SubscriptionNavigationManager with safe navigation methods
  - Implement proper route handling compatible with page-based routing
  - Add fallback navigation strategies for failed route transitions
  - Create navigation error handling with user-friendly messages
  - Test navigation flow between all subscription screens
  - _Requirements: 2.1, 2.2, 2.3, 2.4, 2.5_

- [x] 5. Add comprehensive testing for core subscription models and services
  - Expand existing test/core/models/subscription_models_test.dart with additional test cases
  - Add tests for core subscription service and error handler
  - Create integration tests for core subscription flow
  - Test navigation with SubscriptionNavigationManager
  - Add end-to-end tests for complete updated user journey
  - _Requirements: 1.1, 1.2, 2.1, 3.1, 4.1_

- [x] 6. Move and enhance subscription provider to core
  - Move existing SubscriptionFlowProvider from lib/features/seller/providers/ to lib/core/providers/
  - Update provider to use core SubscriptionRequest/Response models instead of legacy models
  - Integrate core SubscriptionError types for error state management
  - Add proper state management with validation using core models
  - Ensure compatibility with existing subscription screens
  - _Requirements: 1.1, 2.5, 3.5, 4.1_

- [x] 7. Update existing subscription service to use core models exclusively
  - Update lib/features/seller/services/subscription_service.dart to use core models exclusively
  - Replace all legacy SubscriptionRequest/Response usage with core models
  - Update error handling to use core SubscriptionError types instead of AppError
  - Remove legacy model conversion methods and use core models directly
  - Test service integration with core models
  - _Requirements: 1.2, 1.3, 1.4, 4.2, 4.3_

- [x] 8. Update existing subscription forms to use core models
  - Update lib/features/seller/screens/subscription_form_screen.dart to use core SubscriptionRequest model
  - Replace existing validation with core model validation system and Arabic error messages
  - Update form submission to use core SubscriptionError handling
  - Integrate with updated core SubscriptionFlowProvider
  - Test form validation with core models
  - _Requirements: 3.1, 3.5, 4.5, 1.1, 4.1_

- [x] 9. Update subscription status and management screens to use core models
  - Update lib/features/seller/screens/subscription_management_screen.dart to use core models
  - Update subscription status display screens to use core SubscriptionResponse model
  - Integrate core SubscriptionErrorHandler for error handling across all screens
  - Update status display to use new Arabic status messages from core models
  - Ensure proper navigation using existing SubscriptionNavigationManager
  - _Requirements: 3.1, 3.2, 3.5, 2.1, 2.2_

- [x] 10. Fix test compilation errors and enhance test coverage
  - Fix compilation errors in test/core/providers/subscription_flow_provider_test.dart
  - Update test mocks to match current core model interfaces
  - Add missing integration tests for navigation manager
  - Test error scenarios with core error handling system
  - Add performance tests for subscription flow
  - _Requirements: 5.1, 5.2, 5.3, 5.4, 5.5, 1.5, 3.2, 3.3, 3.4_

- [x] 11. Fix core model compilation issues and improve validation
  - Fix JsonKey annotation warnings in core subscription models
  - Complete truncated validation methods in SubscriptionRequest model
  - Ensure all core models compile without warnings
  - Test model serialization/deserialization with proper JSON handling
  - _Requirements: 1.1, 4.1, 4.4_

- [x] 12. Clean up legacy code and optimize performance
  - Remove unused legacy subscription models (lib/features/seller/models/subscription_request_model.dart)
  - Clean up imports and dependencies in subscription screens
  - Remove any remaining references to legacy models in tests
  - Optimize form performance with core validation system
  - Add performance monitoring for updated subscription flow
  - _Requirements: 2.4, 3.4, 3.5, 5.1, 5.2_