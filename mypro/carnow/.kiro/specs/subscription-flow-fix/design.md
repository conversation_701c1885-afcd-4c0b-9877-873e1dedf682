# Design Document

## Overview

The subscription flow fix will address critical issues in the CarNow application by implementing robust error handling, proper navigation management, and ensuring adherence to Forever Plan architecture principles. The solution focuses on fixing database connection failures, navigation assertion errors, and providing clear user feedback while maintaining real data integrity.

## Architecture

### High-Level Architecture
```
Flutter UI Layer (Subscription Forms)
         ↓
Riverpod State Management (Subscription Providers)
         ↓
SimpleApiClient (HTTP Communication)
         ↓
Go Backend API (Subscription Endpoints)
         ↓
Supabase Database (Real Subscription Data)
```

### Key Architectural Principles
- **Real Data Only**: All subscription data must come from Supabase database
- **No Mock Data**: Zero tolerance for hardcoded or fake subscription data
- **Unified Error Handling**: Centralized error management with proper user feedback
- **Material 3 Compliance**: All UI components follow Material 3 design system
- **Navigation Safety**: Proper route management compatible with page-based routing

## Components and Interfaces

### 1. Subscription Data Models

```dart
@freezed
class SubscriptionRequest with _$SubscriptionRequest {
  const factory SubscriptionRequest({
    required String storeName,
    required String phone,
    required String city,
    required String address,
    required String description,
    required String planType,
    required double price,
    required String userId,
  }) = _SubscriptionRequest;
  
  factory SubscriptionRequest.fromJson(Map<String, dynamic> json) =>
      _$SubscriptionRequestFromJson(json);
}

@freezed
class SubscriptionResponse with _$SubscriptionResponse {
  const factory SubscriptionResponse({
    required String id,
    required String status,
    required DateTime createdAt,
    String? message,
  }) = _SubscriptionResponse;
  
  factory SubscriptionResponse.fromJson(Map<String, dynamic> json) =>
      _$SubscriptionResponseFromJson(json);
}
```

### 2. Subscription Service Interface

```dart
abstract class SubscriptionService {
  Future<Result<SubscriptionResponse, AppError>> submitSubscriptionRequest(
    SubscriptionRequest request,
  );
  
  Future<Result<List<SubscriptionResponse>, AppError>> getUserSubscriptions(
    String userId,
  );
  
  Future<Result<SubscriptionResponse, AppError>> getSubscriptionStatus(
    String subscriptionId,
  );
}
```

### 3. Enhanced Error Handling

```dart
@freezed
class SubscriptionError with _$SubscriptionError {
  const factory SubscriptionError.networkError({
    required String message,
    String? code,
  }) = NetworkError;
  
  const factory SubscriptionError.databaseError({
    required String message,
    String? code,
  }) = DatabaseError;
  
  const factory SubscriptionError.validationError({
    required String message,
    required Map<String, String> fieldErrors,
  }) = ValidationError;
  
  const factory SubscriptionError.navigationError({
    required String message,
    required String attemptedRoute,
  }) = NavigationError;
}
```

### 4. Navigation Management

```dart
class SubscriptionNavigationManager {
  static const String subscriptionFormRoute = '/subscription/form';
  static const String subscriptionStatusRoute = '/subscription/status';
  static const String subscriptionSuccessRoute = '/subscription/success';
  
  static Future<void> navigateToSubscriptionStatus({
    required BuildContext context,
    required String subscriptionId,
  }) async {
    try {
      await context.pushNamed(
        subscriptionStatusRoute,
        pathParameters: {'id': subscriptionId},
      );
    } catch (e) {
      // Fallback navigation
      await context.pushReplacementNamed(subscriptionStatusRoute);
    }
  }
  
  static Future<void> handleNavigationError({
    required BuildContext context,
    required NavigationError error,
  }) async {
    // Log error and provide fallback
    logger.error('Navigation failed: ${error.message}');
    
    // Show error dialog and stay on current screen
    await showDialog(
      context: context,
      builder: (context) => ErrorDialog(
        title: 'خطأ في التنقل',
        message: 'حدث خطأ أثناء التنقل. يرجى المحاولة مرة أخرى.',
      ),
    );
  }
}
```

## Data Models

### Subscription Request Flow
1. **Input Validation**: Validate all required fields on the client side
2. **Data Transformation**: Convert form data to SubscriptionRequest model
3. **API Communication**: Send request to Go backend via SimpleApiClient
4. **Response Processing**: Handle success/error responses appropriately
5. **State Management**: Update UI state based on response
6. **Navigation**: Navigate to appropriate screen based on result

### Database Schema (Supabase)
```sql
-- Subscription requests table
CREATE TABLE subscription_requests (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id),
  store_name VARCHAR(255) NOT NULL,
  phone VARCHAR(20) NOT NULL,
  city VARCHAR(100) NOT NULL,
  address TEXT NOT NULL,
  description TEXT,
  plan_type VARCHAR(50) NOT NULL,
  price DECIMAL(10,2) NOT NULL,
  status VARCHAR(20) DEFAULT 'pending',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Subscription plans table
CREATE TABLE subscription_plans (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(100) NOT NULL,
  price DECIMAL(10,2) NOT NULL,
  features JSONB,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## Error Handling

### Error Handling Strategy
1. **Client-Side Validation**: Validate input before API calls
2. **Network Error Handling**: Handle connectivity issues gracefully
3. **Database Error Handling**: Provide meaningful error messages for database failures
4. **Navigation Error Handling**: Implement fallback navigation strategies
5. **User Feedback**: Always provide clear, actionable error messages in Arabic

### Error Recovery Mechanisms
```dart
class SubscriptionErrorHandler {
  static Future<void> handleSubscriptionError({
    required BuildContext context,
    required SubscriptionError error,
    required VoidCallback onRetry,
  }) async {
    switch (error) {
      case NetworkError():
        await _showNetworkErrorDialog(context, onRetry);
        break;
      case DatabaseError():
        await _showDatabaseErrorDialog(context, onRetry);
        break;
      case ValidationError():
        await _showValidationErrorDialog(context, error.fieldErrors);
        break;
      case NavigationError():
        await SubscriptionNavigationManager.handleNavigationError(
          context: context,
          error: error,
        );
        break;
    }
  }
  
  static Future<void> _showNetworkErrorDialog(
    BuildContext context,
    VoidCallback onRetry,
  ) async {
    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('خطأ في الاتصال'),
        content: const Text('تعذر الاتصال بالخادم. يرجى التحقق من اتصال الإنترنت والمحاولة مرة أخرى.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              onRetry();
            },
            child: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }
}
```

## Testing Strategy

### Unit Testing
- Test all data models with various input scenarios
- Test subscription service methods with mock API responses
- Test error handling logic with different error types
- Test navigation manager with various route scenarios

### Integration Testing
- Test complete subscription flow from form submission to response
- Test error scenarios with actual API failures
- Test navigation flow between subscription screens
- Test database connectivity and data persistence

### Widget Testing
- Test subscription form validation
- Test error dialog displays
- Test loading states during submission
- Test success/failure UI states

### End-to-End Testing
- Test complete user journey from login to subscription completion
- Test error recovery scenarios
- Test offline/online state transitions
- Test navigation between all subscription-related screens

## Performance Considerations

### Optimization Strategies
1. **Form Validation**: Implement real-time validation to prevent unnecessary API calls
2. **Caching**: Cache subscription plans and user data to reduce API calls
3. **Loading States**: Provide clear loading indicators during API operations
4. **Error Recovery**: Implement exponential backoff for failed requests
5. **Memory Management**: Properly dispose of controllers and listeners

### Monitoring and Metrics
- Track subscription submission success/failure rates
- Monitor API response times for subscription endpoints
- Track navigation error occurrences
- Monitor database connection health
- Track user abandonment rates in subscription flow