import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../core/errors/app_error.dart';
import '../../../../shared/widgets/loading_button.dart';
import '../models/models.dart';
import '../providers/providers.dart';

class AddCarScreen extends HookConsumerWidget {
  const AddCarScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final formKey = useMemoized(GlobalKey<FormState>.new);
    final makeController = useTextEditingController();
    final modelController = useTextEditingController();
    final generationController = useTextEditingController();
    final yearStartController = useTextEditingController();
    final yearEndController = useTextEditingController();
    final bodyTypeController = useTextEditingController();
    final countryController = useTextEditingController();

    final makeState = useState<int?>(null);
    final modelState = useState<int?>(null);

    // Watch lists of makes and models
    final makesAsyncValue = ref.watch(vehicleMakesProvider);
    final modelsAsyncValue = makeState.value != null
        ? ref.watch(vehicleModelsProvider(makeId: makeState.value))
        : const AsyncValue.data(<VehicleModel>[]);

    // Watch the add car provider for state
    final addCarState = ref.watch(addCarNotifierProvider);

    // Reset fields function
    void resetFields() {
      makeController.clear();
      modelController.clear();
      generationController.clear();
      yearStartController.clear();
      yearEndController.clear();
      bodyTypeController.clear();
      countryController.clear();
      makeState.value = null;
      modelState.value = null;
    }

    return Scaffold(
      appBar: AppBar(title: const Text('إضافة سيارة'), centerTitle: true),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Section: Company/Make
              _buildSectionTitle(context, 'الشركة المصنعة'),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    flex: 3,
                    child: _buildMakeDropdown(
                      context,
                      makesAsyncValue,
                      makeState,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    flex: 5,
                    child: TextFormField(
                      controller: makeController,
                      decoration: const InputDecoration(
                        labelText: 'إضافة شركة جديدة',
                        hintText: 'أدخل اسم الشركة المصنعة',
                        border: OutlineInputBorder(),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              TextFormField(
                controller: countryController,
                decoration: const InputDecoration(
                  labelText: 'بلد المنشأ',
                  hintText: 'أدخل بلد المنشأ',
                  border: OutlineInputBorder(),
                ),
              ),

              const SizedBox(height: 24),

              // Section: Model
              _buildSectionTitle(context, 'الموديل'),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    flex: 3,
                    child: _buildModelDropdown(
                      context,
                      modelsAsyncValue,
                      modelState,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    flex: 5,
                    child: TextFormField(
                      controller: modelController,
                      decoration: const InputDecoration(
                        labelText: 'إضافة موديل جديد',
                        hintText: 'أدخل اسم الموديل',
                        border: OutlineInputBorder(),
                      ),
                      validator: (value) {
                        if (makeState.value == null &&
                            makeController.text.isEmpty) {
                          return 'يجب اختيار أو إضافة شركة أولاً';
                        }
                        return null;
                      },
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      controller: yearStartController,
                      decoration: const InputDecoration(
                        labelText: 'سنة البداية',
                        hintText: 'مثال: 2020',
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value != null && value.isNotEmpty) {
                          final yearStart = int.tryParse(value);
                          if (yearStart == null ||
                              yearStart < 1900 ||
                              yearStart > 2100) {
                            return 'سنة غير صالحة';
                          }
                        }
                        return null;
                      },
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: TextFormField(
                      controller: yearEndController,
                      decoration: const InputDecoration(
                        labelText: 'سنة النهاية (اختياري)',
                        hintText: 'مثال: 2023',
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value != null && value.isNotEmpty) {
                          final yearEnd = int.tryParse(value);
                          if (yearEnd == null ||
                              yearEnd < 1900 ||
                              yearEnd > 2100) {
                            return 'سنة غير صالحة';
                          }

                          final yearStartText = yearStartController.text;
                          if (yearStartText.isNotEmpty) {
                            final yearStart = int.tryParse(yearStartText) ?? 0;
                            if (yearEnd < yearStart) {
                              return 'يجب أن تكون بعد سنة البداية';
                            }
                          }
                        }
                        return null;
                      },
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              TextFormField(
                controller: bodyTypeController,
                decoration: const InputDecoration(
                  labelText: 'نوع الهيكل (اختياري)',
                  hintText: 'مثال: سيدان، هاتشباك، SUV',
                  border: OutlineInputBorder(),
                ),
              ),

              const SizedBox(height: 24),

              // Section: Generation
              _buildSectionTitle(context, 'الجيل (اختياري)'),
              TextFormField(
                controller: generationController,
                decoration: const InputDecoration(
                  labelText: 'اسم الجيل أو الرمز',
                  hintText: 'مثال: الجيل الخامس، MK5',
                  border: OutlineInputBorder(),
                ),
              ),

              const SizedBox(height: 32),

              // Submit Buttons
              addCarState.when(
                loading: () => const Center(child: CircularProgressIndicator()),
                error: (error, _) => _buildErrorMessage(error),
                data: (_) => Row(
                  children: [
                    Expanded(
                      child: LoadingButton(
                        onPressed: () => _handleSubmit(
                          context,
                          ref,
                          formKey,
                          makeState,
                          modelState,
                          makeController,
                          modelController,
                          generationController,
                          yearStartController,
                          yearEndController,
                          bodyTypeController,
                          countryController,
                          resetFields,
                        ),
                        child: const Text('إضافة السيارة'),
                      ),
                    ),
                    const SizedBox(width: 16),
                    OutlinedButton(
                      onPressed: resetFields,
                      child: const Text('إعادة تعيين'),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSectionTitle(BuildContext context, String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Text(
        title,
        style: Theme.of(
          context,
        ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
      ),
    );
  }

  Widget _buildMakeDropdown(
    BuildContext context,
    AsyncValue<List<VehicleMake>> makesAsyncValue,
    ValueNotifier<int?> makeState,
  ) {
    return makesAsyncValue.when(
      loading: () => const CircularProgressIndicator(),
      error: (error, _) => Text('خطأ: ${error.toString()}'),
      data: (makes) {
        return DropdownButtonFormField<int>(
          value: makeState.value,
          decoration: const InputDecoration(
            labelText: 'اختر الشركة',
            border: OutlineInputBorder(),
          ),
          items: makes
              .map(
                (make) =>
                    DropdownMenuItem(value: make.id, child: Text(make.name)),
              )
              .toList(),
          onChanged: (value) => makeState.value = value,
        );
      },
    );
  }

  Widget _buildModelDropdown(
    BuildContext context,
    AsyncValue<List<VehicleModel>> modelsAsyncValue,
    ValueNotifier<int?> modelState,
  ) {
    return modelsAsyncValue.when(
      loading: () => const CircularProgressIndicator(),
      error: (error, _) => Text('خطأ: ${error.toString()}'),
      data: (models) {
        return DropdownButtonFormField<int>(
          value: modelState.value,
          decoration: const InputDecoration(
            labelText: 'اختر الموديل',
            border: OutlineInputBorder(),
          ),
          items: models
              .map(
                (model) =>
                    DropdownMenuItem(value: model.id, child: Text(model.name)),
              )
              .toList(),
          onChanged: (value) => modelState.value = value,
        );
      },
    );
  }

  Widget _buildErrorMessage(Object error) {
    final message = error is AppError
        ? error.userMessage
        : 'حدث خطأ. يرجى المحاولة مرة أخرى.';

    return Column(
      children: [
        SelectableText.rich(
          TextSpan(
            text: 'خطأ: ',
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.red,
            ),
            children: [
              TextSpan(
                text: message,
                style: const TextStyle(fontWeight: FontWeight.normal),
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),
        ElevatedButton(onPressed: () {}, child: const Text('إعادة المحاولة')),
      ],
    );
  }

  Future<void> _handleSubmit(
    BuildContext context,
    WidgetRef ref,
    GlobalKey<FormState> formKey,
    ValueNotifier<int?> makeState,
    ValueNotifier<int?> modelState,
    TextEditingController makeController,
    TextEditingController modelController,
    TextEditingController generationController,
    TextEditingController yearStartController,
    TextEditingController yearEndController,
    TextEditingController bodyTypeController,
    TextEditingController countryController,
    VoidCallback resetFields,
  ) async {
    if (!formKey.currentState!.validate()) {
      return;
    }

    final notifier = ref.read(addCarNotifierProvider.notifier);

    try {
      // Add Make if needed
      int makeId = makeState.value ?? 0;
      if (makeId == 0 && makeController.text.isNotEmpty) {
        final newMake = VehicleMake(
          id: 0, // This will be set by the database
          name: makeController.text,
          country: countryController.text.isEmpty
              ? null
              : countryController.text,
        );

        final makeResult = await notifier.createMake(newMake);
        makeResult.when(
          success: (make) => makeId = make.id,
          failure: (error) => throw error,
        );
      }

      // Can't continue without a make
      if (makeId == 0) {
        if (!context.mounted) return;
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('يجب اختيار أو إضافة شركة مصنعة')),
        );
        return;
      }

      // Add Model if needed
      int modelId = modelState.value ?? 0;
      if (modelId == 0 && modelController.text.isNotEmpty) {
        final yearStart =
            int.tryParse(yearStartController.text) ?? DateTime.now().year;
        final yearEnd = yearEndController.text.isNotEmpty
            ? int.tryParse(yearEndController.text)
            : null;

        final newModel = VehicleModel(
          id: 0, // This will be set by the database
          makeId: makeId,
          name: modelController.text,
          bodyType: bodyTypeController.text.isEmpty
              ? null
              : bodyTypeController.text,
          yearStart: yearStart,
          yearEnd: yearEnd,
        );

        final modelResult = await notifier.createModel(newModel);
        modelResult.when(
          success: (model) => modelId = model.id,
          failure: (error) => throw error,
        );
      }

      // Add Generation if needed
      if (modelId != 0 && generationController.text.isNotEmpty) {
        final yearStart =
            int.tryParse(yearStartController.text) ?? DateTime.now().year;
        final yearEnd = yearEndController.text.isNotEmpty
            ? int.tryParse(yearEndController.text)
            : null;

        final newGeneration = VehicleGeneration(
          id: 0, // This will be set by the database
          modelId: modelId,
          generationName: generationController.text,
          yearStart: yearStart,
          yearEnd: yearEnd,
        );

        await notifier.createGeneration(newGeneration);
      }

      // Show success and reset
      if (!context.mounted) return;
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('تمت إضافة السيارة بنجاح')));

      resetFields();
    } catch (e) {
      if (!context.mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('حدث خطأ: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}
