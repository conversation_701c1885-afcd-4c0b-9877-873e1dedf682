import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:logging/logging.dart';

import '../../../../core/errors/app_error.dart';
import '../../../../features/account/models/user_model.dart';
import '../../../../core/providers/dio_provider.dart';
import '../../../../core/config/backend_config.dart';
import '../../../../core/auth/unified_auth_provider.dart';
import '../../../../core/networking/simple_api_client.dart';

part 'admin_management_provider.g.dart';

/// مزود إدارة المشرفين - تعيين وإزالة المشرفين
@riverpod
class AdminManagement extends _$AdminManagement {
  final Logger _logger = Logger('AdminManagement');

  @override
  FutureOr<List<UserModel>> build() async {
    // جلب جميع المستخدمين مع التركيز على المشرفين
    return await _fetchAllUsers();
  }

  /// جلب جميع المستخدمين من الـ backend
  Future<List<UserModel>> _fetchAllUsers() async {
    try {
      final dio = ref.read(dioProvider);
      final response = await dio.get('${CarnowBackendConfig.apiBaseUrl}/admin/users');

      return (response.data as List).map((json) => UserModel.fromJson(json)).toList();
    } catch (e, st) {
      _logger.severe('خطأ في جلب المستخدمين: $e', e, st);
      throw AppError.network(
        message: 'فشل جلب قائمة المستخدمين',
        originalError: e,
      );
    }
  }

  /// تعيين مستخدم كمشرف
  Future<void> assignAdmin(String userId) async {
    try {
      _logger.info('تعيين مشرف: $userId');

      final dio = ref.read(dioProvider);
      final currentUser = ref.read(currentUserProvider);
      final currentUserId = currentUser?.id;

      if (currentUserId == null) {
        throw const AppError.authentication(message: 'يجب تسجيل الدخول أولاً');
      }

      // التحقق من أن المستخدم الحالي مشرف عبر API
      final isAdminResponse = await dio.get('${CarnowBackendConfig.apiBaseUrl}/admin/is-admin/$currentUserId');
      if (!isAdminResponse.data['is_admin']) {
        throw const AppError.permission(
          message: 'ليس لديك صلاحية تعيين المشرفين',
        );
      }

      // تعيين المشرف عبر API
      await dio.post('${CarnowBackendConfig.apiBaseUrl}/admin/assign', data: {'user_id': userId});

      _logger.info('تم تعيين المشرف بنجاح');

      // تحديث الحالة المحلية
      ref.invalidateSelf();
    } catch (e, st) {
      _logger.severe('خطأ في تعيين المشرف: $e', e, st);

      if (e is AppError) {
        rethrow;
      }

      throw AppError.network(message: 'فشل تعيين المشرف', originalError: e);
    }
  }

  /// إزالة صلاحيات المشرف من المستخدم
  Future<void> removeAdmin(String userId) async {
    try {
      _logger.info('إزالة مشرف: $userId');

      final dio = ref.read(dioProvider);
      final currentUser = ref.read(currentUserProvider);
      final currentUserId = currentUser?.id;

      if (currentUserId == null) {
        throw const AppError.authentication(message: 'يجب تسجيل الدخول أولاً');
      }

      // التحقق من أن المستخدم الحالي مشرف عبر API
      final isAdminResponse = await dio.get('${CarnowBackendConfig.apiBaseUrl}/admin/is-admin/$currentUserId');
      if (!isAdminResponse.data['is_admin']) {
        throw const AppError.permission(
          message: 'ليس لديك صلاحية إزالة المشرفين',
        );
      }

      // منع المستخدم من إزالة نفسه
      if (currentUserId == userId) {
        throw const AppError.validation(
          message: 'لا يمكنك إزالة نفسك من قائمة المشرفين',
        );
      }

      // إزالة المشرف عبر API
      await dio.post('${CarnowBackendConfig.apiBaseUrl}/admin/remove', data: {'user_id': userId});

      _logger.info('تم إزالة المشرف بنجاح');

      // تحديث الحالة المحلية
      ref.invalidateSelf();
    } catch (e, st) {
      _logger.severe('خطأ في إزالة المشرف: $e', e, st);

      if (e is AppError) {
        rethrow;
      }

      throw AppError.network(message: 'فشل إزالة المشرف', originalError: e);
    }
  }

  /// تحديث قائمة المستخدمين
  Future<void> refresh() async {
    ref.invalidateSelf();
  }
}

/// مزود فحص صلاحيات المشرف للمستخدم الحالي
@riverpod
Future<bool> isCurrentUserAdmin(Ref ref) async {
  // منع التخلص المبكر من الموفر الحرج
  ref.keepAlive();
  
  final currentUser = ref.watch(currentUserProvider);
  final currentUserId = currentUser?.id;

  if (currentUserId == null) return false;

  try {
    final apiClient = ref.read(simpleApiClientProvider);
    final response = await apiClient.get('${CarnowBackendConfig.apiBaseUrl}/user/profile');

    if (response.isSuccess && response.data != null) {
      final data = response.data as Map<String, dynamic>;
      final role = data['role'] as String?;

      return role == 'admin' || role == 'super_admin';
    }

    return false;
  } catch (e) {
    return false;
  }
}

/// مزود المشرفين فقط
@riverpod
Future<List<UserModel>> adminUsers(Ref ref) async {
  final allUsers = await ref.watch(adminManagementProvider.future);
  return allUsers.where((user) => user.role == 'admin').toList();
}

/// مزود المستخدمين العاديين (غير المشرفين)
@riverpod
Future<List<UserModel>> regularUsers(Ref ref) async {
  final allUsers = await ref.watch(adminManagementProvider.future);
  return allUsers.where((user) => user.role != 'admin').toList();
}
