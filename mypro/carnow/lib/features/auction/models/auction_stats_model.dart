import 'package:freezed_annotation/freezed_annotation.dart';

part 'auction_stats_model.freezed.dart';
part 'auction_stats_model.g.dart';

@freezed
abstract class AuctionStatsModel with _$AuctionStatsModel {
  const factory AuctionStatsModel({
    @Default(0) int bidCount,
    @Default(0) int bidders,
  }) = _AuctionStatsModel;

  factory AuctionStatsModel.fromJson(Map<String, dynamic> json) =>
      _$AuctionStatsModelFromJson(json);
}
