// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'bids_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$BidModel {

 String get id; String get productId; String get bidderId; double get bidAmount; BidStatus get status; String? get notes; bool get isAutomaticBid; double? get maxBidAmount;@JsonKey(includeFromJson: true, includeToJson: false) DateTime? get createdAt;@JsonKey(includeFromJson: true, includeToJson: false) DateTime? get updatedAt;
/// Create a copy of BidModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$BidModelCopyWith<BidModel> get copyWith => _$BidModelCopyWithImpl<BidModel>(this as BidModel, _$identity);

  /// Serializes this BidModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is BidModel&&(identical(other.id, id) || other.id == id)&&(identical(other.productId, productId) || other.productId == productId)&&(identical(other.bidderId, bidderId) || other.bidderId == bidderId)&&(identical(other.bidAmount, bidAmount) || other.bidAmount == bidAmount)&&(identical(other.status, status) || other.status == status)&&(identical(other.notes, notes) || other.notes == notes)&&(identical(other.isAutomaticBid, isAutomaticBid) || other.isAutomaticBid == isAutomaticBid)&&(identical(other.maxBidAmount, maxBidAmount) || other.maxBidAmount == maxBidAmount)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,productId,bidderId,bidAmount,status,notes,isAutomaticBid,maxBidAmount,createdAt,updatedAt);

@override
String toString() {
  return 'BidModel(id: $id, productId: $productId, bidderId: $bidderId, bidAmount: $bidAmount, status: $status, notes: $notes, isAutomaticBid: $isAutomaticBid, maxBidAmount: $maxBidAmount, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class $BidModelCopyWith<$Res>  {
  factory $BidModelCopyWith(BidModel value, $Res Function(BidModel) _then) = _$BidModelCopyWithImpl;
@useResult
$Res call({
 String id, String productId, String bidderId, double bidAmount, BidStatus status, String? notes, bool isAutomaticBid, double? maxBidAmount,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? createdAt,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? updatedAt
});




}
/// @nodoc
class _$BidModelCopyWithImpl<$Res>
    implements $BidModelCopyWith<$Res> {
  _$BidModelCopyWithImpl(this._self, this._then);

  final BidModel _self;
  final $Res Function(BidModel) _then;

/// Create a copy of BidModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? productId = null,Object? bidderId = null,Object? bidAmount = null,Object? status = null,Object? notes = freezed,Object? isAutomaticBid = null,Object? maxBidAmount = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,productId: null == productId ? _self.productId : productId // ignore: cast_nullable_to_non_nullable
as String,bidderId: null == bidderId ? _self.bidderId : bidderId // ignore: cast_nullable_to_non_nullable
as String,bidAmount: null == bidAmount ? _self.bidAmount : bidAmount // ignore: cast_nullable_to_non_nullable
as double,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as BidStatus,notes: freezed == notes ? _self.notes : notes // ignore: cast_nullable_to_non_nullable
as String?,isAutomaticBid: null == isAutomaticBid ? _self.isAutomaticBid : isAutomaticBid // ignore: cast_nullable_to_non_nullable
as bool,maxBidAmount: freezed == maxBidAmount ? _self.maxBidAmount : maxBidAmount // ignore: cast_nullable_to_non_nullable
as double?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// Adds pattern-matching-related methods to [BidModel].
extension BidModelPatterns on BidModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _BidModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _BidModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _BidModel value)  $default,){
final _that = this;
switch (_that) {
case _BidModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _BidModel value)?  $default,){
final _that = this;
switch (_that) {
case _BidModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String productId,  String bidderId,  double bidAmount,  BidStatus status,  String? notes,  bool isAutomaticBid,  double? maxBidAmount, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _BidModel() when $default != null:
return $default(_that.id,_that.productId,_that.bidderId,_that.bidAmount,_that.status,_that.notes,_that.isAutomaticBid,_that.maxBidAmount,_that.createdAt,_that.updatedAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String productId,  String bidderId,  double bidAmount,  BidStatus status,  String? notes,  bool isAutomaticBid,  double? maxBidAmount, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt)  $default,) {final _that = this;
switch (_that) {
case _BidModel():
return $default(_that.id,_that.productId,_that.bidderId,_that.bidAmount,_that.status,_that.notes,_that.isAutomaticBid,_that.maxBidAmount,_that.createdAt,_that.updatedAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String productId,  String bidderId,  double bidAmount,  BidStatus status,  String? notes,  bool isAutomaticBid,  double? maxBidAmount, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt)?  $default,) {final _that = this;
switch (_that) {
case _BidModel() when $default != null:
return $default(_that.id,_that.productId,_that.bidderId,_that.bidAmount,_that.status,_that.notes,_that.isAutomaticBid,_that.maxBidAmount,_that.createdAt,_that.updatedAt);case _:
  return null;

}
}

}

/// @nodoc

@JsonSerializable(fieldRename: FieldRename.snake, explicitToJson: true)
class _BidModel implements BidModel {
  const _BidModel({required this.id, required this.productId, required this.bidderId, required this.bidAmount, required this.status, this.notes, this.isAutomaticBid = false, this.maxBidAmount, @JsonKey(includeFromJson: true, includeToJson: false) this.createdAt, @JsonKey(includeFromJson: true, includeToJson: false) this.updatedAt});
  factory _BidModel.fromJson(Map<String, dynamic> json) => _$BidModelFromJson(json);

@override final  String id;
@override final  String productId;
@override final  String bidderId;
@override final  double bidAmount;
@override final  BidStatus status;
@override final  String? notes;
@override@JsonKey() final  bool isAutomaticBid;
@override final  double? maxBidAmount;
@override@JsonKey(includeFromJson: true, includeToJson: false) final  DateTime? createdAt;
@override@JsonKey(includeFromJson: true, includeToJson: false) final  DateTime? updatedAt;

/// Create a copy of BidModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$BidModelCopyWith<_BidModel> get copyWith => __$BidModelCopyWithImpl<_BidModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$BidModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _BidModel&&(identical(other.id, id) || other.id == id)&&(identical(other.productId, productId) || other.productId == productId)&&(identical(other.bidderId, bidderId) || other.bidderId == bidderId)&&(identical(other.bidAmount, bidAmount) || other.bidAmount == bidAmount)&&(identical(other.status, status) || other.status == status)&&(identical(other.notes, notes) || other.notes == notes)&&(identical(other.isAutomaticBid, isAutomaticBid) || other.isAutomaticBid == isAutomaticBid)&&(identical(other.maxBidAmount, maxBidAmount) || other.maxBidAmount == maxBidAmount)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,productId,bidderId,bidAmount,status,notes,isAutomaticBid,maxBidAmount,createdAt,updatedAt);

@override
String toString() {
  return 'BidModel(id: $id, productId: $productId, bidderId: $bidderId, bidAmount: $bidAmount, status: $status, notes: $notes, isAutomaticBid: $isAutomaticBid, maxBidAmount: $maxBidAmount, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class _$BidModelCopyWith<$Res> implements $BidModelCopyWith<$Res> {
  factory _$BidModelCopyWith(_BidModel value, $Res Function(_BidModel) _then) = __$BidModelCopyWithImpl;
@override @useResult
$Res call({
 String id, String productId, String bidderId, double bidAmount, BidStatus status, String? notes, bool isAutomaticBid, double? maxBidAmount,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? createdAt,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? updatedAt
});




}
/// @nodoc
class __$BidModelCopyWithImpl<$Res>
    implements _$BidModelCopyWith<$Res> {
  __$BidModelCopyWithImpl(this._self, this._then);

  final _BidModel _self;
  final $Res Function(_BidModel) _then;

/// Create a copy of BidModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? productId = null,Object? bidderId = null,Object? bidAmount = null,Object? status = null,Object? notes = freezed,Object? isAutomaticBid = null,Object? maxBidAmount = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_BidModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,productId: null == productId ? _self.productId : productId // ignore: cast_nullable_to_non_nullable
as String,bidderId: null == bidderId ? _self.bidderId : bidderId // ignore: cast_nullable_to_non_nullable
as String,bidAmount: null == bidAmount ? _self.bidAmount : bidAmount // ignore: cast_nullable_to_non_nullable
as double,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as BidStatus,notes: freezed == notes ? _self.notes : notes // ignore: cast_nullable_to_non_nullable
as String?,isAutomaticBid: null == isAutomaticBid ? _self.isAutomaticBid : isAutomaticBid // ignore: cast_nullable_to_non_nullable
as bool,maxBidAmount: freezed == maxBidAmount ? _self.maxBidAmount : maxBidAmount // ignore: cast_nullable_to_non_nullable
as double?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}


/// @nodoc
mixin _$CompleteBidModel {

 BidModel get bid; String get productName; String? get productImageUrl; String get bidderName; String? get bidderAvatar; String? get sellerId; String? get sellerName;
/// Create a copy of CompleteBidModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CompleteBidModelCopyWith<CompleteBidModel> get copyWith => _$CompleteBidModelCopyWithImpl<CompleteBidModel>(this as CompleteBidModel, _$identity);

  /// Serializes this CompleteBidModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CompleteBidModel&&(identical(other.bid, bid) || other.bid == bid)&&(identical(other.productName, productName) || other.productName == productName)&&(identical(other.productImageUrl, productImageUrl) || other.productImageUrl == productImageUrl)&&(identical(other.bidderName, bidderName) || other.bidderName == bidderName)&&(identical(other.bidderAvatar, bidderAvatar) || other.bidderAvatar == bidderAvatar)&&(identical(other.sellerId, sellerId) || other.sellerId == sellerId)&&(identical(other.sellerName, sellerName) || other.sellerName == sellerName));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,bid,productName,productImageUrl,bidderName,bidderAvatar,sellerId,sellerName);

@override
String toString() {
  return 'CompleteBidModel(bid: $bid, productName: $productName, productImageUrl: $productImageUrl, bidderName: $bidderName, bidderAvatar: $bidderAvatar, sellerId: $sellerId, sellerName: $sellerName)';
}


}

/// @nodoc
abstract mixin class $CompleteBidModelCopyWith<$Res>  {
  factory $CompleteBidModelCopyWith(CompleteBidModel value, $Res Function(CompleteBidModel) _then) = _$CompleteBidModelCopyWithImpl;
@useResult
$Res call({
 BidModel bid, String productName, String? productImageUrl, String bidderName, String? bidderAvatar, String? sellerId, String? sellerName
});


$BidModelCopyWith<$Res> get bid;

}
/// @nodoc
class _$CompleteBidModelCopyWithImpl<$Res>
    implements $CompleteBidModelCopyWith<$Res> {
  _$CompleteBidModelCopyWithImpl(this._self, this._then);

  final CompleteBidModel _self;
  final $Res Function(CompleteBidModel) _then;

/// Create a copy of CompleteBidModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? bid = null,Object? productName = null,Object? productImageUrl = freezed,Object? bidderName = null,Object? bidderAvatar = freezed,Object? sellerId = freezed,Object? sellerName = freezed,}) {
  return _then(_self.copyWith(
bid: null == bid ? _self.bid : bid // ignore: cast_nullable_to_non_nullable
as BidModel,productName: null == productName ? _self.productName : productName // ignore: cast_nullable_to_non_nullable
as String,productImageUrl: freezed == productImageUrl ? _self.productImageUrl : productImageUrl // ignore: cast_nullable_to_non_nullable
as String?,bidderName: null == bidderName ? _self.bidderName : bidderName // ignore: cast_nullable_to_non_nullable
as String,bidderAvatar: freezed == bidderAvatar ? _self.bidderAvatar : bidderAvatar // ignore: cast_nullable_to_non_nullable
as String?,sellerId: freezed == sellerId ? _self.sellerId : sellerId // ignore: cast_nullable_to_non_nullable
as String?,sellerName: freezed == sellerName ? _self.sellerName : sellerName // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}
/// Create a copy of CompleteBidModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$BidModelCopyWith<$Res> get bid {
  
  return $BidModelCopyWith<$Res>(_self.bid, (value) {
    return _then(_self.copyWith(bid: value));
  });
}
}


/// Adds pattern-matching-related methods to [CompleteBidModel].
extension CompleteBidModelPatterns on CompleteBidModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _CompleteBidModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _CompleteBidModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _CompleteBidModel value)  $default,){
final _that = this;
switch (_that) {
case _CompleteBidModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _CompleteBidModel value)?  $default,){
final _that = this;
switch (_that) {
case _CompleteBidModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( BidModel bid,  String productName,  String? productImageUrl,  String bidderName,  String? bidderAvatar,  String? sellerId,  String? sellerName)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _CompleteBidModel() when $default != null:
return $default(_that.bid,_that.productName,_that.productImageUrl,_that.bidderName,_that.bidderAvatar,_that.sellerId,_that.sellerName);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( BidModel bid,  String productName,  String? productImageUrl,  String bidderName,  String? bidderAvatar,  String? sellerId,  String? sellerName)  $default,) {final _that = this;
switch (_that) {
case _CompleteBidModel():
return $default(_that.bid,_that.productName,_that.productImageUrl,_that.bidderName,_that.bidderAvatar,_that.sellerId,_that.sellerName);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( BidModel bid,  String productName,  String? productImageUrl,  String bidderName,  String? bidderAvatar,  String? sellerId,  String? sellerName)?  $default,) {final _that = this;
switch (_that) {
case _CompleteBidModel() when $default != null:
return $default(_that.bid,_that.productName,_that.productImageUrl,_that.bidderName,_that.bidderAvatar,_that.sellerId,_that.sellerName);case _:
  return null;

}
}

}

/// @nodoc

@JsonSerializable(fieldRename: FieldRename.snake, explicitToJson: true)
class _CompleteBidModel implements CompleteBidModel {
  const _CompleteBidModel({required this.bid, required this.productName, this.productImageUrl, required this.bidderName, this.bidderAvatar, this.sellerId, this.sellerName});
  factory _CompleteBidModel.fromJson(Map<String, dynamic> json) => _$CompleteBidModelFromJson(json);

@override final  BidModel bid;
@override final  String productName;
@override final  String? productImageUrl;
@override final  String bidderName;
@override final  String? bidderAvatar;
@override final  String? sellerId;
@override final  String? sellerName;

/// Create a copy of CompleteBidModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$CompleteBidModelCopyWith<_CompleteBidModel> get copyWith => __$CompleteBidModelCopyWithImpl<_CompleteBidModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$CompleteBidModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _CompleteBidModel&&(identical(other.bid, bid) || other.bid == bid)&&(identical(other.productName, productName) || other.productName == productName)&&(identical(other.productImageUrl, productImageUrl) || other.productImageUrl == productImageUrl)&&(identical(other.bidderName, bidderName) || other.bidderName == bidderName)&&(identical(other.bidderAvatar, bidderAvatar) || other.bidderAvatar == bidderAvatar)&&(identical(other.sellerId, sellerId) || other.sellerId == sellerId)&&(identical(other.sellerName, sellerName) || other.sellerName == sellerName));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,bid,productName,productImageUrl,bidderName,bidderAvatar,sellerId,sellerName);

@override
String toString() {
  return 'CompleteBidModel(bid: $bid, productName: $productName, productImageUrl: $productImageUrl, bidderName: $bidderName, bidderAvatar: $bidderAvatar, sellerId: $sellerId, sellerName: $sellerName)';
}


}

/// @nodoc
abstract mixin class _$CompleteBidModelCopyWith<$Res> implements $CompleteBidModelCopyWith<$Res> {
  factory _$CompleteBidModelCopyWith(_CompleteBidModel value, $Res Function(_CompleteBidModel) _then) = __$CompleteBidModelCopyWithImpl;
@override @useResult
$Res call({
 BidModel bid, String productName, String? productImageUrl, String bidderName, String? bidderAvatar, String? sellerId, String? sellerName
});


@override $BidModelCopyWith<$Res> get bid;

}
/// @nodoc
class __$CompleteBidModelCopyWithImpl<$Res>
    implements _$CompleteBidModelCopyWith<$Res> {
  __$CompleteBidModelCopyWithImpl(this._self, this._then);

  final _CompleteBidModel _self;
  final $Res Function(_CompleteBidModel) _then;

/// Create a copy of CompleteBidModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? bid = null,Object? productName = null,Object? productImageUrl = freezed,Object? bidderName = null,Object? bidderAvatar = freezed,Object? sellerId = freezed,Object? sellerName = freezed,}) {
  return _then(_CompleteBidModel(
bid: null == bid ? _self.bid : bid // ignore: cast_nullable_to_non_nullable
as BidModel,productName: null == productName ? _self.productName : productName // ignore: cast_nullable_to_non_nullable
as String,productImageUrl: freezed == productImageUrl ? _self.productImageUrl : productImageUrl // ignore: cast_nullable_to_non_nullable
as String?,bidderName: null == bidderName ? _self.bidderName : bidderName // ignore: cast_nullable_to_non_nullable
as String,bidderAvatar: freezed == bidderAvatar ? _self.bidderAvatar : bidderAvatar // ignore: cast_nullable_to_non_nullable
as String?,sellerId: freezed == sellerId ? _self.sellerId : sellerId // ignore: cast_nullable_to_non_nullable
as String?,sellerName: freezed == sellerName ? _self.sellerName : sellerName // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

/// Create a copy of CompleteBidModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$BidModelCopyWith<$Res> get bid {
  
  return $BidModelCopyWith<$Res>(_self.bid, (value) {
    return _then(_self.copyWith(bid: value));
  });
}
}

// dart format on
