// ============================================================================
// CarNow Unified Authentication System - Error Factory
// ============================================================================
// File: app_error_factory.dart
// Description: Factory for creating localized errors with proper codes and messages
// Forever Plan Architecture: Flutter (UI Only) → Go API → Supabase (Data Only)
// Created: 2024-01-20
// ============================================================================

import 'dart:io';
import 'package:dio/dio.dart';
import 'app_error.dart';

/// Factory class for creating standardized AppError instances
class AppErrorFactory {
  static const String _correlationIdPrefix = 'carnow_';

  /// Generate a unique correlation ID for error tracking
  static String _generateCorrelationId() {
    return '$_correlationIdPrefix${DateTime.now().millisecondsSinceEpoch}';
  }

  // ============================================================================
  // NETWORK ERRORS
  // ============================================================================

  static AppError networkError({
    String? details,
    Object? originalError,
    StackTrace? stackTrace,
  }) {
    return AppError(
      type: AppErrorType.networkError,
      code: 'NETWORK_ERROR',
      message: 'Network connection failed. Please check your internet connection.',
      messageAr: 'فشل الاتصال بالشبكة. يرجى التحقق من اتصال الإنترنت.',
      details: details ?? 'Unable to establish network connection',
      detailsAr: details ?? 'غير قادر على إنشاء اتصال بالشبكة',
      severity: AppErrorSeverity.high,
      originalError: originalError,
      stackTrace: stackTrace,
      isRetryable: true,
      timestamp: DateTime.now(),
      correlationId: _generateCorrelationId(),
    );
  }

  static AppError connectionTimeout({
    Duration? timeout,
    Object? originalError,
    StackTrace? stackTrace,
  }) {
    return AppError(
      type: AppErrorType.connectionTimeout,
      code: 'CONNECTION_TIMEOUT',
      message: 'Connection timed out. Please try again.',
      messageAr: 'انتهت مهلة الاتصال. يرجى المحاولة مرة أخرى.',
      details: timeout != null 
          ? 'Connection timed out after ${timeout.inSeconds} seconds'
          : 'Connection timed out',
      detailsAr: timeout != null 
          ? 'انتهت مهلة الاتصال بعد ${timeout.inSeconds} ثانية'
          : 'انتهت مهلة الاتصال',
      severity: AppErrorSeverity.medium,
      originalError: originalError,
      stackTrace: stackTrace,
      isRetryable: true,
      retryAfter: const Duration(seconds: 2),
      timestamp: DateTime.now(),
      correlationId: _generateCorrelationId(),
    );
  }

  static AppError noInternetConnection() {
    return AppError(
      type: AppErrorType.noInternetConnection,
      code: 'NO_INTERNET',
      message: 'No internet connection available.',
      messageAr: 'لا يوجد اتصال بالإنترنت متاح.',
      details: 'Please check your network settings and try again',
      detailsAr: 'يرجى التحقق من إعدادات الشبكة والمحاولة مرة أخرى',
      severity: AppErrorSeverity.high,
      isRetryable: true,
      retryAfter: const Duration(seconds: 5),
      timestamp: DateTime.now(),
      correlationId: _generateCorrelationId(),
    );
  }

  // ============================================================================
  // AUTHENTICATION ERRORS
  // ============================================================================

  static AppError invalidCredentials() {
    return AppError(
      type: AppErrorType.invalidCredentials,
      code: 'INVALID_CREDENTIALS',
      message: 'Invalid email or password. Please check your credentials.',
      messageAr: 'البريد الإلكتروني أو كلمة المرور غير صحيحة. يرجى التحقق من بياناتك.',
      details: 'The provided email and password combination is incorrect',
      detailsAr: 'مزيج البريد الإلكتروني وكلمة المرور المقدم غير صحيح',
      severity: AppErrorSeverity.medium,
      isRetryable: false,
      timestamp: DateTime.now(),
      correlationId: _generateCorrelationId(),
    );
  }

  static AppError accountNotFound() {
    return AppError(
      type: AppErrorType.accountNotFound,
      code: 'ACCOUNT_NOT_FOUND',
      message: 'Account not found. Please check your email or sign up.',
      messageAr: 'الحساب غير موجود. يرجى التحقق من بريدك الإلكتروني أو إنشاء حساب جديد.',
      details: 'No account exists with the provided email address',
      detailsAr: 'لا يوجد حساب بعنوان البريد الإلكتروني المقدم',
      severity: AppErrorSeverity.medium,
      isRetryable: false,
      timestamp: DateTime.now(),
      correlationId: _generateCorrelationId(),
    );
  }

  static AppError accountDisabled() {
    return AppError(
      type: AppErrorType.accountDisabled,
      code: 'ACCOUNT_DISABLED',
      message: 'Your account has been disabled. Please contact support.',
      messageAr: 'تم تعطيل حسابك. يرجى الاتصال بالدعم الفني.',
      details: 'Account access has been restricted by administrators',
      detailsAr: 'تم تقييد الوصول للحساب من قبل المشرفين',
      severity: AppErrorSeverity.high,
      isRetryable: false,
      timestamp: DateTime.now(),
      correlationId: _generateCorrelationId(),
    );
  }

  static AppError accountLocked({int? remainingTime}) {
    return AppError(
      type: AppErrorType.accountLocked,
      code: 'ACCOUNT_LOCKED',
      message: 'Account temporarily locked due to multiple failed login attempts.',
      messageAr: 'تم قفل الحساب مؤقتاً بسبب محاولات تسجيل دخول فاشلة متعددة.',
      details: remainingTime != null 
          ? 'Account will be unlocked in $remainingTime minutes'
          : 'Account is temporarily locked for security',
      detailsAr: remainingTime != null 
          ? 'سيتم إلغاء قفل الحساب خلال $remainingTime دقيقة'
          : 'الحساب مقفل مؤقتاً لأسباب أمنية',
      severity: AppErrorSeverity.high,
      isRetryable: false,
      retryAfter: Duration(minutes: remainingTime ?? 15),
      timestamp: DateTime.now(),
      correlationId: _generateCorrelationId(),
    );
  }

  static AppError emailNotVerified() {
    return AppError(
      type: AppErrorType.emailNotVerified,
      code: 'EMAIL_NOT_VERIFIED',
      message: 'Please verify your email address before signing in.',
      messageAr: 'يرجى التحقق من عنوان بريدك الإلكتروني قبل تسجيل الدخول.',
      details: 'Check your email for verification link',
      detailsAr: 'تحقق من بريدك الإلكتروني للحصول على رابط التحقق',
      severity: AppErrorSeverity.medium,
      isRetryable: false,
      timestamp: DateTime.now(),
      correlationId: _generateCorrelationId(),
    );
  }

  static AppError tokenExpired() {
    return AppError(
      type: AppErrorType.tokenExpired,
      code: 'TOKEN_EXPIRED',
      message: 'Your session has expired. Please sign in again.',
      messageAr: 'انتهت صلاحية جلستك. يرجى تسجيل الدخول مرة أخرى.',
      details: 'Authentication token has expired and needs renewal',
      detailsAr: 'انتهت صلاحية رمز المصادقة ويحتاج إلى تجديد',
      severity: AppErrorSeverity.medium,
      isRetryable: true,
      timestamp: DateTime.now(),
      correlationId: _generateCorrelationId(),
    );
  }

  // ============================================================================
  // VALIDATION ERRORS
  // ============================================================================

  static AppError invalidEmail() {
    return AppError(
      type: AppErrorType.invalidEmail,
      code: 'INVALID_EMAIL',
      message: 'Please enter a valid email address.',
      messageAr: 'يرجى إدخال عنوان بريد إلكتروني صحيح.',
      details: 'Email format is not valid',
      detailsAr: 'تنسيق البريد الإلكتروني غير صحيح',
      severity: AppErrorSeverity.low,
      isRetryable: false,
      timestamp: DateTime.now(),
      correlationId: _generateCorrelationId(),
    );
  }

  static AppError invalidPassword() {
    return AppError(
      type: AppErrorType.invalidPassword,
      code: 'INVALID_PASSWORD',
      message: 'Password must be at least 8 characters long.',
      messageAr: 'يجب أن تكون كلمة المرور 8 أحرف على الأقل.',
      details: 'Password does not meet minimum requirements',
      detailsAr: 'كلمة المرور لا تلبي الحد الأدنى من المتطلبات',
      severity: AppErrorSeverity.low,
      isRetryable: false,
      timestamp: DateTime.now(),
      correlationId: _generateCorrelationId(),
    );
  }

  static AppError passwordTooWeak() {
    return AppError(
      type: AppErrorType.passwordTooWeak,
      code: 'PASSWORD_TOO_WEAK',
      message: 'Password is too weak. Please use a stronger password.',
      messageAr: 'كلمة المرور ضعيفة جداً. يرجى استخدام كلمة مرور أقوى.',
      details: 'Password should contain uppercase, lowercase, numbers, and symbols',
      detailsAr: 'يجب أن تحتوي كلمة المرور على أحرف كبيرة وصغيرة وأرقام ورموز',
      severity: AppErrorSeverity.medium,
      isRetryable: false,
      timestamp: DateTime.now(),
      correlationId: _generateCorrelationId(),
    );
  }

  static AppError emailAlreadyExists() {
    return AppError(
      type: AppErrorType.emailAlreadyExists,
      code: 'EMAIL_ALREADY_EXISTS',
      message: 'An account with this email already exists.',
      messageAr: 'يوجد حساب بهذا البريد الإلكتروني بالفعل.',
      details: 'Please use a different email or try signing in',
      detailsAr: 'يرجى استخدام بريد إلكتروني مختلف أو محاولة تسجيل الدخول',
      severity: AppErrorSeverity.medium,
      isRetryable: false,
      timestamp: DateTime.now(),
      correlationId: _generateCorrelationId(),
    );
  }

  // ============================================================================
  // RATE LIMITING ERRORS
  // ============================================================================

  static AppError rateLimitExceeded({Duration? retryAfter}) {
    return AppError(
      type: AppErrorType.rateLimitExceeded,
      code: 'RATE_LIMIT_EXCEEDED',
      message: 'Too many requests. Please wait before trying again.',
      messageAr: 'طلبات كثيرة جداً. يرجى الانتظار قبل المحاولة مرة أخرى.',
      details: retryAfter != null 
          ? 'Please wait ${retryAfter.inSeconds} seconds before retrying'
          : 'Request rate limit has been exceeded',
      detailsAr: retryAfter != null 
          ? 'يرجى الانتظار ${retryAfter.inSeconds} ثانية قبل إعادة المحاولة'
          : 'تم تجاوز حد معدل الطلبات',
      severity: AppErrorSeverity.medium,
      isRetryable: true,
      retryAfter: retryAfter ?? const Duration(minutes: 1),
      timestamp: DateTime.now(),
      correlationId: _generateCorrelationId(),
    );
  }

  // ============================================================================
  // GOOGLE OAUTH ERRORS
  // ============================================================================

  static AppError googleAuthCancelled() {
    return AppError(
      type: AppErrorType.googleAuthCancelled,
      code: 'GOOGLE_AUTH_CANCELLED',
      message: 'Google sign-in was cancelled.',
      messageAr: 'تم إلغاء تسجيل الدخول عبر Google.',
      details: 'User cancelled the Google authentication process',
      detailsAr: 'ألغى المستخدم عملية المصادقة عبر Google',
      severity: AppErrorSeverity.low,
      isRetryable: false,
      timestamp: DateTime.now(),
      correlationId: _generateCorrelationId(),
    );
  }

  static AppError googleAuthFailed({String? reason}) {
    return AppError(
      type: AppErrorType.googleAuthFailed,
      code: 'GOOGLE_AUTH_FAILED',
      message: 'Google sign-in failed. Please try again.',
      messageAr: 'فشل تسجيل الدخول عبر Google. يرجى المحاولة مرة أخرى.',
      details: reason ?? 'Google authentication process failed',
      detailsAr: reason ?? 'فشلت عملية المصادقة عبر Google',
      severity: AppErrorSeverity.medium,
      isRetryable: true,
      timestamp: DateTime.now(),
      correlationId: _generateCorrelationId(),
    );
  }

  // ============================================================================
  // SERVER ERRORS
  // ============================================================================

  static AppError internalServerError() {
    return AppError(
      type: AppErrorType.internalServerError,
      code: 'INTERNAL_SERVER_ERROR',
      message: 'Internal server error. Please try again later.',
      messageAr: 'خطأ داخلي في الخادم. يرجى المحاولة لاحقاً.',
      details: 'The server encountered an unexpected error',
      detailsAr: 'واجه الخادم خطأً غير متوقع',
      severity: AppErrorSeverity.high,
      isRetryable: true,
      retryAfter: const Duration(minutes: 2),
      timestamp: DateTime.now(),
      correlationId: _generateCorrelationId(),
    );
  }

  static AppError serviceUnavailable() {
    return AppError(
      type: AppErrorType.serviceUnavailable,
      code: 'SERVICE_UNAVAILABLE',
      message: 'Service temporarily unavailable. Please try again later.',
      messageAr: 'الخدمة غير متاحة مؤقتاً. يرجى المحاولة لاحقاً.',
      details: 'The service is currently under maintenance',
      detailsAr: 'الخدمة قيد الصيانة حالياً',
      severity: AppErrorSeverity.high,
      isRetryable: true,
      retryAfter: const Duration(minutes: 5),
      timestamp: DateTime.now(),
      correlationId: _generateCorrelationId(),
    );
  }

  // ============================================================================
  // GENERIC ERROR CREATION FROM EXCEPTIONS
  // ============================================================================

  /// Create AppError from DioException (HTTP errors)
  static AppError fromDioException(DioException exception) {
    switch (exception.type) {
      case DioExceptionType.connectionTimeout:
        return connectionTimeout(
          timeout: exception.requestOptions.connectTimeout,
          originalError: exception,
          stackTrace: exception.stackTrace,
        );
      
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return connectionTimeout(
          originalError: exception,
          stackTrace: exception.stackTrace,
        );
      
      case DioExceptionType.badResponse:
        final statusCode = exception.response?.statusCode;
        switch (statusCode) {
          case 401:
            return invalidCredentials();
          case 403:
            return AppError(
              type: AppErrorType.forbidden,
              code: 'FORBIDDEN',
              message: 'Access denied.',
              messageAr: 'تم رفض الوصول.',
              severity: AppErrorSeverity.high,
              originalError: exception,
              stackTrace: exception.stackTrace,
              timestamp: DateTime.now(),
              correlationId: _generateCorrelationId(),
            );
          case 404:
            return accountNotFound();
          case 429:
            return rateLimitExceeded();
          case 500:
            return internalServerError();
          case 503:
            return serviceUnavailable();
          default:
            return networkError(
              originalError: exception,
              stackTrace: exception.stackTrace,
            );
        }
      
      case DioExceptionType.cancel:
        return AppError(
          type: AppErrorType.cancelled,
          code: 'REQUEST_CANCELLED',
          message: 'Request was cancelled.',
          messageAr: 'تم إلغاء الطلب.',
          severity: AppErrorSeverity.low,
          originalError: exception,
          stackTrace: exception.stackTrace,
          timestamp: DateTime.now(),
          correlationId: _generateCorrelationId(),
        );
      
      case DioExceptionType.unknown:
      default:
        if (exception.error is SocketException) {
          return noInternetConnection();
        }
        return networkError(
          originalError: exception,
          stackTrace: exception.stackTrace,
        );
    }
  }

  /// Create AppError from generic Exception
  static AppError fromException(Exception exception, {StackTrace? stackTrace}) {
    if (exception is DioException) {
      return fromDioException(exception);
    }
    
    if (exception is SocketException) {
      return noInternetConnection();
    }
    
    return AppError(
      type: AppErrorType.unknown,
      code: 'UNKNOWN_ERROR',
      message: 'An unexpected error occurred.',
      messageAr: 'حدث خطأ غير متوقع.',
      details: exception.toString(),
      detailsAr: 'خطأ غير معروف',
      severity: AppErrorSeverity.medium,
      originalError: exception,
      stackTrace: stackTrace,
      isRetryable: false,
      timestamp: DateTime.now(),
      correlationId: _generateCorrelationId(),
    );
  }

  /// Create AppError from error response body
  static AppError fromErrorResponse(Map<String, dynamic> errorData) {
    final errorCode = errorData['error_code'] as String?;
    final message = errorData['message'] as String?;
    final messageAr = errorData['message_ar'] as String?;
    
    // Map common error codes to AppErrorType
    AppErrorType type = AppErrorType.unknown;
    switch (errorCode) {
      case 'INVALID_CREDENTIALS':
        type = AppErrorType.invalidCredentials;
        break;
      case 'ACCOUNT_NOT_FOUND':
        type = AppErrorType.accountNotFound;
        break;
      case 'EMAIL_ALREADY_EXISTS':
        type = AppErrorType.emailAlreadyExists;
        break;
      case 'RATE_LIMIT_EXCEEDED':
        type = AppErrorType.rateLimitExceeded;
        break;
      default:
        type = AppErrorType.unknown;
    }
    
    return AppError(
      type: type,
      code: errorCode ?? 'UNKNOWN_ERROR',
      message: message ?? 'An error occurred',
      messageAr: messageAr ?? 'حدث خطأ',
      severity: AppErrorSeverity.medium,
      metadata: errorData,
      timestamp: DateTime.now(),
      correlationId: _generateCorrelationId(),
    );
  }
}
