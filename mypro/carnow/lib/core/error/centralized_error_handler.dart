/// ============================================================================
/// CENTRALIZED ERROR HANDLER - Enhanced Error Management System
/// ============================================================================
/// 
/// Centralized error handling with proper error codes and localized messages
/// Task 21: Enhance error handling and user feedback
/// Forever Plan Architecture: Flutter (UI Only) → Go API → Supabase (Data Only)
/// ============================================================================
library;

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Enhanced error codes for comprehensive error categorization
enum CarNowErrorCode {
  // Network Errors (1000-1099)
  networkUnavailable('NETWORK_1000', 'شبكة غير متاحة', 'Network unavailable'),
  connectionTimeout('NETWORK_1001', 'انتهت مهلة الاتصال', 'Connection timeout'),
  serverUnavailable('NETWORK_1002', 'الخادم غير متاح', 'Server unavailable'),
  requestFailed('NETWORK_1003', 'فشل في الطلب', 'Request failed'),
  
  // Authentication Errors (2000-2099)
  invalidCredentials('AUTH_2000', 'بيانات اعتماد غير صحيحة', 'Invalid credentials'),
  userNotFound('AUTH_2001', 'المستخدم غير موجود', 'User not found'),
  emailNotVerified('AUTH_2002', 'البريد الإلكتروني غير مؤكد', 'Email not verified'),
  accountDisabled('AUTH_2003', 'الحساب معطل', 'Account disabled'),
  tokenExpired('AUTH_2004', 'انتهت صلاحية الرمز', 'Token expired'),
  invalidToken('AUTH_2005', 'رمز غير صالح', 'Invalid token'),
  sessionExpired('AUTH_2006', 'انتهت صلاحية الجلسة', 'Session expired'),
  
  // Validation Errors (3000-3099)
  invalidEmail('VALIDATION_3000', 'بريد إلكتروني غير صالح', 'Invalid email'),
  weakPassword('VALIDATION_3001', 'كلمة مرور ضعيفة', 'Weak password'),
  passwordMismatch('VALIDATION_3002', 'كلمات المرور غير متطابقة', 'Password mismatch'),
  requiredField('VALIDATION_3003', 'حقل مطلوب', 'Required field'),
  invalidFormat('VALIDATION_3004', 'تنسيق غير صالح', 'Invalid format'),
  
  // Rate Limiting Errors (4000-4099)
  rateLimitExceeded('RATE_4000', 'تم تجاوز حد المعدل', 'Rate limit exceeded'),
  tooManyRequests('RATE_4001', 'طلبات كثيرة جداً', 'Too many requests'),
  
  // Permission Errors (5000-5099)
  accessDenied('PERMISSION_5000', 'تم رفض الوصول', 'Access denied'),
  insufficientPermissions('PERMISSION_5001', 'صلاحيات غير كافية', 'Insufficient permissions'),
  
  // Data Errors (6000-6099)
  dataNotFound('DATA_6000', 'البيانات غير موجودة', 'Data not found'),
  dataCorrupted('DATA_6001', 'البيانات تالفة', 'Data corrupted'),
  duplicateEntry('DATA_6002', 'إدخال مكرر', 'Duplicate entry'),
  
  // System Errors (9000-9099)
  unknownError('SYSTEM_9000', 'خطأ غير معروف', 'Unknown error'),
  internalError('SYSTEM_9001', 'خطأ داخلي', 'Internal error'),
  serviceUnavailable('SYSTEM_9002', 'الخدمة غير متاحة', 'Service unavailable');

  const CarNowErrorCode(this.code, this.arabicMessage, this.englishMessage);
  
  final String code;
  final String arabicMessage;
  final String englishMessage;
  
  /// Get localized message based on current locale
  String getLocalizedMessage(Locale locale) {
    return locale.languageCode == 'ar' ? arabicMessage : englishMessage;
  }
}

/// Enhanced error severity levels
enum ErrorSeverity {
  info('INFO', Colors.blue),
  warning('WARNING', Colors.orange),
  error('ERROR', Colors.red),
  critical('CRITICAL', Colors.deepPurple);

  const ErrorSeverity(this.name, this.color);
  
  final String name;
  final Color color;
}

/// Comprehensive error information model
class CarNowError {
  const CarNowError({
    required this.code,
    required this.message,
    this.severity = ErrorSeverity.error,
    this.details,
    this.stackTrace,
    this.timestamp,
    this.context,
    this.retryable = false,
    this.userAction,
  });

  final CarNowErrorCode code;
  final String message;
  final ErrorSeverity severity;
  final String? details;
  final StackTrace? stackTrace;
  final DateTime? timestamp;
  final String? context;
  final bool retryable;
  final String? userAction;

  /// Create error from exception
  factory CarNowError.fromException(
    Object exception, {
    StackTrace? stackTrace,
    String? context,
    CarNowErrorCode? errorCode,
  }) {
    CarNowErrorCode code;
    String message;
    bool retryable = false;

    if (exception is CarNowError) {
      return exception;
    }

    // Map common exceptions to error codes
    if (exception.toString().contains('SocketException') ||
        exception.toString().contains('NetworkException')) {
      code = CarNowErrorCode.networkUnavailable;
      message = 'Network connection failed';
      retryable = true;
    } else if (exception.toString().contains('TimeoutException')) {
      code = CarNowErrorCode.connectionTimeout;
      message = 'Request timed out';
      retryable = true;
    } else if (exception.toString().contains('FormatException')) {
      code = CarNowErrorCode.invalidFormat;
      message = 'Invalid data format';
    } else {
      code = errorCode ?? CarNowErrorCode.unknownError;
      message = exception.toString();
    }

    return CarNowError(
      code: code,
      message: message,
      details: exception.toString(),
      stackTrace: stackTrace,
      timestamp: DateTime.now(),
      context: context,
      retryable: retryable,
    );
  }

  /// Get localized error message
  String getLocalizedMessage(Locale locale) {
    return code.getLocalizedMessage(locale);
  }

  /// Create copy with additional information
  CarNowError copyWith({
    CarNowErrorCode? code,
    String? message,
    ErrorSeverity? severity,
    String? details,
    StackTrace? stackTrace,
    DateTime? timestamp,
    String? context,
    bool? retryable,
    String? userAction,
  }) {
    return CarNowError(
      code: code ?? this.code,
      message: message ?? this.message,
      severity: severity ?? this.severity,
      details: details ?? this.details,
      stackTrace: stackTrace ?? this.stackTrace,
      timestamp: timestamp ?? this.timestamp,
      context: context ?? this.context,
      retryable: retryable ?? this.retryable,
      userAction: userAction ?? this.userAction,
    );
  }

  @override
  String toString() {
    return 'CarNowError(code: ${code.code}, message: $message, severity: ${severity.name})';
  }
}

/// Centralized error handler service
class CentralizedErrorHandler {
  CentralizedErrorHandler({
    this.enableLogging = true,
    this.enableCrashReporting = false,
  });

  final bool enableLogging;
  final bool enableCrashReporting;
  final List<CarNowError> _errorHistory = [];

  /// Handle error with comprehensive processing
  Future<void> handleError(
    Object error, {
    StackTrace? stackTrace,
    String? context,
    CarNowErrorCode? errorCode,
    bool showToUser = true,
    VoidCallback? onRetry,
  }) async {
    final carNowError = CarNowError.fromException(
      error,
      stackTrace: stackTrace,
      context: context,
      errorCode: errorCode,
    );

    // Add to error history
    _errorHistory.add(carNowError);
    
    // Keep only last 100 errors
    if (_errorHistory.length > 100) {
      _errorHistory.removeAt(0);
    }

    // Log error if enabled
    if (enableLogging) {
      _logError(carNowError);
    }

    // Report to crash reporting service if enabled
    if (enableCrashReporting && carNowError.severity == ErrorSeverity.critical) {
      await _reportToCrashService(carNowError);
    }

    // Show error to user if requested
    if (showToUser) {
      _showErrorToUser(carNowError, onRetry: onRetry);
    }
  }

  /// Log error with structured information
  void _logError(CarNowError error) {
    final logMessage = '''
=== CarNow Error Log ===
Code: ${error.code.code}
Message: ${error.message}
Severity: ${error.severity.name}
Context: ${error.context ?? 'Unknown'}
Timestamp: ${error.timestamp ?? DateTime.now()}
Details: ${error.details ?? 'No additional details'}
Retryable: ${error.retryable}
${error.stackTrace != null ? 'Stack Trace:\n${error.stackTrace}' : ''}
========================
''';

    if (kDebugMode) {
      debugPrint(logMessage);
    }
  }

  /// Report critical errors to crash reporting service
  Future<void> _reportToCrashService(CarNowError error) async {
    // TODO: Integrate with crash reporting service (Firebase Crashlytics, Sentry, etc.)
    if (kDebugMode) {
      debugPrint('CRITICAL ERROR REPORTED: ${error.code.code} - ${error.message}');
    }
  }

  /// Show error to user with appropriate UI
  void _showErrorToUser(CarNowError error, {VoidCallback? onRetry}) {
    // This will be implemented with proper UI context in the provider
    if (kDebugMode) {
      debugPrint('SHOW TO USER: ${error.getLocalizedMessage(const Locale('ar'))}');
    }
  }

  /// Get error history for debugging
  List<CarNowError> get errorHistory => List.unmodifiable(_errorHistory);

  /// Clear error history
  void clearErrorHistory() {
    _errorHistory.clear();
  }

  /// Get errors by severity
  List<CarNowError> getErrorsBySeverity(ErrorSeverity severity) {
    return _errorHistory.where((error) => error.severity == severity).toList();
  }

  /// Get recent errors (last N errors)
  List<CarNowError> getRecentErrors(int count) {
    if (_errorHistory.length <= count) {
      return List.from(_errorHistory);
    }
    return _errorHistory.sublist(_errorHistory.length - count);
  }
}

/// Riverpod provider for centralized error handler
final centralizedErrorHandlerProvider = Provider<CentralizedErrorHandler>((ref) {
  return CentralizedErrorHandler(
    enableLogging: true,
    enableCrashReporting: false, // Enable in production
  );
});

/// Error state notifier for UI integration
class ErrorStateNotifier extends StateNotifier<CarNowError?> {
  ErrorStateNotifier(this._errorHandler) : super(null);

  final CentralizedErrorHandler _errorHandler;

  /// Show error to user
  void showError(CarNowError error) {
    state = error;
    _errorHandler.handleError(error, showToUser: false);
  }

  /// Clear current error
  void clearError() {
    state = null;
  }

  /// Handle error and show to user
  Future<void> handleAndShowError(
    Object error, {
    StackTrace? stackTrace,
    String? context,
    CarNowErrorCode? errorCode,
  }) async {
    final carNowError = CarNowError.fromException(
      error,
      stackTrace: stackTrace,
      context: context,
      errorCode: errorCode,
    );
    
    await _errorHandler.handleError(carNowError, showToUser: false);
    state = carNowError;
  }
}

/// Provider for error state notifier
final errorStateProvider = StateNotifierProvider<ErrorStateNotifier, CarNowError?>((ref) {
  final errorHandler = ref.read(centralizedErrorHandlerProvider);
  return ErrorStateNotifier(errorHandler);
});

/// Extension for easy error handling in providers
extension ErrorHandlingExtension on Ref {
  /// Handle error with centralized error handler
  Future<void> handleError(
    Object error, {
    StackTrace? stackTrace,
    String? context,
    CarNowErrorCode? errorCode,
    bool showToUser = true,
  }) async {
    final errorHandler = read(centralizedErrorHandlerProvider);
    await errorHandler.handleError(
      error,
      stackTrace: stackTrace,
      context: context,
      errorCode: errorCode,
      showToUser: showToUser,
    );

    if (showToUser) {
      final errorNotifier = read(errorStateProvider.notifier);
      await errorNotifier.handleAndShowError(
        error,
        stackTrace: stackTrace,
        context: context,
        errorCode: errorCode,
      );
    }
  }

  /// Show error to user
  void showErrorToUser(CarNowError error) {
    final errorNotifier = read(errorStateProvider.notifier);
    errorNotifier.showError(error);
  }

  /// Clear current error
  void clearError() {
    final errorNotifier = read(errorStateProvider.notifier);
    errorNotifier.clearError();
  }
}
