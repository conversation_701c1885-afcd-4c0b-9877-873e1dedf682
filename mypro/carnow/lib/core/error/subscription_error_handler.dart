import 'package:flutter/material.dart';
import '../models/subscription_error.dart';

/// Handles subscription-specific errors with user-friendly dialogs in Arabic
/// following Material 3 design principles
class SubscriptionErrorHandler {
  static const Duration _defaultRetryDelay = Duration(seconds: 2);
  static const int _maxRetryAttempts = 3;

  /// Shows appropriate error dialog based on the subscription error type
  static Future<void> handleSubscriptionError({
    required BuildContext context,
    required SubscriptionError error,
    VoidCallback? onRetry,
    VoidCallback? onCancel,
    bool showRetryOption = true,
  }) async {
    switch (error) {
      case NetworkError():
        await _showNetworkErrorDialog(
          context: context,
          error: error,
          onRetry: onRetry,
          onCancel: onCancel,
          showRetryOption: showRetryOption,
        );
        break;
      case DatabaseError():
        await _showDatabaseErrorDialog(
          context: context,
          error: error,
          onRetry: onRetry,
          onCancel: onCancel,
          showRetryOption: showRetryOption,
        );
        break;
      case ValidationError():
        await _showValidationErrorDialog(
          context: context,
          error: error,
          onCancel: onCancel,
        );
        break;
      case NavigationError():
        await _showNavigationErrorDialog(
          context: context,
          error: error,
          onRetry: onRetry,
          onCancel: onCancel,
        );
        break;
      case AuthenticationError():
        await _showAuthenticationErrorDialog(
          context: context,
          error: error,
          onCancel: onCancel,
        );
        break;
      case ServerError():
        await _showServerErrorDialog(
          context: context,
          error: error,
          onRetry: onRetry,
          onCancel: onCancel,
          showRetryOption: showRetryOption,
        );
        break;
      case PaymentError():
        await _showPaymentErrorDialog(
          context: context,
          error: error,
          onCancel: onCancel,
        );
        break;
      case BusinessLogicError():
        await _showBusinessLogicErrorDialog(
          context: context,
          error: error,
          onCancel: onCancel,
        );
        break;
      case UnknownError():
        await _showUnknownErrorDialog(
          context: context,
          error: error,
          onRetry: onRetry,
          onCancel: onCancel,
          showRetryOption: showRetryOption,
        );
        break;
    }
  }

  /// Shows network error dialog with retry option
  static Future<void> _showNetworkErrorDialog({
    required BuildContext context,
    required NetworkError error,
    VoidCallback? onRetry,
    VoidCallback? onCancel,
    bool showRetryOption = true,
  }) async {
    await showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          icon: Icon(
            Icons.wifi_off_rounded,
            color: Theme.of(context).colorScheme.error,
            size: 32,
          ),
          title: const Text(
            'خطأ في الاتصال',
            textAlign: TextAlign.center,
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                error.userFriendlyMessageArabic,
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              const SizedBox(height: 8),
              Text(
                error.suggestedActionArabic,
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                onCancel?.call();
              },
              child: const Text('إلغاء'),
            ),
            if (showRetryOption && onRetry != null)
              FilledButton.icon(
                onPressed: () {
                  Navigator.of(context).pop();
                  onRetry();
                },
                icon: const Icon(Icons.refresh_rounded),
                label: const Text('إعادة المحاولة'),
              ),
          ],
        );
      },
    );
  }

  /// Shows database error dialog with retry option
  static Future<void> _showDatabaseErrorDialog({
    required BuildContext context,
    required DatabaseError error,
    VoidCallback? onRetry,
    VoidCallback? onCancel,
    bool showRetryOption = true,
  }) async {
    await showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          icon: Icon(
            Icons.storage_rounded,
            color: Theme.of(context).colorScheme.error,
            size: 32,
          ),
          title: const Text(
            'خطأ في قاعدة البيانات',
            textAlign: TextAlign.center,
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                error.userFriendlyMessageArabic,
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              const SizedBox(height: 8),
              Text(
                error.suggestedActionArabic,
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                onCancel?.call();
              },
              child: const Text('إلغاء'),
            ),
            if (showRetryOption && onRetry != null)
              FilledButton.icon(
                onPressed: () {
                  Navigator.of(context).pop();
                  onRetry();
                },
                icon: const Icon(Icons.refresh_rounded),
                label: const Text('إعادة المحاولة'),
              ),
          ],
        );
      },
    );
  }

  /// Shows validation error dialog with field-specific messages
  static Future<void> _showValidationErrorDialog({
    required BuildContext context,
    required ValidationError error,
    VoidCallback? onCancel,
  }) async {
    await showDialog<void>(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return AlertDialog(
          icon: Icon(
            Icons.error_outline_rounded,
            color: Theme.of(context).colorScheme.error,
            size: 32,
          ),
          title: const Text(
            'بيانات غير صحيحة',
            textAlign: TextAlign.center,
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                error.userFriendlyMessageArabic,
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              if (error.fieldErrors.isNotEmpty) ...[
                const SizedBox(height: 16),
                Text(
                  'الأخطاء:',
                  style: Theme.of(
                    context,
                  ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                ...error.fieldErrors.entries.map(
                  (entry) => Padding(
                    padding: const EdgeInsets.only(bottom: 4),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Icon(
                          Icons.circle,
                          size: 6,
                          color: Theme.of(context).colorScheme.error,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            '${_getFieldNameInArabic(entry.key)}: ${entry.value}',
                            style: Theme.of(context).textTheme.bodySmall,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
              const SizedBox(height: 8),
              Text(
                error.suggestedActionArabic,
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ),
          actions: [
            FilledButton(
              onPressed: () {
                Navigator.of(context).pop();
                onCancel?.call();
              },
              child: const Text('موافق'),
            ),
          ],
        );
      },
    );
  }

  /// Shows navigation error dialog
  static Future<void> _showNavigationErrorDialog({
    required BuildContext context,
    required NavigationError error,
    VoidCallback? onRetry,
    VoidCallback? onCancel,
  }) async {
    await showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          icon: Icon(
            Icons.navigation_rounded,
            color: Theme.of(context).colorScheme.error,
            size: 32,
          ),
          title: const Text(
            'خطأ في التنقل',
            textAlign: TextAlign.center,
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                error.userFriendlyMessageArabic,
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              const SizedBox(height: 8),
              Text(
                error.suggestedActionArabic,
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                onCancel?.call();
              },
              child: const Text('إلغاء'),
            ),
            if (onRetry != null)
              FilledButton.icon(
                onPressed: () {
                  Navigator.of(context).pop();
                  onRetry();
                },
                icon: const Icon(Icons.refresh_rounded),
                label: const Text('إعادة المحاولة'),
              ),
          ],
        );
      },
    );
  }

  /// Shows authentication error dialog
  static Future<void> _showAuthenticationErrorDialog({
    required BuildContext context,
    required AuthenticationError error,
    VoidCallback? onCancel,
  }) async {
    await showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          icon: Icon(
            Icons.lock_outline_rounded,
            color: Theme.of(context).colorScheme.error,
            size: 32,
          ),
          title: const Text(
            'خطأ في المصادقة',
            textAlign: TextAlign.center,
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                error.userFriendlyMessageArabic,
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              const SizedBox(height: 8),
              Text(
                error.suggestedActionArabic,
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ),
          actions: [
            FilledButton(
              onPressed: () {
                Navigator.of(context).pop();
                onCancel?.call();
              },
              child: const Text('موافق'),
            ),
          ],
        );
      },
    );
  }

  /// Shows server error dialog with retry option
  static Future<void> _showServerErrorDialog({
    required BuildContext context,
    required ServerError error,
    VoidCallback? onRetry,
    VoidCallback? onCancel,
    bool showRetryOption = true,
  }) async {
    await showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          icon: Icon(
            Icons.cloud_off_rounded,
            color: Theme.of(context).colorScheme.error,
            size: 32,
          ),
          title: const Text(
            'خطأ في الخادم',
            textAlign: TextAlign.center,
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                error.userFriendlyMessageArabic,
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              const SizedBox(height: 8),
              Text(
                error.suggestedActionArabic,
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                onCancel?.call();
              },
              child: const Text('إلغاء'),
            ),
            if (showRetryOption && onRetry != null && error.isRetryable)
              FilledButton.icon(
                onPressed: () {
                  Navigator.of(context).pop();
                  onRetry();
                },
                icon: const Icon(Icons.refresh_rounded),
                label: const Text('إعادة المحاولة'),
              ),
          ],
        );
      },
    );
  }

  /// Shows payment error dialog
  static Future<void> _showPaymentErrorDialog({
    required BuildContext context,
    required PaymentError error,
    VoidCallback? onCancel,
  }) async {
    await showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          icon: Icon(
            Icons.payment_rounded,
            color: Theme.of(context).colorScheme.error,
            size: 32,
          ),
          title: const Text(
            'خطأ في عملية الدفع',
            textAlign: TextAlign.center,
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                error.userFriendlyMessageArabic,
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              const SizedBox(height: 8),
              Text(
                error.suggestedActionArabic,
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ),
          actions: [
            FilledButton(
              onPressed: () {
                Navigator.of(context).pop();
                onCancel?.call();
              },
              child: const Text('موافق'),
            ),
          ],
        );
      },
    );
  }

  /// Shows business logic error dialog
  static Future<void> _showBusinessLogicErrorDialog({
    required BuildContext context,
    required BusinessLogicError error,
    VoidCallback? onCancel,
  }) async {
    await showDialog<void>(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return AlertDialog(
          icon: Icon(
            Icons.business_rounded,
            color: Theme.of(context).colorScheme.error,
            size: 32,
          ),
          title: const Text(
            'خطأ في العملية',
            textAlign: TextAlign.center,
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                error.userFriendlyMessageArabic,
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              const SizedBox(height: 8),
              Text(
                error.suggestedActionArabic,
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ),
          actions: [
            FilledButton(
              onPressed: () {
                Navigator.of(context).pop();
                onCancel?.call();
              },
              child: const Text('موافق'),
            ),
          ],
        );
      },
    );
  }

  /// Shows unknown error dialog with retry option
  static Future<void> _showUnknownErrorDialog({
    required BuildContext context,
    required UnknownError error,
    VoidCallback? onRetry,
    VoidCallback? onCancel,
    bool showRetryOption = true,
  }) async {
    await showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          icon: Icon(
            Icons.help_outline_rounded,
            color: Theme.of(context).colorScheme.error,
            size: 32,
          ),
          title: const Text(
            'خطأ غير متوقع',
            textAlign: TextAlign.center,
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                error.userFriendlyMessageArabic,
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              const SizedBox(height: 8),
              Text(
                error.suggestedActionArabic,
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                onCancel?.call();
              },
              child: const Text('إلغاء'),
            ),
            if (showRetryOption && onRetry != null)
              FilledButton.icon(
                onPressed: () {
                  Navigator.of(context).pop();
                  onRetry();
                },
                icon: const Icon(Icons.refresh_rounded),
                label: const Text('إعادة المحاولة'),
              ),
          ],
        );
      },
    );
  }

  /// Shows a loading dialog during retry operations
  static Future<void> showRetryLoadingDialog({
    required BuildContext context,
    required String message,
  }) async {
    await showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const CircularProgressIndicator(),
              const SizedBox(height: 16),
              Text(
                message,
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ],
          ),
        );
      },
    );
  }

  /// Handles retry logic with exponential backoff
  static Future<T?> handleRetryOperation<T>({
    required BuildContext context,
    required Future<T> Function() operation,
    required SubscriptionError Function(Exception) errorMapper,
    int maxAttempts = _maxRetryAttempts,
    Duration initialDelay = _defaultRetryDelay,
    String? loadingMessage,
  }) async {
    int attempts = 0;
    Duration currentDelay = initialDelay;

    while (attempts < maxAttempts) {
      try {
        if (attempts > 0 && loadingMessage != null) {
          showRetryLoadingDialog(context: context, message: loadingMessage);
        }

        final result = await operation();

        if (attempts > 0 && loadingMessage != null) {
          Navigator.of(context).pop(); // Close loading dialog
        }

        return result;
      } catch (e) {
        attempts++;

        if (attempts > 0 && loadingMessage != null) {
          Navigator.of(context).pop(); // Close loading dialog
        }

        if (attempts >= maxAttempts) {
          final error = errorMapper(e as Exception);
          await handleSubscriptionError(
            context: context,
            error: error,
            showRetryOption: false,
          );
          return null;
        }

        // Wait before retry with exponential backoff
        await Future.delayed(currentDelay);
        currentDelay = Duration(
          milliseconds: (currentDelay.inMilliseconds * 1.5).round(),
        );
      }
    }

    return null;
  }

  /// Shows a snackbar for minor errors that don't require a dialog
  static void showErrorSnackBar({
    required BuildContext context,
    required SubscriptionError error,
    VoidCallback? onRetry,
  }) {
    final messenger = ScaffoldMessenger.of(context);

    messenger.showSnackBar(
      SnackBar(
        content: Text(
          error.userFriendlyMessageArabic,
          style: const TextStyle(color: Colors.white),
        ),
        backgroundColor: Theme.of(context).colorScheme.error,
        behavior: SnackBarBehavior.floating,
        action: error.isRetryable && onRetry != null
            ? SnackBarAction(
                label: 'إعادة المحاولة',
                textColor: Colors.white,
                onPressed: onRetry,
              )
            : null,
        duration: const Duration(seconds: 4),
      ),
    );
  }

  /// Converts field names to Arabic for validation errors
  static String _getFieldNameInArabic(String fieldName) {
    const fieldTranslations = {
      'storeName': 'اسم المتجر',
      'phone': 'رقم الهاتف',
      'city': 'المدينة',
      'address': 'العنوان',
      'description': 'الوصف',
      'planType': 'نوع الخطة',
      'price': 'السعر',
      'userId': 'معرف المستخدم',
      'email': 'البريد الإلكتروني',
      'name': 'الاسم',
      'general': 'عام',
    };

    return fieldTranslations[fieldName] ?? fieldName;
  }

  /// Creates a Material 3 error card widget for inline error display
  static Widget buildErrorCard({
    required BuildContext context,
    required SubscriptionError error,
    VoidCallback? onRetry,
    VoidCallback? onDismiss,
  }) {
    return Card(
      color: Theme.of(context).colorScheme.errorContainer,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              children: [
                Icon(
                  _getErrorIcon(error),
                  color: Theme.of(context).colorScheme.onErrorContainer,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    error.userFriendlyMessageArabic,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(context).colorScheme.onErrorContainer,
                    ),
                  ),
                ),
                if (onDismiss != null)
                  IconButton(
                    onPressed: onDismiss,
                    icon: Icon(
                      Icons.close_rounded,
                      color: Theme.of(context).colorScheme.onErrorContainer,
                    ),
                  ),
              ],
            ),
            if (error.isRetryable && onRetry != null) ...[
              const SizedBox(height: 12),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton.icon(
                    onPressed: onRetry,
                    icon: const Icon(Icons.refresh_rounded),
                    label: const Text('إعادة المحاولة'),
                    style: TextButton.styleFrom(
                      foregroundColor: Theme.of(
                        context,
                      ).colorScheme.onErrorContainer,
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// Gets appropriate icon for error type
  static IconData _getErrorIcon(SubscriptionError error) {
    return error.when(
      networkError: (_, __, ___) => Icons.wifi_off_rounded,
      databaseError: (_, __, ___) => Icons.storage_rounded,
      validationError: (_, __, ___) => Icons.error_outline_rounded,
      navigationError: (_, __, ___) => Icons.navigation_rounded,
      authenticationError: (_, __, ___) => Icons.lock_outline_rounded,
      serverError: (_, __, ___, ____) => Icons.cloud_off_rounded,
      paymentError: (_, __, ___, ____) => Icons.payment_rounded,
      businessLogicError: (_, __, ___, ____) => Icons.business_rounded,
      unknownError: (_, __, ___) => Icons.help_outline_rounded,
    );
  }
}
