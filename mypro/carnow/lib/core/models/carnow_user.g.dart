// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'carnow_user.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_CarnowUser _$CarnowUserFromJson(Map<String, dynamic> json) => _CarnowUser(
  id: json['id'] as String,
  email: json['email'] as String,
  firstName: json['first_name'] as String,
  lastName: json['last_name'] as String,
  phoneNumber: json['phone_number'] as String?,
  avatarUrl: json['avatar_url'] as String?,
  isActive: json['is_active'] as bool,
  createdAt: DateTime.parse(json['created_at'] as String),
  updatedAt: DateTime.parse(json['updated_at'] as String),
  carnowUserId: json['carnow_user_id'] as String?,
);

Map<String, dynamic> _$CarnowUserToJson(_CarnowUser instance) =>
    <String, dynamic>{
      'id': instance.id,
      'email': instance.email,
      'first_name': instance.firstName,
      'last_name': instance.lastName,
      'phone_number': instance.phoneNumber,
      'avatar_url': instance.avatarUrl,
      'is_active': instance.isActive,
      'created_at': instance.createdAt.toIso8601String(),
      'updated_at': instance.updatedAt.toIso8601String(),
      'carnow_user_id': instance.carnowUserId,
    };

_CreateCarnowUserRequest _$CreateCarnowUserRequestFromJson(
  Map<String, dynamic> json,
) => _CreateCarnowUserRequest(
  email: json['email'] as String,
  firstName: json['first_name'] as String,
  lastName: json['last_name'] as String,
  phoneNumber: json['phone_number'] as String?,
  avatarUrl: json['avatar_url'] as String?,
);

Map<String, dynamic> _$CreateCarnowUserRequestToJson(
  _CreateCarnowUserRequest instance,
) => <String, dynamic>{
  'email': instance.email,
  'first_name': instance.firstName,
  'last_name': instance.lastName,
  'phone_number': instance.phoneNumber,
  'avatar_url': instance.avatarUrl,
};

_UpdateCarnowUserRequest _$UpdateCarnowUserRequestFromJson(
  Map<String, dynamic> json,
) => _UpdateCarnowUserRequest(
  firstName: json['first_name'] as String?,
  lastName: json['last_name'] as String?,
  phoneNumber: json['phone_number'] as String?,
  avatarUrl: json['avatar_url'] as String?,
);

Map<String, dynamic> _$UpdateCarnowUserRequestToJson(
  _UpdateCarnowUserRequest instance,
) => <String, dynamic>{
  'first_name': instance.firstName,
  'last_name': instance.lastName,
  'phone_number': instance.phoneNumber,
  'avatar_url': instance.avatarUrl,
};
