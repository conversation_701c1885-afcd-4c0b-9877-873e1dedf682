// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'carnow_user.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$CarnowUser {

 String get id; String get email;@JsonKey(name: 'first_name') String get firstName;@Json<PERSON>ey(name: 'last_name') String get lastName;@JsonKey(name: 'phone_number') String? get phoneNumber;@JsonKey(name: 'avatar_url') String? get avatarUrl;@JsonKey(name: 'is_active') bool get isActive;@JsonKey(name: 'created_at') DateTime get createdAt;@JsonKey(name: 'updated_at') DateTime get updatedAt;@JsonKey(name: 'carnow_user_id') String? get carnowUserId;
/// Create a copy of CarnowUser
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CarnowUserCopyWith<CarnowUser> get copyWith => _$CarnowUserCopyWithImpl<CarnowUser>(this as CarnowUser, _$identity);

  /// Serializes this CarnowUser to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CarnowUser&&(identical(other.id, id) || other.id == id)&&(identical(other.email, email) || other.email == email)&&(identical(other.firstName, firstName) || other.firstName == firstName)&&(identical(other.lastName, lastName) || other.lastName == lastName)&&(identical(other.phoneNumber, phoneNumber) || other.phoneNumber == phoneNumber)&&(identical(other.avatarUrl, avatarUrl) || other.avatarUrl == avatarUrl)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.carnowUserId, carnowUserId) || other.carnowUserId == carnowUserId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,email,firstName,lastName,phoneNumber,avatarUrl,isActive,createdAt,updatedAt,carnowUserId);

@override
String toString() {
  return 'CarnowUser(id: $id, email: $email, firstName: $firstName, lastName: $lastName, phoneNumber: $phoneNumber, avatarUrl: $avatarUrl, isActive: $isActive, createdAt: $createdAt, updatedAt: $updatedAt, carnowUserId: $carnowUserId)';
}


}

/// @nodoc
abstract mixin class $CarnowUserCopyWith<$Res>  {
  factory $CarnowUserCopyWith(CarnowUser value, $Res Function(CarnowUser) _then) = _$CarnowUserCopyWithImpl;
@useResult
$Res call({
 String id, String email,@JsonKey(name: 'first_name') String firstName,@JsonKey(name: 'last_name') String lastName,@JsonKey(name: 'phone_number') String? phoneNumber,@JsonKey(name: 'avatar_url') String? avatarUrl,@JsonKey(name: 'is_active') bool isActive,@JsonKey(name: 'created_at') DateTime createdAt,@JsonKey(name: 'updated_at') DateTime updatedAt,@JsonKey(name: 'carnow_user_id') String? carnowUserId
});




}
/// @nodoc
class _$CarnowUserCopyWithImpl<$Res>
    implements $CarnowUserCopyWith<$Res> {
  _$CarnowUserCopyWithImpl(this._self, this._then);

  final CarnowUser _self;
  final $Res Function(CarnowUser) _then;

/// Create a copy of CarnowUser
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? email = null,Object? firstName = null,Object? lastName = null,Object? phoneNumber = freezed,Object? avatarUrl = freezed,Object? isActive = null,Object? createdAt = null,Object? updatedAt = null,Object? carnowUserId = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,email: null == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String,firstName: null == firstName ? _self.firstName : firstName // ignore: cast_nullable_to_non_nullable
as String,lastName: null == lastName ? _self.lastName : lastName // ignore: cast_nullable_to_non_nullable
as String,phoneNumber: freezed == phoneNumber ? _self.phoneNumber : phoneNumber // ignore: cast_nullable_to_non_nullable
as String?,avatarUrl: freezed == avatarUrl ? _self.avatarUrl : avatarUrl // ignore: cast_nullable_to_non_nullable
as String?,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,carnowUserId: freezed == carnowUserId ? _self.carnowUserId : carnowUserId // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// Adds pattern-matching-related methods to [CarnowUser].
extension CarnowUserPatterns on CarnowUser {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _CarnowUser value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _CarnowUser() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _CarnowUser value)  $default,){
final _that = this;
switch (_that) {
case _CarnowUser():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _CarnowUser value)?  $default,){
final _that = this;
switch (_that) {
case _CarnowUser() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String email, @JsonKey(name: 'first_name')  String firstName, @JsonKey(name: 'last_name')  String lastName, @JsonKey(name: 'phone_number')  String? phoneNumber, @JsonKey(name: 'avatar_url')  String? avatarUrl, @JsonKey(name: 'is_active')  bool isActive, @JsonKey(name: 'created_at')  DateTime createdAt, @JsonKey(name: 'updated_at')  DateTime updatedAt, @JsonKey(name: 'carnow_user_id')  String? carnowUserId)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _CarnowUser() when $default != null:
return $default(_that.id,_that.email,_that.firstName,_that.lastName,_that.phoneNumber,_that.avatarUrl,_that.isActive,_that.createdAt,_that.updatedAt,_that.carnowUserId);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String email, @JsonKey(name: 'first_name')  String firstName, @JsonKey(name: 'last_name')  String lastName, @JsonKey(name: 'phone_number')  String? phoneNumber, @JsonKey(name: 'avatar_url')  String? avatarUrl, @JsonKey(name: 'is_active')  bool isActive, @JsonKey(name: 'created_at')  DateTime createdAt, @JsonKey(name: 'updated_at')  DateTime updatedAt, @JsonKey(name: 'carnow_user_id')  String? carnowUserId)  $default,) {final _that = this;
switch (_that) {
case _CarnowUser():
return $default(_that.id,_that.email,_that.firstName,_that.lastName,_that.phoneNumber,_that.avatarUrl,_that.isActive,_that.createdAt,_that.updatedAt,_that.carnowUserId);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String email, @JsonKey(name: 'first_name')  String firstName, @JsonKey(name: 'last_name')  String lastName, @JsonKey(name: 'phone_number')  String? phoneNumber, @JsonKey(name: 'avatar_url')  String? avatarUrl, @JsonKey(name: 'is_active')  bool isActive, @JsonKey(name: 'created_at')  DateTime createdAt, @JsonKey(name: 'updated_at')  DateTime updatedAt, @JsonKey(name: 'carnow_user_id')  String? carnowUserId)?  $default,) {final _that = this;
switch (_that) {
case _CarnowUser() when $default != null:
return $default(_that.id,_that.email,_that.firstName,_that.lastName,_that.phoneNumber,_that.avatarUrl,_that.isActive,_that.createdAt,_that.updatedAt,_that.carnowUserId);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _CarnowUser implements CarnowUser {
  const _CarnowUser({required this.id, required this.email, @JsonKey(name: 'first_name') required this.firstName, @JsonKey(name: 'last_name') required this.lastName, @JsonKey(name: 'phone_number') this.phoneNumber, @JsonKey(name: 'avatar_url') this.avatarUrl, @JsonKey(name: 'is_active') required this.isActive, @JsonKey(name: 'created_at') required this.createdAt, @JsonKey(name: 'updated_at') required this.updatedAt, @JsonKey(name: 'carnow_user_id') this.carnowUserId});
  factory _CarnowUser.fromJson(Map<String, dynamic> json) => _$CarnowUserFromJson(json);

@override final  String id;
@override final  String email;
@override@JsonKey(name: 'first_name') final  String firstName;
@override@JsonKey(name: 'last_name') final  String lastName;
@override@JsonKey(name: 'phone_number') final  String? phoneNumber;
@override@JsonKey(name: 'avatar_url') final  String? avatarUrl;
@override@JsonKey(name: 'is_active') final  bool isActive;
@override@JsonKey(name: 'created_at') final  DateTime createdAt;
@override@JsonKey(name: 'updated_at') final  DateTime updatedAt;
@override@JsonKey(name: 'carnow_user_id') final  String? carnowUserId;

/// Create a copy of CarnowUser
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$CarnowUserCopyWith<_CarnowUser> get copyWith => __$CarnowUserCopyWithImpl<_CarnowUser>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$CarnowUserToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _CarnowUser&&(identical(other.id, id) || other.id == id)&&(identical(other.email, email) || other.email == email)&&(identical(other.firstName, firstName) || other.firstName == firstName)&&(identical(other.lastName, lastName) || other.lastName == lastName)&&(identical(other.phoneNumber, phoneNumber) || other.phoneNumber == phoneNumber)&&(identical(other.avatarUrl, avatarUrl) || other.avatarUrl == avatarUrl)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.carnowUserId, carnowUserId) || other.carnowUserId == carnowUserId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,email,firstName,lastName,phoneNumber,avatarUrl,isActive,createdAt,updatedAt,carnowUserId);

@override
String toString() {
  return 'CarnowUser(id: $id, email: $email, firstName: $firstName, lastName: $lastName, phoneNumber: $phoneNumber, avatarUrl: $avatarUrl, isActive: $isActive, createdAt: $createdAt, updatedAt: $updatedAt, carnowUserId: $carnowUserId)';
}


}

/// @nodoc
abstract mixin class _$CarnowUserCopyWith<$Res> implements $CarnowUserCopyWith<$Res> {
  factory _$CarnowUserCopyWith(_CarnowUser value, $Res Function(_CarnowUser) _then) = __$CarnowUserCopyWithImpl;
@override @useResult
$Res call({
 String id, String email,@JsonKey(name: 'first_name') String firstName,@JsonKey(name: 'last_name') String lastName,@JsonKey(name: 'phone_number') String? phoneNumber,@JsonKey(name: 'avatar_url') String? avatarUrl,@JsonKey(name: 'is_active') bool isActive,@JsonKey(name: 'created_at') DateTime createdAt,@JsonKey(name: 'updated_at') DateTime updatedAt,@JsonKey(name: 'carnow_user_id') String? carnowUserId
});




}
/// @nodoc
class __$CarnowUserCopyWithImpl<$Res>
    implements _$CarnowUserCopyWith<$Res> {
  __$CarnowUserCopyWithImpl(this._self, this._then);

  final _CarnowUser _self;
  final $Res Function(_CarnowUser) _then;

/// Create a copy of CarnowUser
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? email = null,Object? firstName = null,Object? lastName = null,Object? phoneNumber = freezed,Object? avatarUrl = freezed,Object? isActive = null,Object? createdAt = null,Object? updatedAt = null,Object? carnowUserId = freezed,}) {
  return _then(_CarnowUser(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,email: null == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String,firstName: null == firstName ? _self.firstName : firstName // ignore: cast_nullable_to_non_nullable
as String,lastName: null == lastName ? _self.lastName : lastName // ignore: cast_nullable_to_non_nullable
as String,phoneNumber: freezed == phoneNumber ? _self.phoneNumber : phoneNumber // ignore: cast_nullable_to_non_nullable
as String?,avatarUrl: freezed == avatarUrl ? _self.avatarUrl : avatarUrl // ignore: cast_nullable_to_non_nullable
as String?,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,carnowUserId: freezed == carnowUserId ? _self.carnowUserId : carnowUserId // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}


/// @nodoc
mixin _$CreateCarnowUserRequest {

 String get email;@JsonKey(name: 'first_name') String get firstName;@JsonKey(name: 'last_name') String get lastName;@JsonKey(name: 'phone_number') String? get phoneNumber;@JsonKey(name: 'avatar_url') String? get avatarUrl;
/// Create a copy of CreateCarnowUserRequest
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CreateCarnowUserRequestCopyWith<CreateCarnowUserRequest> get copyWith => _$CreateCarnowUserRequestCopyWithImpl<CreateCarnowUserRequest>(this as CreateCarnowUserRequest, _$identity);

  /// Serializes this CreateCarnowUserRequest to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CreateCarnowUserRequest&&(identical(other.email, email) || other.email == email)&&(identical(other.firstName, firstName) || other.firstName == firstName)&&(identical(other.lastName, lastName) || other.lastName == lastName)&&(identical(other.phoneNumber, phoneNumber) || other.phoneNumber == phoneNumber)&&(identical(other.avatarUrl, avatarUrl) || other.avatarUrl == avatarUrl));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,email,firstName,lastName,phoneNumber,avatarUrl);

@override
String toString() {
  return 'CreateCarnowUserRequest(email: $email, firstName: $firstName, lastName: $lastName, phoneNumber: $phoneNumber, avatarUrl: $avatarUrl)';
}


}

/// @nodoc
abstract mixin class $CreateCarnowUserRequestCopyWith<$Res>  {
  factory $CreateCarnowUserRequestCopyWith(CreateCarnowUserRequest value, $Res Function(CreateCarnowUserRequest) _then) = _$CreateCarnowUserRequestCopyWithImpl;
@useResult
$Res call({
 String email,@JsonKey(name: 'first_name') String firstName,@JsonKey(name: 'last_name') String lastName,@JsonKey(name: 'phone_number') String? phoneNumber,@JsonKey(name: 'avatar_url') String? avatarUrl
});




}
/// @nodoc
class _$CreateCarnowUserRequestCopyWithImpl<$Res>
    implements $CreateCarnowUserRequestCopyWith<$Res> {
  _$CreateCarnowUserRequestCopyWithImpl(this._self, this._then);

  final CreateCarnowUserRequest _self;
  final $Res Function(CreateCarnowUserRequest) _then;

/// Create a copy of CreateCarnowUserRequest
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? email = null,Object? firstName = null,Object? lastName = null,Object? phoneNumber = freezed,Object? avatarUrl = freezed,}) {
  return _then(_self.copyWith(
email: null == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String,firstName: null == firstName ? _self.firstName : firstName // ignore: cast_nullable_to_non_nullable
as String,lastName: null == lastName ? _self.lastName : lastName // ignore: cast_nullable_to_non_nullable
as String,phoneNumber: freezed == phoneNumber ? _self.phoneNumber : phoneNumber // ignore: cast_nullable_to_non_nullable
as String?,avatarUrl: freezed == avatarUrl ? _self.avatarUrl : avatarUrl // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// Adds pattern-matching-related methods to [CreateCarnowUserRequest].
extension CreateCarnowUserRequestPatterns on CreateCarnowUserRequest {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _CreateCarnowUserRequest value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _CreateCarnowUserRequest() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _CreateCarnowUserRequest value)  $default,){
final _that = this;
switch (_that) {
case _CreateCarnowUserRequest():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _CreateCarnowUserRequest value)?  $default,){
final _that = this;
switch (_that) {
case _CreateCarnowUserRequest() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String email, @JsonKey(name: 'first_name')  String firstName, @JsonKey(name: 'last_name')  String lastName, @JsonKey(name: 'phone_number')  String? phoneNumber, @JsonKey(name: 'avatar_url')  String? avatarUrl)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _CreateCarnowUserRequest() when $default != null:
return $default(_that.email,_that.firstName,_that.lastName,_that.phoneNumber,_that.avatarUrl);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String email, @JsonKey(name: 'first_name')  String firstName, @JsonKey(name: 'last_name')  String lastName, @JsonKey(name: 'phone_number')  String? phoneNumber, @JsonKey(name: 'avatar_url')  String? avatarUrl)  $default,) {final _that = this;
switch (_that) {
case _CreateCarnowUserRequest():
return $default(_that.email,_that.firstName,_that.lastName,_that.phoneNumber,_that.avatarUrl);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String email, @JsonKey(name: 'first_name')  String firstName, @JsonKey(name: 'last_name')  String lastName, @JsonKey(name: 'phone_number')  String? phoneNumber, @JsonKey(name: 'avatar_url')  String? avatarUrl)?  $default,) {final _that = this;
switch (_that) {
case _CreateCarnowUserRequest() when $default != null:
return $default(_that.email,_that.firstName,_that.lastName,_that.phoneNumber,_that.avatarUrl);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _CreateCarnowUserRequest implements CreateCarnowUserRequest {
  const _CreateCarnowUserRequest({required this.email, @JsonKey(name: 'first_name') required this.firstName, @JsonKey(name: 'last_name') required this.lastName, @JsonKey(name: 'phone_number') this.phoneNumber, @JsonKey(name: 'avatar_url') this.avatarUrl});
  factory _CreateCarnowUserRequest.fromJson(Map<String, dynamic> json) => _$CreateCarnowUserRequestFromJson(json);

@override final  String email;
@override@JsonKey(name: 'first_name') final  String firstName;
@override@JsonKey(name: 'last_name') final  String lastName;
@override@JsonKey(name: 'phone_number') final  String? phoneNumber;
@override@JsonKey(name: 'avatar_url') final  String? avatarUrl;

/// Create a copy of CreateCarnowUserRequest
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$CreateCarnowUserRequestCopyWith<_CreateCarnowUserRequest> get copyWith => __$CreateCarnowUserRequestCopyWithImpl<_CreateCarnowUserRequest>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$CreateCarnowUserRequestToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _CreateCarnowUserRequest&&(identical(other.email, email) || other.email == email)&&(identical(other.firstName, firstName) || other.firstName == firstName)&&(identical(other.lastName, lastName) || other.lastName == lastName)&&(identical(other.phoneNumber, phoneNumber) || other.phoneNumber == phoneNumber)&&(identical(other.avatarUrl, avatarUrl) || other.avatarUrl == avatarUrl));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,email,firstName,lastName,phoneNumber,avatarUrl);

@override
String toString() {
  return 'CreateCarnowUserRequest(email: $email, firstName: $firstName, lastName: $lastName, phoneNumber: $phoneNumber, avatarUrl: $avatarUrl)';
}


}

/// @nodoc
abstract mixin class _$CreateCarnowUserRequestCopyWith<$Res> implements $CreateCarnowUserRequestCopyWith<$Res> {
  factory _$CreateCarnowUserRequestCopyWith(_CreateCarnowUserRequest value, $Res Function(_CreateCarnowUserRequest) _then) = __$CreateCarnowUserRequestCopyWithImpl;
@override @useResult
$Res call({
 String email,@JsonKey(name: 'first_name') String firstName,@JsonKey(name: 'last_name') String lastName,@JsonKey(name: 'phone_number') String? phoneNumber,@JsonKey(name: 'avatar_url') String? avatarUrl
});




}
/// @nodoc
class __$CreateCarnowUserRequestCopyWithImpl<$Res>
    implements _$CreateCarnowUserRequestCopyWith<$Res> {
  __$CreateCarnowUserRequestCopyWithImpl(this._self, this._then);

  final _CreateCarnowUserRequest _self;
  final $Res Function(_CreateCarnowUserRequest) _then;

/// Create a copy of CreateCarnowUserRequest
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? email = null,Object? firstName = null,Object? lastName = null,Object? phoneNumber = freezed,Object? avatarUrl = freezed,}) {
  return _then(_CreateCarnowUserRequest(
email: null == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String,firstName: null == firstName ? _self.firstName : firstName // ignore: cast_nullable_to_non_nullable
as String,lastName: null == lastName ? _self.lastName : lastName // ignore: cast_nullable_to_non_nullable
as String,phoneNumber: freezed == phoneNumber ? _self.phoneNumber : phoneNumber // ignore: cast_nullable_to_non_nullable
as String?,avatarUrl: freezed == avatarUrl ? _self.avatarUrl : avatarUrl // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}


/// @nodoc
mixin _$UpdateCarnowUserRequest {

@JsonKey(name: 'first_name') String? get firstName;@JsonKey(name: 'last_name') String? get lastName;@JsonKey(name: 'phone_number') String? get phoneNumber;@JsonKey(name: 'avatar_url') String? get avatarUrl;
/// Create a copy of UpdateCarnowUserRequest
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$UpdateCarnowUserRequestCopyWith<UpdateCarnowUserRequest> get copyWith => _$UpdateCarnowUserRequestCopyWithImpl<UpdateCarnowUserRequest>(this as UpdateCarnowUserRequest, _$identity);

  /// Serializes this UpdateCarnowUserRequest to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is UpdateCarnowUserRequest&&(identical(other.firstName, firstName) || other.firstName == firstName)&&(identical(other.lastName, lastName) || other.lastName == lastName)&&(identical(other.phoneNumber, phoneNumber) || other.phoneNumber == phoneNumber)&&(identical(other.avatarUrl, avatarUrl) || other.avatarUrl == avatarUrl));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,firstName,lastName,phoneNumber,avatarUrl);

@override
String toString() {
  return 'UpdateCarnowUserRequest(firstName: $firstName, lastName: $lastName, phoneNumber: $phoneNumber, avatarUrl: $avatarUrl)';
}


}

/// @nodoc
abstract mixin class $UpdateCarnowUserRequestCopyWith<$Res>  {
  factory $UpdateCarnowUserRequestCopyWith(UpdateCarnowUserRequest value, $Res Function(UpdateCarnowUserRequest) _then) = _$UpdateCarnowUserRequestCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: 'first_name') String? firstName,@JsonKey(name: 'last_name') String? lastName,@JsonKey(name: 'phone_number') String? phoneNumber,@JsonKey(name: 'avatar_url') String? avatarUrl
});




}
/// @nodoc
class _$UpdateCarnowUserRequestCopyWithImpl<$Res>
    implements $UpdateCarnowUserRequestCopyWith<$Res> {
  _$UpdateCarnowUserRequestCopyWithImpl(this._self, this._then);

  final UpdateCarnowUserRequest _self;
  final $Res Function(UpdateCarnowUserRequest) _then;

/// Create a copy of UpdateCarnowUserRequest
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? firstName = freezed,Object? lastName = freezed,Object? phoneNumber = freezed,Object? avatarUrl = freezed,}) {
  return _then(_self.copyWith(
firstName: freezed == firstName ? _self.firstName : firstName // ignore: cast_nullable_to_non_nullable
as String?,lastName: freezed == lastName ? _self.lastName : lastName // ignore: cast_nullable_to_non_nullable
as String?,phoneNumber: freezed == phoneNumber ? _self.phoneNumber : phoneNumber // ignore: cast_nullable_to_non_nullable
as String?,avatarUrl: freezed == avatarUrl ? _self.avatarUrl : avatarUrl // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// Adds pattern-matching-related methods to [UpdateCarnowUserRequest].
extension UpdateCarnowUserRequestPatterns on UpdateCarnowUserRequest {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _UpdateCarnowUserRequest value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _UpdateCarnowUserRequest() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _UpdateCarnowUserRequest value)  $default,){
final _that = this;
switch (_that) {
case _UpdateCarnowUserRequest():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _UpdateCarnowUserRequest value)?  $default,){
final _that = this;
switch (_that) {
case _UpdateCarnowUserRequest() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function(@JsonKey(name: 'first_name')  String? firstName, @JsonKey(name: 'last_name')  String? lastName, @JsonKey(name: 'phone_number')  String? phoneNumber, @JsonKey(name: 'avatar_url')  String? avatarUrl)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _UpdateCarnowUserRequest() when $default != null:
return $default(_that.firstName,_that.lastName,_that.phoneNumber,_that.avatarUrl);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function(@JsonKey(name: 'first_name')  String? firstName, @JsonKey(name: 'last_name')  String? lastName, @JsonKey(name: 'phone_number')  String? phoneNumber, @JsonKey(name: 'avatar_url')  String? avatarUrl)  $default,) {final _that = this;
switch (_that) {
case _UpdateCarnowUserRequest():
return $default(_that.firstName,_that.lastName,_that.phoneNumber,_that.avatarUrl);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function(@JsonKey(name: 'first_name')  String? firstName, @JsonKey(name: 'last_name')  String? lastName, @JsonKey(name: 'phone_number')  String? phoneNumber, @JsonKey(name: 'avatar_url')  String? avatarUrl)?  $default,) {final _that = this;
switch (_that) {
case _UpdateCarnowUserRequest() when $default != null:
return $default(_that.firstName,_that.lastName,_that.phoneNumber,_that.avatarUrl);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _UpdateCarnowUserRequest implements UpdateCarnowUserRequest {
  const _UpdateCarnowUserRequest({@JsonKey(name: 'first_name') this.firstName, @JsonKey(name: 'last_name') this.lastName, @JsonKey(name: 'phone_number') this.phoneNumber, @JsonKey(name: 'avatar_url') this.avatarUrl});
  factory _UpdateCarnowUserRequest.fromJson(Map<String, dynamic> json) => _$UpdateCarnowUserRequestFromJson(json);

@override@JsonKey(name: 'first_name') final  String? firstName;
@override@JsonKey(name: 'last_name') final  String? lastName;
@override@JsonKey(name: 'phone_number') final  String? phoneNumber;
@override@JsonKey(name: 'avatar_url') final  String? avatarUrl;

/// Create a copy of UpdateCarnowUserRequest
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$UpdateCarnowUserRequestCopyWith<_UpdateCarnowUserRequest> get copyWith => __$UpdateCarnowUserRequestCopyWithImpl<_UpdateCarnowUserRequest>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$UpdateCarnowUserRequestToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _UpdateCarnowUserRequest&&(identical(other.firstName, firstName) || other.firstName == firstName)&&(identical(other.lastName, lastName) || other.lastName == lastName)&&(identical(other.phoneNumber, phoneNumber) || other.phoneNumber == phoneNumber)&&(identical(other.avatarUrl, avatarUrl) || other.avatarUrl == avatarUrl));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,firstName,lastName,phoneNumber,avatarUrl);

@override
String toString() {
  return 'UpdateCarnowUserRequest(firstName: $firstName, lastName: $lastName, phoneNumber: $phoneNumber, avatarUrl: $avatarUrl)';
}


}

/// @nodoc
abstract mixin class _$UpdateCarnowUserRequestCopyWith<$Res> implements $UpdateCarnowUserRequestCopyWith<$Res> {
  factory _$UpdateCarnowUserRequestCopyWith(_UpdateCarnowUserRequest value, $Res Function(_UpdateCarnowUserRequest) _then) = __$UpdateCarnowUserRequestCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: 'first_name') String? firstName,@JsonKey(name: 'last_name') String? lastName,@JsonKey(name: 'phone_number') String? phoneNumber,@JsonKey(name: 'avatar_url') String? avatarUrl
});




}
/// @nodoc
class __$UpdateCarnowUserRequestCopyWithImpl<$Res>
    implements _$UpdateCarnowUserRequestCopyWith<$Res> {
  __$UpdateCarnowUserRequestCopyWithImpl(this._self, this._then);

  final _UpdateCarnowUserRequest _self;
  final $Res Function(_UpdateCarnowUserRequest) _then;

/// Create a copy of UpdateCarnowUserRequest
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? firstName = freezed,Object? lastName = freezed,Object? phoneNumber = freezed,Object? avatarUrl = freezed,}) {
  return _then(_UpdateCarnowUserRequest(
firstName: freezed == firstName ? _self.firstName : firstName // ignore: cast_nullable_to_non_nullable
as String?,lastName: freezed == lastName ? _self.lastName : lastName // ignore: cast_nullable_to_non_nullable
as String?,phoneNumber: freezed == phoneNumber ? _self.phoneNumber : phoneNumber // ignore: cast_nullable_to_non_nullable
as String?,avatarUrl: freezed == avatarUrl ? _self.avatarUrl : avatarUrl // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

// dart format on
