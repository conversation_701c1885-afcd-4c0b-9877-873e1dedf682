// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'part_language_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$PartTermModel {

 String get id; String get arabicName; String get englishName; String get italianName; String? get category; bool get isCommonInLibya; int get usageFrequency; DateTime? get createdAt; DateTime? get updatedAt;
/// Create a copy of PartTermModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PartTermModelCopyWith<PartTermModel> get copyWith => _$PartTermModelCopyWithImpl<PartTermModel>(this as PartTermModel, _$identity);

  /// Serializes this PartTermModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PartTermModel&&(identical(other.id, id) || other.id == id)&&(identical(other.arabicName, arabicName) || other.arabicName == arabicName)&&(identical(other.englishName, englishName) || other.englishName == englishName)&&(identical(other.italianName, italianName) || other.italianName == italianName)&&(identical(other.category, category) || other.category == category)&&(identical(other.isCommonInLibya, isCommonInLibya) || other.isCommonInLibya == isCommonInLibya)&&(identical(other.usageFrequency, usageFrequency) || other.usageFrequency == usageFrequency)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,arabicName,englishName,italianName,category,isCommonInLibya,usageFrequency,createdAt,updatedAt);

@override
String toString() {
  return 'PartTermModel(id: $id, arabicName: $arabicName, englishName: $englishName, italianName: $italianName, category: $category, isCommonInLibya: $isCommonInLibya, usageFrequency: $usageFrequency, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class $PartTermModelCopyWith<$Res>  {
  factory $PartTermModelCopyWith(PartTermModel value, $Res Function(PartTermModel) _then) = _$PartTermModelCopyWithImpl;
@useResult
$Res call({
 String id, String arabicName, String englishName, String italianName, String? category, bool isCommonInLibya, int usageFrequency, DateTime? createdAt, DateTime? updatedAt
});




}
/// @nodoc
class _$PartTermModelCopyWithImpl<$Res>
    implements $PartTermModelCopyWith<$Res> {
  _$PartTermModelCopyWithImpl(this._self, this._then);

  final PartTermModel _self;
  final $Res Function(PartTermModel) _then;

/// Create a copy of PartTermModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? arabicName = null,Object? englishName = null,Object? italianName = null,Object? category = freezed,Object? isCommonInLibya = null,Object? usageFrequency = null,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,arabicName: null == arabicName ? _self.arabicName : arabicName // ignore: cast_nullable_to_non_nullable
as String,englishName: null == englishName ? _self.englishName : englishName // ignore: cast_nullable_to_non_nullable
as String,italianName: null == italianName ? _self.italianName : italianName // ignore: cast_nullable_to_non_nullable
as String,category: freezed == category ? _self.category : category // ignore: cast_nullable_to_non_nullable
as String?,isCommonInLibya: null == isCommonInLibya ? _self.isCommonInLibya : isCommonInLibya // ignore: cast_nullable_to_non_nullable
as bool,usageFrequency: null == usageFrequency ? _self.usageFrequency : usageFrequency // ignore: cast_nullable_to_non_nullable
as int,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// Adds pattern-matching-related methods to [PartTermModel].
extension PartTermModelPatterns on PartTermModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _PartTermModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _PartTermModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _PartTermModel value)  $default,){
final _that = this;
switch (_that) {
case _PartTermModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _PartTermModel value)?  $default,){
final _that = this;
switch (_that) {
case _PartTermModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String arabicName,  String englishName,  String italianName,  String? category,  bool isCommonInLibya,  int usageFrequency,  DateTime? createdAt,  DateTime? updatedAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _PartTermModel() when $default != null:
return $default(_that.id,_that.arabicName,_that.englishName,_that.italianName,_that.category,_that.isCommonInLibya,_that.usageFrequency,_that.createdAt,_that.updatedAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String arabicName,  String englishName,  String italianName,  String? category,  bool isCommonInLibya,  int usageFrequency,  DateTime? createdAt,  DateTime? updatedAt)  $default,) {final _that = this;
switch (_that) {
case _PartTermModel():
return $default(_that.id,_that.arabicName,_that.englishName,_that.italianName,_that.category,_that.isCommonInLibya,_that.usageFrequency,_that.createdAt,_that.updatedAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String arabicName,  String englishName,  String italianName,  String? category,  bool isCommonInLibya,  int usageFrequency,  DateTime? createdAt,  DateTime? updatedAt)?  $default,) {final _that = this;
switch (_that) {
case _PartTermModel() when $default != null:
return $default(_that.id,_that.arabicName,_that.englishName,_that.italianName,_that.category,_that.isCommonInLibya,_that.usageFrequency,_that.createdAt,_that.updatedAt);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _PartTermModel implements PartTermModel {
  const _PartTermModel({required this.id, required this.arabicName, required this.englishName, required this.italianName, this.category, this.isCommonInLibya = true, this.usageFrequency = 1, this.createdAt, this.updatedAt});
  factory _PartTermModel.fromJson(Map<String, dynamic> json) => _$PartTermModelFromJson(json);

@override final  String id;
@override final  String arabicName;
@override final  String englishName;
@override final  String italianName;
@override final  String? category;
@override@JsonKey() final  bool isCommonInLibya;
@override@JsonKey() final  int usageFrequency;
@override final  DateTime? createdAt;
@override final  DateTime? updatedAt;

/// Create a copy of PartTermModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PartTermModelCopyWith<_PartTermModel> get copyWith => __$PartTermModelCopyWithImpl<_PartTermModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$PartTermModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PartTermModel&&(identical(other.id, id) || other.id == id)&&(identical(other.arabicName, arabicName) || other.arabicName == arabicName)&&(identical(other.englishName, englishName) || other.englishName == englishName)&&(identical(other.italianName, italianName) || other.italianName == italianName)&&(identical(other.category, category) || other.category == category)&&(identical(other.isCommonInLibya, isCommonInLibya) || other.isCommonInLibya == isCommonInLibya)&&(identical(other.usageFrequency, usageFrequency) || other.usageFrequency == usageFrequency)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,arabicName,englishName,italianName,category,isCommonInLibya,usageFrequency,createdAt,updatedAt);

@override
String toString() {
  return 'PartTermModel(id: $id, arabicName: $arabicName, englishName: $englishName, italianName: $italianName, category: $category, isCommonInLibya: $isCommonInLibya, usageFrequency: $usageFrequency, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class _$PartTermModelCopyWith<$Res> implements $PartTermModelCopyWith<$Res> {
  factory _$PartTermModelCopyWith(_PartTermModel value, $Res Function(_PartTermModel) _then) = __$PartTermModelCopyWithImpl;
@override @useResult
$Res call({
 String id, String arabicName, String englishName, String italianName, String? category, bool isCommonInLibya, int usageFrequency, DateTime? createdAt, DateTime? updatedAt
});




}
/// @nodoc
class __$PartTermModelCopyWithImpl<$Res>
    implements _$PartTermModelCopyWith<$Res> {
  __$PartTermModelCopyWithImpl(this._self, this._then);

  final _PartTermModel _self;
  final $Res Function(_PartTermModel) _then;

/// Create a copy of PartTermModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? arabicName = null,Object? englishName = null,Object? italianName = null,Object? category = freezed,Object? isCommonInLibya = null,Object? usageFrequency = null,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_PartTermModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,arabicName: null == arabicName ? _self.arabicName : arabicName // ignore: cast_nullable_to_non_nullable
as String,englishName: null == englishName ? _self.englishName : englishName // ignore: cast_nullable_to_non_nullable
as String,italianName: null == italianName ? _self.italianName : italianName // ignore: cast_nullable_to_non_nullable
as String,category: freezed == category ? _self.category : category // ignore: cast_nullable_to_non_nullable
as String?,isCommonInLibya: null == isCommonInLibya ? _self.isCommonInLibya : isCommonInLibya // ignore: cast_nullable_to_non_nullable
as bool,usageFrequency: null == usageFrequency ? _self.usageFrequency : usageFrequency // ignore: cast_nullable_to_non_nullable
as int,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}


/// @nodoc
mixin _$PartLanguageSettings {

 PartLanguage get primaryLanguage; PartLanguage get secondaryLanguage; bool get showSecondaryInParentheses; bool get enableAutoTranslation; bool get preferLibyanTerms;
/// Create a copy of PartLanguageSettings
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PartLanguageSettingsCopyWith<PartLanguageSettings> get copyWith => _$PartLanguageSettingsCopyWithImpl<PartLanguageSettings>(this as PartLanguageSettings, _$identity);

  /// Serializes this PartLanguageSettings to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PartLanguageSettings&&(identical(other.primaryLanguage, primaryLanguage) || other.primaryLanguage == primaryLanguage)&&(identical(other.secondaryLanguage, secondaryLanguage) || other.secondaryLanguage == secondaryLanguage)&&(identical(other.showSecondaryInParentheses, showSecondaryInParentheses) || other.showSecondaryInParentheses == showSecondaryInParentheses)&&(identical(other.enableAutoTranslation, enableAutoTranslation) || other.enableAutoTranslation == enableAutoTranslation)&&(identical(other.preferLibyanTerms, preferLibyanTerms) || other.preferLibyanTerms == preferLibyanTerms));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,primaryLanguage,secondaryLanguage,showSecondaryInParentheses,enableAutoTranslation,preferLibyanTerms);

@override
String toString() {
  return 'PartLanguageSettings(primaryLanguage: $primaryLanguage, secondaryLanguage: $secondaryLanguage, showSecondaryInParentheses: $showSecondaryInParentheses, enableAutoTranslation: $enableAutoTranslation, preferLibyanTerms: $preferLibyanTerms)';
}


}

/// @nodoc
abstract mixin class $PartLanguageSettingsCopyWith<$Res>  {
  factory $PartLanguageSettingsCopyWith(PartLanguageSettings value, $Res Function(PartLanguageSettings) _then) = _$PartLanguageSettingsCopyWithImpl;
@useResult
$Res call({
 PartLanguage primaryLanguage, PartLanguage secondaryLanguage, bool showSecondaryInParentheses, bool enableAutoTranslation, bool preferLibyanTerms
});




}
/// @nodoc
class _$PartLanguageSettingsCopyWithImpl<$Res>
    implements $PartLanguageSettingsCopyWith<$Res> {
  _$PartLanguageSettingsCopyWithImpl(this._self, this._then);

  final PartLanguageSettings _self;
  final $Res Function(PartLanguageSettings) _then;

/// Create a copy of PartLanguageSettings
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? primaryLanguage = null,Object? secondaryLanguage = null,Object? showSecondaryInParentheses = null,Object? enableAutoTranslation = null,Object? preferLibyanTerms = null,}) {
  return _then(_self.copyWith(
primaryLanguage: null == primaryLanguage ? _self.primaryLanguage : primaryLanguage // ignore: cast_nullable_to_non_nullable
as PartLanguage,secondaryLanguage: null == secondaryLanguage ? _self.secondaryLanguage : secondaryLanguage // ignore: cast_nullable_to_non_nullable
as PartLanguage,showSecondaryInParentheses: null == showSecondaryInParentheses ? _self.showSecondaryInParentheses : showSecondaryInParentheses // ignore: cast_nullable_to_non_nullable
as bool,enableAutoTranslation: null == enableAutoTranslation ? _self.enableAutoTranslation : enableAutoTranslation // ignore: cast_nullable_to_non_nullable
as bool,preferLibyanTerms: null == preferLibyanTerms ? _self.preferLibyanTerms : preferLibyanTerms // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [PartLanguageSettings].
extension PartLanguageSettingsPatterns on PartLanguageSettings {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _PartLanguageSettings value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _PartLanguageSettings() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _PartLanguageSettings value)  $default,){
final _that = this;
switch (_that) {
case _PartLanguageSettings():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _PartLanguageSettings value)?  $default,){
final _that = this;
switch (_that) {
case _PartLanguageSettings() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( PartLanguage primaryLanguage,  PartLanguage secondaryLanguage,  bool showSecondaryInParentheses,  bool enableAutoTranslation,  bool preferLibyanTerms)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _PartLanguageSettings() when $default != null:
return $default(_that.primaryLanguage,_that.secondaryLanguage,_that.showSecondaryInParentheses,_that.enableAutoTranslation,_that.preferLibyanTerms);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( PartLanguage primaryLanguage,  PartLanguage secondaryLanguage,  bool showSecondaryInParentheses,  bool enableAutoTranslation,  bool preferLibyanTerms)  $default,) {final _that = this;
switch (_that) {
case _PartLanguageSettings():
return $default(_that.primaryLanguage,_that.secondaryLanguage,_that.showSecondaryInParentheses,_that.enableAutoTranslation,_that.preferLibyanTerms);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( PartLanguage primaryLanguage,  PartLanguage secondaryLanguage,  bool showSecondaryInParentheses,  bool enableAutoTranslation,  bool preferLibyanTerms)?  $default,) {final _that = this;
switch (_that) {
case _PartLanguageSettings() when $default != null:
return $default(_that.primaryLanguage,_that.secondaryLanguage,_that.showSecondaryInParentheses,_that.enableAutoTranslation,_that.preferLibyanTerms);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _PartLanguageSettings implements PartLanguageSettings {
  const _PartLanguageSettings({this.primaryLanguage = PartLanguage.arabic, this.secondaryLanguage = PartLanguage.italian, this.showSecondaryInParentheses = true, this.enableAutoTranslation = true, this.preferLibyanTerms = false});
  factory _PartLanguageSettings.fromJson(Map<String, dynamic> json) => _$PartLanguageSettingsFromJson(json);

@override@JsonKey() final  PartLanguage primaryLanguage;
@override@JsonKey() final  PartLanguage secondaryLanguage;
@override@JsonKey() final  bool showSecondaryInParentheses;
@override@JsonKey() final  bool enableAutoTranslation;
@override@JsonKey() final  bool preferLibyanTerms;

/// Create a copy of PartLanguageSettings
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PartLanguageSettingsCopyWith<_PartLanguageSettings> get copyWith => __$PartLanguageSettingsCopyWithImpl<_PartLanguageSettings>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$PartLanguageSettingsToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PartLanguageSettings&&(identical(other.primaryLanguage, primaryLanguage) || other.primaryLanguage == primaryLanguage)&&(identical(other.secondaryLanguage, secondaryLanguage) || other.secondaryLanguage == secondaryLanguage)&&(identical(other.showSecondaryInParentheses, showSecondaryInParentheses) || other.showSecondaryInParentheses == showSecondaryInParentheses)&&(identical(other.enableAutoTranslation, enableAutoTranslation) || other.enableAutoTranslation == enableAutoTranslation)&&(identical(other.preferLibyanTerms, preferLibyanTerms) || other.preferLibyanTerms == preferLibyanTerms));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,primaryLanguage,secondaryLanguage,showSecondaryInParentheses,enableAutoTranslation,preferLibyanTerms);

@override
String toString() {
  return 'PartLanguageSettings(primaryLanguage: $primaryLanguage, secondaryLanguage: $secondaryLanguage, showSecondaryInParentheses: $showSecondaryInParentheses, enableAutoTranslation: $enableAutoTranslation, preferLibyanTerms: $preferLibyanTerms)';
}


}

/// @nodoc
abstract mixin class _$PartLanguageSettingsCopyWith<$Res> implements $PartLanguageSettingsCopyWith<$Res> {
  factory _$PartLanguageSettingsCopyWith(_PartLanguageSettings value, $Res Function(_PartLanguageSettings) _then) = __$PartLanguageSettingsCopyWithImpl;
@override @useResult
$Res call({
 PartLanguage primaryLanguage, PartLanguage secondaryLanguage, bool showSecondaryInParentheses, bool enableAutoTranslation, bool preferLibyanTerms
});




}
/// @nodoc
class __$PartLanguageSettingsCopyWithImpl<$Res>
    implements _$PartLanguageSettingsCopyWith<$Res> {
  __$PartLanguageSettingsCopyWithImpl(this._self, this._then);

  final _PartLanguageSettings _self;
  final $Res Function(_PartLanguageSettings) _then;

/// Create a copy of PartLanguageSettings
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? primaryLanguage = null,Object? secondaryLanguage = null,Object? showSecondaryInParentheses = null,Object? enableAutoTranslation = null,Object? preferLibyanTerms = null,}) {
  return _then(_PartLanguageSettings(
primaryLanguage: null == primaryLanguage ? _self.primaryLanguage : primaryLanguage // ignore: cast_nullable_to_non_nullable
as PartLanguage,secondaryLanguage: null == secondaryLanguage ? _self.secondaryLanguage : secondaryLanguage // ignore: cast_nullable_to_non_nullable
as PartLanguage,showSecondaryInParentheses: null == showSecondaryInParentheses ? _self.showSecondaryInParentheses : showSecondaryInParentheses // ignore: cast_nullable_to_non_nullable
as bool,enableAutoTranslation: null == enableAutoTranslation ? _self.enableAutoTranslation : enableAutoTranslation // ignore: cast_nullable_to_non_nullable
as bool,preferLibyanTerms: null == preferLibyanTerms ? _self.preferLibyanTerms : preferLibyanTerms // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}

// dart format on
