// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'product_category_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_ProductCategoryModel _$ProductCategoryModelFromJson(
  Map<String, dynamic> json,
) => _ProductCategoryModel(
  id: json['id'] as String,
  name: json['name'] as String,
  nameAr: json['nameAr'] as String?,
  nameEn: json['nameEn'] as String?,
  nameIt: json['nameIt'] as String?,
  description: json['description'] as String?,
  descriptionAr: json['descriptionAr'] as String?,
  descriptionEn: json['descriptionEn'] as String?,
  descriptionIt: json['descriptionIt'] as String?,
  parentId: json['parentId'] as String?,
  productType:
      $enumDecodeNullable(_$ProductTypeEnumMap, json['productType']) ??
      ProductType.other,
  iconUrl: json['iconUrl'] as String?,
  imageUrl: json['imageUrl'] as String?,
  colorCode: json['colorCode'] as String?,
  isActive: json['isActive'] as bool? ?? true,
  sortOrder: (json['sortOrder'] as num?)?.toInt() ?? 0,
  level: (json['level'] as num?)?.toInt() ?? 0,
  requiredFields:
      (json['requiredFields'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  optionalFields:
      (json['optionalFields'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  formTemplate: json['formTemplate'] as Map<String, dynamic>?,
  subcategories:
      (json['subcategories'] as List<dynamic>?)
          ?.map((e) => ProductCategoryModel.fromJson(e as Map<String, dynamic>))
          .toList() ??
      const [],
  createdAt: json['createdAt'] == null
      ? null
      : DateTime.parse(json['createdAt'] as String),
  updatedAt: json['updatedAt'] == null
      ? null
      : DateTime.parse(json['updatedAt'] as String),
  vehicleCategory: $enumDecodeNullable(
    _$VehicleCategoryEnumMap,
    json['vehicleCategory'],
  ),
  autoPartsCategory: $enumDecodeNullable(
    _$AutoPartsCategoryEnumMap,
    json['autoPartsCategory'],
  ),
  electronicsCategory: $enumDecodeNullable(
    _$AutomotiveElectronicsCategoryEnumMap,
    json['electronicsCategory'],
  ),
  toolsCategory: $enumDecodeNullable(
    _$ToolsCategoryEnumMap,
    json['toolsCategory'],
  ),
  accessoriesCategory: $enumDecodeNullable(
    _$AccessoriesCategoryEnumMap,
    json['accessoriesCategory'],
  ),
);

Map<String, dynamic> _$ProductCategoryModelToJson(
  _ProductCategoryModel instance,
) => <String, dynamic>{
  'id': instance.id,
  'name': instance.name,
  'nameAr': instance.nameAr,
  'nameEn': instance.nameEn,
  'nameIt': instance.nameIt,
  'description': instance.description,
  'descriptionAr': instance.descriptionAr,
  'descriptionEn': instance.descriptionEn,
  'descriptionIt': instance.descriptionIt,
  'parentId': instance.parentId,
  'productType': _$ProductTypeEnumMap[instance.productType]!,
  'iconUrl': instance.iconUrl,
  'imageUrl': instance.imageUrl,
  'colorCode': instance.colorCode,
  'isActive': instance.isActive,
  'sortOrder': instance.sortOrder,
  'level': instance.level,
  'requiredFields': instance.requiredFields,
  'optionalFields': instance.optionalFields,
  'formTemplate': instance.formTemplate,
  'subcategories': instance.subcategories,
  'createdAt': instance.createdAt?.toIso8601String(),
  'updatedAt': instance.updatedAt?.toIso8601String(),
  'vehicleCategory': _$VehicleCategoryEnumMap[instance.vehicleCategory],
  'autoPartsCategory': _$AutoPartsCategoryEnumMap[instance.autoPartsCategory],
  'electronicsCategory':
      _$AutomotiveElectronicsCategoryEnumMap[instance.electronicsCategory],
  'toolsCategory': _$ToolsCategoryEnumMap[instance.toolsCategory],
  'accessoriesCategory':
      _$AccessoriesCategoryEnumMap[instance.accessoriesCategory],
};

const _$ProductTypeEnumMap = {
  ProductType.vehicles: 'vehicles',
  ProductType.autoParts: 'auto_parts',
  ProductType.electronics: 'electronics',
  ProductType.tools: 'tools',
  ProductType.accessories: 'accessories',
  ProductType.maintenance: 'maintenance',
  ProductType.other: 'other',
};

const _$VehicleCategoryEnumMap = {
  VehicleCategory.passengerCars: 'passenger_cars',
  VehicleCategory.commercialVehicles: 'commercial_vehicles',
  VehicleCategory.motorcycles: 'motorcycles',
  VehicleCategory.heavyVehicles: 'heavy_vehicles',
  VehicleCategory.specialVehicles: 'special_vehicles',
  VehicleCategory.other: 'other',
};

const _$AutoPartsCategoryEnumMap = {
  AutoPartsCategory.engineParts: 'engine_parts',
  AutoPartsCategory.transmissionParts: 'transmission_parts',
  AutoPartsCategory.suspensionSteering: 'suspension_steering',
  AutoPartsCategory.brakeSystem: 'brake_system',
  AutoPartsCategory.electricalElectronic: 'electrical_electronic',
  AutoPartsCategory.bodyExterior: 'body_exterior',
  AutoPartsCategory.interiorParts: 'interior_parts',
  AutoPartsCategory.tiresWheels: 'tires_wheels',
  AutoPartsCategory.exhaustSystem: 'exhaust_system',
  AutoPartsCategory.coolingAC: 'cooling_ac',
  AutoPartsCategory.fuelSystem: 'fuel_system',
  AutoPartsCategory.filters: 'filters',
  AutoPartsCategory.beltsChains: 'belts_chains',
  AutoPartsCategory.hosestubes: 'hoses_tubes',
  AutoPartsCategory.sensorsGauges: 'sensors_gauges',
  AutoPartsCategory.lightingSystem: 'lighting_system',
  AutoPartsCategory.other: 'other',
};

const _$AutomotiveElectronicsCategoryEnumMap = {
  AutomotiveElectronicsCategory.audioVideo: 'audio_video',
  AutomotiveElectronicsCategory.navigationGPS: 'navigation_gps',
  AutomotiveElectronicsCategory.securitySafety: 'security_safety',
  AutomotiveElectronicsCategory.advancedLighting: 'advanced_lighting',
  AutomotiveElectronicsCategory.sensorsDiagnostics: 'sensors_diagnostics',
  AutomotiveElectronicsCategory.controlModules: 'control_modules',
  AutomotiveElectronicsCategory.electronicAccessories: 'electronic_accessories',
  AutomotiveElectronicsCategory.diagnosticTools: 'diagnostic_tools',
  AutomotiveElectronicsCategory.other: 'other',
};

const _$ToolsCategoryEnumMap = {
  ToolsCategory.handTools: 'hand_tools',
  ToolsCategory.powerTools: 'power_tools',
  ToolsCategory.diagnosticEquipment: 'diagnostic_equipment',
  ToolsCategory.liftingEquipment: 'lifting_equipment',
  ToolsCategory.measuringTesting: 'measuring_testing',
  ToolsCategory.weldingCutting: 'welding_cutting',
  ToolsCategory.cleaningMaintenance: 'cleaning_maintenance',
  ToolsCategory.other: 'other',
};

const _$AccessoriesCategoryEnumMap = {
  AccessoriesCategory.exteriorAccessories: 'exterior_accessories',
  AccessoriesCategory.interiorAccessories: 'interior_accessories',
  AccessoriesCategory.safetyAccessories: 'safety_accessories',
  AccessoriesCategory.comfortAccessories: 'comfort_accessories',
  AccessoriesCategory.performanceAccessories: 'performance_accessories',
  AccessoriesCategory.other: 'other',
};
