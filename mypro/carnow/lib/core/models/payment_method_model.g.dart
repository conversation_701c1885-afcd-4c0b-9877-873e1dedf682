// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'payment_method_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_PaymentMethodModel _$PaymentMethodModelFromJson(Map<String, dynamic> json) =>
    _PaymentMethodModel(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      type: $enumDecode(_$PaymentMethodTypeEnumMap, json['type']),
      cardLastFour: json['card_last_four'] as String?,
      cardBrand: json['card_brand'] as String?,
      expiryMonth: (json['expiry_month'] as num?)?.toInt(),
      expiryYear: (json['expiry_year'] as num?)?.toInt(),
      cardholderName: json['cardholder_name'] as String?,
      bankName: json['bank_name'] as String?,
      accountLastFour: json['account_last_four'] as String?,
      walletProvider: json['wallet_provider'] as String?,
      walletEmail: json['wallet_email'] as String?,
      isDefault: json['is_default'] as bool? ?? false,
      isActive: json['is_active'] as bool? ?? true,
      isVerified: json['is_verified'] as bool? ?? false,
      createdAt: json['created_at'] == null
          ? null
          : DateTime.parse(json['created_at'] as String),
      updatedAt: json['updated_at'] == null
          ? null
          : DateTime.parse(json['updated_at'] as String),
      isDeleted: json['is_deleted'] as bool? ?? false,
    );

Map<String, dynamic> _$PaymentMethodModelToJson(_PaymentMethodModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'user_id': instance.userId,
      'type': _$PaymentMethodTypeEnumMap[instance.type]!,
      'card_last_four': instance.cardLastFour,
      'card_brand': instance.cardBrand,
      'expiry_month': instance.expiryMonth,
      'expiry_year': instance.expiryYear,
      'cardholder_name': instance.cardholderName,
      'bank_name': instance.bankName,
      'account_last_four': instance.accountLastFour,
      'wallet_provider': instance.walletProvider,
      'wallet_email': instance.walletEmail,
      'is_default': instance.isDefault,
      'is_active': instance.isActive,
      'is_verified': instance.isVerified,
    };

const _$PaymentMethodTypeEnumMap = {
  PaymentMethodType.card: 'card',
  PaymentMethodType.bankAccount: 'bank_account',
  PaymentMethodType.wallet: 'wallet',
};
