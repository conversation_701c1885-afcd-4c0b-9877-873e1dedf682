// ignore_for_file: invalid_annotation_target
import 'package:freezed_annotation/freezed_annotation.dart';

part 'car_model_model.freezed.dart';
part 'car_model_model.g.dart';

@freezed
abstract class CarModelModel with _$CarModelModel {
  const factory CarModelModel({int? id, String? name, int? brandId}) =
      _CarModelModel;

  factory CarModelModel.fromJson(Map<String, dynamic> json) =>
      _$CarModelModelFromJson(json);
}
