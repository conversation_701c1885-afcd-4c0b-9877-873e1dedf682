// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'order_details_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_OrderDetailsModel _$OrderDetailsModelFromJson(Map<String, dynamic> json) =>
    _OrderDetailsModel(
      id: json['id'] as String,
      orderNumber: json['order_number'] as String,
      status: $enumDecode(_$OrderStatusEnumMap, json['status']),
      createdAt: DateTime.parse(json['created_at'] as String),
      totalAmount: (json['total_amount'] as num).toDouble(),
      subtotal: (json['subtotal'] as num).toDouble(),
      taxAmount: (json['tax_amount'] as num).toDouble(),
      shippingCost: (json['shipping_cost'] as num).toDouble(),
      currency: json['currency'] as String,
      buyerName: json['buyer_name'] as String,
      buyerEmail: json['buyer_email'] as String,
      buyerPhone: json['buyer_phone'] as String,
      sellerName: json['seller_name'] as String,
      sellerEmail: json['seller_email'] as String,
      shippingAddress: json['shipping_address'] as String,
      items: (json['items'] as List<dynamic>)
          .map((e) => OrderItemDetailsModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      statusHistory: (json['status_history'] as List<dynamic>)
          .map(
            (e) => OrderStatusHistoryModel.fromJson(e as Map<String, dynamic>),
          )
          .toList(),
      updatedAt: json['updated_at'] == null
          ? null
          : DateTime.parse(json['updated_at'] as String),
      trackingNumber: json['tracking_number'] as String?,
      notes: json['notes'] as String?,
    );

Map<String, dynamic> _$OrderDetailsModelToJson(_OrderDetailsModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'order_number': instance.orderNumber,
      'status': _$OrderStatusEnumMap[instance.status]!,
      'created_at': instance.createdAt.toIso8601String(),
      'total_amount': instance.totalAmount,
      'subtotal': instance.subtotal,
      'tax_amount': instance.taxAmount,
      'shipping_cost': instance.shippingCost,
      'currency': instance.currency,
      'buyer_name': instance.buyerName,
      'buyer_email': instance.buyerEmail,
      'buyer_phone': instance.buyerPhone,
      'seller_name': instance.sellerName,
      'seller_email': instance.sellerEmail,
      'shipping_address': instance.shippingAddress,
      'items': instance.items.map((e) => e.toJson()).toList(),
      'status_history': instance.statusHistory.map((e) => e.toJson()).toList(),
      'updated_at': instance.updatedAt?.toIso8601String(),
      'tracking_number': instance.trackingNumber,
      'notes': instance.notes,
    };

const _$OrderStatusEnumMap = {
  OrderStatus.pending: 'pending',
  OrderStatus.processing: 'processing',
  OrderStatus.confirmed: 'confirmed',
  OrderStatus.shipped: 'shipped',
  OrderStatus.delivered: 'delivered',
  OrderStatus.cancelled: 'cancelled',
  OrderStatus.returned: 'returned',
  OrderStatus.refunded: 'refunded',
};
