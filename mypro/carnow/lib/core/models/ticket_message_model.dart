// ignore_for_file: invalid_annotation_target
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:carnow/features/account/models/user_model.dart';

part 'ticket_message_model.freezed.dart';
part 'ticket_message_model.g.dart';

enum MessageSender {
  @JsonValue('user')
  user,
  @JsonValue('agent')
  agent,
  @JsonValue('system')
  system,
}

@freezed
abstract class TicketMessageModel with _$TicketMessageModel {
  const factory TicketMessageModel({
    required String message,
    int? id,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'ticket_id') int? ticketId,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'sender_id') int? senderId,
    UserModel? sender,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'sender_type')
    @Default(MessageSender.user)
    MessageSender senderType,
    @Json<PERSON>ey(name: 'is_internal') @Default(false) bool isInternal,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'read_at') DateTime? readAt,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'sent_at') DateTime? sentAt,
    @Json<PERSON><PERSON>(name: 'created_at') DateTime? createdAt,
    @Json<PERSON><PERSON>(name: 'updated_at') DateTime? updatedAt,
    @<PERSON>son<PERSON>ey(name: 'is_deleted', defaultValue: false)
    @Default(false)
    bool isDeleted,
    // UI state
    @JsonKey(includeFromJson: false, includeToJson: false)
    @Default(false)
    bool isSending,
    @JsonKey(includeFromJson: false, includeToJson: false)
    @Default(false)
    bool isError,
    @JsonKey(includeFromJson: false, includeToJson: false) String? errorMessage,
  }) = _TicketMessageModel;
  const TicketMessageModel._();

  factory TicketMessageModel.fromJson(Map<String, dynamic> json) =>
      _$TicketMessageModelFromJson(json);

  /// Check if message is from user
  bool get isFromUser => senderType == MessageSender.user;

  /// Check if message is from support agent
  bool get isFromAgent => senderType == MessageSender.agent;

  /// Check if message is a system message
  bool get isFromSystem => senderType == MessageSender.system;

  /// Check if message has been read
  bool get isRead => readAt != null;

  /// Get formatted sender name
  String get senderDisplayName {
    if (sender?.name != null && sender!.name!.isNotEmpty) {
      return sender!.name!;
    }

    switch (senderType) {
      case MessageSender.user:
        return 'العميل';
      case MessageSender.agent:
        return 'مدعم العملاء';
      case MessageSender.system:
        return 'النظام';
    }
  }
}
