import 'package:equatable/equatable.dart';

/// نموذج الفئة المرن للتوسع المستقبلي
class CategoryModel extends Equatable {
  const CategoryModel({
    required this.id,
    required this.name,
    required this.nameEn,
    this.nameIt,
    required this.type,
    this.description,
    this.descriptionEn,
    this.descriptionIt,
    this.imageUrl,
    this.iconUrl,
    this.parentId,
    this.sortOrder = 0,
    this.isActive = true,
    this.isFeatured = false,
    this.customAttributes,
    this.allowedFilters,
    this.configuration,
    this.createdAt,
    this.updatedAt,
  });

  factory CategoryModel.fromJson(Map<String, dynamic> json) => CategoryModel(
    id: json['id'] as String,
    name: json['name_ar'] as String? ?? json['name'] as String,
    nameEn: json['name_en'] as String? ?? json['name'] as String,
    nameIt: json['name_it'] as String?,
    description:
        json['description_ar'] as String? ?? json['description'] as String?,
    descriptionEn: json['description_en'] as String?,
    descriptionIt: json['description_it'] as String?,
    imageUrl: json['image_url'] as String?,
    iconUrl: json['icon_url'] as String?,
    parentId: json['parent_id'] as String?,
    type: CategoryType.fromString(json['type'] as String? ?? 'auto_parts'),
    sortOrder: json['sort_order'] as int? ?? 0,
    isActive: json['is_active'] as bool? ?? true,
    isFeatured: json['is_featured'] as bool? ?? false,
    customAttributes: json['custom_attributes'] as Map<String, dynamic>?,
    allowedFilters: json['allowed_filters'] != null
        ? List<String>.from(json['allowed_filters'] as Iterable)
        : null,
    configuration: json['configuration'] != null
        ? CategoryConfiguration.fromJson(
            json['configuration'] as Map<String, dynamic>,
          )
        : null,
    createdAt: json['created_at'] != null
        ? DateTime.parse(json['created_at'] as String)
        : null,
    updatedAt: json['updated_at'] != null
        ? DateTime.parse(json['updated_at'] as String)
        : null,
  );
  final String id;
  final String name;
  final String nameEn;
  final String? nameIt;
  final String? description;
  final String? descriptionEn;
  final String? descriptionIt;
  final String? imageUrl;
  final String? iconUrl;
  final String? parentId;
  final CategoryType type;
  final int sortOrder;
  final bool isActive;
  final bool isFeatured;
  final Map<String, dynamic>? customAttributes;
  final List<String>? allowedFilters;
  final CategoryConfiguration? configuration;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  Map<String, dynamic> toJson() => {
    'id': id,
    'name': name,
    'name_en': nameEn,
    'name_it': nameIt,
    'description': description,
    'description_en': descriptionEn,
    'description_it': descriptionIt,
    'image_url': imageUrl,
    'icon_url': iconUrl,
    'parent_id': parentId,
    'type': type.value,
    'sort_order': sortOrder,
    'is_active': isActive,
    'is_featured': isFeatured,
    'custom_attributes': customAttributes,
    'allowed_filters': allowedFilters,
    'configuration': configuration?.toJson(),
    'created_at': createdAt?.toIso8601String(),
    'updated_at': updatedAt?.toIso8601String(),
  };

  CategoryModel copyWith({
    String? id,
    String? name,
    String? nameEn,
    String? nameIt,
    String? description,
    String? descriptionEn,
    String? descriptionIt,
    String? imageUrl,
    String? iconUrl,
    String? parentId,
    CategoryType? type,
    int? sortOrder,
    bool? isActive,
    bool? isFeatured,
    Map<String, dynamic>? customAttributes,
    List<String>? allowedFilters,
    CategoryConfiguration? configuration,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) => CategoryModel(
    id: id ?? this.id,
    name: name ?? this.name,
    nameEn: nameEn ?? this.nameEn,
    nameIt: nameIt ?? this.nameIt,
    description: description ?? this.description,
    descriptionEn: descriptionEn ?? this.descriptionEn,
    descriptionIt: descriptionIt ?? this.descriptionIt,
    imageUrl: imageUrl ?? this.imageUrl,
    iconUrl: iconUrl ?? this.iconUrl,
    parentId: parentId ?? this.parentId,
    type: type ?? this.type,
    sortOrder: sortOrder ?? this.sortOrder,
    isActive: isActive ?? this.isActive,
    isFeatured: isFeatured ?? this.isFeatured,
    customAttributes: customAttributes ?? this.customAttributes,
    allowedFilters: allowedFilters ?? this.allowedFilters,
    configuration: configuration ?? this.configuration,
    createdAt: createdAt ?? this.createdAt,
    updatedAt: updatedAt ?? this.updatedAt,
  );

  @override
  List<Object?> get props => [
    id,
    name,
    nameEn,
    nameIt,
    description,
    descriptionEn,
    descriptionIt,
    imageUrl,
    iconUrl,
    parentId,
    type,
    sortOrder,
    isActive,
    isFeatured,
    customAttributes,
    allowedFilters,
    configuration,
    createdAt,
    updatedAt,
  ];

  /// Returns the localized name based on the provided language code.
  /// Falls back to the default Arabic `name` if the localized name is not available.
  String getLocalizedName(String langCode) {
    switch (langCode) {
      case 'en':
        return nameEn;
      case 'it':
        return nameIt ?? name;
      case 'ar':
      default:
        return name;
    }
  }

  /// Returns the localized description based on the provided language code.
  /// Falls back to the default Arabic `description` if the localized version is not available.
  String? getLocalizedDescription(String langCode) {
    switch (langCode) {
      case 'en':
        return descriptionEn ?? description;
      case 'it':
        return descriptionIt ?? description;
      case 'ar':
      default:
        return description;
    }
  }

  // خصائص محسوبة
  String get displayName => name;
  String get displayDescription => description ?? '';
  bool get hasParent => parentId != null;
  bool get hasIcon => iconUrl != null && iconUrl!.isNotEmpty;
  bool get hasImage => imageUrl != null && imageUrl!.isNotEmpty;

  /// التحقق من دعم نوع فلتر معين
  bool supportsFilter(String filterType) =>
      allowedFilters?.contains(filterType) ?? false;

  /// الحصول على إعدادات مخصصة
  T? getCustomAttribute<T>(String key) => customAttributes?[key] as T?;
}

/// أنواع الفئات القابلة للتوسع
enum CategoryType {
  autoParts('auto_parts', 'قطع غيار السيارات', '🚗'),
  electronics('electronics', 'الإلكترونيات', '📱'),
  clothing('clothing', 'الملابس', '👕'),
  home('home', 'المنزل والحديقة', '🏠'),
  sports('sports', 'الرياضة واللياقة', '⚽'),
  beauty('beauty', 'الجمال والعناية', '💄'),
  books('books', 'الكتب والمجلات', '📚'),
  toys('toys', 'الألعاب والأطفال', '🧸'),
  food('food', 'الطعام والمشروبات', '🍔'),
  other('other', 'أخرى', '📦');

  const CategoryType(this.value, this.displayName, this.emoji);

  final String value;
  final String displayName;
  final String emoji;

  static CategoryType fromString(String value) =>
      CategoryType.values.firstWhere(
        (type) => type.value == value,
        orElse: () => CategoryType.other,
      );

  /// التحقق من كون النوع نشط حالياً
  bool get isActive {
    switch (this) {
      case CategoryType.autoParts:
        return true; // النوع الوحيد النشط حالياً
      case CategoryType.electronics:
      case CategoryType.clothing:
      case CategoryType.home:
      case CategoryType.sports:
      case CategoryType.beauty:
      case CategoryType.books:
      case CategoryType.toys:
      case CategoryType.food:
      case CategoryType.other:
        return false; // سيتم تفعيلها لاحقاً
    }
  }

  /// الحصول على الفلاتر المدعومة لكل نوع
  List<String> get supportedFilters {
    switch (this) {
      case CategoryType.autoParts:
        return [
          'car_brand',
          'car_model',
          'car_year',
          'part_type',
          'condition',
          'warranty',
        ];
      case CategoryType.electronics:
        return [
          'brand',
          'screen_size',
          'storage',
          'ram',
          'connectivity',
          'warranty',
        ];
      case CategoryType.clothing:
        return ['size', 'color', 'material', 'brand', 'season', 'gender'];
      case CategoryType.home:
        return ['room_type', 'material', 'color', 'brand', 'dimensions'];
      case CategoryType.sports:
        return ['sport_type', 'brand', 'size', 'gender', 'level'];
      case CategoryType.beauty:
        return ['brand', 'skin_type', 'gender', 'age_group', 'ingredients'];
      case CategoryType.books:
        return ['author', 'language', 'publisher', 'genre', 'format'];
      case CategoryType.toys:
        return [
          'age_group',
          'brand',
          'material',
          'educational',
          'battery_required',
        ];
      case CategoryType.food:
        return ['brand', 'organic', 'diet_type', 'expiry_date', 'origin'];
      case CategoryType.other:
        return [];
    }
  }
}

/// إعدادات الفئة المخصصة
class CategoryConfiguration extends Equatable {
  const CategoryConfiguration({
    this.showBrand = true,
    this.showModel = false,
    this.showYear = false,
    this.showCondition = false,
    this.showWarranty = false,
    this.showCompatibility = false,
    this.requiredFields,
    this.optionalFields,
    this.validationRules,
    this.defaultSortBy,
    this.listViewType,
  });

  factory CategoryConfiguration.fromJson(Map<String, dynamic> json) =>
      CategoryConfiguration(
        showBrand: json['show_brand'] as bool? ?? true,
        showModel: json['show_model'] as bool? ?? false,
        showYear: json['show_year'] as bool? ?? false,
        showCondition: json['show_condition'] as bool? ?? false,
        showWarranty: json['show_warranty'] as bool? ?? false,
        showCompatibility: json['show_compatibility'] as bool? ?? false,
        requiredFields: json['required_fields'] != null
            ? List<String>.from(json['required_fields'] as Iterable)
            : null,
        optionalFields: json['optional_fields'] != null
            ? List<String>.from(json['optional_fields'] as Iterable)
            : null,
        validationRules: json['validation_rules'] as Map<String, dynamic>?,
        defaultSortBy: json['default_sort_by'] as String?,
        listViewType: json['list_view_type'] as String?,
      );
  final bool showBrand;
  final bool showModel;
  final bool showYear;
  final bool showCondition;
  final bool showWarranty;
  final bool showCompatibility;
  final List<String>? requiredFields;
  final List<String>? optionalFields;
  final Map<String, dynamic>? validationRules;
  final String? defaultSortBy;
  final String? listViewType;

  Map<String, dynamic> toJson() => {
    'show_brand': showBrand,
    'show_model': showModel,
    'show_year': showYear,
    'show_condition': showCondition,
    'show_warranty': showWarranty,
    'show_compatibility': showCompatibility,
    'required_fields': requiredFields,
    'optional_fields': optionalFields,
    'validation_rules': validationRules,
    'default_sort_by': defaultSortBy,
    'list_view_type': listViewType,
  };

  CategoryConfiguration copyWith({
    bool? showBrand,
    bool? showModel,
    bool? showYear,
    bool? showCondition,
    bool? showWarranty,
    bool? showCompatibility,
    List<String>? requiredFields,
    List<String>? optionalFields,
    Map<String, dynamic>? validationRules,
    String? defaultSortBy,
    String? listViewType,
  }) => CategoryConfiguration(
    showBrand: showBrand ?? this.showBrand,
    showModel: showModel ?? this.showModel,
    showYear: showYear ?? this.showYear,
    showCondition: showCondition ?? this.showCondition,
    showWarranty: showWarranty ?? this.showWarranty,
    showCompatibility: showCompatibility ?? this.showCompatibility,
    requiredFields: requiredFields ?? this.requiredFields,
    optionalFields: optionalFields ?? this.optionalFields,
    validationRules: validationRules ?? this.validationRules,
    defaultSortBy: defaultSortBy ?? this.defaultSortBy,
    listViewType: listViewType ?? this.listViewType,
  );

  @override
  List<Object?> get props => [
    showBrand,
    showModel,
    showYear,
    showCondition,
    showWarranty,
    showCompatibility,
    requiredFields,
    optionalFields,
    validationRules,
    defaultSortBy,
    listViewType,
  ];
}

/// فئات السيارات المحددة (المرحلة الحالية)
class AutoPartsCategory {
  static const String engineParts = 'engine_parts';
  static const String brakeParts = 'brake_parts';
  static const String suspensionParts = 'suspension_parts';
  static const String electricalParts = 'electrical_parts';
  static const String bodyParts = 'body_parts';
  static const String interiorParts = 'interior_parts';
  static const String exteriorParts = 'exterior_parts';
  static const String oilsAndFluids = 'oils_and_fluids';
  static const String filters = 'filters';
  static const String tires = 'tires';
  static const String batteries = 'batteries';
  static const String accessories = 'accessories';

  static const Map<String, String> displayNames = {
    engineParts: 'قطع المحرك',
    brakeParts: 'قطع الفرامل',
    suspensionParts: 'قطع التعليق',
    electricalParts: 'القطع الكهربائية',
    bodyParts: 'قطع الهيكل',
    interiorParts: 'القطع الداخلية',
    exteriorParts: 'القطع الخارجية',
    oilsAndFluids: 'الزيوت والسوائل',
    filters: 'الفلاتر',
    tires: 'الإطارات',
    batteries: 'البطاريات',
    accessories: 'الإكسسوارات',
  };
}

/// المساعدات لبناء الفئات
class CategoryHelper {
  /// إنشاء فئة قطع غيار السيارات
  static CategoryModel createAutoPartsCategory({
    required String id,
    required String name,
    String? parentId,
    String? description,
    String? imageUrl,
    String? iconUrl,
    int sortOrder = 0,
  }) => CategoryModel(
    id: id,
    name: name,
    nameEn: name, // يمكن ترجمتها لاحقاً
    description: description,
    parentId: parentId,
    type: CategoryType.autoParts,
    imageUrl: imageUrl,
    iconUrl: iconUrl,
    sortOrder: sortOrder,
    allowedFilters: CategoryType.autoParts.supportedFilters,
    configuration: const CategoryConfiguration(
      showModel: true,
      showYear: true,
      showCondition: true,
      showWarranty: true,
      showCompatibility: true,
      defaultSortBy: 'newest',
      listViewType: 'grid',
    ),
  );

  /// الحصول على جميع الفئات النشطة
  static List<CategoryType> getActiveTypes() =>
      CategoryType.values.where((type) => type.isActive).toList();

  /// التحقق من إمكانية إضافة نوع فئة جديد
  static bool canAddCategoryType(CategoryType type) {
    // منطق لتحديد متى يمكن إضافة نوع جديد
    // مثلاً بناءً على عدد المنتجات أو إعدادات الإدارة
    return type == CategoryType.autoParts; // حالياً فقط قطع السيارات
  }
}
