import 'package:freezed_annotation/freezed_annotation.dart';

part 'payment_method_model.freezed.dart';
part 'payment_method_model.g.dart';

/// نموذج طريقة الدفع
@freezed
abstract class PaymentMethodModel with _$PaymentMethodModel {
  @JsonSerializable(fieldRename: FieldRename.snake)
  const factory PaymentMethodModel({
    required String id,
    @J<PERSON><PERSON><PERSON>(name: 'user_id') required String userId,
    required PaymentMethodType type,
    @Json<PERSON>ey(name: 'card_last_four') String? cardLastFour,
    @Json<PERSON>ey(name: 'card_brand') String? cardBrand,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'expiry_month') int? expiryMonth,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'expiry_year') int? expiryYear,
    @Json<PERSON>ey(name: 'cardholder_name') String? cardholderName,
    @Json<PERSON>ey(name: 'bank_name') String? bankName,
    @<PERSON>son<PERSON><PERSON>(name: 'account_last_four') String? accountLastFour,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'wallet_provider') String? walletProvider,
    @Json<PERSON>ey(name: 'wallet_email') String? walletEmail,
    @Json<PERSON>ey(name: 'is_default') @Default(false) bool isDefault,
    @JsonKey(name: 'is_active') @Default(true) bool isActive,
    @JsonKey(name: 'is_verified') @Default(false) bool isVerified,
    @JsonKey(includeFromJson: true, includeToJson: false) DateTime? createdAt,
    @JsonKey(includeFromJson: true, includeToJson: false) DateTime? updatedAt,
    @JsonKey(includeFromJson: true, includeToJson: false, name: 'is_deleted')
    @Default(false)
    bool isDeleted,
  }) = _PaymentMethodModel;

  factory PaymentMethodModel.fromJson(Map<String, dynamic> json) =>
      _$PaymentMethodModelFromJson(json);
}

/// Extended functionality for PaymentMethodModel
extension PaymentMethodModelExtension on PaymentMethodModel {
  /// تحقق من انتهاء صلاحية البطاقة قريباً
  bool get isExpiringSoon {
    if (type != PaymentMethodType.card ||
        expiryMonth == null ||
        expiryYear == null) {
      return false;
    }

    final now = DateTime.now();
    final expiry = DateTime(expiryYear!, expiryMonth!);
    final monthsUntilExpiry = expiry.difference(now).inDays / 30;
    return monthsUntilExpiry <= 2;
  }

  /// الحصول على نص وصفي لطريقة الدفع
  String get displayText {
    switch (type) {
      case PaymentMethodType.card:
        return '**** **** **** ${cardLastFour ?? '****'}';
      case PaymentMethodType.bankAccount:
        return '${bankName ?? 'بنك'} - *${accountLastFour ?? '****'}';
      case PaymentMethodType.wallet:
        return '${walletProvider ?? 'محفظة إلكترونية'} - ${walletEmail ?? ''}';
    }
  }

  /// أيقونة طريقة الدفع
  String get iconPath {
    switch (type) {
      case PaymentMethodType.card:
        return _getCardIcon(cardBrand);
      case PaymentMethodType.bankAccount:
        return 'assets/icons/bank.svg';
      case PaymentMethodType.wallet:
        return _getWalletIcon(walletProvider);
    }
  }

  String _getCardIcon(String? brand) {
    switch (brand?.toLowerCase()) {
      case 'visa':
        return 'assets/icons/visa.svg';
      case 'mastercard':
        return 'assets/icons/mastercard.svg';
      case 'american express':
      case 'amex':
        return 'assets/icons/amex.svg';
      default:
        return 'assets/icons/card.svg';
    }
  }

  String _getWalletIcon(String? provider) {
    switch (provider?.toLowerCase()) {
      case 'paypal':
        return 'assets/icons/paypal.svg';
      case 'apple pay':
        return 'assets/icons/apple_pay.svg';
      case 'google pay':
        return 'assets/icons/google_pay.svg';
      default:
        return 'assets/icons/wallet.svg';
    }
  }
}

/// أنواع طرق الدفع
@JsonEnum(fieldRename: FieldRename.snake)
enum PaymentMethodType {
  @JsonValue('card')
  card,
  @JsonValue('bank_account')
  bankAccount,
  @JsonValue('wallet')
  wallet,
}

/// Extension لأنواع طرق الدفع
extension PaymentMethodTypeExtension on PaymentMethodType {
  String get displayName {
    switch (this) {
      case PaymentMethodType.card:
        return 'بطاقة ائتمان';
      case PaymentMethodType.bankAccount:
        return 'حساب مصرفي';
      case PaymentMethodType.wallet:
        return 'محفظة إلكترونية';
    }
  }
}
