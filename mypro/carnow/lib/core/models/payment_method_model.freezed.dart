// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'payment_method_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$PaymentMethodModel {

 String get id;@JsonKey(name: 'user_id') String get userId; PaymentMethodType get type;@JsonKey(name: 'card_last_four') String? get cardLastFour;@JsonKey(name: 'card_brand') String? get cardBrand;@JsonKey(name: 'expiry_month') int? get expiryMonth;@JsonKey(name: 'expiry_year') int? get expiryYear;@JsonKey(name: 'cardholder_name') String? get cardholderName;@JsonKey(name: 'bank_name') String? get bankName;@JsonKey(name: 'account_last_four') String? get accountLastFour;@JsonKey(name: 'wallet_provider') String? get walletProvider;@JsonKey(name: 'wallet_email') String? get walletEmail;@JsonKey(name: 'is_default') bool get isDefault;@JsonKey(name: 'is_active') bool get isActive;@JsonKey(name: 'is_verified') bool get isVerified;@JsonKey(includeFromJson: true, includeToJson: false) DateTime? get createdAt;@JsonKey(includeFromJson: true, includeToJson: false) DateTime? get updatedAt;@JsonKey(includeFromJson: true, includeToJson: false, name: 'is_deleted') bool get isDeleted;
/// Create a copy of PaymentMethodModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PaymentMethodModelCopyWith<PaymentMethodModel> get copyWith => _$PaymentMethodModelCopyWithImpl<PaymentMethodModel>(this as PaymentMethodModel, _$identity);

  /// Serializes this PaymentMethodModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PaymentMethodModel&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.type, type) || other.type == type)&&(identical(other.cardLastFour, cardLastFour) || other.cardLastFour == cardLastFour)&&(identical(other.cardBrand, cardBrand) || other.cardBrand == cardBrand)&&(identical(other.expiryMonth, expiryMonth) || other.expiryMonth == expiryMonth)&&(identical(other.expiryYear, expiryYear) || other.expiryYear == expiryYear)&&(identical(other.cardholderName, cardholderName) || other.cardholderName == cardholderName)&&(identical(other.bankName, bankName) || other.bankName == bankName)&&(identical(other.accountLastFour, accountLastFour) || other.accountLastFour == accountLastFour)&&(identical(other.walletProvider, walletProvider) || other.walletProvider == walletProvider)&&(identical(other.walletEmail, walletEmail) || other.walletEmail == walletEmail)&&(identical(other.isDefault, isDefault) || other.isDefault == isDefault)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&(identical(other.isVerified, isVerified) || other.isVerified == isVerified)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.isDeleted, isDeleted) || other.isDeleted == isDeleted));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,userId,type,cardLastFour,cardBrand,expiryMonth,expiryYear,cardholderName,bankName,accountLastFour,walletProvider,walletEmail,isDefault,isActive,isVerified,createdAt,updatedAt,isDeleted);

@override
String toString() {
  return 'PaymentMethodModel(id: $id, userId: $userId, type: $type, cardLastFour: $cardLastFour, cardBrand: $cardBrand, expiryMonth: $expiryMonth, expiryYear: $expiryYear, cardholderName: $cardholderName, bankName: $bankName, accountLastFour: $accountLastFour, walletProvider: $walletProvider, walletEmail: $walletEmail, isDefault: $isDefault, isActive: $isActive, isVerified: $isVerified, createdAt: $createdAt, updatedAt: $updatedAt, isDeleted: $isDeleted)';
}


}

/// @nodoc
abstract mixin class $PaymentMethodModelCopyWith<$Res>  {
  factory $PaymentMethodModelCopyWith(PaymentMethodModel value, $Res Function(PaymentMethodModel) _then) = _$PaymentMethodModelCopyWithImpl;
@useResult
$Res call({
 String id,@JsonKey(name: 'user_id') String userId, PaymentMethodType type,@JsonKey(name: 'card_last_four') String? cardLastFour,@JsonKey(name: 'card_brand') String? cardBrand,@JsonKey(name: 'expiry_month') int? expiryMonth,@JsonKey(name: 'expiry_year') int? expiryYear,@JsonKey(name: 'cardholder_name') String? cardholderName,@JsonKey(name: 'bank_name') String? bankName,@JsonKey(name: 'account_last_four') String? accountLastFour,@JsonKey(name: 'wallet_provider') String? walletProvider,@JsonKey(name: 'wallet_email') String? walletEmail,@JsonKey(name: 'is_default') bool isDefault,@JsonKey(name: 'is_active') bool isActive,@JsonKey(name: 'is_verified') bool isVerified,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? createdAt,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? updatedAt,@JsonKey(includeFromJson: true, includeToJson: false, name: 'is_deleted') bool isDeleted
});




}
/// @nodoc
class _$PaymentMethodModelCopyWithImpl<$Res>
    implements $PaymentMethodModelCopyWith<$Res> {
  _$PaymentMethodModelCopyWithImpl(this._self, this._then);

  final PaymentMethodModel _self;
  final $Res Function(PaymentMethodModel) _then;

/// Create a copy of PaymentMethodModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? userId = null,Object? type = null,Object? cardLastFour = freezed,Object? cardBrand = freezed,Object? expiryMonth = freezed,Object? expiryYear = freezed,Object? cardholderName = freezed,Object? bankName = freezed,Object? accountLastFour = freezed,Object? walletProvider = freezed,Object? walletEmail = freezed,Object? isDefault = null,Object? isActive = null,Object? isVerified = null,Object? createdAt = freezed,Object? updatedAt = freezed,Object? isDeleted = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as PaymentMethodType,cardLastFour: freezed == cardLastFour ? _self.cardLastFour : cardLastFour // ignore: cast_nullable_to_non_nullable
as String?,cardBrand: freezed == cardBrand ? _self.cardBrand : cardBrand // ignore: cast_nullable_to_non_nullable
as String?,expiryMonth: freezed == expiryMonth ? _self.expiryMonth : expiryMonth // ignore: cast_nullable_to_non_nullable
as int?,expiryYear: freezed == expiryYear ? _self.expiryYear : expiryYear // ignore: cast_nullable_to_non_nullable
as int?,cardholderName: freezed == cardholderName ? _self.cardholderName : cardholderName // ignore: cast_nullable_to_non_nullable
as String?,bankName: freezed == bankName ? _self.bankName : bankName // ignore: cast_nullable_to_non_nullable
as String?,accountLastFour: freezed == accountLastFour ? _self.accountLastFour : accountLastFour // ignore: cast_nullable_to_non_nullable
as String?,walletProvider: freezed == walletProvider ? _self.walletProvider : walletProvider // ignore: cast_nullable_to_non_nullable
as String?,walletEmail: freezed == walletEmail ? _self.walletEmail : walletEmail // ignore: cast_nullable_to_non_nullable
as String?,isDefault: null == isDefault ? _self.isDefault : isDefault // ignore: cast_nullable_to_non_nullable
as bool,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,isVerified: null == isVerified ? _self.isVerified : isVerified // ignore: cast_nullable_to_non_nullable
as bool,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,isDeleted: null == isDeleted ? _self.isDeleted : isDeleted // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [PaymentMethodModel].
extension PaymentMethodModelPatterns on PaymentMethodModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _PaymentMethodModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _PaymentMethodModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _PaymentMethodModel value)  $default,){
final _that = this;
switch (_that) {
case _PaymentMethodModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _PaymentMethodModel value)?  $default,){
final _that = this;
switch (_that) {
case _PaymentMethodModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id, @JsonKey(name: 'user_id')  String userId,  PaymentMethodType type, @JsonKey(name: 'card_last_four')  String? cardLastFour, @JsonKey(name: 'card_brand')  String? cardBrand, @JsonKey(name: 'expiry_month')  int? expiryMonth, @JsonKey(name: 'expiry_year')  int? expiryYear, @JsonKey(name: 'cardholder_name')  String? cardholderName, @JsonKey(name: 'bank_name')  String? bankName, @JsonKey(name: 'account_last_four')  String? accountLastFour, @JsonKey(name: 'wallet_provider')  String? walletProvider, @JsonKey(name: 'wallet_email')  String? walletEmail, @JsonKey(name: 'is_default')  bool isDefault, @JsonKey(name: 'is_active')  bool isActive, @JsonKey(name: 'is_verified')  bool isVerified, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt, @JsonKey(includeFromJson: true, includeToJson: false, name: 'is_deleted')  bool isDeleted)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _PaymentMethodModel() when $default != null:
return $default(_that.id,_that.userId,_that.type,_that.cardLastFour,_that.cardBrand,_that.expiryMonth,_that.expiryYear,_that.cardholderName,_that.bankName,_that.accountLastFour,_that.walletProvider,_that.walletEmail,_that.isDefault,_that.isActive,_that.isVerified,_that.createdAt,_that.updatedAt,_that.isDeleted);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id, @JsonKey(name: 'user_id')  String userId,  PaymentMethodType type, @JsonKey(name: 'card_last_four')  String? cardLastFour, @JsonKey(name: 'card_brand')  String? cardBrand, @JsonKey(name: 'expiry_month')  int? expiryMonth, @JsonKey(name: 'expiry_year')  int? expiryYear, @JsonKey(name: 'cardholder_name')  String? cardholderName, @JsonKey(name: 'bank_name')  String? bankName, @JsonKey(name: 'account_last_four')  String? accountLastFour, @JsonKey(name: 'wallet_provider')  String? walletProvider, @JsonKey(name: 'wallet_email')  String? walletEmail, @JsonKey(name: 'is_default')  bool isDefault, @JsonKey(name: 'is_active')  bool isActive, @JsonKey(name: 'is_verified')  bool isVerified, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt, @JsonKey(includeFromJson: true, includeToJson: false, name: 'is_deleted')  bool isDeleted)  $default,) {final _that = this;
switch (_that) {
case _PaymentMethodModel():
return $default(_that.id,_that.userId,_that.type,_that.cardLastFour,_that.cardBrand,_that.expiryMonth,_that.expiryYear,_that.cardholderName,_that.bankName,_that.accountLastFour,_that.walletProvider,_that.walletEmail,_that.isDefault,_that.isActive,_that.isVerified,_that.createdAt,_that.updatedAt,_that.isDeleted);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id, @JsonKey(name: 'user_id')  String userId,  PaymentMethodType type, @JsonKey(name: 'card_last_four')  String? cardLastFour, @JsonKey(name: 'card_brand')  String? cardBrand, @JsonKey(name: 'expiry_month')  int? expiryMonth, @JsonKey(name: 'expiry_year')  int? expiryYear, @JsonKey(name: 'cardholder_name')  String? cardholderName, @JsonKey(name: 'bank_name')  String? bankName, @JsonKey(name: 'account_last_four')  String? accountLastFour, @JsonKey(name: 'wallet_provider')  String? walletProvider, @JsonKey(name: 'wallet_email')  String? walletEmail, @JsonKey(name: 'is_default')  bool isDefault, @JsonKey(name: 'is_active')  bool isActive, @JsonKey(name: 'is_verified')  bool isVerified, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? createdAt, @JsonKey(includeFromJson: true, includeToJson: false)  DateTime? updatedAt, @JsonKey(includeFromJson: true, includeToJson: false, name: 'is_deleted')  bool isDeleted)?  $default,) {final _that = this;
switch (_that) {
case _PaymentMethodModel() when $default != null:
return $default(_that.id,_that.userId,_that.type,_that.cardLastFour,_that.cardBrand,_that.expiryMonth,_that.expiryYear,_that.cardholderName,_that.bankName,_that.accountLastFour,_that.walletProvider,_that.walletEmail,_that.isDefault,_that.isActive,_that.isVerified,_that.createdAt,_that.updatedAt,_that.isDeleted);case _:
  return null;

}
}

}

/// @nodoc

@JsonSerializable(fieldRename: FieldRename.snake)
class _PaymentMethodModel implements PaymentMethodModel {
  const _PaymentMethodModel({required this.id, @JsonKey(name: 'user_id') required this.userId, required this.type, @JsonKey(name: 'card_last_four') this.cardLastFour, @JsonKey(name: 'card_brand') this.cardBrand, @JsonKey(name: 'expiry_month') this.expiryMonth, @JsonKey(name: 'expiry_year') this.expiryYear, @JsonKey(name: 'cardholder_name') this.cardholderName, @JsonKey(name: 'bank_name') this.bankName, @JsonKey(name: 'account_last_four') this.accountLastFour, @JsonKey(name: 'wallet_provider') this.walletProvider, @JsonKey(name: 'wallet_email') this.walletEmail, @JsonKey(name: 'is_default') this.isDefault = false, @JsonKey(name: 'is_active') this.isActive = true, @JsonKey(name: 'is_verified') this.isVerified = false, @JsonKey(includeFromJson: true, includeToJson: false) this.createdAt, @JsonKey(includeFromJson: true, includeToJson: false) this.updatedAt, @JsonKey(includeFromJson: true, includeToJson: false, name: 'is_deleted') this.isDeleted = false});
  factory _PaymentMethodModel.fromJson(Map<String, dynamic> json) => _$PaymentMethodModelFromJson(json);

@override final  String id;
@override@JsonKey(name: 'user_id') final  String userId;
@override final  PaymentMethodType type;
@override@JsonKey(name: 'card_last_four') final  String? cardLastFour;
@override@JsonKey(name: 'card_brand') final  String? cardBrand;
@override@JsonKey(name: 'expiry_month') final  int? expiryMonth;
@override@JsonKey(name: 'expiry_year') final  int? expiryYear;
@override@JsonKey(name: 'cardholder_name') final  String? cardholderName;
@override@JsonKey(name: 'bank_name') final  String? bankName;
@override@JsonKey(name: 'account_last_four') final  String? accountLastFour;
@override@JsonKey(name: 'wallet_provider') final  String? walletProvider;
@override@JsonKey(name: 'wallet_email') final  String? walletEmail;
@override@JsonKey(name: 'is_default') final  bool isDefault;
@override@JsonKey(name: 'is_active') final  bool isActive;
@override@JsonKey(name: 'is_verified') final  bool isVerified;
@override@JsonKey(includeFromJson: true, includeToJson: false) final  DateTime? createdAt;
@override@JsonKey(includeFromJson: true, includeToJson: false) final  DateTime? updatedAt;
@override@JsonKey(includeFromJson: true, includeToJson: false, name: 'is_deleted') final  bool isDeleted;

/// Create a copy of PaymentMethodModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PaymentMethodModelCopyWith<_PaymentMethodModel> get copyWith => __$PaymentMethodModelCopyWithImpl<_PaymentMethodModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$PaymentMethodModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PaymentMethodModel&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.type, type) || other.type == type)&&(identical(other.cardLastFour, cardLastFour) || other.cardLastFour == cardLastFour)&&(identical(other.cardBrand, cardBrand) || other.cardBrand == cardBrand)&&(identical(other.expiryMonth, expiryMonth) || other.expiryMonth == expiryMonth)&&(identical(other.expiryYear, expiryYear) || other.expiryYear == expiryYear)&&(identical(other.cardholderName, cardholderName) || other.cardholderName == cardholderName)&&(identical(other.bankName, bankName) || other.bankName == bankName)&&(identical(other.accountLastFour, accountLastFour) || other.accountLastFour == accountLastFour)&&(identical(other.walletProvider, walletProvider) || other.walletProvider == walletProvider)&&(identical(other.walletEmail, walletEmail) || other.walletEmail == walletEmail)&&(identical(other.isDefault, isDefault) || other.isDefault == isDefault)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&(identical(other.isVerified, isVerified) || other.isVerified == isVerified)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.isDeleted, isDeleted) || other.isDeleted == isDeleted));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,userId,type,cardLastFour,cardBrand,expiryMonth,expiryYear,cardholderName,bankName,accountLastFour,walletProvider,walletEmail,isDefault,isActive,isVerified,createdAt,updatedAt,isDeleted);

@override
String toString() {
  return 'PaymentMethodModel(id: $id, userId: $userId, type: $type, cardLastFour: $cardLastFour, cardBrand: $cardBrand, expiryMonth: $expiryMonth, expiryYear: $expiryYear, cardholderName: $cardholderName, bankName: $bankName, accountLastFour: $accountLastFour, walletProvider: $walletProvider, walletEmail: $walletEmail, isDefault: $isDefault, isActive: $isActive, isVerified: $isVerified, createdAt: $createdAt, updatedAt: $updatedAt, isDeleted: $isDeleted)';
}


}

/// @nodoc
abstract mixin class _$PaymentMethodModelCopyWith<$Res> implements $PaymentMethodModelCopyWith<$Res> {
  factory _$PaymentMethodModelCopyWith(_PaymentMethodModel value, $Res Function(_PaymentMethodModel) _then) = __$PaymentMethodModelCopyWithImpl;
@override @useResult
$Res call({
 String id,@JsonKey(name: 'user_id') String userId, PaymentMethodType type,@JsonKey(name: 'card_last_four') String? cardLastFour,@JsonKey(name: 'card_brand') String? cardBrand,@JsonKey(name: 'expiry_month') int? expiryMonth,@JsonKey(name: 'expiry_year') int? expiryYear,@JsonKey(name: 'cardholder_name') String? cardholderName,@JsonKey(name: 'bank_name') String? bankName,@JsonKey(name: 'account_last_four') String? accountLastFour,@JsonKey(name: 'wallet_provider') String? walletProvider,@JsonKey(name: 'wallet_email') String? walletEmail,@JsonKey(name: 'is_default') bool isDefault,@JsonKey(name: 'is_active') bool isActive,@JsonKey(name: 'is_verified') bool isVerified,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? createdAt,@JsonKey(includeFromJson: true, includeToJson: false) DateTime? updatedAt,@JsonKey(includeFromJson: true, includeToJson: false, name: 'is_deleted') bool isDeleted
});




}
/// @nodoc
class __$PaymentMethodModelCopyWithImpl<$Res>
    implements _$PaymentMethodModelCopyWith<$Res> {
  __$PaymentMethodModelCopyWithImpl(this._self, this._then);

  final _PaymentMethodModel _self;
  final $Res Function(_PaymentMethodModel) _then;

/// Create a copy of PaymentMethodModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? userId = null,Object? type = null,Object? cardLastFour = freezed,Object? cardBrand = freezed,Object? expiryMonth = freezed,Object? expiryYear = freezed,Object? cardholderName = freezed,Object? bankName = freezed,Object? accountLastFour = freezed,Object? walletProvider = freezed,Object? walletEmail = freezed,Object? isDefault = null,Object? isActive = null,Object? isVerified = null,Object? createdAt = freezed,Object? updatedAt = freezed,Object? isDeleted = null,}) {
  return _then(_PaymentMethodModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as PaymentMethodType,cardLastFour: freezed == cardLastFour ? _self.cardLastFour : cardLastFour // ignore: cast_nullable_to_non_nullable
as String?,cardBrand: freezed == cardBrand ? _self.cardBrand : cardBrand // ignore: cast_nullable_to_non_nullable
as String?,expiryMonth: freezed == expiryMonth ? _self.expiryMonth : expiryMonth // ignore: cast_nullable_to_non_nullable
as int?,expiryYear: freezed == expiryYear ? _self.expiryYear : expiryYear // ignore: cast_nullable_to_non_nullable
as int?,cardholderName: freezed == cardholderName ? _self.cardholderName : cardholderName // ignore: cast_nullable_to_non_nullable
as String?,bankName: freezed == bankName ? _self.bankName : bankName // ignore: cast_nullable_to_non_nullable
as String?,accountLastFour: freezed == accountLastFour ? _self.accountLastFour : accountLastFour // ignore: cast_nullable_to_non_nullable
as String?,walletProvider: freezed == walletProvider ? _self.walletProvider : walletProvider // ignore: cast_nullable_to_non_nullable
as String?,walletEmail: freezed == walletEmail ? _self.walletEmail : walletEmail // ignore: cast_nullable_to_non_nullable
as String?,isDefault: null == isDefault ? _self.isDefault : isDefault // ignore: cast_nullable_to_non_nullable
as bool,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,isVerified: null == isVerified ? _self.isVerified : isVerified // ignore: cast_nullable_to_non_nullable
as bool,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,isDeleted: null == isDeleted ? _self.isDeleted : isDeleted // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}

// dart format on
