class CategoryAttribute {
  final String id;
  final String mainCategoryId;
  final String attributeNameEn;
  final String attributeNameAr;
  final String attributeType;
  final bool isRequired;
  final bool isSearchable;
  final bool isFilterable;
  final int sortOrder;
  final Map<String, dynamic>? validationRules;
  final String? defaultValue;
  final DateTime createdAt;
  final DateTime updatedAt;

  CategoryAttribute({
    required this.id,
    required this.mainCategoryId,
    required this.attributeNameEn,
    required this.attributeNameAr,
    required this.attributeType,
    this.isRequired = false,
    this.isSearchable = true,
    this.isFilterable = true,
    this.sortOrder = 0,
    this.validationRules,
    this.defaultValue,
    required this.createdAt,
    required this.updatedAt,
  });

  factory CategoryAttribute.fromJson(Map<String, dynamic> json) {
    return CategoryAttribute(
      id: json['id'] as String,
      mainCategoryId: json['main_category_id'] as String,
      attributeNameEn: json['attribute_name_en'] as String,
      attributeNameAr: json['attribute_name_ar'] as String,
      attributeType: json['attribute_type'] as String,
      isRequired: json['is_required'] as bool? ?? false,
      isSearchable: json['is_searchable'] as bool? ?? true,
      isFilterable: json['is_filterable'] as bool? ?? true,
      sortOrder: json['sort_order'] as int? ?? 0,
      validationRules: json['validation_rules'] as Map<String, dynamic>?,
      defaultValue: json['default_value'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'main_category_id': mainCategoryId,
      'attribute_name_en': attributeNameEn,
      'attribute_name_ar': attributeNameAr,
      'attribute_type': attributeType,
      'is_required': isRequired,
      'is_searchable': isSearchable,
      'is_filterable': isFilterable,
      'sort_order': sortOrder,
      'validation_rules': validationRules,
      'default_value': defaultValue,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }
}

class MainCategory {
  final String id;
  final String partCategoryId;
  final String nameEn;
  final String nameAr;
  final String? descriptionEn;
  final String? descriptionAr;
  final bool isActive;
  final int sortOrder;
  final DateTime createdAt;
  final DateTime updatedAt;

  MainCategory({
    required this.id,
    required this.partCategoryId,
    required this.nameEn,
    required this.nameAr,
    this.descriptionEn,
    this.descriptionAr,
    this.isActive = true,
    this.sortOrder = 0,
    required this.createdAt,
    required this.updatedAt,
  });

  factory MainCategory.fromJson(Map<String, dynamic> json) {
    return MainCategory(
      id: json['id'] as String,
      partCategoryId: json['part_category_id'] as String,
      nameEn: json['name_en'] as String,
      nameAr: json['name_ar'] as String,
      descriptionEn: json['description_en'] as String?,
      descriptionAr: json['description_ar'] as String?,
      isActive: json['is_active'] as bool? ?? true,
      sortOrder: json['sort_order'] as int? ?? 0,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'part_category_id': partCategoryId,
      'name_en': nameEn,
      'name_ar': nameAr,
      'description_en': descriptionEn,
      'description_ar': descriptionAr,
      'is_active': isActive,
      'sort_order': sortOrder,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }
} 