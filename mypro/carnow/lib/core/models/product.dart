class Product {
  final String id;
  final String mainCategoryId;
  final String nameEn;
  final String nameAr;
  final String? descriptionEn;
  final String? descriptionAr;
  final String? manufacturerPartNumber;
  final List<String>? oemPartNumbers;
  final String? brand;
  final String conditionType;
  final String? fitmentType;
  final double coreCharge;
  final String? manufacturerWarranty;
  final double? price;
  final double? salePrice;
  final String currency;
  final int stockQuantity;
  final int minOrderQuantity;
  final double? weightKg;
  final String? dimensionsCm;
  final String? countryOfOrigin;
  final String? surfaceFinish;
  final bool isActive;
  final bool isFeatured;
  final bool isBestseller;
  final int sortOrder;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String? createdBy;
  final String? updatedBy;

  Product({
    required this.id,
    required this.mainCategoryId,
    required this.nameEn,
    required this.nameAr,
    this.descriptionEn,
    this.descriptionAr,
    this.manufacturerPartNumber,
    this.oemPartNumbers,
    this.brand,
    this.conditionType = 'new',
    this.fitmentType,
    this.coreCharge = 0.0,
    this.manufacturerWarranty,
    this.price,
    this.salePrice,
    this.currency = 'USD',
    this.stockQuantity = 0,
    this.minOrderQuantity = 1,
    this.weightKg,
    this.dimensionsCm,
    this.countryOfOrigin,
    this.surfaceFinish,
    this.isActive = true,
    this.isFeatured = false,
    this.isBestseller = false,
    this.sortOrder = 0,
    required this.createdAt,
    required this.updatedAt,
    this.createdBy,
    this.updatedBy,
  });

  factory Product.fromJson(Map<String, dynamic> json) {
    return Product(
      id: json['id'] as String,
      mainCategoryId: json['main_category_id'] as String,
      nameEn: json['name_en'] as String,
      nameAr: json['name_ar'] as String,
      descriptionEn: json['description_en'] as String?,
      descriptionAr: json['description_ar'] as String?,
      manufacturerPartNumber: json['manufacturer_part_number'] as String?,
      oemPartNumbers: (json['oem_part_numbers'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      brand: json['brand'] as String?,
      conditionType: json['condition_type'] as String? ?? 'new',
      fitmentType: json['fitment_type'] as String?,
      coreCharge: (json['core_charge'] as num?)?.toDouble() ?? 0.0,
      manufacturerWarranty: json['manufacturer_warranty'] as String?,
      price: (json['price'] as num?)?.toDouble(),
      salePrice: (json['sale_price'] as num?)?.toDouble(),
      currency: json['currency'] as String? ?? 'USD',
      stockQuantity: json['stock_quantity'] as int? ?? 0,
      minOrderQuantity: json['min_order_quantity'] as int? ?? 1,
      weightKg: (json['weight_kg'] as num?)?.toDouble(),
      dimensionsCm: json['dimensions_cm'] as String?,
      countryOfOrigin: json['country_of_origin'] as String?,
      surfaceFinish: json['surface_finish'] as String?,
      isActive: json['is_active'] as bool? ?? true,
      isFeatured: json['is_featured'] as bool? ?? false,
      isBestseller: json['is_bestseller'] as bool? ?? false,
      sortOrder: json['sort_order'] as int? ?? 0,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      createdBy: json['created_by'] as String?,
      updatedBy: json['updated_by'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'main_category_id': mainCategoryId,
      'name_en': nameEn,
      'name_ar': nameAr,
      'description_en': descriptionEn,
      'description_ar': descriptionAr,
      'manufacturer_part_number': manufacturerPartNumber,
      'oem_part_numbers': oemPartNumbers,
      'brand': brand,
      'condition_type': conditionType,
      'fitment_type': fitmentType,
      'core_charge': coreCharge,
      'manufacturer_warranty': manufacturerWarranty,
      'price': price,
      'sale_price': salePrice,
      'currency': currency,
      'stock_quantity': stockQuantity,
      'min_order_quantity': minOrderQuantity,
      'weight_kg': weightKg,
      'dimensions_cm': dimensionsCm,
      'country_of_origin': countryOfOrigin,
      'surface_finish': surfaceFinish,
      'is_active': isActive,
      'is_featured': isFeatured,
      'is_bestseller': isBestseller,
      'sort_order': sortOrder,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'created_by': createdBy,
      'updated_by': updatedBy,
    };
  }
} 