// ignore_for_file: invalid_annotation_target
import 'package:freezed_annotation/freezed_annotation.dart';

import 'enums.dart';
import 'order_item_model.dart';

part 'order_model.freezed.dart';
part 'order_model.g.dart';

@freezed
abstract class OrderModel with _$OrderModel {
  const factory OrderModel({
    int? id,
    int? buyerId, // Renaming back from customerId based on original
    double? totalAmount,
    @Default('LYD') String? currency,
    OrderStatus? status, // Make nullable
    String? shippingAddress,
    DateTime? createdAt, // Make nullable
    DateTime? updatedAt,
    List<OrderItemModel>? items,
    String? trackingNumber, // إضافة رقم تتبع الشحنة
  }) = _OrderModel;

  factory OrderModel.fromJson(Map<String, dynamic> json) =>
      _$OrderModelFromJson(json);
}

// Removed OrderItemModel definition from here
// Removed old OrderStatus class
