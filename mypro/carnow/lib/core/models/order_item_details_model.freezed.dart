// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'order_item_details_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$OrderItemDetailsModel {

 String get id; String get productName; int get quantity; double get unitPrice; double get totalPrice; String? get productImage; String? get sku;
/// Create a copy of OrderItemDetailsModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$OrderItemDetailsModelCopyWith<OrderItemDetailsModel> get copyWith => _$OrderItemDetailsModelCopyWithImpl<OrderItemDetailsModel>(this as OrderItemDetailsModel, _$identity);

  /// Serializes this OrderItemDetailsModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is OrderItemDetailsModel&&(identical(other.id, id) || other.id == id)&&(identical(other.productName, productName) || other.productName == productName)&&(identical(other.quantity, quantity) || other.quantity == quantity)&&(identical(other.unitPrice, unitPrice) || other.unitPrice == unitPrice)&&(identical(other.totalPrice, totalPrice) || other.totalPrice == totalPrice)&&(identical(other.productImage, productImage) || other.productImage == productImage)&&(identical(other.sku, sku) || other.sku == sku));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,productName,quantity,unitPrice,totalPrice,productImage,sku);

@override
String toString() {
  return 'OrderItemDetailsModel(id: $id, productName: $productName, quantity: $quantity, unitPrice: $unitPrice, totalPrice: $totalPrice, productImage: $productImage, sku: $sku)';
}


}

/// @nodoc
abstract mixin class $OrderItemDetailsModelCopyWith<$Res>  {
  factory $OrderItemDetailsModelCopyWith(OrderItemDetailsModel value, $Res Function(OrderItemDetailsModel) _then) = _$OrderItemDetailsModelCopyWithImpl;
@useResult
$Res call({
 String id, String productName, int quantity, double unitPrice, double totalPrice, String? productImage, String? sku
});




}
/// @nodoc
class _$OrderItemDetailsModelCopyWithImpl<$Res>
    implements $OrderItemDetailsModelCopyWith<$Res> {
  _$OrderItemDetailsModelCopyWithImpl(this._self, this._then);

  final OrderItemDetailsModel _self;
  final $Res Function(OrderItemDetailsModel) _then;

/// Create a copy of OrderItemDetailsModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? productName = null,Object? quantity = null,Object? unitPrice = null,Object? totalPrice = null,Object? productImage = freezed,Object? sku = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,productName: null == productName ? _self.productName : productName // ignore: cast_nullable_to_non_nullable
as String,quantity: null == quantity ? _self.quantity : quantity // ignore: cast_nullable_to_non_nullable
as int,unitPrice: null == unitPrice ? _self.unitPrice : unitPrice // ignore: cast_nullable_to_non_nullable
as double,totalPrice: null == totalPrice ? _self.totalPrice : totalPrice // ignore: cast_nullable_to_non_nullable
as double,productImage: freezed == productImage ? _self.productImage : productImage // ignore: cast_nullable_to_non_nullable
as String?,sku: freezed == sku ? _self.sku : sku // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// Adds pattern-matching-related methods to [OrderItemDetailsModel].
extension OrderItemDetailsModelPatterns on OrderItemDetailsModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _OrderItemDetailsModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _OrderItemDetailsModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _OrderItemDetailsModel value)  $default,){
final _that = this;
switch (_that) {
case _OrderItemDetailsModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _OrderItemDetailsModel value)?  $default,){
final _that = this;
switch (_that) {
case _OrderItemDetailsModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String productName,  int quantity,  double unitPrice,  double totalPrice,  String? productImage,  String? sku)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _OrderItemDetailsModel() when $default != null:
return $default(_that.id,_that.productName,_that.quantity,_that.unitPrice,_that.totalPrice,_that.productImage,_that.sku);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String productName,  int quantity,  double unitPrice,  double totalPrice,  String? productImage,  String? sku)  $default,) {final _that = this;
switch (_that) {
case _OrderItemDetailsModel():
return $default(_that.id,_that.productName,_that.quantity,_that.unitPrice,_that.totalPrice,_that.productImage,_that.sku);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String productName,  int quantity,  double unitPrice,  double totalPrice,  String? productImage,  String? sku)?  $default,) {final _that = this;
switch (_that) {
case _OrderItemDetailsModel() when $default != null:
return $default(_that.id,_that.productName,_that.quantity,_that.unitPrice,_that.totalPrice,_that.productImage,_that.sku);case _:
  return null;

}
}

}

/// @nodoc

@JsonSerializable(explicitToJson: true, fieldRename: FieldRename.snake)
class _OrderItemDetailsModel implements OrderItemDetailsModel {
  const _OrderItemDetailsModel({required this.id, required this.productName, required this.quantity, required this.unitPrice, required this.totalPrice, this.productImage, this.sku});
  factory _OrderItemDetailsModel.fromJson(Map<String, dynamic> json) => _$OrderItemDetailsModelFromJson(json);

@override final  String id;
@override final  String productName;
@override final  int quantity;
@override final  double unitPrice;
@override final  double totalPrice;
@override final  String? productImage;
@override final  String? sku;

/// Create a copy of OrderItemDetailsModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$OrderItemDetailsModelCopyWith<_OrderItemDetailsModel> get copyWith => __$OrderItemDetailsModelCopyWithImpl<_OrderItemDetailsModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$OrderItemDetailsModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OrderItemDetailsModel&&(identical(other.id, id) || other.id == id)&&(identical(other.productName, productName) || other.productName == productName)&&(identical(other.quantity, quantity) || other.quantity == quantity)&&(identical(other.unitPrice, unitPrice) || other.unitPrice == unitPrice)&&(identical(other.totalPrice, totalPrice) || other.totalPrice == totalPrice)&&(identical(other.productImage, productImage) || other.productImage == productImage)&&(identical(other.sku, sku) || other.sku == sku));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,productName,quantity,unitPrice,totalPrice,productImage,sku);

@override
String toString() {
  return 'OrderItemDetailsModel(id: $id, productName: $productName, quantity: $quantity, unitPrice: $unitPrice, totalPrice: $totalPrice, productImage: $productImage, sku: $sku)';
}


}

/// @nodoc
abstract mixin class _$OrderItemDetailsModelCopyWith<$Res> implements $OrderItemDetailsModelCopyWith<$Res> {
  factory _$OrderItemDetailsModelCopyWith(_OrderItemDetailsModel value, $Res Function(_OrderItemDetailsModel) _then) = __$OrderItemDetailsModelCopyWithImpl;
@override @useResult
$Res call({
 String id, String productName, int quantity, double unitPrice, double totalPrice, String? productImage, String? sku
});




}
/// @nodoc
class __$OrderItemDetailsModelCopyWithImpl<$Res>
    implements _$OrderItemDetailsModelCopyWith<$Res> {
  __$OrderItemDetailsModelCopyWithImpl(this._self, this._then);

  final _OrderItemDetailsModel _self;
  final $Res Function(_OrderItemDetailsModel) _then;

/// Create a copy of OrderItemDetailsModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? productName = null,Object? quantity = null,Object? unitPrice = null,Object? totalPrice = null,Object? productImage = freezed,Object? sku = freezed,}) {
  return _then(_OrderItemDetailsModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,productName: null == productName ? _self.productName : productName // ignore: cast_nullable_to_non_nullable
as String,quantity: null == quantity ? _self.quantity : quantity // ignore: cast_nullable_to_non_nullable
as int,unitPrice: null == unitPrice ? _self.unitPrice : unitPrice // ignore: cast_nullable_to_non_nullable
as double,totalPrice: null == totalPrice ? _self.totalPrice : totalPrice // ignore: cast_nullable_to_non_nullable
as double,productImage: freezed == productImage ? _self.productImage : productImage // ignore: cast_nullable_to_non_nullable
as String?,sku: freezed == sku ? _self.sku : sku // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

// dart format on
