import 'package:freezed_annotation/freezed_annotation.dart';

part 'subscription_response.freezed.dart';
part 'subscription_response.g.dart';

@freezed
abstract class SubscriptionResponse with _$SubscriptionResponse {
  const factory SubscriptionResponse({
    required String id,
    required String status,
    @J<PERSON><PERSON><PERSON>(name: 'created_at') required DateTime createdAt,
    @J<PERSON><PERSON><PERSON>(name: 'updated_at') DateTime? updatedAt,
    String? message,
    @Json<PERSON>ey(name: 'store_name') String? storeName,
    String? phone,
    String? city,
    String? address,
    String? description,
    @Json<PERSON>ey(name: 'plan_type') String? planType,
    double? price,
    @Json<PERSON>ey(name: 'user_id') String? userId,
    @J<PERSON><PERSON><PERSON>(name: 'error_code') String? errorCode,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'error_details') Map<String, dynamic>? errorDetails,
  }) = _SubscriptionResponse;

  factory SubscriptionResponse.fromJson(Map<String, dynamic> json) =>
      _$SubscriptionResponseFromJson(json);
}

extension SubscriptionResponseHelpers on SubscriptionResponse {
  /// Checks if the subscription was successful
  bool get isSuccess =>
      status.toLowerCase() == 'success' || status.toLowerCase() == 'approved';

  /// Checks if the subscription is pending
  bool get isPending => status.toLowerCase() == 'pending';

  /// Checks if the subscription failed
  bool get isFailed =>
      status.toLowerCase() == 'failed' || status.toLowerCase() == 'rejected';

  /// Checks if the subscription is in processing state
  bool get isProcessing => status.toLowerCase() == 'processing';

  /// Gets user-friendly status message in Arabic
  String get statusMessageArabic {
    switch (status.toLowerCase()) {
      case 'success':
      case 'approved':
        return 'تم قبول طلب الاشتراك بنجاح';
      case 'pending':
        return 'طلب الاشتراك قيد المراجعة';
      case 'processing':
        return 'جاري معالجة طلب الاشتراك';
      case 'failed':
        return 'فشل في معالجة طلب الاشتراك';
      case 'rejected':
        return 'تم رفض طلب الاشتراك';
      case 'cancelled':
        return 'تم إلغاء طلب الاشتراك';
      case 'expired':
        return 'انتهت صلاحية طلب الاشتراك';
      default:
        return 'حالة غير معروفة';
    }
  }

  /// Gets user-friendly error message in Arabic if available
  String? get errorMessageArabic {
    if (message != null && message!.isNotEmpty) {
      return message;
    }

    if (errorCode != null) {
      return _getErrorMessageFromCode(errorCode!);
    }

    return null;
  }

  /// Gets formatted subscription details for display
  Map<String, String> get displayDetails {
    final details = <String, String>{};

    if (storeName != null && storeName!.isNotEmpty) {
      details['اسم المتجر'] = storeName!;
    }

    if (phone != null && phone!.isNotEmpty) {
      details['رقم الهاتف'] = phone!;
    }

    if (city != null && city!.isNotEmpty) {
      details['المدينة'] = city!;
    }

    if (planType != null && planType!.isNotEmpty) {
      details['نوع الخطة'] = _getPlanTypeArabic(planType!);
    }

    if (price != null && price! > 0) {
      details['السعر'] = '${price!.toStringAsFixed(2)} ريال';
    }

    details['تاريخ الطلب'] = _formatDate(createdAt);

    if (updatedAt != null) {
      details['تاريخ التحديث'] = _formatDate(updatedAt!);
    }

    return details;
  }

  /// Converts error code to Arabic message
  String _getErrorMessageFromCode(String errorCode) {
    switch (errorCode.toLowerCase()) {
      case 'invalid_data':
        return 'البيانات المدخلة غير صحيحة';
      case 'duplicate_request':
        return 'طلب مكرر، يوجد طلب اشتراك سابق';
      case 'insufficient_funds':
        return 'رصيد غير كافي';
      case 'payment_failed':
        return 'فشل في عملية الدفع';
      case 'user_not_found':
        return 'المستخدم غير موجود';
      case 'plan_not_available':
        return 'الخطة المطلوبة غير متاحة';
      case 'database_error':
        return 'خطأ في قاعدة البيانات';
      case 'network_error':
        return 'خطأ في الاتصال';
      case 'server_error':
        return 'خطأ في الخادم';
      case 'validation_error':
        return 'خطأ في التحقق من البيانات';
      case 'unauthorized':
        return 'غير مصرح بهذا الإجراء';
      case 'rate_limit_exceeded':
        return 'تم تجاوز الحد المسموح من الطلبات';
      default:
        return 'حدث خطأ غير متوقع';
    }
  }

  /// Converts plan type to Arabic
  String _getPlanTypeArabic(String planType) {
    switch (planType.toLowerCase()) {
      case 'basic':
        return 'أساسية';
      case 'premium':
        return 'مميزة';
      case 'enterprise':
        return 'للشركات';
      case 'starter':
        return 'للمبتدئين';
      case 'professional':
        return 'احترافية';
      default:
        return planType;
    }
  }

  /// Formats date for display
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
  }
}
