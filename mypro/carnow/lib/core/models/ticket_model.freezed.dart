// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'ticket_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$TicketModel {

 int? get id;@JsonKey(name: 'user_id') int? get userId; UserModel? get user; String? get subject; String? get description;@JsonKey(name: 'ticket_status') TicketStatus get status;@JsonKey(name: 'priority') TicketPriority get priority;@JsonKey(name: 'category') TicketCategory get category;@JsonKey(name: 'assigned_to') int? get assignedTo; UserModel? get assignedAgent;@JsonKey(name: 'resolved_at') DateTime? get resolvedAt;@JsonKey(name: 'created_at') DateTime? get createdAt;@JsonKey(name: 'updated_at') DateTime? get updatedAt;@JsonKey(name: 'is_deleted', defaultValue: false) bool get isDeleted; List<TicketMessageModel> get messages;// Computed fields
@JsonKey(name: 'last_message_at') DateTime? get lastMessageAt;@JsonKey(name: 'last_message_text') String? get lastMessageText;@JsonKey(name: 'unread_count') int get unreadCount;
/// Create a copy of TicketModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$TicketModelCopyWith<TicketModel> get copyWith => _$TicketModelCopyWithImpl<TicketModel>(this as TicketModel, _$identity);

  /// Serializes this TicketModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is TicketModel&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.user, user) || other.user == user)&&(identical(other.subject, subject) || other.subject == subject)&&(identical(other.description, description) || other.description == description)&&(identical(other.status, status) || other.status == status)&&(identical(other.priority, priority) || other.priority == priority)&&(identical(other.category, category) || other.category == category)&&(identical(other.assignedTo, assignedTo) || other.assignedTo == assignedTo)&&(identical(other.assignedAgent, assignedAgent) || other.assignedAgent == assignedAgent)&&(identical(other.resolvedAt, resolvedAt) || other.resolvedAt == resolvedAt)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.isDeleted, isDeleted) || other.isDeleted == isDeleted)&&const DeepCollectionEquality().equals(other.messages, messages)&&(identical(other.lastMessageAt, lastMessageAt) || other.lastMessageAt == lastMessageAt)&&(identical(other.lastMessageText, lastMessageText) || other.lastMessageText == lastMessageText)&&(identical(other.unreadCount, unreadCount) || other.unreadCount == unreadCount));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,userId,user,subject,description,status,priority,category,assignedTo,assignedAgent,resolvedAt,createdAt,updatedAt,isDeleted,const DeepCollectionEquality().hash(messages),lastMessageAt,lastMessageText,unreadCount);

@override
String toString() {
  return 'TicketModel(id: $id, userId: $userId, user: $user, subject: $subject, description: $description, status: $status, priority: $priority, category: $category, assignedTo: $assignedTo, assignedAgent: $assignedAgent, resolvedAt: $resolvedAt, createdAt: $createdAt, updatedAt: $updatedAt, isDeleted: $isDeleted, messages: $messages, lastMessageAt: $lastMessageAt, lastMessageText: $lastMessageText, unreadCount: $unreadCount)';
}


}

/// @nodoc
abstract mixin class $TicketModelCopyWith<$Res>  {
  factory $TicketModelCopyWith(TicketModel value, $Res Function(TicketModel) _then) = _$TicketModelCopyWithImpl;
@useResult
$Res call({
 int? id,@JsonKey(name: 'user_id') int? userId, UserModel? user, String? subject, String? description,@JsonKey(name: 'ticket_status') TicketStatus status,@JsonKey(name: 'priority') TicketPriority priority,@JsonKey(name: 'category') TicketCategory category,@JsonKey(name: 'assigned_to') int? assignedTo, UserModel? assignedAgent,@JsonKey(name: 'resolved_at') DateTime? resolvedAt,@JsonKey(name: 'created_at') DateTime? createdAt,@JsonKey(name: 'updated_at') DateTime? updatedAt,@JsonKey(name: 'is_deleted', defaultValue: false) bool isDeleted, List<TicketMessageModel> messages,@JsonKey(name: 'last_message_at') DateTime? lastMessageAt,@JsonKey(name: 'last_message_text') String? lastMessageText,@JsonKey(name: 'unread_count') int unreadCount
});


$UserModelCopyWith<$Res>? get user;$UserModelCopyWith<$Res>? get assignedAgent;

}
/// @nodoc
class _$TicketModelCopyWithImpl<$Res>
    implements $TicketModelCopyWith<$Res> {
  _$TicketModelCopyWithImpl(this._self, this._then);

  final TicketModel _self;
  final $Res Function(TicketModel) _then;

/// Create a copy of TicketModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = freezed,Object? userId = freezed,Object? user = freezed,Object? subject = freezed,Object? description = freezed,Object? status = null,Object? priority = null,Object? category = null,Object? assignedTo = freezed,Object? assignedAgent = freezed,Object? resolvedAt = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,Object? isDeleted = null,Object? messages = null,Object? lastMessageAt = freezed,Object? lastMessageText = freezed,Object? unreadCount = null,}) {
  return _then(_self.copyWith(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,userId: freezed == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as int?,user: freezed == user ? _self.user : user // ignore: cast_nullable_to_non_nullable
as UserModel?,subject: freezed == subject ? _self.subject : subject // ignore: cast_nullable_to_non_nullable
as String?,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as TicketStatus,priority: null == priority ? _self.priority : priority // ignore: cast_nullable_to_non_nullable
as TicketPriority,category: null == category ? _self.category : category // ignore: cast_nullable_to_non_nullable
as TicketCategory,assignedTo: freezed == assignedTo ? _self.assignedTo : assignedTo // ignore: cast_nullable_to_non_nullable
as int?,assignedAgent: freezed == assignedAgent ? _self.assignedAgent : assignedAgent // ignore: cast_nullable_to_non_nullable
as UserModel?,resolvedAt: freezed == resolvedAt ? _self.resolvedAt : resolvedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,isDeleted: null == isDeleted ? _self.isDeleted : isDeleted // ignore: cast_nullable_to_non_nullable
as bool,messages: null == messages ? _self.messages : messages // ignore: cast_nullable_to_non_nullable
as List<TicketMessageModel>,lastMessageAt: freezed == lastMessageAt ? _self.lastMessageAt : lastMessageAt // ignore: cast_nullable_to_non_nullable
as DateTime?,lastMessageText: freezed == lastMessageText ? _self.lastMessageText : lastMessageText // ignore: cast_nullable_to_non_nullable
as String?,unreadCount: null == unreadCount ? _self.unreadCount : unreadCount // ignore: cast_nullable_to_non_nullable
as int,
  ));
}
/// Create a copy of TicketModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$UserModelCopyWith<$Res>? get user {
    if (_self.user == null) {
    return null;
  }

  return $UserModelCopyWith<$Res>(_self.user!, (value) {
    return _then(_self.copyWith(user: value));
  });
}/// Create a copy of TicketModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$UserModelCopyWith<$Res>? get assignedAgent {
    if (_self.assignedAgent == null) {
    return null;
  }

  return $UserModelCopyWith<$Res>(_self.assignedAgent!, (value) {
    return _then(_self.copyWith(assignedAgent: value));
  });
}
}


/// Adds pattern-matching-related methods to [TicketModel].
extension TicketModelPatterns on TicketModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _TicketModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _TicketModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _TicketModel value)  $default,){
final _that = this;
switch (_that) {
case _TicketModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _TicketModel value)?  $default,){
final _that = this;
switch (_that) {
case _TicketModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int? id, @JsonKey(name: 'user_id')  int? userId,  UserModel? user,  String? subject,  String? description, @JsonKey(name: 'ticket_status')  TicketStatus status, @JsonKey(name: 'priority')  TicketPriority priority, @JsonKey(name: 'category')  TicketCategory category, @JsonKey(name: 'assigned_to')  int? assignedTo,  UserModel? assignedAgent, @JsonKey(name: 'resolved_at')  DateTime? resolvedAt, @JsonKey(name: 'created_at')  DateTime? createdAt, @JsonKey(name: 'updated_at')  DateTime? updatedAt, @JsonKey(name: 'is_deleted', defaultValue: false)  bool isDeleted,  List<TicketMessageModel> messages, @JsonKey(name: 'last_message_at')  DateTime? lastMessageAt, @JsonKey(name: 'last_message_text')  String? lastMessageText, @JsonKey(name: 'unread_count')  int unreadCount)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _TicketModel() when $default != null:
return $default(_that.id,_that.userId,_that.user,_that.subject,_that.description,_that.status,_that.priority,_that.category,_that.assignedTo,_that.assignedAgent,_that.resolvedAt,_that.createdAt,_that.updatedAt,_that.isDeleted,_that.messages,_that.lastMessageAt,_that.lastMessageText,_that.unreadCount);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int? id, @JsonKey(name: 'user_id')  int? userId,  UserModel? user,  String? subject,  String? description, @JsonKey(name: 'ticket_status')  TicketStatus status, @JsonKey(name: 'priority')  TicketPriority priority, @JsonKey(name: 'category')  TicketCategory category, @JsonKey(name: 'assigned_to')  int? assignedTo,  UserModel? assignedAgent, @JsonKey(name: 'resolved_at')  DateTime? resolvedAt, @JsonKey(name: 'created_at')  DateTime? createdAt, @JsonKey(name: 'updated_at')  DateTime? updatedAt, @JsonKey(name: 'is_deleted', defaultValue: false)  bool isDeleted,  List<TicketMessageModel> messages, @JsonKey(name: 'last_message_at')  DateTime? lastMessageAt, @JsonKey(name: 'last_message_text')  String? lastMessageText, @JsonKey(name: 'unread_count')  int unreadCount)  $default,) {final _that = this;
switch (_that) {
case _TicketModel():
return $default(_that.id,_that.userId,_that.user,_that.subject,_that.description,_that.status,_that.priority,_that.category,_that.assignedTo,_that.assignedAgent,_that.resolvedAt,_that.createdAt,_that.updatedAt,_that.isDeleted,_that.messages,_that.lastMessageAt,_that.lastMessageText,_that.unreadCount);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int? id, @JsonKey(name: 'user_id')  int? userId,  UserModel? user,  String? subject,  String? description, @JsonKey(name: 'ticket_status')  TicketStatus status, @JsonKey(name: 'priority')  TicketPriority priority, @JsonKey(name: 'category')  TicketCategory category, @JsonKey(name: 'assigned_to')  int? assignedTo,  UserModel? assignedAgent, @JsonKey(name: 'resolved_at')  DateTime? resolvedAt, @JsonKey(name: 'created_at')  DateTime? createdAt, @JsonKey(name: 'updated_at')  DateTime? updatedAt, @JsonKey(name: 'is_deleted', defaultValue: false)  bool isDeleted,  List<TicketMessageModel> messages, @JsonKey(name: 'last_message_at')  DateTime? lastMessageAt, @JsonKey(name: 'last_message_text')  String? lastMessageText, @JsonKey(name: 'unread_count')  int unreadCount)?  $default,) {final _that = this;
switch (_that) {
case _TicketModel() when $default != null:
return $default(_that.id,_that.userId,_that.user,_that.subject,_that.description,_that.status,_that.priority,_that.category,_that.assignedTo,_that.assignedAgent,_that.resolvedAt,_that.createdAt,_that.updatedAt,_that.isDeleted,_that.messages,_that.lastMessageAt,_that.lastMessageText,_that.unreadCount);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _TicketModel extends TicketModel {
  const _TicketModel({this.id, @JsonKey(name: 'user_id') this.userId, this.user, this.subject, this.description, @JsonKey(name: 'ticket_status') this.status = TicketStatus.pending, @JsonKey(name: 'priority') this.priority = TicketPriority.medium, @JsonKey(name: 'category') this.category = TicketCategory.general, @JsonKey(name: 'assigned_to') this.assignedTo, this.assignedAgent, @JsonKey(name: 'resolved_at') this.resolvedAt, @JsonKey(name: 'created_at') this.createdAt, @JsonKey(name: 'updated_at') this.updatedAt, @JsonKey(name: 'is_deleted', defaultValue: false) this.isDeleted = false, final  List<TicketMessageModel> messages = const [], @JsonKey(name: 'last_message_at') this.lastMessageAt, @JsonKey(name: 'last_message_text') this.lastMessageText, @JsonKey(name: 'unread_count') this.unreadCount = 0}): _messages = messages,super._();
  factory _TicketModel.fromJson(Map<String, dynamic> json) => _$TicketModelFromJson(json);

@override final  int? id;
@override@JsonKey(name: 'user_id') final  int? userId;
@override final  UserModel? user;
@override final  String? subject;
@override final  String? description;
@override@JsonKey(name: 'ticket_status') final  TicketStatus status;
@override@JsonKey(name: 'priority') final  TicketPriority priority;
@override@JsonKey(name: 'category') final  TicketCategory category;
@override@JsonKey(name: 'assigned_to') final  int? assignedTo;
@override final  UserModel? assignedAgent;
@override@JsonKey(name: 'resolved_at') final  DateTime? resolvedAt;
@override@JsonKey(name: 'created_at') final  DateTime? createdAt;
@override@JsonKey(name: 'updated_at') final  DateTime? updatedAt;
@override@JsonKey(name: 'is_deleted', defaultValue: false) final  bool isDeleted;
 final  List<TicketMessageModel> _messages;
@override@JsonKey() List<TicketMessageModel> get messages {
  if (_messages is EqualUnmodifiableListView) return _messages;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_messages);
}

// Computed fields
@override@JsonKey(name: 'last_message_at') final  DateTime? lastMessageAt;
@override@JsonKey(name: 'last_message_text') final  String? lastMessageText;
@override@JsonKey(name: 'unread_count') final  int unreadCount;

/// Create a copy of TicketModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$TicketModelCopyWith<_TicketModel> get copyWith => __$TicketModelCopyWithImpl<_TicketModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$TicketModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _TicketModel&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.user, user) || other.user == user)&&(identical(other.subject, subject) || other.subject == subject)&&(identical(other.description, description) || other.description == description)&&(identical(other.status, status) || other.status == status)&&(identical(other.priority, priority) || other.priority == priority)&&(identical(other.category, category) || other.category == category)&&(identical(other.assignedTo, assignedTo) || other.assignedTo == assignedTo)&&(identical(other.assignedAgent, assignedAgent) || other.assignedAgent == assignedAgent)&&(identical(other.resolvedAt, resolvedAt) || other.resolvedAt == resolvedAt)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.isDeleted, isDeleted) || other.isDeleted == isDeleted)&&const DeepCollectionEquality().equals(other._messages, _messages)&&(identical(other.lastMessageAt, lastMessageAt) || other.lastMessageAt == lastMessageAt)&&(identical(other.lastMessageText, lastMessageText) || other.lastMessageText == lastMessageText)&&(identical(other.unreadCount, unreadCount) || other.unreadCount == unreadCount));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,userId,user,subject,description,status,priority,category,assignedTo,assignedAgent,resolvedAt,createdAt,updatedAt,isDeleted,const DeepCollectionEquality().hash(_messages),lastMessageAt,lastMessageText,unreadCount);

@override
String toString() {
  return 'TicketModel(id: $id, userId: $userId, user: $user, subject: $subject, description: $description, status: $status, priority: $priority, category: $category, assignedTo: $assignedTo, assignedAgent: $assignedAgent, resolvedAt: $resolvedAt, createdAt: $createdAt, updatedAt: $updatedAt, isDeleted: $isDeleted, messages: $messages, lastMessageAt: $lastMessageAt, lastMessageText: $lastMessageText, unreadCount: $unreadCount)';
}


}

/// @nodoc
abstract mixin class _$TicketModelCopyWith<$Res> implements $TicketModelCopyWith<$Res> {
  factory _$TicketModelCopyWith(_TicketModel value, $Res Function(_TicketModel) _then) = __$TicketModelCopyWithImpl;
@override @useResult
$Res call({
 int? id,@JsonKey(name: 'user_id') int? userId, UserModel? user, String? subject, String? description,@JsonKey(name: 'ticket_status') TicketStatus status,@JsonKey(name: 'priority') TicketPriority priority,@JsonKey(name: 'category') TicketCategory category,@JsonKey(name: 'assigned_to') int? assignedTo, UserModel? assignedAgent,@JsonKey(name: 'resolved_at') DateTime? resolvedAt,@JsonKey(name: 'created_at') DateTime? createdAt,@JsonKey(name: 'updated_at') DateTime? updatedAt,@JsonKey(name: 'is_deleted', defaultValue: false) bool isDeleted, List<TicketMessageModel> messages,@JsonKey(name: 'last_message_at') DateTime? lastMessageAt,@JsonKey(name: 'last_message_text') String? lastMessageText,@JsonKey(name: 'unread_count') int unreadCount
});


@override $UserModelCopyWith<$Res>? get user;@override $UserModelCopyWith<$Res>? get assignedAgent;

}
/// @nodoc
class __$TicketModelCopyWithImpl<$Res>
    implements _$TicketModelCopyWith<$Res> {
  __$TicketModelCopyWithImpl(this._self, this._then);

  final _TicketModel _self;
  final $Res Function(_TicketModel) _then;

/// Create a copy of TicketModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = freezed,Object? userId = freezed,Object? user = freezed,Object? subject = freezed,Object? description = freezed,Object? status = null,Object? priority = null,Object? category = null,Object? assignedTo = freezed,Object? assignedAgent = freezed,Object? resolvedAt = freezed,Object? createdAt = freezed,Object? updatedAt = freezed,Object? isDeleted = null,Object? messages = null,Object? lastMessageAt = freezed,Object? lastMessageText = freezed,Object? unreadCount = null,}) {
  return _then(_TicketModel(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,userId: freezed == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as int?,user: freezed == user ? _self.user : user // ignore: cast_nullable_to_non_nullable
as UserModel?,subject: freezed == subject ? _self.subject : subject // ignore: cast_nullable_to_non_nullable
as String?,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as TicketStatus,priority: null == priority ? _self.priority : priority // ignore: cast_nullable_to_non_nullable
as TicketPriority,category: null == category ? _self.category : category // ignore: cast_nullable_to_non_nullable
as TicketCategory,assignedTo: freezed == assignedTo ? _self.assignedTo : assignedTo // ignore: cast_nullable_to_non_nullable
as int?,assignedAgent: freezed == assignedAgent ? _self.assignedAgent : assignedAgent // ignore: cast_nullable_to_non_nullable
as UserModel?,resolvedAt: freezed == resolvedAt ? _self.resolvedAt : resolvedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,isDeleted: null == isDeleted ? _self.isDeleted : isDeleted // ignore: cast_nullable_to_non_nullable
as bool,messages: null == messages ? _self._messages : messages // ignore: cast_nullable_to_non_nullable
as List<TicketMessageModel>,lastMessageAt: freezed == lastMessageAt ? _self.lastMessageAt : lastMessageAt // ignore: cast_nullable_to_non_nullable
as DateTime?,lastMessageText: freezed == lastMessageText ? _self.lastMessageText : lastMessageText // ignore: cast_nullable_to_non_nullable
as String?,unreadCount: null == unreadCount ? _self.unreadCount : unreadCount // ignore: cast_nullable_to_non_nullable
as int,
  ));
}

/// Create a copy of TicketModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$UserModelCopyWith<$Res>? get user {
    if (_self.user == null) {
    return null;
  }

  return $UserModelCopyWith<$Res>(_self.user!, (value) {
    return _then(_self.copyWith(user: value));
  });
}/// Create a copy of TicketModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$UserModelCopyWith<$Res>? get assignedAgent {
    if (_self.assignedAgent == null) {
    return null;
  }

  return $UserModelCopyWith<$Res>(_self.assignedAgent!, (value) {
    return _then(_self.copyWith(assignedAgent: value));
  });
}
}

// dart format on
