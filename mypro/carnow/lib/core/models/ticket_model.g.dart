// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ticket_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_TicketModel _$TicketModelFromJson(Map<String, dynamic> json) => _TicketModel(
  id: (json['id'] as num?)?.toInt(),
  userId: (json['user_id'] as num?)?.toInt(),
  user: json['user'] == null
      ? null
      : UserModel.fromJson(json['user'] as Map<String, dynamic>),
  subject: json['subject'] as String?,
  description: json['description'] as String?,
  status:
      $enumDecodeNullable(_$TicketStatusEnumMap, json['ticket_status']) ??
      TicketStatus.pending,
  priority:
      $enumDecodeNullable(_$TicketPriorityEnumMap, json['priority']) ??
      TicketPriority.medium,
  category:
      $enumDecodeNullable(_$TicketCategoryEnumMap, json['category']) ??
      TicketCategory.general,
  assignedTo: (json['assigned_to'] as num?)?.toInt(),
  assignedAgent: json['assignedAgent'] == null
      ? null
      : UserModel.fromJson(json['assignedAgent'] as Map<String, dynamic>),
  resolvedAt: json['resolved_at'] == null
      ? null
      : DateTime.parse(json['resolved_at'] as String),
  createdAt: json['created_at'] == null
      ? null
      : DateTime.parse(json['created_at'] as String),
  updatedAt: json['updated_at'] == null
      ? null
      : DateTime.parse(json['updated_at'] as String),
  isDeleted: json['is_deleted'] as bool? ?? false,
  messages:
      (json['messages'] as List<dynamic>?)
          ?.map((e) => TicketMessageModel.fromJson(e as Map<String, dynamic>))
          .toList() ??
      const [],
  lastMessageAt: json['last_message_at'] == null
      ? null
      : DateTime.parse(json['last_message_at'] as String),
  lastMessageText: json['last_message_text'] as String?,
  unreadCount: (json['unread_count'] as num?)?.toInt() ?? 0,
);

Map<String, dynamic> _$TicketModelToJson(_TicketModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'user_id': instance.userId,
      'user': instance.user,
      'subject': instance.subject,
      'description': instance.description,
      'ticket_status': _$TicketStatusEnumMap[instance.status]!,
      'priority': _$TicketPriorityEnumMap[instance.priority]!,
      'category': _$TicketCategoryEnumMap[instance.category]!,
      'assigned_to': instance.assignedTo,
      'assignedAgent': instance.assignedAgent,
      'resolved_at': instance.resolvedAt?.toIso8601String(),
      'created_at': instance.createdAt?.toIso8601String(),
      'updated_at': instance.updatedAt?.toIso8601String(),
      'is_deleted': instance.isDeleted,
      'messages': instance.messages,
      'last_message_at': instance.lastMessageAt?.toIso8601String(),
      'last_message_text': instance.lastMessageText,
      'unread_count': instance.unreadCount,
    };

const _$TicketStatusEnumMap = {
  TicketStatus.pending: 'pending',
  TicketStatus.open: 'open',
  TicketStatus.resolved: 'resolved',
  TicketStatus.closed: 'closed',
};

const _$TicketPriorityEnumMap = {
  TicketPriority.low: 'low',
  TicketPriority.medium: 'medium',
  TicketPriority.high: 'high',
  TicketPriority.urgent: 'urgent',
};

const _$TicketCategoryEnumMap = {
  TicketCategory.general: 'general',
  TicketCategory.account: 'account',
  TicketCategory.orders: 'orders',
  TicketCategory.payments: 'payments',
  TicketCategory.technical: 'technical',
  TicketCategory.seller: 'seller',
  TicketCategory.returns: 'returns',
};
