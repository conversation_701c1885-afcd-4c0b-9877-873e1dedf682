import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:carnow/core/models/order_item_details_model.dart';
import 'package:carnow/core/models/order_status_history_model.dart';
import 'enums.dart';

part 'order_details_model.freezed.dart';
part 'order_details_model.g.dart';

@freezed
abstract class OrderDetailsModel with _$OrderDetailsModel {
  @JsonSerializable(explicitToJson: true, fieldRename: FieldRename.snake)
  const factory OrderDetailsModel({
    required String id,
    required String orderNumber,
    required OrderStatus status,
    required DateTime createdAt,
    required double totalAmount,
    required double subtotal,
    required double taxAmount,
    required double shippingCost,
    required String currency,
    required String buyerName,
    required String buyerEmail,
    required String buyerPhone,
    required String sellerName,
    required String sellerEmail,
    required String shippingAddress,
    required List<OrderItemDetailsModel> items,
    required List<OrderStatusHistoryModel> statusHistory,
    DateTime? updatedAt,
    String? trackingNumber,
    String? notes,
  }) = _OrderDetailsModel;

  factory OrderDetailsModel.fromJson(Map<String, dynamic> json) =>
      _$OrderDetailsModelFromJson(json);
}
