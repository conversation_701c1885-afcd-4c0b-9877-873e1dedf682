import 'package:json_annotation/json_annotation.dart';

/// حالة المنتج
@JsonEnum(valueField: 'value')
enum ProductCondition {
  /// جديد
  newCondition('new', 'جديد'),

  /// مستعمل - حالة ممتازة
  likeNew('like_new', 'حالة ممتازة'),

  /// مستعمل - حالة جيدة جداً
  veryGood('very_good', 'حالة جيدة جداً'),

  /// مستعمل - حالة جيدة
  good('good', 'حالة جيدة'),

  /// مستعمل - حالة مقبولة
  fair('fair', 'حالة مقبولة'),

  /// تحتاج إصلاح
  needsRepair('needs_repair', 'تحتاج إصلاح');

  const ProductCondition(this.value, this.displayName);

  final String value;
  final String displayName;

  @override
  String toString() => displayName;
}
