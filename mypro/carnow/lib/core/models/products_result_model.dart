import '../../features/products/models/product_model.dart';

class ProductsResult {
  const ProductsResult({
    required this.products,
    required this.totalCount,
    required this.currentPage,
    required this.totalPages,
    required this.hasMore,
  });

  factory ProductsResult.fromJson(Map<String, dynamic> json) {
    return ProductsResult(
      products: (json['products'] as List<dynamic>)
          .map((e) => ProductModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      totalCount: json['total_count'] as int,
      currentPage: json['current_page'] as int,
      totalPages: json['total_pages'] as int,
      hasMore: json['has_more'] as bool,
    );
  }

  final List<ProductModel> products;
  final int totalCount;
  final int currentPage;
  final int totalPages;
  final bool hasMore;

  Map<String, dynamic> toJson() {
    return {
      'products': products.map((e) => e.toJson()).toList(),
      'total_count': totalCount,
      'current_page': currentPage,
      'total_pages': totalPages,
      'has_more': hasMore,
    };
  }
}
