import 'package:freezed_annotation/freezed_annotation.dart';

part 'carnow_wallet.freezed.dart';
part 'carnow_wallet.g.dart';

@freezed
abstract class CarnowWallet with _$CarnowWallet {
  const factory CarnowWallet({
    required String id,
    @J<PERSON><PERSON><PERSON>(name: 'user_id') required String userId,
    @Default(0.0) double balance,
    @JsonKey(name: 'available_balance') @Default(0.0) double availableBalance,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'frozen_balance') @Default(0.0) double frozenBalance,
    @Default('LYD') String currency,
    @JsonKey(name: 'is_active') @Default(true) bool isActive,
    @J<PERSON><PERSON>ey(name: 'created_at') required DateTime createdAt,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'updated_at') required DateTime updatedAt,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'last_transaction_id') String? lastTransactionId,
    @JsonKey(name: 'last_transaction_date') DateTime? lastTransactionDate,
  }) = _CarnowWallet;

  factory CarnowWallet.fromJson(Map<String, dynamic> json) => _$CarnowWalletFromJson(json);
}

@freezed
abstract class CreateWalletRequest with _$CreateWalletRequest {
  const factory CreateWalletRequest({
    required String userId,
    @Default('LYD') String currency,
  }) = _CreateWalletRequest;

  factory CreateWalletRequest.fromJson(Map<String, dynamic> json) => 
      _$CreateWalletRequestFromJson(json);
}

@freezed
abstract class DepositRequest with _$DepositRequest {
  const factory DepositRequest({
    required double amount,
    required String description,
    String? reference,
    Map<String, dynamic>? metadata,
  }) = _DepositRequest;

  factory DepositRequest.fromJson(Map<String, dynamic> json) => 
      _$DepositRequestFromJson(json);
}

@freezed
abstract class WithdrawRequest with _$WithdrawRequest {
  const factory WithdrawRequest({
    required double amount,
    required String description,
    String? reference,
    Map<String, dynamic>? metadata,
  }) = _WithdrawRequest;

  factory WithdrawRequest.fromJson(Map<String, dynamic> json) => 
      _$WithdrawRequestFromJson(json);
} 