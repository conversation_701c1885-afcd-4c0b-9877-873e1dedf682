// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'subscription_error.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$SubscriptionError {

 String get message; String? get code;
/// Create a copy of SubscriptionError
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SubscriptionErrorCopyWith<SubscriptionError> get copyWith => _$SubscriptionErrorCopyWithImpl<SubscriptionError>(this as SubscriptionError, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SubscriptionError&&(identical(other.message, message) || other.message == message)&&(identical(other.code, code) || other.code == code));
}


@override
int get hashCode => Object.hash(runtimeType,message,code);

@override
String toString() {
  return 'SubscriptionError(message: $message, code: $code)';
}


}

/// @nodoc
abstract mixin class $SubscriptionErrorCopyWith<$Res>  {
  factory $SubscriptionErrorCopyWith(SubscriptionError value, $Res Function(SubscriptionError) _then) = _$SubscriptionErrorCopyWithImpl;
@useResult
$Res call({
 String message, String? code
});




}
/// @nodoc
class _$SubscriptionErrorCopyWithImpl<$Res>
    implements $SubscriptionErrorCopyWith<$Res> {
  _$SubscriptionErrorCopyWithImpl(this._self, this._then);

  final SubscriptionError _self;
  final $Res Function(SubscriptionError) _then;

/// Create a copy of SubscriptionError
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? message = null,Object? code = freezed,}) {
  return _then(_self.copyWith(
message: null == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String,code: freezed == code ? _self.code : code // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// Adds pattern-matching-related methods to [SubscriptionError].
extension SubscriptionErrorPatterns on SubscriptionError {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>({TResult Function( NetworkError value)?  networkError,TResult Function( DatabaseError value)?  databaseError,TResult Function( ValidationError value)?  validationError,TResult Function( NavigationError value)?  navigationError,TResult Function( AuthenticationError value)?  authenticationError,TResult Function( ServerError value)?  serverError,TResult Function( PaymentError value)?  paymentError,TResult Function( BusinessLogicError value)?  businessLogicError,TResult Function( UnknownError value)?  unknownError,required TResult orElse(),}){
final _that = this;
switch (_that) {
case NetworkError() when networkError != null:
return networkError(_that);case DatabaseError() when databaseError != null:
return databaseError(_that);case ValidationError() when validationError != null:
return validationError(_that);case NavigationError() when navigationError != null:
return navigationError(_that);case AuthenticationError() when authenticationError != null:
return authenticationError(_that);case ServerError() when serverError != null:
return serverError(_that);case PaymentError() when paymentError != null:
return paymentError(_that);case BusinessLogicError() when businessLogicError != null:
return businessLogicError(_that);case UnknownError() when unknownError != null:
return unknownError(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>({required TResult Function( NetworkError value)  networkError,required TResult Function( DatabaseError value)  databaseError,required TResult Function( ValidationError value)  validationError,required TResult Function( NavigationError value)  navigationError,required TResult Function( AuthenticationError value)  authenticationError,required TResult Function( ServerError value)  serverError,required TResult Function( PaymentError value)  paymentError,required TResult Function( BusinessLogicError value)  businessLogicError,required TResult Function( UnknownError value)  unknownError,}){
final _that = this;
switch (_that) {
case NetworkError():
return networkError(_that);case DatabaseError():
return databaseError(_that);case ValidationError():
return validationError(_that);case NavigationError():
return navigationError(_that);case AuthenticationError():
return authenticationError(_that);case ServerError():
return serverError(_that);case PaymentError():
return paymentError(_that);case BusinessLogicError():
return businessLogicError(_that);case UnknownError():
return unknownError(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>({TResult? Function( NetworkError value)?  networkError,TResult? Function( DatabaseError value)?  databaseError,TResult? Function( ValidationError value)?  validationError,TResult? Function( NavigationError value)?  navigationError,TResult? Function( AuthenticationError value)?  authenticationError,TResult? Function( ServerError value)?  serverError,TResult? Function( PaymentError value)?  paymentError,TResult? Function( BusinessLogicError value)?  businessLogicError,TResult? Function( UnknownError value)?  unknownError,}){
final _that = this;
switch (_that) {
case NetworkError() when networkError != null:
return networkError(_that);case DatabaseError() when databaseError != null:
return databaseError(_that);case ValidationError() when validationError != null:
return validationError(_that);case NavigationError() when navigationError != null:
return navigationError(_that);case AuthenticationError() when authenticationError != null:
return authenticationError(_that);case ServerError() when serverError != null:
return serverError(_that);case PaymentError() when paymentError != null:
return paymentError(_that);case BusinessLogicError() when businessLogicError != null:
return businessLogicError(_that);case UnknownError() when unknownError != null:
return unknownError(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>({TResult Function( String message,  String? code,  Map<String, dynamic>? details)?  networkError,TResult Function( String message,  String? code,  Map<String, dynamic>? details)?  databaseError,TResult Function( String message,  Map<String, String> fieldErrors,  String? code)?  validationError,TResult Function( String message,  String attemptedRoute,  String? code)?  navigationError,TResult Function( String message,  String? code,  Map<String, dynamic>? details)?  authenticationError,TResult Function( String message,  String? code,  int? statusCode,  Map<String, dynamic>? details)?  serverError,TResult Function( String message,  String? code,  String? paymentMethod,  Map<String, dynamic>? details)?  paymentError,TResult Function( String message,  String? code,  String? businessRule,  Map<String, dynamic>? details)?  businessLogicError,TResult Function( String message,  String? code,  Map<String, dynamic>? details)?  unknownError,required TResult orElse(),}) {final _that = this;
switch (_that) {
case NetworkError() when networkError != null:
return networkError(_that.message,_that.code,_that.details);case DatabaseError() when databaseError != null:
return databaseError(_that.message,_that.code,_that.details);case ValidationError() when validationError != null:
return validationError(_that.message,_that.fieldErrors,_that.code);case NavigationError() when navigationError != null:
return navigationError(_that.message,_that.attemptedRoute,_that.code);case AuthenticationError() when authenticationError != null:
return authenticationError(_that.message,_that.code,_that.details);case ServerError() when serverError != null:
return serverError(_that.message,_that.code,_that.statusCode,_that.details);case PaymentError() when paymentError != null:
return paymentError(_that.message,_that.code,_that.paymentMethod,_that.details);case BusinessLogicError() when businessLogicError != null:
return businessLogicError(_that.message,_that.code,_that.businessRule,_that.details);case UnknownError() when unknownError != null:
return unknownError(_that.message,_that.code,_that.details);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>({required TResult Function( String message,  String? code,  Map<String, dynamic>? details)  networkError,required TResult Function( String message,  String? code,  Map<String, dynamic>? details)  databaseError,required TResult Function( String message,  Map<String, String> fieldErrors,  String? code)  validationError,required TResult Function( String message,  String attemptedRoute,  String? code)  navigationError,required TResult Function( String message,  String? code,  Map<String, dynamic>? details)  authenticationError,required TResult Function( String message,  String? code,  int? statusCode,  Map<String, dynamic>? details)  serverError,required TResult Function( String message,  String? code,  String? paymentMethod,  Map<String, dynamic>? details)  paymentError,required TResult Function( String message,  String? code,  String? businessRule,  Map<String, dynamic>? details)  businessLogicError,required TResult Function( String message,  String? code,  Map<String, dynamic>? details)  unknownError,}) {final _that = this;
switch (_that) {
case NetworkError():
return networkError(_that.message,_that.code,_that.details);case DatabaseError():
return databaseError(_that.message,_that.code,_that.details);case ValidationError():
return validationError(_that.message,_that.fieldErrors,_that.code);case NavigationError():
return navigationError(_that.message,_that.attemptedRoute,_that.code);case AuthenticationError():
return authenticationError(_that.message,_that.code,_that.details);case ServerError():
return serverError(_that.message,_that.code,_that.statusCode,_that.details);case PaymentError():
return paymentError(_that.message,_that.code,_that.paymentMethod,_that.details);case BusinessLogicError():
return businessLogicError(_that.message,_that.code,_that.businessRule,_that.details);case UnknownError():
return unknownError(_that.message,_that.code,_that.details);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>({TResult? Function( String message,  String? code,  Map<String, dynamic>? details)?  networkError,TResult? Function( String message,  String? code,  Map<String, dynamic>? details)?  databaseError,TResult? Function( String message,  Map<String, String> fieldErrors,  String? code)?  validationError,TResult? Function( String message,  String attemptedRoute,  String? code)?  navigationError,TResult? Function( String message,  String? code,  Map<String, dynamic>? details)?  authenticationError,TResult? Function( String message,  String? code,  int? statusCode,  Map<String, dynamic>? details)?  serverError,TResult? Function( String message,  String? code,  String? paymentMethod,  Map<String, dynamic>? details)?  paymentError,TResult? Function( String message,  String? code,  String? businessRule,  Map<String, dynamic>? details)?  businessLogicError,TResult? Function( String message,  String? code,  Map<String, dynamic>? details)?  unknownError,}) {final _that = this;
switch (_that) {
case NetworkError() when networkError != null:
return networkError(_that.message,_that.code,_that.details);case DatabaseError() when databaseError != null:
return databaseError(_that.message,_that.code,_that.details);case ValidationError() when validationError != null:
return validationError(_that.message,_that.fieldErrors,_that.code);case NavigationError() when navigationError != null:
return navigationError(_that.message,_that.attemptedRoute,_that.code);case AuthenticationError() when authenticationError != null:
return authenticationError(_that.message,_that.code,_that.details);case ServerError() when serverError != null:
return serverError(_that.message,_that.code,_that.statusCode,_that.details);case PaymentError() when paymentError != null:
return paymentError(_that.message,_that.code,_that.paymentMethod,_that.details);case BusinessLogicError() when businessLogicError != null:
return businessLogicError(_that.message,_that.code,_that.businessRule,_that.details);case UnknownError() when unknownError != null:
return unknownError(_that.message,_that.code,_that.details);case _:
  return null;

}
}

}

/// @nodoc


class NetworkError implements SubscriptionError {
  const NetworkError({required this.message, this.code, final  Map<String, dynamic>? details}): _details = details;
  

@override final  String message;
@override final  String? code;
 final  Map<String, dynamic>? _details;
 Map<String, dynamic>? get details {
  final value = _details;
  if (value == null) return null;
  if (_details is EqualUnmodifiableMapView) return _details;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}


/// Create a copy of SubscriptionError
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$NetworkErrorCopyWith<NetworkError> get copyWith => _$NetworkErrorCopyWithImpl<NetworkError>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is NetworkError&&(identical(other.message, message) || other.message == message)&&(identical(other.code, code) || other.code == code)&&const DeepCollectionEquality().equals(other._details, _details));
}


@override
int get hashCode => Object.hash(runtimeType,message,code,const DeepCollectionEquality().hash(_details));

@override
String toString() {
  return 'SubscriptionError.networkError(message: $message, code: $code, details: $details)';
}


}

/// @nodoc
abstract mixin class $NetworkErrorCopyWith<$Res> implements $SubscriptionErrorCopyWith<$Res> {
  factory $NetworkErrorCopyWith(NetworkError value, $Res Function(NetworkError) _then) = _$NetworkErrorCopyWithImpl;
@override @useResult
$Res call({
 String message, String? code, Map<String, dynamic>? details
});




}
/// @nodoc
class _$NetworkErrorCopyWithImpl<$Res>
    implements $NetworkErrorCopyWith<$Res> {
  _$NetworkErrorCopyWithImpl(this._self, this._then);

  final NetworkError _self;
  final $Res Function(NetworkError) _then;

/// Create a copy of SubscriptionError
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? message = null,Object? code = freezed,Object? details = freezed,}) {
  return _then(NetworkError(
message: null == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String,code: freezed == code ? _self.code : code // ignore: cast_nullable_to_non_nullable
as String?,details: freezed == details ? _self._details : details // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}


}

/// @nodoc


class DatabaseError implements SubscriptionError {
  const DatabaseError({required this.message, this.code, final  Map<String, dynamic>? details}): _details = details;
  

@override final  String message;
@override final  String? code;
 final  Map<String, dynamic>? _details;
 Map<String, dynamic>? get details {
  final value = _details;
  if (value == null) return null;
  if (_details is EqualUnmodifiableMapView) return _details;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}


/// Create a copy of SubscriptionError
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$DatabaseErrorCopyWith<DatabaseError> get copyWith => _$DatabaseErrorCopyWithImpl<DatabaseError>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is DatabaseError&&(identical(other.message, message) || other.message == message)&&(identical(other.code, code) || other.code == code)&&const DeepCollectionEquality().equals(other._details, _details));
}


@override
int get hashCode => Object.hash(runtimeType,message,code,const DeepCollectionEquality().hash(_details));

@override
String toString() {
  return 'SubscriptionError.databaseError(message: $message, code: $code, details: $details)';
}


}

/// @nodoc
abstract mixin class $DatabaseErrorCopyWith<$Res> implements $SubscriptionErrorCopyWith<$Res> {
  factory $DatabaseErrorCopyWith(DatabaseError value, $Res Function(DatabaseError) _then) = _$DatabaseErrorCopyWithImpl;
@override @useResult
$Res call({
 String message, String? code, Map<String, dynamic>? details
});




}
/// @nodoc
class _$DatabaseErrorCopyWithImpl<$Res>
    implements $DatabaseErrorCopyWith<$Res> {
  _$DatabaseErrorCopyWithImpl(this._self, this._then);

  final DatabaseError _self;
  final $Res Function(DatabaseError) _then;

/// Create a copy of SubscriptionError
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? message = null,Object? code = freezed,Object? details = freezed,}) {
  return _then(DatabaseError(
message: null == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String,code: freezed == code ? _self.code : code // ignore: cast_nullable_to_non_nullable
as String?,details: freezed == details ? _self._details : details // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}


}

/// @nodoc


class ValidationError implements SubscriptionError {
  const ValidationError({required this.message, required final  Map<String, String> fieldErrors, this.code}): _fieldErrors = fieldErrors;
  

@override final  String message;
 final  Map<String, String> _fieldErrors;
 Map<String, String> get fieldErrors {
  if (_fieldErrors is EqualUnmodifiableMapView) return _fieldErrors;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_fieldErrors);
}

@override final  String? code;

/// Create a copy of SubscriptionError
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ValidationErrorCopyWith<ValidationError> get copyWith => _$ValidationErrorCopyWithImpl<ValidationError>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ValidationError&&(identical(other.message, message) || other.message == message)&&const DeepCollectionEquality().equals(other._fieldErrors, _fieldErrors)&&(identical(other.code, code) || other.code == code));
}


@override
int get hashCode => Object.hash(runtimeType,message,const DeepCollectionEquality().hash(_fieldErrors),code);

@override
String toString() {
  return 'SubscriptionError.validationError(message: $message, fieldErrors: $fieldErrors, code: $code)';
}


}

/// @nodoc
abstract mixin class $ValidationErrorCopyWith<$Res> implements $SubscriptionErrorCopyWith<$Res> {
  factory $ValidationErrorCopyWith(ValidationError value, $Res Function(ValidationError) _then) = _$ValidationErrorCopyWithImpl;
@override @useResult
$Res call({
 String message, Map<String, String> fieldErrors, String? code
});




}
/// @nodoc
class _$ValidationErrorCopyWithImpl<$Res>
    implements $ValidationErrorCopyWith<$Res> {
  _$ValidationErrorCopyWithImpl(this._self, this._then);

  final ValidationError _self;
  final $Res Function(ValidationError) _then;

/// Create a copy of SubscriptionError
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? message = null,Object? fieldErrors = null,Object? code = freezed,}) {
  return _then(ValidationError(
message: null == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String,fieldErrors: null == fieldErrors ? _self._fieldErrors : fieldErrors // ignore: cast_nullable_to_non_nullable
as Map<String, String>,code: freezed == code ? _self.code : code // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

/// @nodoc


class NavigationError implements SubscriptionError {
  const NavigationError({required this.message, required this.attemptedRoute, this.code});
  

@override final  String message;
 final  String attemptedRoute;
@override final  String? code;

/// Create a copy of SubscriptionError
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$NavigationErrorCopyWith<NavigationError> get copyWith => _$NavigationErrorCopyWithImpl<NavigationError>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is NavigationError&&(identical(other.message, message) || other.message == message)&&(identical(other.attemptedRoute, attemptedRoute) || other.attemptedRoute == attemptedRoute)&&(identical(other.code, code) || other.code == code));
}


@override
int get hashCode => Object.hash(runtimeType,message,attemptedRoute,code);

@override
String toString() {
  return 'SubscriptionError.navigationError(message: $message, attemptedRoute: $attemptedRoute, code: $code)';
}


}

/// @nodoc
abstract mixin class $NavigationErrorCopyWith<$Res> implements $SubscriptionErrorCopyWith<$Res> {
  factory $NavigationErrorCopyWith(NavigationError value, $Res Function(NavigationError) _then) = _$NavigationErrorCopyWithImpl;
@override @useResult
$Res call({
 String message, String attemptedRoute, String? code
});




}
/// @nodoc
class _$NavigationErrorCopyWithImpl<$Res>
    implements $NavigationErrorCopyWith<$Res> {
  _$NavigationErrorCopyWithImpl(this._self, this._then);

  final NavigationError _self;
  final $Res Function(NavigationError) _then;

/// Create a copy of SubscriptionError
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? message = null,Object? attemptedRoute = null,Object? code = freezed,}) {
  return _then(NavigationError(
message: null == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String,attemptedRoute: null == attemptedRoute ? _self.attemptedRoute : attemptedRoute // ignore: cast_nullable_to_non_nullable
as String,code: freezed == code ? _self.code : code // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

/// @nodoc


class AuthenticationError implements SubscriptionError {
  const AuthenticationError({required this.message, this.code, final  Map<String, dynamic>? details}): _details = details;
  

@override final  String message;
@override final  String? code;
 final  Map<String, dynamic>? _details;
 Map<String, dynamic>? get details {
  final value = _details;
  if (value == null) return null;
  if (_details is EqualUnmodifiableMapView) return _details;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}


/// Create a copy of SubscriptionError
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AuthenticationErrorCopyWith<AuthenticationError> get copyWith => _$AuthenticationErrorCopyWithImpl<AuthenticationError>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AuthenticationError&&(identical(other.message, message) || other.message == message)&&(identical(other.code, code) || other.code == code)&&const DeepCollectionEquality().equals(other._details, _details));
}


@override
int get hashCode => Object.hash(runtimeType,message,code,const DeepCollectionEquality().hash(_details));

@override
String toString() {
  return 'SubscriptionError.authenticationError(message: $message, code: $code, details: $details)';
}


}

/// @nodoc
abstract mixin class $AuthenticationErrorCopyWith<$Res> implements $SubscriptionErrorCopyWith<$Res> {
  factory $AuthenticationErrorCopyWith(AuthenticationError value, $Res Function(AuthenticationError) _then) = _$AuthenticationErrorCopyWithImpl;
@override @useResult
$Res call({
 String message, String? code, Map<String, dynamic>? details
});




}
/// @nodoc
class _$AuthenticationErrorCopyWithImpl<$Res>
    implements $AuthenticationErrorCopyWith<$Res> {
  _$AuthenticationErrorCopyWithImpl(this._self, this._then);

  final AuthenticationError _self;
  final $Res Function(AuthenticationError) _then;

/// Create a copy of SubscriptionError
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? message = null,Object? code = freezed,Object? details = freezed,}) {
  return _then(AuthenticationError(
message: null == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String,code: freezed == code ? _self.code : code // ignore: cast_nullable_to_non_nullable
as String?,details: freezed == details ? _self._details : details // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}


}

/// @nodoc


class ServerError implements SubscriptionError {
  const ServerError({required this.message, this.code, this.statusCode, final  Map<String, dynamic>? details}): _details = details;
  

@override final  String message;
@override final  String? code;
 final  int? statusCode;
 final  Map<String, dynamic>? _details;
 Map<String, dynamic>? get details {
  final value = _details;
  if (value == null) return null;
  if (_details is EqualUnmodifiableMapView) return _details;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}


/// Create a copy of SubscriptionError
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ServerErrorCopyWith<ServerError> get copyWith => _$ServerErrorCopyWithImpl<ServerError>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ServerError&&(identical(other.message, message) || other.message == message)&&(identical(other.code, code) || other.code == code)&&(identical(other.statusCode, statusCode) || other.statusCode == statusCode)&&const DeepCollectionEquality().equals(other._details, _details));
}


@override
int get hashCode => Object.hash(runtimeType,message,code,statusCode,const DeepCollectionEquality().hash(_details));

@override
String toString() {
  return 'SubscriptionError.serverError(message: $message, code: $code, statusCode: $statusCode, details: $details)';
}


}

/// @nodoc
abstract mixin class $ServerErrorCopyWith<$Res> implements $SubscriptionErrorCopyWith<$Res> {
  factory $ServerErrorCopyWith(ServerError value, $Res Function(ServerError) _then) = _$ServerErrorCopyWithImpl;
@override @useResult
$Res call({
 String message, String? code, int? statusCode, Map<String, dynamic>? details
});




}
/// @nodoc
class _$ServerErrorCopyWithImpl<$Res>
    implements $ServerErrorCopyWith<$Res> {
  _$ServerErrorCopyWithImpl(this._self, this._then);

  final ServerError _self;
  final $Res Function(ServerError) _then;

/// Create a copy of SubscriptionError
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? message = null,Object? code = freezed,Object? statusCode = freezed,Object? details = freezed,}) {
  return _then(ServerError(
message: null == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String,code: freezed == code ? _self.code : code // ignore: cast_nullable_to_non_nullable
as String?,statusCode: freezed == statusCode ? _self.statusCode : statusCode // ignore: cast_nullable_to_non_nullable
as int?,details: freezed == details ? _self._details : details // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}


}

/// @nodoc


class PaymentError implements SubscriptionError {
  const PaymentError({required this.message, this.code, this.paymentMethod, final  Map<String, dynamic>? details}): _details = details;
  

@override final  String message;
@override final  String? code;
 final  String? paymentMethod;
 final  Map<String, dynamic>? _details;
 Map<String, dynamic>? get details {
  final value = _details;
  if (value == null) return null;
  if (_details is EqualUnmodifiableMapView) return _details;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}


/// Create a copy of SubscriptionError
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PaymentErrorCopyWith<PaymentError> get copyWith => _$PaymentErrorCopyWithImpl<PaymentError>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PaymentError&&(identical(other.message, message) || other.message == message)&&(identical(other.code, code) || other.code == code)&&(identical(other.paymentMethod, paymentMethod) || other.paymentMethod == paymentMethod)&&const DeepCollectionEquality().equals(other._details, _details));
}


@override
int get hashCode => Object.hash(runtimeType,message,code,paymentMethod,const DeepCollectionEquality().hash(_details));

@override
String toString() {
  return 'SubscriptionError.paymentError(message: $message, code: $code, paymentMethod: $paymentMethod, details: $details)';
}


}

/// @nodoc
abstract mixin class $PaymentErrorCopyWith<$Res> implements $SubscriptionErrorCopyWith<$Res> {
  factory $PaymentErrorCopyWith(PaymentError value, $Res Function(PaymentError) _then) = _$PaymentErrorCopyWithImpl;
@override @useResult
$Res call({
 String message, String? code, String? paymentMethod, Map<String, dynamic>? details
});




}
/// @nodoc
class _$PaymentErrorCopyWithImpl<$Res>
    implements $PaymentErrorCopyWith<$Res> {
  _$PaymentErrorCopyWithImpl(this._self, this._then);

  final PaymentError _self;
  final $Res Function(PaymentError) _then;

/// Create a copy of SubscriptionError
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? message = null,Object? code = freezed,Object? paymentMethod = freezed,Object? details = freezed,}) {
  return _then(PaymentError(
message: null == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String,code: freezed == code ? _self.code : code // ignore: cast_nullable_to_non_nullable
as String?,paymentMethod: freezed == paymentMethod ? _self.paymentMethod : paymentMethod // ignore: cast_nullable_to_non_nullable
as String?,details: freezed == details ? _self._details : details // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}


}

/// @nodoc


class BusinessLogicError implements SubscriptionError {
  const BusinessLogicError({required this.message, this.code, this.businessRule, final  Map<String, dynamic>? details}): _details = details;
  

@override final  String message;
@override final  String? code;
 final  String? businessRule;
 final  Map<String, dynamic>? _details;
 Map<String, dynamic>? get details {
  final value = _details;
  if (value == null) return null;
  if (_details is EqualUnmodifiableMapView) return _details;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}


/// Create a copy of SubscriptionError
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$BusinessLogicErrorCopyWith<BusinessLogicError> get copyWith => _$BusinessLogicErrorCopyWithImpl<BusinessLogicError>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is BusinessLogicError&&(identical(other.message, message) || other.message == message)&&(identical(other.code, code) || other.code == code)&&(identical(other.businessRule, businessRule) || other.businessRule == businessRule)&&const DeepCollectionEquality().equals(other._details, _details));
}


@override
int get hashCode => Object.hash(runtimeType,message,code,businessRule,const DeepCollectionEquality().hash(_details));

@override
String toString() {
  return 'SubscriptionError.businessLogicError(message: $message, code: $code, businessRule: $businessRule, details: $details)';
}


}

/// @nodoc
abstract mixin class $BusinessLogicErrorCopyWith<$Res> implements $SubscriptionErrorCopyWith<$Res> {
  factory $BusinessLogicErrorCopyWith(BusinessLogicError value, $Res Function(BusinessLogicError) _then) = _$BusinessLogicErrorCopyWithImpl;
@override @useResult
$Res call({
 String message, String? code, String? businessRule, Map<String, dynamic>? details
});




}
/// @nodoc
class _$BusinessLogicErrorCopyWithImpl<$Res>
    implements $BusinessLogicErrorCopyWith<$Res> {
  _$BusinessLogicErrorCopyWithImpl(this._self, this._then);

  final BusinessLogicError _self;
  final $Res Function(BusinessLogicError) _then;

/// Create a copy of SubscriptionError
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? message = null,Object? code = freezed,Object? businessRule = freezed,Object? details = freezed,}) {
  return _then(BusinessLogicError(
message: null == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String,code: freezed == code ? _self.code : code // ignore: cast_nullable_to_non_nullable
as String?,businessRule: freezed == businessRule ? _self.businessRule : businessRule // ignore: cast_nullable_to_non_nullable
as String?,details: freezed == details ? _self._details : details // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}


}

/// @nodoc


class UnknownError implements SubscriptionError {
  const UnknownError({required this.message, this.code, final  Map<String, dynamic>? details}): _details = details;
  

@override final  String message;
@override final  String? code;
 final  Map<String, dynamic>? _details;
 Map<String, dynamic>? get details {
  final value = _details;
  if (value == null) return null;
  if (_details is EqualUnmodifiableMapView) return _details;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}


/// Create a copy of SubscriptionError
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$UnknownErrorCopyWith<UnknownError> get copyWith => _$UnknownErrorCopyWithImpl<UnknownError>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is UnknownError&&(identical(other.message, message) || other.message == message)&&(identical(other.code, code) || other.code == code)&&const DeepCollectionEquality().equals(other._details, _details));
}


@override
int get hashCode => Object.hash(runtimeType,message,code,const DeepCollectionEquality().hash(_details));

@override
String toString() {
  return 'SubscriptionError.unknownError(message: $message, code: $code, details: $details)';
}


}

/// @nodoc
abstract mixin class $UnknownErrorCopyWith<$Res> implements $SubscriptionErrorCopyWith<$Res> {
  factory $UnknownErrorCopyWith(UnknownError value, $Res Function(UnknownError) _then) = _$UnknownErrorCopyWithImpl;
@override @useResult
$Res call({
 String message, String? code, Map<String, dynamic>? details
});




}
/// @nodoc
class _$UnknownErrorCopyWithImpl<$Res>
    implements $UnknownErrorCopyWith<$Res> {
  _$UnknownErrorCopyWithImpl(this._self, this._then);

  final UnknownError _self;
  final $Res Function(UnknownError) _then;

/// Create a copy of SubscriptionError
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? message = null,Object? code = freezed,Object? details = freezed,}) {
  return _then(UnknownError(
message: null == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String,code: freezed == code ? _self.code : code // ignore: cast_nullable_to_non_nullable
as String?,details: freezed == details ? _self._details : details // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}


}

// dart format on
