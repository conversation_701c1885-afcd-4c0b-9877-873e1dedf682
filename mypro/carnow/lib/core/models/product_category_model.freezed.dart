// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'product_category_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ProductCategoryModel {

 String get id; String get name; String? get nameAr; String? get nameEn; String? get nameIt; String? get description; String? get descriptionAr; String? get descriptionEn; String? get descriptionIt; String? get parentId; ProductType get productType; String? get iconUrl; String? get imageUrl; String? get colorCode; bool get isActive; int get sortOrder; int get level; List<String> get requiredFields; List<String> get optionalFields; Map<String, dynamic>? get formTemplate; List<ProductCategoryModel> get subcategories; DateTime? get createdAt; DateTime? get updatedAt;// الحقول الجديدة للنظام المحدث
 VehicleCategory? get vehicleCategory; AutoPartsCategory? get autoPartsCategory; AutomotiveElectronicsCategory? get electronicsCategory; ToolsCategory? get toolsCategory; AccessoriesCategory? get accessoriesCategory;
/// Create a copy of ProductCategoryModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ProductCategoryModelCopyWith<ProductCategoryModel> get copyWith => _$ProductCategoryModelCopyWithImpl<ProductCategoryModel>(this as ProductCategoryModel, _$identity);

  /// Serializes this ProductCategoryModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ProductCategoryModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.nameAr, nameAr) || other.nameAr == nameAr)&&(identical(other.nameEn, nameEn) || other.nameEn == nameEn)&&(identical(other.nameIt, nameIt) || other.nameIt == nameIt)&&(identical(other.description, description) || other.description == description)&&(identical(other.descriptionAr, descriptionAr) || other.descriptionAr == descriptionAr)&&(identical(other.descriptionEn, descriptionEn) || other.descriptionEn == descriptionEn)&&(identical(other.descriptionIt, descriptionIt) || other.descriptionIt == descriptionIt)&&(identical(other.parentId, parentId) || other.parentId == parentId)&&(identical(other.productType, productType) || other.productType == productType)&&(identical(other.iconUrl, iconUrl) || other.iconUrl == iconUrl)&&(identical(other.imageUrl, imageUrl) || other.imageUrl == imageUrl)&&(identical(other.colorCode, colorCode) || other.colorCode == colorCode)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&(identical(other.sortOrder, sortOrder) || other.sortOrder == sortOrder)&&(identical(other.level, level) || other.level == level)&&const DeepCollectionEquality().equals(other.requiredFields, requiredFields)&&const DeepCollectionEquality().equals(other.optionalFields, optionalFields)&&const DeepCollectionEquality().equals(other.formTemplate, formTemplate)&&const DeepCollectionEquality().equals(other.subcategories, subcategories)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.vehicleCategory, vehicleCategory) || other.vehicleCategory == vehicleCategory)&&(identical(other.autoPartsCategory, autoPartsCategory) || other.autoPartsCategory == autoPartsCategory)&&(identical(other.electronicsCategory, electronicsCategory) || other.electronicsCategory == electronicsCategory)&&(identical(other.toolsCategory, toolsCategory) || other.toolsCategory == toolsCategory)&&(identical(other.accessoriesCategory, accessoriesCategory) || other.accessoriesCategory == accessoriesCategory));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,name,nameAr,nameEn,nameIt,description,descriptionAr,descriptionEn,descriptionIt,parentId,productType,iconUrl,imageUrl,colorCode,isActive,sortOrder,level,const DeepCollectionEquality().hash(requiredFields),const DeepCollectionEquality().hash(optionalFields),const DeepCollectionEquality().hash(formTemplate),const DeepCollectionEquality().hash(subcategories),createdAt,updatedAt,vehicleCategory,autoPartsCategory,electronicsCategory,toolsCategory,accessoriesCategory]);

@override
String toString() {
  return 'ProductCategoryModel(id: $id, name: $name, nameAr: $nameAr, nameEn: $nameEn, nameIt: $nameIt, description: $description, descriptionAr: $descriptionAr, descriptionEn: $descriptionEn, descriptionIt: $descriptionIt, parentId: $parentId, productType: $productType, iconUrl: $iconUrl, imageUrl: $imageUrl, colorCode: $colorCode, isActive: $isActive, sortOrder: $sortOrder, level: $level, requiredFields: $requiredFields, optionalFields: $optionalFields, formTemplate: $formTemplate, subcategories: $subcategories, createdAt: $createdAt, updatedAt: $updatedAt, vehicleCategory: $vehicleCategory, autoPartsCategory: $autoPartsCategory, electronicsCategory: $electronicsCategory, toolsCategory: $toolsCategory, accessoriesCategory: $accessoriesCategory)';
}


}

/// @nodoc
abstract mixin class $ProductCategoryModelCopyWith<$Res>  {
  factory $ProductCategoryModelCopyWith(ProductCategoryModel value, $Res Function(ProductCategoryModel) _then) = _$ProductCategoryModelCopyWithImpl;
@useResult
$Res call({
 String id, String name, String? nameAr, String? nameEn, String? nameIt, String? description, String? descriptionAr, String? descriptionEn, String? descriptionIt, String? parentId, ProductType productType, String? iconUrl, String? imageUrl, String? colorCode, bool isActive, int sortOrder, int level, List<String> requiredFields, List<String> optionalFields, Map<String, dynamic>? formTemplate, List<ProductCategoryModel> subcategories, DateTime? createdAt, DateTime? updatedAt, VehicleCategory? vehicleCategory, AutoPartsCategory? autoPartsCategory, AutomotiveElectronicsCategory? electronicsCategory, ToolsCategory? toolsCategory, AccessoriesCategory? accessoriesCategory
});




}
/// @nodoc
class _$ProductCategoryModelCopyWithImpl<$Res>
    implements $ProductCategoryModelCopyWith<$Res> {
  _$ProductCategoryModelCopyWithImpl(this._self, this._then);

  final ProductCategoryModel _self;
  final $Res Function(ProductCategoryModel) _then;

/// Create a copy of ProductCategoryModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? name = null,Object? nameAr = freezed,Object? nameEn = freezed,Object? nameIt = freezed,Object? description = freezed,Object? descriptionAr = freezed,Object? descriptionEn = freezed,Object? descriptionIt = freezed,Object? parentId = freezed,Object? productType = null,Object? iconUrl = freezed,Object? imageUrl = freezed,Object? colorCode = freezed,Object? isActive = null,Object? sortOrder = null,Object? level = null,Object? requiredFields = null,Object? optionalFields = null,Object? formTemplate = freezed,Object? subcategories = null,Object? createdAt = freezed,Object? updatedAt = freezed,Object? vehicleCategory = freezed,Object? autoPartsCategory = freezed,Object? electronicsCategory = freezed,Object? toolsCategory = freezed,Object? accessoriesCategory = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,nameAr: freezed == nameAr ? _self.nameAr : nameAr // ignore: cast_nullable_to_non_nullable
as String?,nameEn: freezed == nameEn ? _self.nameEn : nameEn // ignore: cast_nullable_to_non_nullable
as String?,nameIt: freezed == nameIt ? _self.nameIt : nameIt // ignore: cast_nullable_to_non_nullable
as String?,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,descriptionAr: freezed == descriptionAr ? _self.descriptionAr : descriptionAr // ignore: cast_nullable_to_non_nullable
as String?,descriptionEn: freezed == descriptionEn ? _self.descriptionEn : descriptionEn // ignore: cast_nullable_to_non_nullable
as String?,descriptionIt: freezed == descriptionIt ? _self.descriptionIt : descriptionIt // ignore: cast_nullable_to_non_nullable
as String?,parentId: freezed == parentId ? _self.parentId : parentId // ignore: cast_nullable_to_non_nullable
as String?,productType: null == productType ? _self.productType : productType // ignore: cast_nullable_to_non_nullable
as ProductType,iconUrl: freezed == iconUrl ? _self.iconUrl : iconUrl // ignore: cast_nullable_to_non_nullable
as String?,imageUrl: freezed == imageUrl ? _self.imageUrl : imageUrl // ignore: cast_nullable_to_non_nullable
as String?,colorCode: freezed == colorCode ? _self.colorCode : colorCode // ignore: cast_nullable_to_non_nullable
as String?,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,sortOrder: null == sortOrder ? _self.sortOrder : sortOrder // ignore: cast_nullable_to_non_nullable
as int,level: null == level ? _self.level : level // ignore: cast_nullable_to_non_nullable
as int,requiredFields: null == requiredFields ? _self.requiredFields : requiredFields // ignore: cast_nullable_to_non_nullable
as List<String>,optionalFields: null == optionalFields ? _self.optionalFields : optionalFields // ignore: cast_nullable_to_non_nullable
as List<String>,formTemplate: freezed == formTemplate ? _self.formTemplate : formTemplate // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,subcategories: null == subcategories ? _self.subcategories : subcategories // ignore: cast_nullable_to_non_nullable
as List<ProductCategoryModel>,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,vehicleCategory: freezed == vehicleCategory ? _self.vehicleCategory : vehicleCategory // ignore: cast_nullable_to_non_nullable
as VehicleCategory?,autoPartsCategory: freezed == autoPartsCategory ? _self.autoPartsCategory : autoPartsCategory // ignore: cast_nullable_to_non_nullable
as AutoPartsCategory?,electronicsCategory: freezed == electronicsCategory ? _self.electronicsCategory : electronicsCategory // ignore: cast_nullable_to_non_nullable
as AutomotiveElectronicsCategory?,toolsCategory: freezed == toolsCategory ? _self.toolsCategory : toolsCategory // ignore: cast_nullable_to_non_nullable
as ToolsCategory?,accessoriesCategory: freezed == accessoriesCategory ? _self.accessoriesCategory : accessoriesCategory // ignore: cast_nullable_to_non_nullable
as AccessoriesCategory?,
  ));
}

}


/// Adds pattern-matching-related methods to [ProductCategoryModel].
extension ProductCategoryModelPatterns on ProductCategoryModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ProductCategoryModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ProductCategoryModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ProductCategoryModel value)  $default,){
final _that = this;
switch (_that) {
case _ProductCategoryModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ProductCategoryModel value)?  $default,){
final _that = this;
switch (_that) {
case _ProductCategoryModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String name,  String? nameAr,  String? nameEn,  String? nameIt,  String? description,  String? descriptionAr,  String? descriptionEn,  String? descriptionIt,  String? parentId,  ProductType productType,  String? iconUrl,  String? imageUrl,  String? colorCode,  bool isActive,  int sortOrder,  int level,  List<String> requiredFields,  List<String> optionalFields,  Map<String, dynamic>? formTemplate,  List<ProductCategoryModel> subcategories,  DateTime? createdAt,  DateTime? updatedAt,  VehicleCategory? vehicleCategory,  AutoPartsCategory? autoPartsCategory,  AutomotiveElectronicsCategory? electronicsCategory,  ToolsCategory? toolsCategory,  AccessoriesCategory? accessoriesCategory)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ProductCategoryModel() when $default != null:
return $default(_that.id,_that.name,_that.nameAr,_that.nameEn,_that.nameIt,_that.description,_that.descriptionAr,_that.descriptionEn,_that.descriptionIt,_that.parentId,_that.productType,_that.iconUrl,_that.imageUrl,_that.colorCode,_that.isActive,_that.sortOrder,_that.level,_that.requiredFields,_that.optionalFields,_that.formTemplate,_that.subcategories,_that.createdAt,_that.updatedAt,_that.vehicleCategory,_that.autoPartsCategory,_that.electronicsCategory,_that.toolsCategory,_that.accessoriesCategory);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String name,  String? nameAr,  String? nameEn,  String? nameIt,  String? description,  String? descriptionAr,  String? descriptionEn,  String? descriptionIt,  String? parentId,  ProductType productType,  String? iconUrl,  String? imageUrl,  String? colorCode,  bool isActive,  int sortOrder,  int level,  List<String> requiredFields,  List<String> optionalFields,  Map<String, dynamic>? formTemplate,  List<ProductCategoryModel> subcategories,  DateTime? createdAt,  DateTime? updatedAt,  VehicleCategory? vehicleCategory,  AutoPartsCategory? autoPartsCategory,  AutomotiveElectronicsCategory? electronicsCategory,  ToolsCategory? toolsCategory,  AccessoriesCategory? accessoriesCategory)  $default,) {final _that = this;
switch (_that) {
case _ProductCategoryModel():
return $default(_that.id,_that.name,_that.nameAr,_that.nameEn,_that.nameIt,_that.description,_that.descriptionAr,_that.descriptionEn,_that.descriptionIt,_that.parentId,_that.productType,_that.iconUrl,_that.imageUrl,_that.colorCode,_that.isActive,_that.sortOrder,_that.level,_that.requiredFields,_that.optionalFields,_that.formTemplate,_that.subcategories,_that.createdAt,_that.updatedAt,_that.vehicleCategory,_that.autoPartsCategory,_that.electronicsCategory,_that.toolsCategory,_that.accessoriesCategory);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String name,  String? nameAr,  String? nameEn,  String? nameIt,  String? description,  String? descriptionAr,  String? descriptionEn,  String? descriptionIt,  String? parentId,  ProductType productType,  String? iconUrl,  String? imageUrl,  String? colorCode,  bool isActive,  int sortOrder,  int level,  List<String> requiredFields,  List<String> optionalFields,  Map<String, dynamic>? formTemplate,  List<ProductCategoryModel> subcategories,  DateTime? createdAt,  DateTime? updatedAt,  VehicleCategory? vehicleCategory,  AutoPartsCategory? autoPartsCategory,  AutomotiveElectronicsCategory? electronicsCategory,  ToolsCategory? toolsCategory,  AccessoriesCategory? accessoriesCategory)?  $default,) {final _that = this;
switch (_that) {
case _ProductCategoryModel() when $default != null:
return $default(_that.id,_that.name,_that.nameAr,_that.nameEn,_that.nameIt,_that.description,_that.descriptionAr,_that.descriptionEn,_that.descriptionIt,_that.parentId,_that.productType,_that.iconUrl,_that.imageUrl,_that.colorCode,_that.isActive,_that.sortOrder,_that.level,_that.requiredFields,_that.optionalFields,_that.formTemplate,_that.subcategories,_that.createdAt,_that.updatedAt,_that.vehicleCategory,_that.autoPartsCategory,_that.electronicsCategory,_that.toolsCategory,_that.accessoriesCategory);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _ProductCategoryModel implements ProductCategoryModel {
  const _ProductCategoryModel({required this.id, required this.name, this.nameAr, this.nameEn, this.nameIt, this.description, this.descriptionAr, this.descriptionEn, this.descriptionIt, this.parentId, this.productType = ProductType.other, this.iconUrl, this.imageUrl, this.colorCode, this.isActive = true, this.sortOrder = 0, this.level = 0, final  List<String> requiredFields = const [], final  List<String> optionalFields = const [], final  Map<String, dynamic>? formTemplate, final  List<ProductCategoryModel> subcategories = const [], this.createdAt, this.updatedAt, this.vehicleCategory, this.autoPartsCategory, this.electronicsCategory, this.toolsCategory, this.accessoriesCategory}): _requiredFields = requiredFields,_optionalFields = optionalFields,_formTemplate = formTemplate,_subcategories = subcategories;
  factory _ProductCategoryModel.fromJson(Map<String, dynamic> json) => _$ProductCategoryModelFromJson(json);

@override final  String id;
@override final  String name;
@override final  String? nameAr;
@override final  String? nameEn;
@override final  String? nameIt;
@override final  String? description;
@override final  String? descriptionAr;
@override final  String? descriptionEn;
@override final  String? descriptionIt;
@override final  String? parentId;
@override@JsonKey() final  ProductType productType;
@override final  String? iconUrl;
@override final  String? imageUrl;
@override final  String? colorCode;
@override@JsonKey() final  bool isActive;
@override@JsonKey() final  int sortOrder;
@override@JsonKey() final  int level;
 final  List<String> _requiredFields;
@override@JsonKey() List<String> get requiredFields {
  if (_requiredFields is EqualUnmodifiableListView) return _requiredFields;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_requiredFields);
}

 final  List<String> _optionalFields;
@override@JsonKey() List<String> get optionalFields {
  if (_optionalFields is EqualUnmodifiableListView) return _optionalFields;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_optionalFields);
}

 final  Map<String, dynamic>? _formTemplate;
@override Map<String, dynamic>? get formTemplate {
  final value = _formTemplate;
  if (value == null) return null;
  if (_formTemplate is EqualUnmodifiableMapView) return _formTemplate;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}

 final  List<ProductCategoryModel> _subcategories;
@override@JsonKey() List<ProductCategoryModel> get subcategories {
  if (_subcategories is EqualUnmodifiableListView) return _subcategories;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_subcategories);
}

@override final  DateTime? createdAt;
@override final  DateTime? updatedAt;
// الحقول الجديدة للنظام المحدث
@override final  VehicleCategory? vehicleCategory;
@override final  AutoPartsCategory? autoPartsCategory;
@override final  AutomotiveElectronicsCategory? electronicsCategory;
@override final  ToolsCategory? toolsCategory;
@override final  AccessoriesCategory? accessoriesCategory;

/// Create a copy of ProductCategoryModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ProductCategoryModelCopyWith<_ProductCategoryModel> get copyWith => __$ProductCategoryModelCopyWithImpl<_ProductCategoryModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ProductCategoryModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ProductCategoryModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.nameAr, nameAr) || other.nameAr == nameAr)&&(identical(other.nameEn, nameEn) || other.nameEn == nameEn)&&(identical(other.nameIt, nameIt) || other.nameIt == nameIt)&&(identical(other.description, description) || other.description == description)&&(identical(other.descriptionAr, descriptionAr) || other.descriptionAr == descriptionAr)&&(identical(other.descriptionEn, descriptionEn) || other.descriptionEn == descriptionEn)&&(identical(other.descriptionIt, descriptionIt) || other.descriptionIt == descriptionIt)&&(identical(other.parentId, parentId) || other.parentId == parentId)&&(identical(other.productType, productType) || other.productType == productType)&&(identical(other.iconUrl, iconUrl) || other.iconUrl == iconUrl)&&(identical(other.imageUrl, imageUrl) || other.imageUrl == imageUrl)&&(identical(other.colorCode, colorCode) || other.colorCode == colorCode)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&(identical(other.sortOrder, sortOrder) || other.sortOrder == sortOrder)&&(identical(other.level, level) || other.level == level)&&const DeepCollectionEquality().equals(other._requiredFields, _requiredFields)&&const DeepCollectionEquality().equals(other._optionalFields, _optionalFields)&&const DeepCollectionEquality().equals(other._formTemplate, _formTemplate)&&const DeepCollectionEquality().equals(other._subcategories, _subcategories)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.vehicleCategory, vehicleCategory) || other.vehicleCategory == vehicleCategory)&&(identical(other.autoPartsCategory, autoPartsCategory) || other.autoPartsCategory == autoPartsCategory)&&(identical(other.electronicsCategory, electronicsCategory) || other.electronicsCategory == electronicsCategory)&&(identical(other.toolsCategory, toolsCategory) || other.toolsCategory == toolsCategory)&&(identical(other.accessoriesCategory, accessoriesCategory) || other.accessoriesCategory == accessoriesCategory));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,name,nameAr,nameEn,nameIt,description,descriptionAr,descriptionEn,descriptionIt,parentId,productType,iconUrl,imageUrl,colorCode,isActive,sortOrder,level,const DeepCollectionEquality().hash(_requiredFields),const DeepCollectionEquality().hash(_optionalFields),const DeepCollectionEquality().hash(_formTemplate),const DeepCollectionEquality().hash(_subcategories),createdAt,updatedAt,vehicleCategory,autoPartsCategory,electronicsCategory,toolsCategory,accessoriesCategory]);

@override
String toString() {
  return 'ProductCategoryModel(id: $id, name: $name, nameAr: $nameAr, nameEn: $nameEn, nameIt: $nameIt, description: $description, descriptionAr: $descriptionAr, descriptionEn: $descriptionEn, descriptionIt: $descriptionIt, parentId: $parentId, productType: $productType, iconUrl: $iconUrl, imageUrl: $imageUrl, colorCode: $colorCode, isActive: $isActive, sortOrder: $sortOrder, level: $level, requiredFields: $requiredFields, optionalFields: $optionalFields, formTemplate: $formTemplate, subcategories: $subcategories, createdAt: $createdAt, updatedAt: $updatedAt, vehicleCategory: $vehicleCategory, autoPartsCategory: $autoPartsCategory, electronicsCategory: $electronicsCategory, toolsCategory: $toolsCategory, accessoriesCategory: $accessoriesCategory)';
}


}

/// @nodoc
abstract mixin class _$ProductCategoryModelCopyWith<$Res> implements $ProductCategoryModelCopyWith<$Res> {
  factory _$ProductCategoryModelCopyWith(_ProductCategoryModel value, $Res Function(_ProductCategoryModel) _then) = __$ProductCategoryModelCopyWithImpl;
@override @useResult
$Res call({
 String id, String name, String? nameAr, String? nameEn, String? nameIt, String? description, String? descriptionAr, String? descriptionEn, String? descriptionIt, String? parentId, ProductType productType, String? iconUrl, String? imageUrl, String? colorCode, bool isActive, int sortOrder, int level, List<String> requiredFields, List<String> optionalFields, Map<String, dynamic>? formTemplate, List<ProductCategoryModel> subcategories, DateTime? createdAt, DateTime? updatedAt, VehicleCategory? vehicleCategory, AutoPartsCategory? autoPartsCategory, AutomotiveElectronicsCategory? electronicsCategory, ToolsCategory? toolsCategory, AccessoriesCategory? accessoriesCategory
});




}
/// @nodoc
class __$ProductCategoryModelCopyWithImpl<$Res>
    implements _$ProductCategoryModelCopyWith<$Res> {
  __$ProductCategoryModelCopyWithImpl(this._self, this._then);

  final _ProductCategoryModel _self;
  final $Res Function(_ProductCategoryModel) _then;

/// Create a copy of ProductCategoryModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? name = null,Object? nameAr = freezed,Object? nameEn = freezed,Object? nameIt = freezed,Object? description = freezed,Object? descriptionAr = freezed,Object? descriptionEn = freezed,Object? descriptionIt = freezed,Object? parentId = freezed,Object? productType = null,Object? iconUrl = freezed,Object? imageUrl = freezed,Object? colorCode = freezed,Object? isActive = null,Object? sortOrder = null,Object? level = null,Object? requiredFields = null,Object? optionalFields = null,Object? formTemplate = freezed,Object? subcategories = null,Object? createdAt = freezed,Object? updatedAt = freezed,Object? vehicleCategory = freezed,Object? autoPartsCategory = freezed,Object? electronicsCategory = freezed,Object? toolsCategory = freezed,Object? accessoriesCategory = freezed,}) {
  return _then(_ProductCategoryModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,nameAr: freezed == nameAr ? _self.nameAr : nameAr // ignore: cast_nullable_to_non_nullable
as String?,nameEn: freezed == nameEn ? _self.nameEn : nameEn // ignore: cast_nullable_to_non_nullable
as String?,nameIt: freezed == nameIt ? _self.nameIt : nameIt // ignore: cast_nullable_to_non_nullable
as String?,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,descriptionAr: freezed == descriptionAr ? _self.descriptionAr : descriptionAr // ignore: cast_nullable_to_non_nullable
as String?,descriptionEn: freezed == descriptionEn ? _self.descriptionEn : descriptionEn // ignore: cast_nullable_to_non_nullable
as String?,descriptionIt: freezed == descriptionIt ? _self.descriptionIt : descriptionIt // ignore: cast_nullable_to_non_nullable
as String?,parentId: freezed == parentId ? _self.parentId : parentId // ignore: cast_nullable_to_non_nullable
as String?,productType: null == productType ? _self.productType : productType // ignore: cast_nullable_to_non_nullable
as ProductType,iconUrl: freezed == iconUrl ? _self.iconUrl : iconUrl // ignore: cast_nullable_to_non_nullable
as String?,imageUrl: freezed == imageUrl ? _self.imageUrl : imageUrl // ignore: cast_nullable_to_non_nullable
as String?,colorCode: freezed == colorCode ? _self.colorCode : colorCode // ignore: cast_nullable_to_non_nullable
as String?,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,sortOrder: null == sortOrder ? _self.sortOrder : sortOrder // ignore: cast_nullable_to_non_nullable
as int,level: null == level ? _self.level : level // ignore: cast_nullable_to_non_nullable
as int,requiredFields: null == requiredFields ? _self._requiredFields : requiredFields // ignore: cast_nullable_to_non_nullable
as List<String>,optionalFields: null == optionalFields ? _self._optionalFields : optionalFields // ignore: cast_nullable_to_non_nullable
as List<String>,formTemplate: freezed == formTemplate ? _self._formTemplate : formTemplate // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,subcategories: null == subcategories ? _self._subcategories : subcategories // ignore: cast_nullable_to_non_nullable
as List<ProductCategoryModel>,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,vehicleCategory: freezed == vehicleCategory ? _self.vehicleCategory : vehicleCategory // ignore: cast_nullable_to_non_nullable
as VehicleCategory?,autoPartsCategory: freezed == autoPartsCategory ? _self.autoPartsCategory : autoPartsCategory // ignore: cast_nullable_to_non_nullable
as AutoPartsCategory?,electronicsCategory: freezed == electronicsCategory ? _self.electronicsCategory : electronicsCategory // ignore: cast_nullable_to_non_nullable
as AutomotiveElectronicsCategory?,toolsCategory: freezed == toolsCategory ? _self.toolsCategory : toolsCategory // ignore: cast_nullable_to_non_nullable
as ToolsCategory?,accessoriesCategory: freezed == accessoriesCategory ? _self.accessoriesCategory : accessoriesCategory // ignore: cast_nullable_to_non_nullable
as AccessoriesCategory?,
  ));
}


}

// dart format on
