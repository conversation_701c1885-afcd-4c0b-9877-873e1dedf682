import 'package:freezed_annotation/freezed_annotation.dart';

enum UserRole {
  @JsonValue('buyer')
  buyer,
  @JsonValue('seller')
  seller,
  @JsonValue('admin')
  admin,
  @JsonValue('super_admin')
  superAdmin,
  @JsonValue('customer')
  customer,
  @JsonValue('moderator')
  moderator,
  @JsonValue('support')
  support,
  @JsonValue('guest')
  guest,
}

enum ProductType {
  // الأقسام الرئيسية للمنتجات
  @JsonValue('vehicles')
  vehicles, // قسم السيارات والمركبات

  @JsonValue('auto_parts')
  autoParts, // قسم قطع الغيار

  @JsonValue('electronics')
  electronics, // قسم الإلكترونيات

  @JsonValue('tools')
  tools, // قسم الأدوات والمعدات

  @JsonValue('accessories')
  accessories, // قسم الإكسسوارات

  @JsonValue('maintenance')
  maintenance, // قسم مواد الصيانة

  @JsonValue('other')
  other, // أخرى
}

enum ProductCondition {
  @JsonValue('new')
  new_,
  @JsonValue('like_new')
  likeNew,
  @JsonValue('good')
  good,
  @JsonValue('used')
  used,
  @JsonValue('fair')
  fair,
  @JsonValue('poor')
  poor,
  @JsonValue('for_parts')
  forParts,
  @JsonValue('refurbished')
  refurbished,
}

enum ProductStatus {
  @JsonValue('active')
  active,
  @JsonValue('inactive')
  inactive,
  @JsonValue('draft')
  draft,
  @JsonValue('sold')
  sold,
  @JsonValue('deleted')
  deleted,
  @JsonValue('pending')
  pending,
}

enum OrderStatus {
  @JsonValue('pending')
  pending,
  @JsonValue('processing')
  processing,
  @JsonValue('confirmed')
  confirmed,
  @JsonValue('shipped')
  shipped,
  @JsonValue('delivered')
  delivered,
  @JsonValue('cancelled')
  cancelled,
  @JsonValue('returned')
  returned,
  @JsonValue('refunded')
  refunded,
}

enum PaymentStatus {
  @JsonValue('pending')
  pending,
  @JsonValue('paid')
  paid,
  @JsonValue('failed')
  failed,
  @JsonValue('refunded')
  refunded,
}

enum PaymentMethod {
  @JsonValue('cash')
  cash,
  @JsonValue('card')
  card,
  @JsonValue('bank_transfer')
  bankTransfer,
  @JsonValue('wallet')
  wallet,
}

enum ShippingMethod {
  @JsonValue('standard')
  standard,
  @JsonValue('express')
  express,
  @JsonValue('pickup')
  pickup,
}

enum NotificationType {
  @JsonValue('order')
  order,
  @JsonValue('message')
  message,
  @JsonValue('system')
  system,
  @JsonValue('promotion')
  promotion,
}

enum VehicleType {
  @JsonValue('car')
  car,
  @JsonValue('truck')
  truck,
  @JsonValue('motorcycle')
  motorcycle,
  @JsonValue('bus')
  bus,
  @JsonValue('other')
  other,
}

enum FuelType {
  @JsonValue('gasoline')
  gasoline,
  @JsonValue('diesel')
  diesel,
  @JsonValue('electric')
  electric,
  @JsonValue('hybrid')
  hybrid,
  @JsonValue('other')
  other,
}

enum TransmissionType {
  @JsonValue('manual')
  manual,
  @JsonValue('automatic')
  automatic,
  @JsonValue('cvt')
  cvt,
  @JsonValue('other')
  other,
}

enum SellerStatus {
  @JsonValue('pending')
  pending,
  @JsonValue('approved')
  approved,
  @JsonValue('rejected')
  rejected,
  @JsonValue('suspended')
  suspended,
  @JsonValue('request_pending')
  requestPending,
}

// enum AutomotiveType محذوف - تم استبداله بـ AutoPartsCategory و VehicleCategory
// للحفاظ على التوافق مع الكود الموجود، سنبقي على هذا enum مؤقتاً
enum AutomotiveType {
  @JsonValue('engine_parts')
  engineParts,
  @JsonValue('transmission_parts')
  transmissionParts,
  @JsonValue('suspension_steering')
  suspensionSteering,
  @JsonValue('brake_system')
  brakeSystem,
  @JsonValue('electrical_electronic')
  electricalElectronic,
  @JsonValue('body_exterior')
  bodyExterior,
  @JsonValue('interior_parts')
  interiorParts,
  @JsonValue('tires_wheels')
  tiresWheels,
  @JsonValue('exhaust_system')
  exhaustSystem,
  @JsonValue('cooling_ac')
  coolingAC,
  @JsonValue('fuel_system')
  fuelSystem,
  @JsonValue('auto_parts')
  autoParts,
  @JsonValue('vehicles')
  vehicles,
  @JsonValue('other')
  other,
}

enum AutoPartCategory {
  @JsonValue('engine')
  engine,
  @JsonValue('transmission')
  transmission,
  @JsonValue('suspension')
  suspension,
  @JsonValue('brakes')
  brakes,
  @JsonValue('electrical')
  electrical,
  @JsonValue('body')
  body,
  @JsonValue('interior')
  interior,
  @JsonValue('wheels')
  wheels,
  @JsonValue('exhaust')
  exhaust,
  @JsonValue('cooling')
  cooling,
  @JsonValue('fuel')
  fuel,
  @JsonValue('filters')
  filters,
  @JsonValue('belts')
  belts,
  @JsonValue('hoses')
  hoses,
  @JsonValue('sensors')
  sensors,
  @JsonValue('lighting')
  lighting,
  @JsonValue('other')
  other,
}

enum ElectronicsCategory {
  @JsonValue('audio')
  audio,
  @JsonValue('video')
  video,
  @JsonValue('navigation')
  navigation,
  @JsonValue('security')
  security,
  @JsonValue('lighting')
  lighting,
  @JsonValue('sensors')
  sensors,
  @JsonValue('control_units')
  controlUnits,
  @JsonValue('accessories')
  accessories,
  @JsonValue('tools')
  tools,
  @JsonValue('other')
  other,
}

enum BatteryType {
  @JsonValue('lead_acid')
  leadAcid,
  @JsonValue('agm')
  agm,
  @JsonValue('gel')
  gel,
  @JsonValue('lithium')
  lithium,
  @JsonValue('maintenance_free')
  maintenanceFree,
  @JsonValue('other')
  other,
}

enum BatteryVoltage {
  @JsonValue('6v')
  sixVolt,
  @JsonValue('12v')
  twelveVolt,
  @JsonValue('24v')
  twentyFourVolt,
  @JsonValue('other')
  other,
}

// فئات السيارات والمركبات
enum VehicleCategory {
  @JsonValue('passenger_cars')
  passengerCars, // السيارات الشخصية

  @JsonValue('commercial_vehicles')
  commercialVehicles, // المركبات التجارية

  @JsonValue('motorcycles')
  motorcycles, // الدراجات النارية

  @JsonValue('heavy_vehicles')
  heavyVehicles, // المركبات الثقيلة

  @JsonValue('special_vehicles')
  specialVehicles, // المركبات الخاصة

  @JsonValue('other')
  other,
}

// فئات قطع الغيار - محدثة ومفصلة
enum AutoPartsCategory {
  // قطع المحرك
  @JsonValue('engine_parts')
  engineParts,

  // نظام نقل الحركة
  @JsonValue('transmission_parts')
  transmissionParts,

  // نظام التعليق والتوجيه
  @JsonValue('suspension_steering')
  suspensionSteering,

  // نظام الفرامل
  @JsonValue('brake_system')
  brakeSystem,

  // النظام الكهربائي والإلكتروني
  @JsonValue('electrical_electronic')
  electricalElectronic,

  // هيكل السيارة والصاج
  @JsonValue('body_exterior')
  bodyExterior,

  // الأجزاء الداخلية
  @JsonValue('interior_parts')
  interiorParts,

  // الإطارات والعجلات
  @JsonValue('tires_wheels')
  tiresWheels,

  // نظام العادم
  @JsonValue('exhaust_system')
  exhaustSystem,

  // نظام التبريد والتكييف
  @JsonValue('cooling_ac')
  coolingAC,

  // نظام الوقود
  @JsonValue('fuel_system')
  fuelSystem,

  // الفلاتر والمرشحات
  @JsonValue('filters')
  filters,

  // الأحزمة والسلاسل
  @JsonValue('belts_chains')
  beltsChains,

  // الخراطيم والأنابيب
  @JsonValue('hoses_tubes')
  hosestubes,

  // الحساسات وأجهزة القياس
  @JsonValue('sensors_gauges')
  sensorsGauges,

  // نظام الإضاءة
  @JsonValue('lighting_system')
  lightingSystem,

  @JsonValue('other')
  other,
}

// فئات الإلكترونيات السيارات
enum AutomotiveElectronicsCategory {
  // أنظمة الصوت والمرئيات
  @JsonValue('audio_video')
  audioVideo,

  // أنظمة الملاحة والخرائط
  @JsonValue('navigation_gps')
  navigationGPS,

  // أنظمة الأمان والحماية
  @JsonValue('security_safety')
  securitySafety,

  // أنظمة الإضاءة المتقدمة
  @JsonValue('advanced_lighting')
  advancedLighting,

  // حساسات ومعدات القياس
  @JsonValue('sensors_diagnostics')
  sensorsDiagnostics,

  // وحدات التحكم الإلكترونية
  @JsonValue('control_modules')
  controlModules,

  // إكسسوارات إلكترونية
  @JsonValue('electronic_accessories')
  electronicAccessories,

  // أدوات التشخيص
  @JsonValue('diagnostic_tools')
  diagnosticTools,

  @JsonValue('other')
  other,
}

// أنواع الأدوات والمعدات
enum ToolsCategory {
  // أدوات يدوية
  @JsonValue('hand_tools')
  handTools,

  // أدوات كهربائية
  @JsonValue('power_tools')
  powerTools,

  // معدات التشخيص
  @JsonValue('diagnostic_equipment')
  diagnosticEquipment,

  // معدات الرفع والحمل
  @JsonValue('lifting_equipment')
  liftingEquipment,

  // أدوات القياس والاختبار
  @JsonValue('measuring_testing')
  measuringTesting,

  // معدات اللحام والقطع
  @JsonValue('welding_cutting')
  weldingCutting,

  // أدوات التنظيف والصيانة
  @JsonValue('cleaning_maintenance')
  cleaningMaintenance,

  @JsonValue('other')
  other,
}

// فئات الإكسسوارات
enum AccessoriesCategory {
  // إكسسوارات خارجية
  @JsonValue('exterior_accessories')
  exteriorAccessories,

  // إكسسوارات داخلية
  @JsonValue('interior_accessories')
  interiorAccessories,

  // إكسسوارات الأمان
  @JsonValue('safety_accessories')
  safetyAccessories,

  // إكسسوارات الراحة
  @JsonValue('comfort_accessories')
  comfortAccessories,

  // إكسسوارات رياضية
  @JsonValue('performance_accessories')
  performanceAccessories,

  @JsonValue('other')
  other,
}
