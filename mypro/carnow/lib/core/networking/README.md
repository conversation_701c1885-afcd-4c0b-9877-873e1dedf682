# SimpleApiClient - Forever Plan Architecture

## Overview - نظرة عامة

`SimpleApiClient` is a lightweight HTTP client wrapper built on top of Dio for the CarNow Flutter application. It follows the Forever Plan Architecture where Flutter handles UI only, while all data operations go through the Go backend API.

`SimpleApiClient` هو عميل HTTP خفيف مبني على Dio لتطبيق CarNow Flutter. يتبع بنية الخطة الدائمة حيث Flutter يتعامل مع واجهة المستخدم فقط، بينما جميع عمليات البيانات تمر عبر Go backend API.

## Architecture Flow - تدفق البنية

```
Flutter (UI Only) → Go API → Supabase (Data Only)
```

- **Flutter**: UI components, state management, user interactions
- **Go Backend**: Business logic, authentication, data validation, API endpoints
- **Supabase**: Database storage, real-time subscriptions, file storage

## Features - الميزات

✅ **JWT Authentication**: Automatic token injection from Supabase auth  
✅ **Error Handling**: Comprehensive error handling with logging  
✅ **Timeout Configuration**: Configurable request timeouts  
✅ **Base URL Management**: Centralized backend configuration  
✅ **Extension Methods**: Simple API for common HTTP operations  
✅ **Riverpod Integration**: Works seamlessly with Riverpod providers  

## Configuration - التكوين

### Backend Configuration

The API client uses `BackendConfig` for base URL and timeout settings:

```dart
// lib/core/config/backend_config.dart
class BackendConfig {
  static const String baseUrl = 'https://backend-go-8klm.onrender.com/api/v1';
  static const Duration requestTimeout = Duration(seconds: 30);
  static const Duration connectTimeout = Duration(seconds: 15);
}
```

### Authentication Integration

The client automatically injects JWT tokens from Supabase authentication:

```dart
// Automatic token injection
class AuthInterceptor extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    final authData = SimpleAuthSystem.instance.authData;
    final token = authData.token;
    
    if (token != null) {
      options.headers['Authorization'] = 'Bearer $token';
    }
    
    handler.next(options);
  }
}
```

## Usage - الاستخدام

### Basic Provider Setup

```dart
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../networking/simple_api_client.dart';

part 'my_service.g.dart';

@riverpod
Future<List<Product>> products(Ref ref) async {
  final apiClient = ref.read(simpleApiClientProvider);
  
  try {
    final response = await apiClient.getApi<Map<String, dynamic>>('/products');
    
    if (response.statusCode == 200 && response.data != null) {
      final data = response.data!;
      final productsJson = data['data'] as List<dynamic>;
      
      return productsJson
          .map((json) => Product.fromJson(json as Map<String, dynamic>))
          .toList();
    } else {
      throw Exception('Failed to fetch products: ${response.statusCode}');
    }
  } on DioException catch (e) {
    throw Exception('Network error: ${e.message}');
  }
}
```

### CRUD Operations

#### GET Request
```dart
final response = await apiClient.getApi<Map<String, dynamic>>('/products');
```

#### POST Request
```dart
final response = await apiClient.postApi<Map<String, dynamic>>(
  '/products',
  data: {
    'name': 'New Product',
    'price': 99.99,
    'description': 'Product description',
  },
);
```

#### PUT Request
```dart
final response = await apiClient.putApi<Map<String, dynamic>>(
  '/products/123',
  data: {
    'name': 'Updated Product',
    'price': 149.99,
  },
);
```

#### DELETE Request
```dart
final response = await apiClient.deleteApi<Map<String, dynamic>>('/products/123');
```

### Error Handling

```dart
try {
  final response = await apiClient.getApi<Map<String, dynamic>>('/products');
  // Handle success
} on DioException catch (e) {
  // Handle network errors
  if (e.response?.statusCode == 401) {
    // Handle authentication error
  } else if (e.response?.statusCode == 404) {
    // Handle not found
  } else {
    // Handle other HTTP errors
  }
} catch (e) {
  // Handle unexpected errors
}
```

### UI Integration

```dart
class ProductListScreen extends ConsumerWidget {
  const ProductListScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final productsAsync = ref.watch(productsProvider);
    
    return Scaffold(
      appBar: AppBar(title: const Text('Products')),
      body: productsAsync.when(
        data: (products) => ListView.builder(
          itemCount: products.length,
          itemBuilder: (context, index) {
            final product = products[index];
            return ListTile(
              title: Text(product.name),
              subtitle: Text('\$${product.price}'),
            );
          },
        ),
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => Center(
          child: Text('Error: $error'),
        ),
      ),
    );
  }
}
```

## API Response Format

The Go backend returns responses in this format:

```json
{
  "success": true,
  "data": {
    // Actual data here
  },
  "message": "Success message",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

For lists:
```json
{
  "success": true,
  "data": [
    // Array of items
  ],
  "message": "Success message",
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 100
  }
}
```

## Best Practices - أفضل الممارسات

### ✅ DO
- Use Riverpod providers for API calls
- Handle errors appropriately in UI
- Use proper typing with generics
- Log important operations
- Invalidate providers when data changes

### ❌ DON'T
- Make direct Supabase calls from UI
- Ignore error handling
- Store sensitive data in client
- Make API calls in build methods
- Forget to handle loading states

## Testing

Unit tests are available in `test/core/networking/simple_api_client_test.dart`:

```bash
flutter test test/core/networking/simple_api_client_test.dart
```

## Migration from Direct Supabase

If you're migrating from direct Supabase calls:

### Before (❌ OLD)
```dart
final response = await Supabase.instance.client
    .from('products')
    .select()
    .execute();
```

### After (✅ NEW)
```dart
final response = await apiClient.getApi<Map<String, dynamic>>('/products');
```

## Troubleshooting

### Common Issues

1. **401 Unauthorized**: Check if user is authenticated and token is valid
2. **Network timeout**: Check internet connection and backend availability
3. **404 Not Found**: Verify API endpoint exists in Go backend
4. **500 Server Error**: Check backend logs for internal errors

### Debug Logging

Enable debug logging to see request/response details:

```dart
// In development mode
if (kDebugMode) {
  Logger.root.level = Level.ALL;
}
```

## Related Files

- `lib/core/networking/simple_api_client.dart` - Main implementation
- `lib/core/config/backend_config.dart` - Configuration
- `lib/core/auth/simple_auth_system.dart` - Authentication
- `lib/core/examples/simple_api_client_usage_example.dart` - Usage examples
- `test/core/networking/simple_api_client_test.dart` - Unit tests