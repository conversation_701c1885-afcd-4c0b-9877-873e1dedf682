import 'package:logger/logger.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:carnow/shared/models/user_model.dart'; // This was correctly identified as unused
// import 'package:carnow/shared/models/support_ticket_model.dart'; // MODIFIED - REMOVED THIS LINE
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../networking/simple_api_client.dart';
import '../models/ticket_message_model.dart';
import '../models/ticket_model.dart';

part 'support_ticket_repository.g.dart';

final _logger = Logger();

/// Repository for managing support tickets and messages
class SupportTicketRepository {
  SupportTicketRepository(this._apiClient);
  final SimpleApiClient _apiClient;

  

  /// Get all tickets for a user
  Future<List<TicketModel>> getUserTickets(String userId) async {
    try {
      final apiResponse = await _apiClient.get<List<dynamic>>('/support/tickets/user/$userId');

      if (!apiResponse.isSuccess || apiResponse.data == null) {
        throw Exception('Failed to fetch user tickets: ${apiResponse.message}');
      }

      return apiResponse.data!
          .map((json) => TicketModel.fromJson(json as Map<String, dynamic>))
          .toList();
    } catch (e, stackTrace) {
      _logger.e(
        'Error fetching user tickets',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Get a specific ticket by ID
  Future<TicketModel?> getTicketById(int ticketId, String userId) async {
    try {
      final apiResponse = await _apiClient.get<Map<String, dynamic>>(
        '/support/tickets/$ticketId',
        queryParameters: {'userId': userId},
      );

      if (!apiResponse.isSuccess || apiResponse.data == null) {
        return null;
      }

      return TicketModel.fromJson(apiResponse.data!);
    } catch (e, stackTrace) {
      _logger.e(
        'Error fetching ticket by ID',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Create a new support ticket
  Future<TicketModel> createTicket({
    required String userId,
    required String subject,
    required String description,
    required TicketCategory category,
    TicketPriority priority = TicketPriority.medium,
  }) async {
    try {
      final ticketData = {
        'user_id': userId,
        'subject': subject,
        'description': description,
        'category': category.name,
        'priority': priority.name,
        'ticket_status': TicketStatus.pending.name,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      };

      final apiResponse = await _apiClient.post<Map<String, dynamic>>(
        '/support/tickets',
        data: ticketData,
      );

      if (!apiResponse.isSuccess || apiResponse.data == null) {
        throw Exception('Failed to create ticket: ${apiResponse.message}');
      }

      return TicketModel.fromJson(apiResponse.data!);
    } catch (e, stackTrace) {
      _logger.e('Error creating ticket', error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Update ticket status
  Future<TicketModel> updateTicketStatus(
    int ticketId,
    String userId,
    TicketStatus status,
  ) async {
    try {
      final updateData = {
        'ticket_status': status.name,
        'updated_at': DateTime.now().toIso8601String(),
      };

      // Add resolved_at if marking as resolved
      if (status == TicketStatus.resolved) {
        updateData['resolved_at'] = DateTime.now().toIso8601String();
      }

      final apiResponse = await _apiClient.put<Map<String, dynamic>>(
        '/support/tickets/$ticketId',
        data: updateData,
        queryParameters: {'userId': userId},
      );

      if (!apiResponse.isSuccess || apiResponse.data == null) {
        throw Exception('Failed to update ticket status: ${apiResponse.message}');
      }

      return TicketModel.fromJson(apiResponse.data!);
    } catch (e, stackTrace) {
      _logger.e(
        'Error updating ticket status',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Get messages for a ticket
  Future<List<TicketMessageModel>> getTicketMessages(int ticketId) async {
    try {
      final apiResponse = await _apiClient.get<List<dynamic>>(
        '/support/tickets/$ticketId/messages',
      );

      if (!apiResponse.isSuccess || apiResponse.data == null) {
        throw Exception('Failed to fetch ticket messages: ${apiResponse.message}');
      }

      return apiResponse.data!
          .map((json) => TicketMessageModel.fromJson(json as Map<String, dynamic>))
          .toList();
    } catch (e, stackTrace) {
      _logger.e(
        'Error fetching ticket messages',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Send a message in a ticket
  Future<TicketMessageModel> sendMessage({
    required int ticketId,
    required String senderId,
    required String message,
    MessageSender senderType = MessageSender.user,
    bool isInternal = false,
  }) async {
    try {
      final messageData = {
        'ticket_id': ticketId,
        'sender_id': senderId,
        'message': message,
        'sender_type': senderType.name,
        'is_internal': isInternal,
        'sent_at': DateTime.now().toIso8601String(),
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      };

      final apiResponse = await _apiClient.post<Map<String, dynamic>>(
        '/support/tickets/$ticketId/messages',
        data: messageData,
      );

      if (!apiResponse.isSuccess || apiResponse.data == null) {
        throw Exception('Failed to send message: ${apiResponse.message}');
      }

      return TicketMessageModel.fromJson(apiResponse.data!);
    } catch (e, stackTrace) {
      _logger.e('Error sending message', error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Mark messages as read
  Future<void> markMessagesAsRead(int ticketId, String userId) async {
    try {
      final apiResponse = await _apiClient.put<dynamic>(
        '/support/tickets/$ticketId/messages/read',
        queryParameters: {'userId': userId},
      );

      if (!apiResponse.isSuccess) {
        throw Exception('Failed to mark messages as read: ${apiResponse.message}');
      }
    } catch (e, stackTrace) {
      _logger.e(
        'Error marking messages as read',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Get unread message count for a ticket
  Future<int> getUnreadMessageCount(int ticketId, String userId) async {
    try {
      final apiResponse = await _apiClient.get<Map<String, dynamic>>(
        '/support/tickets/$ticketId/messages/unread',
        queryParameters: {'userId': userId},
      );

      if (!apiResponse.isSuccess || apiResponse.data == null) {
        return 0;
      }

      return apiResponse.data!['count'] ?? 0;
    } catch (e, stackTrace) {
      _logger.e(
        'Error getting unread message count',
        error: e,
        stackTrace: stackTrace,
      );
      return 0;
    }
  }

  /// Subscribe to real-time ticket updates
  /// DEPRECATED: Real-time subscriptions should be handled by Go backend WebSocket connections
  /// Direct Supabase realtime subscriptions removed as per architecture cleanup
  void subscribeToTicketUpdates(
    int ticketId,
    void Function(TicketMessageModel) onNewMessage,
  ) {
    throw UnimplementedError(
      'Use WebSocket connections through Go backend instead',
    );
  }
}

/// Provider for SupportTicketRepository
@riverpod
SupportTicketRepository supportTicketRepository(Ref ref) {
  final apiClient = ref.watch(simpleApiClientProvider);
  return SupportTicketRepository(apiClient);
}
