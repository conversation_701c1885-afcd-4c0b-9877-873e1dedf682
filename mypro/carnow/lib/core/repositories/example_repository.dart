import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../errors/result.dart';
import 'base_repository.dart';

/// نموذج بيانات بسيط للتوضيح
/// Simple data model for illustration
class ExampleModel {
  ExampleModel({
    required this.id,
    required this.name,
    required this.description,
    this.isActive = true,
    this.createdAt,
    this.updatedAt,
    this.isDeleted = false,
  });

  /// إنشاء نموذج من بيانات JSON
  /// Create a model from JSON data
  factory ExampleModel.fromJson(Map<String, dynamic> json) {
    return ExampleModel(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      isActive: json['is_active'] as bool? ?? true,
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'] as String)
          : null,
      updatedAt: json['updated_at'] != null
          ? DateTime.parse(json['updated_at'] as String)
          : null,
      isDeleted: json['is_deleted'] as bool? ?? false,
    );
  }

  final String id;
  final String name;
  final String description;
  final bool isActive;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final bool isDeleted;

  /// إنشاء نسخة معدلة من النموذج
  /// Create a modified copy of the model
  ExampleModel copyWith({
    String? id,
    String? name,
    String? description,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isDeleted,
  }) {
    return ExampleModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isDeleted: isDeleted ?? this.isDeleted,
    );
  }

  /// تحويل النموذج إلى JSON
  /// Convert model to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'is_active': isActive,
      if (createdAt != null) 'created_at': createdAt!.toIso8601String(),
      if (updatedAt != null) 'updated_at': updatedAt!.toIso8601String(),
      'is_deleted': isDeleted,
    };
  }
}

/// مزود للمستودع النموذجي
/// Provider for the example repository
final exampleRepositoryProvider = Provider<ExampleRepository>((ref) {
  return ExampleRepository(ref);
});

/// مستودع نموذجي يمتد المستودع الأساسي
/// Example repository extending the base repository
class ExampleRepository extends BaseRepository {
  ExampleRepository(super.ref);

  /// API path for examples
  @override
  String get apiPath => '/api/v1/examples';

  /// البحث عن النماذج المتطابقة مع الاستعلام
  /// Search for models matching the query
  Future<Result<List<ExampleModel>>> searchExamples(String query) {
    return executeWithErrorHandling(() async {
      final response = await apiClient.get(
        '$apiPath/search',
        queryParameters: {'q': query},
      );

      return (response.data as List)
          .map((json) => ExampleModel.fromJson(json as Map<String, dynamic>))
          .toList();
    }, operationName: 'searchExamples');
  }

  /// الحصول على جميع النماذج النشطة
  /// Get all active models
  Future<Result<List<ExampleModel>>> getActiveExamples() {
    return executeWithErrorHandling(() async {
      final response = await apiClient.get(
        '$apiPath/active',
      );

      return (response.data as List)
          .map((json) => ExampleModel.fromJson(json as Map<String, dynamic>))
          .toList();
    }, operationName: 'getActiveExamples');
  }

  /// إنشاء نموذج جديد
  /// Create a new model
  Future<Result<ExampleModel>> createExample(ExampleModel example) {
    return create<ExampleModel>(
      example.toJson(),
      fromJson: ExampleModel.fromJson,
    );
  }

  /// تحديث نموذج موجود
  /// Update an existing model
  Future<Result<ExampleModel>> updateExample(ExampleModel example) {
    return update<ExampleModel>(
      example.id,
      example.toJson(),
      fromJson: ExampleModel.fromJson,
    );
  }

  /// حذف نموذج (حذف ناعم)
  /// Delete a model (soft delete)
  Future<Result<void>> deleteExample(String id) {
    return delete(id);
  }
}
