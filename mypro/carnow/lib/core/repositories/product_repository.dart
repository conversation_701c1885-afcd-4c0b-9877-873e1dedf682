import 'package:dio/dio.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logging/logging.dart';
import 'package:flutter/foundation.dart';


import '../../features/products/models/product_filter_model.dart';
import '../models/product_model.dart';
import '../models/products_result_model.dart';
import '../networking/simple_api_client.dart';

/// Provider for Product Repository following Forever Plan Architecture
/// Flutter (UI Only) → Go API → Supabase (Data Only)
final productRepositoryProvider = Provider<ProductRepository>((ref) {
  final apiClient = ref.watch(simpleApiClientProvider);
  return ProductRepository(apiClient);
});

/// Product Repository using SimpleApiClient for backend communication
/// NO direct Supabase calls - only HTTP calls to Go backend
class ProductRepository {
  ProductRepository(this._apiClient);
  final SimpleApiClient _apiClient;
  final Logger _logger = Logger('ProductRepository');

  /// Helper method to ensure proper type conversion for product JSON data
  ProductModel _convertJsonToProduct(Map<String, dynamic> json) {
    try {
      // Clean and convert data before sending to model
      final cleanedJson = <String, dynamic>{};

      // Handle ID - required
      if (json['id'] == null) {
        throw ArgumentError('Product ID cannot be null');
      }
      cleanedJson['id'] = json['id'].toString();

      // Handle name - required
      if (json['name'] == null || json['name'].toString().trim().isEmpty) {
        throw ArgumentError('Product name cannot be null or empty');
      }
      cleanedJson['name'] = json['name'].toString().trim();

      // Handle price - required
      if (json['price'] == null) {
        throw ArgumentError('Product price cannot be null');
      }

      double? price;
      if (json['price'] is num) {
        price = json['price'].toDouble();
      } else if (json['price'] is String) {
        price = double.tryParse(json['price']);
      }

      if (price == null || price < 0) {
        throw ArgumentError('Invalid price: ${json['price']}');
      }
      cleanedJson['price'] = price;

      // Handle category ID - required
      if (json['category_id'] == null) {
        _logger.warning(
          'Product has null category_id, using empty string as fallback: ${json['id']}',
        );
        cleanedJson['category_id'] = '';
      } else {
        cleanedJson['category_id'] = json['category_id'].toString();
      }

      // Handle seller ID - required
      if (json['seller_id'] == null) {
        _logger.warning(
          'Product has null seller_id, using empty string as fallback: ${json['id']}',
        );
        cleanedJson['seller_id'] = '';
      } else {
        cleanedJson['seller_id'] = json['seller_id'].toString();
      }

      // Copy other fields with safe handling
      final otherFields = [
        'name_ar', 'name_en', 'name_it',
        'description', 'description_ar', 'description_en', 'description_it',
        'original_price', 'condition', 'is_featured', 'is_available', 'is_active',
        'stock_quantity', 'images', 'specifications', 'brand', 'manufacturer',
        'weight', 'dimensions', 'tags', 'meta_description', 'meta_keywords',
        'sku', 'barcode', 'warranty_info', 'shipping_info', 'return_policy',
        'discounts', 'reviews_count', 'average_rating', 'created_at',
        'updated_at', 'is_deleted', 'status'
      ];

      for (final field in otherFields) {
        if (json.containsKey(field)) {
          cleanedJson[field] = json[field];
        }
      }

      return ProductModel.fromJson(cleanedJson);
    } catch (e) {
      _logger.severe('Error converting JSON to ProductModel: $e');
      rethrow;
    }
  }

  // ==================== Public API Methods ====================

  /// Get all products with optional filtering and pagination
  Future<ProductsResult> getAllProducts({
    int page = 1,
    int limit = 20,
    ProductFilter? filter,
    CancelToken? cancelToken,
  }) async {
    try {
      _logger.info('Fetching products page $page with limit $limit');

      final queryParams = <String, dynamic>{
        'page': page,
        'limit': limit,
      };

      // Add filter parameters if provided
      if (filter != null) {
        if (filter.categoryId?.isNotEmpty == true) {
          queryParams['category_id'] = filter.categoryId;
        }
        if (filter.searchQuery?.isNotEmpty == true) {
          queryParams['search'] = filter.searchQuery;
        }
        if (filter.minPrice != null) {
          queryParams['min_price'] = filter.minPrice;
        }
        if (filter.maxPrice != null) {
          queryParams['max_price'] = filter.maxPrice;
        }
        if (filter.tags?.isNotEmpty == true) {
          queryParams['tags'] = filter.tags;
        }
        if (filter.sellerId?.isNotEmpty == true) {
          queryParams['seller_id'] = filter.sellerId;
        }
        if (filter.isAvailable != null) {
          queryParams['is_available'] = filter.isAvailable;
        }
        if (filter.isFeatured != null) {
          queryParams['is_featured'] = filter.isFeatured;
        }
        if (filter.sortBy?.isNotEmpty == true) {
          queryParams['sort_by'] = filter.sortBy;
        }
      }

      final apiResponse = await _apiClient.get<Map<String, dynamic>>(
        '/products',
        queryParameters: queryParams,
      );

      if (!apiResponse.isSuccess || apiResponse.data == null) {
        throw Exception('Failed to fetch products: ${apiResponse.message}');
      }

      final data = apiResponse.data!;
      
      // Use isolate for heavy parsing in release mode
      List<ProductModel> products;
      if (kReleaseMode && data['products'] is List && (data['products'] as List).length > 10) {
        products = await compute(_parseProductsListIsolate, data['products'] as List);
      } else {
        products = (data['products'] as List)
            .map<ProductModel>((json) => _convertJsonToProduct(json as Map<String, dynamic>))
            .toList();
      }

      return ProductsResult(
        products: products,
        totalCount: data['total_count'] ?? 0,
        hasMore: data['has_more'] ?? false,
        currentPage: page,
        totalPages: data['total_pages'] ?? 1,
      );
    } catch (e) {
      _logger.severe('Error fetching products: $e');
      rethrow;
    }
  }

  /// Get product by ID
  Future<ProductModel?> getProductById(
    String id, {
    CancelToken? cancelToken,
  }) async {
    try {
      _logger.info('Fetching product by id: $id');

      final apiResponse = await _apiClient.get<Map<String, dynamic>>(
        '/products/$id',
      );

      if (!apiResponse.isSuccess || apiResponse.data == null) {
        return null;
      }

      return _convertJsonToProduct(apiResponse.data as Map<String, dynamic>);
    } catch (e) {
      if (e is DioException && e.response?.statusCode == 404) {
        return null;
      }
      _logger.severe('Error fetching product by id: $e');
      rethrow;
    }
  }

  /// Create new product
  Future<String> createProduct(ProductModel product) async {
    _logger.info('Creating product: ${product.name}');
    try {
      final productData = product.toJson();
      
      final apiResponse = await _apiClient.post<Map<String, dynamic>>(
        '/products',
        data: productData,
      );

      if (!apiResponse.isSuccess || apiResponse.data == null) {
        throw Exception('Failed to create product: ${apiResponse.message}');
      }

      final responseData = apiResponse.data!;
      return responseData['id'].toString();
    } catch (e) {
      _logger.severe('Error creating product: $e');
      throw Exception('Failed to create product: $e');
    }
  }

  /// Update existing product
  Future<ProductModel> updateProduct(ProductModel product) async {
    try {
      _logger.info('Updating product: ${product.id}');

      final productData = product.toJson();

      final apiResponse = await _apiClient.put<Map<String, dynamic>>(
        '/products/${product.id}',
        data: productData,
      );

      if (!apiResponse.isSuccess || apiResponse.data == null) {
        throw Exception('Failed to update product: ${apiResponse.message}');
      }

      return _convertJsonToProduct(apiResponse.data!);
    } catch (e) {
      _logger.severe('Error updating product: $e');
      rethrow;
    }
  }

  /// Delete product (soft delete)
  Future<void> deleteProduct(String id) async {
    try {
      _logger.info('Deleting product: $id');
      
      await _apiClient.delete<void>('/products/$id');
    } catch (e) {
      _logger.severe('Error deleting product: $e');
      rethrow;
    }
  }

  /// Get featured products
  Future<List<ProductModel>> getFeaturedProducts() async {
    try {
      _logger.info('Fetching featured products');

      final apiResponse = await _apiClient.get<List>('/products/featured');
      
      if (!apiResponse.isSuccess || apiResponse.data == null) {
        return [];
      }
      
      final productsData = apiResponse.data as List;
      return productsData
          .map<ProductModel>((json) => _convertJsonToProduct(json as Map<String, dynamic>))
          .toList();
    } catch (e) {
      _logger.severe('Error fetching featured products: $e');
      return [];
    }
  }

  /// Search products
  Future<List<ProductModel>> searchProducts(
    String query, {
    int limit = 20,
    int offset = 0,
  }) async {
    try {
      _logger.info('Searching products with query: $query');

      final apiResponse = await _apiClient.get<List>(
        '/products/search',
        queryParameters: {
          'q': query,
          'limit': limit,
          'offset': offset,
        },
      );

      if (!apiResponse.isSuccess || apiResponse.data == null) {
        return [];
      }

      final productsData = apiResponse.data as List;
      return productsData
          .map<ProductModel>((json) => _convertJsonToProduct(json as Map<String, dynamic>))
          .toList();
    } catch (e) {
      _logger.severe('Error searching products: $e');
      return [];
    }
  }

  /// Get products by category
  Future<List<ProductModel>> getProductsByCategory(String categoryId) async {
    try {
      _logger.info('Fetching products by category: $categoryId');

      final apiResponse = await _apiClient.get<List>(
        '/products/category/$categoryId',
      );

      if (!apiResponse.isSuccess || apiResponse.data == null) {
        return [];
      }

      final productsData = apiResponse.data as List;
      return productsData
          .map<ProductModel>((json) => _convertJsonToProduct(json as Map<String, dynamic>))
          .toList();
    } catch (e) {
      _logger.severe('Error fetching products by category: $e');
      return [];
    }
  }

  /// Get similar products
  Future<List<ProductModel>> getSimilarProducts(
    String productId, {
    int limit = 5,
  }) async {
    try {
      _logger.info('Fetching similar products for: $productId');

      final apiResponse = await _apiClient.get<List>(
        '/products/$productId/similar',
        queryParameters: {'limit': limit},
      );

      if (!apiResponse.isSuccess || apiResponse.data == null) {
        return [];
      }

      final productsData = apiResponse.data as List;
      return productsData
          .map<ProductModel>((json) => _convertJsonToProduct(json as Map<String, dynamic>))
          .toList();
    } catch (e) {
      _logger.severe('Error finding similar products for $productId: $e');
      return [];
    }
  }

  /// Get product tags
  Future<List<String>> getProductTags(String productId) async {
    try {
      final apiResponse = await _apiClient.get<List>('/products/$productId/tags');

      if (!apiResponse.isSuccess || apiResponse.data == null) {
        return [];
      }

      if (apiResponse.data is List) {
        return List<String>.from(apiResponse.data as List);
      }
      return [];
    } catch (e) {
      _logger.severe('Error getting product tags: $e');
      return [];
    }
  }

  /// Set product tags
  Future<void> setProductTags(String productId, List<String> tags) async {
    try {
      await _apiClient.put<void>(
        '/products/$productId/tags',
        data: {'tags': tags},
      );
    } catch (e) {
      _logger.severe('Error setting product tags: $e');
      rethrow;
    }
  }

  /// Get product with seller information
  Future<Map<String, dynamic>> getProductWithSeller(String productId) async {
    try {
      final apiResponse = await _apiClient.get<Map<String, dynamic>>('/products/$productId/with-seller');
      
      if (!apiResponse.isSuccess || apiResponse.data == null) {
        throw Exception('Failed to fetch product with seller information');
      }
      
      return apiResponse.data as Map<String, dynamic>;
    } catch (e) {
      _logger.severe('Error fetching product with seller info: $e');
      rethrow;
    }
  }

  /// Update product images
  Future<void> updateProductImages(String productId, List<String> imageUrls) async {
    try {
      _logger.info('Updating images for product: $productId with ${imageUrls.length} images');
      
      await _apiClient.put<void>(
        '/products/$productId/images',
        data: {'images': imageUrls},
      );
      
      _logger.info('Successfully updated images for product: $productId');
    } catch (e) {
      _logger.severe('Error updating product images for $productId: $e');
      throw Exception('Failed to update product images: $e');
    }
  }
}

/// Exception for cancellation operations
class CancellationException implements Exception {
  const CancellationException(this.message);
  final String message;

  @override
  String toString() => 'CancellationException: $message';
}

/// -------------------- Isolate helpers --------------------

// Top-level because compute() requires a static entry point.
List<ProductModel> _parseProductsListIsolate(List<dynamic> rawList) {
  return rawList.map<ProductModel>((item) {
    final json = item as Map<String, dynamic>;
    return _convertJsonToProductStatic(json);
  }).toList();
}

// Simplified copy of _convertJsonToProduct (no logging). Only basic validation.
ProductModel _convertJsonToProductStatic(Map<String, dynamic> json) {
  final cleanedJson = Map<String, dynamic>.from(json);

  // Ensure required fields have sane defaults
  cleanedJson['id'] = cleanedJson['id']?.toString() ?? '';
  cleanedJson['name'] = cleanedJson['name']?.toString() ?? '';
  cleanedJson['price'] = (cleanedJson['price'] is num)
      ? (cleanedJson['price'] as num).toDouble()
      : double.tryParse(cleanedJson['price']?.toString() ?? '0') ?? 0.0;

  // Fallbacks for nullable relations to avoid crashes
  cleanedJson['category_id'] = cleanedJson['category_id']?.toString() ?? '';
  cleanedJson['seller_id'] = cleanedJson['seller_id']?.toString() ?? '';

  return ProductModel.fromJson(cleanedJson);
}
