import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logging/logging.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../features/seller/models/seller_profile_model.dart';
import '../networking/simple_api_client.dart';

part 'seller_repository.g.dart';

final _logger = Logger('SellerRepository');

class SellerRepository {
  SellerRepository(this._apiClient);
  final SimpleApiClient _apiClient;

  /// Fetches the seller profile for a given user ID (from public.users.id).
  Future<SellerProfile?> getSellerProfile(int userId) async {
    try {
      _logger.info('Fetching seller profile for user ID: $userId');
      final response = await _apiClient.get('/sellers/profile/$userId');

      if (response.data == null) {
        _logger.info('No seller profile found for user ID: $userId');
        return null;
      }
      _logger.fine('Seller profile data: ${response.data}');
      return SellerProfile.fromJson(response.data);
    } catch (e, st) {
      _logger.severe(
        'Error fetching seller profile for user ID: $userId',
        e,
        st,
      );
      rethrow;
    }
  }

  /// Creates or updates a seller profile.
  Future<SellerProfile> upsertSellerProfile(SellerProfile profile) async {
    try {
      _logger.info('Upserting seller profile for user ID: ${profile.userId}');
      final profileJson = profile.toJson();
      // Ensure read-only fields are not sent if not managed by client
      profileJson
        ..remove('created_at')
        ..remove('updated_at')
        ..remove('approved_by')
        ..remove('approved_at');

      _logger.fine('Upserting data: $profileJson');

      final response = await _apiClient.put('/sellers/profile', data: profileJson);

      _logger.info(
        'Seller profile upserted successfully for user ID: ${profile.userId}',
      );
      return SellerProfile.fromJson(response.data);
    } catch (e, st) {
      _logger.severe(
        'Error upserting seller profile for user ID: ${profile.userId}',
        e,
        st,
      );
      rethrow;
    }
  }
}

@Riverpod(keepAlive: true)
SellerRepository sellerRepository(Ref ref) {
  final apiClient = ref.watch(simpleApiClientProvider);
  return SellerRepository(apiClient);
}
