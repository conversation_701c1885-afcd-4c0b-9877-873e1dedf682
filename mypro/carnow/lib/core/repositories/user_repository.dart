import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logging/logging.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../features/account/models/user_model.dart';
import '../networking/simple_api_client.dart';
import '../../core/models/enums.dart';

/// مزود لمستودع المستخدمين
final userRepositoryProvider = Provider<UserRepository>(
  (ref) => UserRepository(ref.watch(simpleApiClientProvider)),
);

/// مستودع لإدارة عمليات المستخدمين مع API
class UserRepository {
  UserRepository(this._apiClient);
  final SimpleApiClient _apiClient;
  final Logger _logger = Logger('UserRepository');

  /// جلب جميع المستخدمين (يمكن استخدامه للمسؤولين فقط)
  Future<List<UserModel>> getAllUsers() async {
    try {
      final response = await _apiClient.get<List<dynamic>>('/users');
      
      if (!response.isSuccess || response.data == null) {
        throw Exception(response.error ?? 'Failed to fetch users');
      }

      final users = response.data!.map((e) => UserModel.fromJson(e)).toList();
      return users;
    } catch (e) {
      _logger.severe('Error fetching all users: $e');
      rethrow;
    }
  }

  /// جلب معلومات مستخدم بواسطة auth ID
  Future<UserModel?> getUserByAuthId(String authId) async {
    try {
      final response = await _apiClient.get<Map<String, dynamic>>('/users/auth/$authId');
      
      if (!response.isSuccess || response.data == null) {
        return null;
      }
      
      return UserModel.fromJson(response.data!);
    } catch (e) {
      _logger.severe('Error fetching user by auth ID: $e');
      return null;
    }
  }

  /// جلب معلومات مستخدم محدد بواسطة المعرف
  Future<UserModel?> getUserById(String userId) async {
    try {
      _logger.info('Getting user by ID: $userId');

      final response = await _apiClient.get('/users/$userId');

      if (response.data == null) {
        _logger.warning('User not found with ID: $userId');
        return null;
      }

      return UserModel.fromJson(response.data);
    } catch (e, st) {
      _logger.severe('Error getting user by ID: $userId', e, st);
      rethrow;
    }
  }

  /// جلب معلومات المستخدم الحالي المصادق عليه
  Future<UserModel?> getCurrentUser() async {
    try {
      final response = await _apiClient.get('/users/me');
      if (response.data == null) {
        return null;
      }
      return UserModel.fromJson(response.data);
    } catch (e) {
      _logger.severe('Error fetching current user: $e');
      rethrow;
    }
  }

  /// إنشاء مستخدم جديد
  Future<UserModel> createUser(UserModel user) async {
    try {
      final userData = user.toJson();

      final response = await _apiClient.post('/users', data: userData);
      final newUser = UserModel.fromJson(response.data);

      return newUser;
    } catch (e) {
      _logger.severe('Error creating user: $e');
      rethrow;
    }
  }

  /// تحديث معلومات مستخدم موجود
  Future<UserModel> updateUser(UserModel user) async {
    try {
      final userData = user.toJson();

      final response = await _apiClient.put('/users/${user.id}', data: userData);

      final updatedUser = UserModel.fromJson(response.data);

      return updatedUser;
    } catch (e) {
      _logger.severe('Error updating user: $e');
      rethrow;
    }
  }

  /// طلب أن يصبح المستخدم بائعًا
  Future<void> requestToBeSeller(String userId) async {
    try {
      _logger.info('User $userId requested to become a seller.');
      await _apiClient.put('/users/$userId/request-seller');
    } catch (e) {
      _logger.severe('Error processing seller request for user $userId: $e');
      rethrow;
    }
  }

  /// حذف مستخدم
  Future<void> deleteUser(int id) async {
    try {
      await _apiClient.delete('/users/$id');
    } catch (e) {
      _logger.severe('Error deleting user: $e');
      rethrow;
    }
  }

  /// الحصول على أدوار المستخدم
  Future<List<UserRole>> getUserRoles(int userId) async {
    try {
      final response = await _apiClient.get('/users/$userId/roles');
      
      if (response.data == null || (response.data as List).isEmpty) {
        return [];
      }

      // تحويل أسماء الأدوار إلى النوع المحدد
      final userRoles = (response.data as List)
          .map((role) => _stringToUserRole(role['name'] as String?))
          .where((role) => role != null)
          .cast<UserRole>()
          .toList();

      return userRoles;
    } catch (e) {
      _logger.warning('Error fetching user roles: $e');
      return [];
    }
  }

  /// تحديث أدوار المستخدم
  Future<void> updateUserRoles(int userId, List<UserRole> roles) async {
    try {
      final roleNames = roles.map(_userRoleToString).toList();
      await _apiClient.put('/users/$userId/roles', data: {'roles': roleNames});
    } catch (e) {
      _logger.severe('Error updating user roles: $e');
      rethrow;
    }
  }

  /// تحويل النص إلى نوع UserRole
  UserRole? _stringToUserRole(String? roleName) {
    if (roleName == null) {
      return null;
    }

    switch (roleName) {
      case 'customer':
        return UserRole.customer;
      case 'seller':
        return UserRole.seller;
      case 'admin':
        return UserRole.admin;
      case 'moderator':
        return UserRole.moderator;
      case 'support':
        return UserRole.support;
      case 'guest':
        return UserRole.guest;
      case 'buyer':
        return UserRole.buyer;
      case 'super_admin':
        return UserRole.superAdmin;
      default:
        return null;
    }
  }

  /// تحويل UserRole إلى نص
  String _userRoleToString(UserRole role) {
    switch (role) {
      case UserRole.customer:
        return 'customer';
      case UserRole.seller:
        return 'seller';
      case UserRole.admin:
        return 'admin';
      case UserRole.moderator:
        return 'moderator';
      case UserRole.support:
        return 'support';
      case UserRole.guest:
        return 'guest';
      case UserRole.buyer:
        return 'buyer';
      case UserRole.superAdmin:
        return 'super_admin';
    }
  }

  /// Update user profile
  Future<void> updateUserProfile(String userId, UserModel userModel) async {
    try {
      _logger.info('Updating user profile: $userId');

      final userJson = userModel.toJson();
      userJson['updated_at'] = DateTime.now().toUtc().toIso8601String();

      await _apiClient.put('/users/$userId/profile', data: userJson);

      _logger.info('User profile updated successfully: $userId');
    } catch (e, st) {
      _logger.severe('Error updating user profile: $userId', e, st);
      rethrow;
    }
  }

  /// Delete user profile (soft delete)
  Future<void> deleteUserProfile(String userId) async {
    try {
      _logger.info('Deleting user profile: $userId');

      await _apiClient.delete('/users/$userId/profile');

      _logger.info('User profile deleted successfully: $userId');
    } catch (e, st) {
      _logger.severe('Error deleting user profile: $userId', e, st);
      rethrow;
    }
  }
}
