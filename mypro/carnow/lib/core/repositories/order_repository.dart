import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logging/logging.dart';

import '../models/enums.dart';
import '../models/order_item_model.dart';
import '../models/order_model.dart';
import '../networking/simple_api_client.dart';

/// مزود لمستودع الطلبات
final orderRepositoryProvider = Provider<OrderRepository>(
  (ref) => OrderRepository(ref.watch(simpleApiClientProvider)),
);

/// مستودع لإدارة عمليات الطلبات مع SimpleApiClient
class OrderRepository {
  OrderRepository(this._apiClient);
  final SimpleApiClient _apiClient;
  final Logger _logger = Logger('OrderRepository');

  /// جلب جميع الطلبات مع إمكانية التصفية والترتيب
  Future<List<OrderModel>> getAllOrders({
    String? orderBy = 'created_at',
    bool descending = true,
    int? limit,
    Map<String, dynamic>? filters,
  }) async {
    try {
      final queryParams = <String, dynamic>{};
      
      if (orderBy != null) {
        queryParams['order_by'] = orderBy;
      }
      
      if (descending) {
        queryParams['descending'] = descending;
      }
      
      if (limit != null) {
        queryParams['limit'] = limit;
      }
      
      if (filters != null) {
        queryParams.addAll(filters);
      }

      final response = await _apiClient.get<List<dynamic>>('/orders', queryParameters: queryParams);

      if (!response.isSuccess || response.data == null) {
        throw Exception(response.error ?? 'Failed to fetch orders');
      }

      final orders = response.data!.map((json) => OrderModel.fromJson(json)).toList();

      final ordersWithItems = <OrderModel>[];
      for (final order in orders) {
        if (order.id != null) {
          final items = await getOrderItems(order.id.toString());
          ordersWithItems.add(order.copyWith(items: items));
        } else {
          ordersWithItems.add(order);
        }
      }

      return ordersWithItems;
    } catch (e) {
      _logger.severe('Error fetching orders: $e');
      rethrow;
    }
  }

  /// جلب طلبات مستخدم محدد
  Future<List<OrderModel>> getUserOrders(String userId) async =>
      getAllOrders(filters: {'buyer_id': userId});

  /// جلب طلب محدد بواسطة المعرف
  Future<OrderModel?> getOrderById(String id) async {
    try {
      final response = await _apiClient.get('/orders/$id');
      
      final order = OrderModel.fromJson(response.data);

      final items = await getOrderItems(id);

      return order.copyWith(items: items);
    } catch (e) {
      _logger.severe('Error fetching order by id: $e');
      return null;
    }
  }

  /// إنشاء طلب جديد
  Future<OrderModel> createOrder(OrderModel order) async {
    try {
      final orderData = order.toJson();

      final response = await _apiClient.post('/orders', data: orderData);
      final newOrder = OrderModel.fromJson(response.data);

      return newOrder;
    } catch (e) {
      _logger.severe('Error creating order: $e');
      rethrow;
    }
  }

  /// تحديث حالة طلب
  Future<OrderModel> updateOrderStatus(
    String orderId,
    OrderStatus status,
  ) async {
    try {
      final response = await _apiClient.put('/orders/$orderId/status', data: {
        'status': status.name,
      });

      return OrderModel.fromJson(response.data);
    } catch (e) {
      _logger.severe('Error updating order status: $e');
      rethrow;
    }
  }

  /// حذف طلب
  Future<void> deleteOrder(String id) async {
    try {
      await _apiClient.delete('/orders/$id');
    } catch (e) {
      _logger.severe('Error deleting order: $e');
      rethrow;
    }
  }

  /// جلب عناصر طلب محدد
  Future<List<OrderItemModel>> getOrderItems(String orderId) async {
    try {
      final response = await _apiClient.get('/orders/$orderId/items');

      return (response.data as List).map((json) => OrderItemModel.fromJson(json)).toList();
    } catch (e) {
      _logger.warning('Error fetching order items: $e');
      return [];
    }
  }
}
