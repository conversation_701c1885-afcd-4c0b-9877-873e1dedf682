import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logging/logging.dart';

import '../../features/taxonomy/models/category_model.dart';
import '../../features/taxonomy/models/section_model.dart';
import '../../features/taxonomy/models/subcategory_model.dart';
import '../networking/simple_api_client.dart';

/// مزود لمستودع التصنيفات
final taxonomyRepositoryProvider = Provider<TaxonomyRepository>(
  (ref) => TaxonomyRepository(ref.watch(simpleApiClientProvider)),
);

/// مستودع لإدارة عمليات التصنيفات مع API
class TaxonomyRepository {
  TaxonomyRepository(this._apiClient);
  final SimpleApiClient _apiClient;
  final Logger _logger = Logger('TaxonomyRepository');

  /// جلب جميع الأقسام
  Future<List<SectionModel>> getAllSections() async {
    try {
      _logger.info('Fetching all sections');
      final response = await _apiClient.get('/taxonomy/sections');

      final sections = (response.data as List)
          .map((json) => SectionModel.fromJson(json))
          .toList();

      _logger.info('Successfully fetched ${sections.length} sections');
      return sections;
    } catch (e, st) {
      _logger.severe('Error fetching sections', e, st);
      return []; // Return empty list instead of throwing to prevent app crash
    }
  }

  /// جلب قسم محدد بواسطة المعرف
  Future<SectionModel?> getSectionById(String sectionId) async {
    try {
      _logger.info('Fetching section by ID: $sectionId');
      final response = await _apiClient.get('/taxonomy/sections/$sectionId');

      if (response.data == null) {
        _logger.warning('Section not found with ID: $sectionId');
        return null;
      }

      return SectionModel.fromJson(response.data);
    } catch (e, st) {
      _logger.severe('Error fetching section by ID: $sectionId', e, st);
      return null;
    }
  }

  /// جلب جميع الفئات
  Future<List<CategoryModel>> getAllCategories() async {
    try {
      _logger.info('Fetching all categories');
      final response = await _apiClient.get('/taxonomy/categories');

      final categories = (response.data as List)
          .map((json) => CategoryModel.fromJson(json))
          .toList();

      _logger.info('Successfully fetched ${categories.length} categories');
      return categories;
    } catch (e, st) {
      _logger.severe('Error fetching categories', e, st);
      return []; // Return empty list instead of throwing
    }
  }

  /// جلب فئة محددة بواسطة المعرف
  Future<CategoryModel?> getCategoryById(String categoryId) async {
    try {
      _logger.info('Fetching category by ID: $categoryId');
      final response = await _apiClient.get('/taxonomy/categories/$categoryId');

      if (response.data == null) {
        _logger.warning('Category not found with ID: $categoryId');
        return null;
      }

      return CategoryModel.fromJson(response.data);
    } catch (e, st) {
      _logger.severe('Error fetching category by ID: $categoryId', e, st);
      return null;
    }
  }

  /// جلب الفئات حسب القسم
  Future<List<CategoryModel>> getCategoriesBySection(String sectionId) async {
    try {
      _logger.info('Fetching categories for section: $sectionId');
      final response = await _apiClient.get('/taxonomy/sections/$sectionId/categories');

      final categories = (response.data as List)
          .map((json) => CategoryModel.fromJson(json))
          .toList();

      _logger.info('Successfully fetched ${categories.length} categories for section $sectionId');
      return categories;
    } catch (e, st) {
      _logger.severe('Error fetching categories for section: $sectionId', e, st);
      return [];
    }
  }

  /// جلب جميع الفئات الفرعية
  Future<List<SubCategoryModel>> getAllSubCategories() async {
    try {
      _logger.info('Fetching all subcategories');
      final response = await _apiClient.get('/taxonomy/subcategories');

      final subCategories = (response.data as List)
          .map((json) => SubCategoryModel.fromJson(json))
          .toList();

      _logger.info('Successfully fetched ${subCategories.length} subcategories');
      return subCategories;
    } catch (e, st) {
      _logger.severe('Error fetching subcategories', e, st);
      return [];
    }
  }

  /// جلب الفئات الفرعية حسب الفئة
  Future<List<SubCategoryModel>> getSubCategoriesByCategory(String categoryId) async {
    try {
      _logger.info('Fetching subcategories for category: $categoryId');
      final response = await _apiClient.get('/taxonomy/categories/$categoryId/subcategories');

      final subCategories = (response.data as List)
          .map((json) => SubCategoryModel.fromJson(json))
          .toList();

      _logger.info('Successfully fetched ${subCategories.length} subcategories for category $categoryId');
      return subCategories;
    } catch (e, st) {
      _logger.severe('Error fetching subcategories for category: $categoryId', e, st);
      return [];
    }
  }

  /// البحث في التصنيفات
  Future<Map<String, List<dynamic>>> searchTaxonomy(String query) async {
    try {
      _logger.info('Searching taxonomy with query: $query');
      final response = await _apiClient.get('/taxonomy/search', 
        queryParameters: {'q': query});

      final results = {
        'sections': (response.data['sections'] as List? ?? [])
            .map((json) => SectionModel.fromJson(json))
            .toList(),
        'categories': (response.data['categories'] as List? ?? [])
            .map((json) => CategoryModel.fromJson(json))
            .toList(),
        'subcategories': (response.data['subcategories'] as List? ?? [])
            .map((json) => SubCategoryModel.fromJson(json))
            .toList(),
      };

      _logger.info('Search completed. Found ${results['sections']?.length} sections, '
          '${results['categories']?.length} categories, '
          '${results['subcategories']?.length} subcategories');

      return results;
    } catch (e, st) {
      _logger.severe('Error searching taxonomy with query: $query', e, st);
      return {
        'sections': <SectionModel>[],
        'categories': <CategoryModel>[],
        'subcategories': <SubCategoryModel>[],
      };
    }
  }
} 