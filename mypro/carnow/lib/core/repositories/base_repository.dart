import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logging/logging.dart';

import '../errors/app_error.dart';
import '../errors/result.dart';
import '../networking/simple_api_client.dart';

/// مستودع أساسي لمشاركة الدوال والسلوكيات المشتركة بين المستودعات
/// Base repository to share common functions and behaviors across repositories
/// 
/// Updated for Forever Plan Architecture:
/// Flutter (UI Only) → Go API → Supabase (Data Only)
abstract class BaseRepository {
  BaseRepository(this.ref) {
    _logger = Logger(runtimeType.toString());
  }

  final Ref ref;
  late final Logger _logger;
  
  /// Get the API client for making HTTP requests to Go backend
  SimpleApiClient get apiClient => ref.read(simpleApiClientProvider);

  /// Get the API endpoint path for this repository
  String get apiPath;



  /// تنفيذ عملية مع معالجة الأخطاء
  /// Execute an operation with error handling
  Future<Result<T>> executeWithErrorHandling<T>(
    Future<T> Function() operation, {
    String? operationName,
    bool shouldLog = true,
  }) async {
    try {
      if (shouldLog) {
        _logger.info(
          'Executing operation${operationName != null ? ' $operationName' : ''}',
        );
      }

      final result = await operation();
      return Result.success(result);
    } catch (e, stackTrace) {
      if (shouldLog) {
        _logger.severe(
          'Error in ${operationName ?? 'operation'}: $e',
          e,
          stackTrace,
        );
      }
      return Result.failure(
        AppErrorFactory.fromError(e, stackTrace: stackTrace),
      );
    }
  }

  /// جلب عنصر بواسطة المعرف
  /// Get an item by ID
  Future<Result<T>> getById<T>(
    String id, {
    required T Function(Map<String, dynamic>) fromJson,
    Map<String, dynamic>? queryParams,
  }) => executeWithErrorHandling(() async {
    final params = <String, dynamic>{
      if (queryParams != null) ...queryParams,
    };
    
    final response = await apiClient.get(
      '$apiPath/$id',
      queryParameters: params.isNotEmpty ? params : null,
    );
    
    return fromJson(response.data as Map<String, dynamic>);
  }, operationName: 'getById');

  /// جلب قائمة من العناصر
  /// Get a list of items
  Future<Result<List<T>>> getAll<T>({
    required T Function(Map<String, dynamic>) fromJson,
    Map<String, dynamic>? queryParams,
  }) => executeWithErrorHandling(() async {
    final response = await apiClient.get(
      apiPath,
      queryParameters: queryParams,
    );
    
    final List<dynamic> data = response.data as List<dynamic>;
    return data
        .map((json) => fromJson(json as Map<String, dynamic>))
        .toList();
  }, operationName: 'getAll');

  /// إنشاء عنصر جديد
  /// Create a new item
  Future<Result<T>> create<T>(
    Map<String, dynamic> data, {
    required T Function(Map<String, dynamic>) fromJson,
  }) => executeWithErrorHandling(() async {
    // إزالة الحقول الفارغة
    // Remove null values
    data.removeWhere((key, value) => value == null);

    final response = await apiClient.post(
      apiPath,
      data: data,
    );

    return fromJson(response.data as Map<String, dynamic>);
  }, operationName: 'create');

  /// تحديث عنصر موجود
  /// Update an existing item
  Future<Result<T>> update<T>(
    String id,
    Map<String, dynamic> data, {
    required T Function(Map<String, dynamic>) fromJson,
  }) => executeWithErrorHandling(() async {
    // إزالة الحقول التي لا يجب تحديثها
    // Remove fields that should not be updated
    data.removeWhere((key, value) => value == null);
    data.remove('id');
    data.remove('created_at');

    final response = await apiClient.put(
      '$apiPath/$id',
      data: data,
    );

    // Return the updated object
    return fromJson(response.data);
  }, operationName: 'update');

  /// حذف عنصر
  /// Delete an item
  Future<Result<void>> delete(String id) =>
      executeWithErrorHandling(() async {
        await apiClient.delete('$apiPath/$id');
      }, operationName: 'delete');
}

/// مزود لقالب المستودع
/// A provider factory for repositories
class RepositoryProvider {
  /// إنشاء مزود لمستودع
  /// Create a provider for a repository
  static Provider<T> create<T extends BaseRepository>(
    T Function(Ref) create,
  ) => Provider<T>((ref) {
    return create(ref);
  });
}
