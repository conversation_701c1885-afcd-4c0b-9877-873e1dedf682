// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'support_ticket_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$supportTicketRepositoryHash() =>
    r'5d402da9d501e8905e66c27c6ab5a423050d1685';

/// Provider for SupportTicketRepository
///
/// Copied from [supportTicketRepository].
@ProviderFor(supportTicketRepository)
final supportTicketRepositoryProvider =
    AutoDisposeProvider<SupportTicketRepository>.internal(
      supportTicketRepository,
      name: r'supportTicketRepositoryProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$supportTicketRepositoryHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef SupportTicketRepositoryRef =
    AutoDisposeProviderRef<SupportTicketRepository>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
