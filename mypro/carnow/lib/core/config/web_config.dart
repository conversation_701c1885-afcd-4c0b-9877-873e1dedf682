// Web-specific configuration for CarNow
//
// This file contains configurations and optimizations specifically for web platform

import 'package:flutter/foundation.dart';

class WebConfig {
  /// Configure web-specific settings
  static void configure() {
    if (kIsWeb) {
      // Enable web optimizations
      debugPrint('Configuring CarNow for Web platform');

      // Set any web-specific configurations here
      _configureErrorHandling();
      _configurePerformance();
    }
  }

  /// Configure error handling for web
  static void _configureErrorHandling() {
    FlutterError.onError = (FlutterErrorDetails details) {
      if (kDebugMode) {
        FlutterError.presentError(details);
      } else {
        // In production, log errors appropriately
        debugPrint('Flutter Error: ${details.exception}');
      }
    };
  }

  /// Configure performance optimizations for web
  static void _configurePerformance() {
    // Web-specific performance configurations can be added here
    debugPrint('Web performance optimizations applied');
  }

  /// Check if running on web
  static bool get isWeb => kIsWeb;

  /// Get web-specific API endpoint
  static String get webApiEndpoint {
    if (kDebugMode) {
      return 'http://127.0.1:54321';
    } else {
      return 'https://your-production-url.com';
    }
  }
}
