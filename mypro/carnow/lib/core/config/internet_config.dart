/// تكوين نظام الإنترنت المطلوب
/// 
/// يحدد هذا الملف السياسات المتعلقة بمتطلبات الإنترنت في التطبيق
class InternetConfig {
  // منع إنشاء instance
  InternetConfig._();

  /// هل التطبيق يتطلب إنترنت دائماً؟
  static const bool requiresInternet = true;

  /// هل نعرض رسائل "عدم الاتصال" في المحتوى؟
  static const bool showOfflineMessages = false;

  /// هل نسمح بالعمل في وضع offline؟
  static const bool allowOfflineMode = false;

  /// هل نعرض إشعارات تغيير حالة الاتصال؟
  static const bool showConnectionToasts = false;

  /// هل نخزن البيانات محلياً للاستخدام offline؟
  static const bool enableOfflineStorage = false;

  /// هل نقوم بمزامنة البيانات عند عودة الاتصال؟
  static const bool enableDataSync = false;

  /// رسالة عدم الاتصال الافتراضية
  static const String defaultOfflineMessage = 'يتطلب هذا التطبيق اتصالاً بالإنترنت للعمل';

  /// رسالة فحص الاتصال
  static const String checkingConnectionMessage = 'جاري فحص الاتصال...';

  /// تحديد ما إذا كانت الميزة تتطلب إنترنت
  static bool featureRequiresInternet(String featureName) {
    // جميع الميزات تتطلب إنترنت في هذا التطبيق
    return true;
  }

  /// تحديد ما إذا كان يجب عرض overlay الإنترنت
  static bool shouldShowInternetOverlay() {
    return requiresInternet;
  }

  /// تحديد ما إذا كان يجب منع التفاعل عند عدم وجود إنترنت
  static bool shouldBlockInteraction() {
    return requiresInternet;
  }
} 