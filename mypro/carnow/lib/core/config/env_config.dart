import 'package:flutter/foundation.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'local_env_config.dart';

/// Environment configuration manager for CarNow
/// Handles loading and accessing environment variables with fallbacks
class EnvConfig {
  static bool _isLoaded = false;

  /// Initialize environment configuration
  static Future<void> initialize() async {
    if (_isLoaded) return;

    try {
      await dotenv.load(fileName: '.env');
      _isLoaded = true;
      debugPrint('Environment configuration loaded successfully from .env file');
    } catch (e) {
      debugPrint('Warning: Could not load .env file: $e');
      debugPrint('Using local configuration fallbacks from LocalEnvConfig');
      
      // Initialize dotenv with empty values to prevent null errors
      dotenv.testLoad(fileInput: '');
      
      // Mark as loaded to use local fallbacks
      _isLoaded = true;
    }
  }

  /// Get environment variable with fallback
  static String _getEnv(String key, {String fallback = ''}) {
    if (!_isLoaded) {
      debugPrint('Warning: Environment not loaded, using fallback for $key');
      return fallback;
    }
    
    // Get value from dotenv, but if it's null or empty, use fallback
    final value = dotenv.env[key];
    if (value == null || value.isEmpty || value.contains(':-""}')) {
      debugPrint('Using fallback for $key: $fallback');
      return fallback;
    }
    
    return value;
  }

  // Sentry Configuration
  static String get sentryDsn => _getEnv('SENTRY_DSN');
  static bool get isSentryEnabled =>
      sentryDsn.isNotEmpty && !sentryDsn.contains('your-sentry-dsn');

  // Supabase Configuration
  static String get supabaseUrl =>
      _getEnv('SUPABASE_URL', fallback: 'https://lpxtghyvxuenyyisrrro.supabase.co');

  static String get supabaseAnonKey =>
      _getEnv('SUPABASE_ANON_KEY', fallback: LocalEnvConfig.supabaseAnonKey);

  

  // Google OAuth Configuration
  static String get googleWebClientId => _getEnv(
    'CARNOW_GOOGLE_CLIENT_ID',
    fallback: LocalEnvConfig.googleWebClientId,
  );
  static String get googleAndroidClientId => _getEnv(
    'CARNOW_GOOGLE_ANDROID_CLIENT_ID',
    fallback: LocalEnvConfig.googleAndroidClientId,
  );
  static String get webRedirectUrl =>
      _getEnv('WEB_REDIRECT_URL', fallback: LocalEnvConfig.webRedirectUrl);
  
  static String get googleMobileRedirectUrl => _getEnv(
    'GOOGLE_MOBILE_REDIRECT_URL',
    fallback: LocalEnvConfig.googleMobileRedirectUrl,
  );

  static String get googleMobileRedirectUrlSecondary => _getEnv(
    'GOOGLE_MOBILE_REDIRECT_URL_SECONDARY',
    fallback: LocalEnvConfig.googleMobileRedirectUrlSecondary,
  );

  static String get webRedirectUrlFallback => _getEnv(
        'WEB_REDIRECT_URL_FALLBACK',
        fallback: LocalEnvConfig.webRedirectUrlFallback,
      );

  // Performance Configuration
  static bool get enablePerformanceMonitoring =>
      _getEnv(
        'ENABLE_PERFORMANCE_MONITORING',
        fallback: LocalEnvConfig.enablePerformanceMonitoring.toString(),
      ) ==
      'true';
  static double get performanceSampleRate =>
      double.tryParse(
        _getEnv(
          'PERFORMANCE_SAMPLE_RATE',
          fallback: LocalEnvConfig.performanceSampleRate.toString(),
        ),
      ) ??
      LocalEnvConfig.performanceSampleRate;

  // API Configuration
  static String get apiBaseUrl =>
      _getEnv('API_BASE_URL', fallback: LocalEnvConfig.apiBaseUrl);

  /// Validate critical configuration
  static bool validateConfig() {
    final issues = <String>[];

    if (!isSentryEnabled && !kDebugMode) {
      issues.add('Sentry DSN not configured for production');
    }

    if (supabaseUrl.isEmpty) {
      issues.add('Supabase URL not configured');
    }

    if (supabaseAnonKey.isEmpty) {
      issues.add('Supabase Anon Key not configured');
    }

    // تحقق من صحة مفتاح Supabase
    if (supabaseAnonKey.contains('demo') ||
        supabaseAnonKey.contains('your-anon-key')) {
      issues.add(
        'Supabase Anon Key appears to be a placeholder - update with real key',
      );
    }



    // تحقق من صحة URL
    if (!supabaseUrl.startsWith('https://')) {
      issues.add('Supabase URL should use HTTPS');
    }

    if (issues.isNotEmpty) {
      debugPrint('Configuration Issues:');
      for (final issue in issues) {
        debugPrint('- $issue');
      }
      return false;
    }

    return true;
  }

  /// Print configuration summary (for debugging)
  static void printConfigSummary() {
    if (!kDebugMode) return;

    debugPrint('=== CarNow Configuration Summary ===');
    debugPrint('Environment loaded: $_isLoaded');
    
    // Handle corrupted URLs safely
    final url = supabaseUrl;
    final displayUrl = url.contains(':-""}') ? 'CORRUPTED - Using fallback' : url;
    
    debugPrint('Using fallback for SENTRY_DSN: $sentryDsn');
    debugPrint('Sentry enabled: $isSentryEnabled');
    debugPrint('Supabase URL: $displayUrl');
    debugPrint('Supabase Key configured: ${supabaseAnonKey.isNotEmpty}');
    debugPrint('Using fallback for ENABLE_PERFORMANCE_MONITORING: $enablePerformanceMonitoring');
    debugPrint('Performance monitoring: $enablePerformanceMonitoring');
    debugPrint('API Base URL: $apiBaseUrl');
    debugPrint('Google Web Client ID: $googleWebClientId');
    debugPrint('Google Android Client ID: $googleAndroidClientId');
    debugPrint('===================================');
  }
}
