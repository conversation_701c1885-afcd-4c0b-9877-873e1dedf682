# 🔗 Server Configuration Guide

## 📍 تغيير عنوان السيرفر بسهولة

### 🎯 الملف الوحيد للتعديل

عند تغيير الاستضافة، قم بتعديل **ملف واحد فقط**:

```
lib/core/config/server_urls.dart
```

### 🔧 كيفية التعديل

افتح الملف `server_urls.dart` وعدل هذه الأسطر:

```dart
/// Main production backend server
/// Change this when hosting provider changes
static const String productionBackend = 'https://backend-go-8klm.onrender.com';

/// Staging backend server (if different from production)
static const String stagingBackend = 'https://backend-go-8klm.onrender.com';

/// Development backend server (if different)
static const String developmentBackend = 'https://backend-go-8klm.onrender.com';
```

### 📋 مثال على التغيير

**قبل التغيير:**
```dart
static const String productionBackend = 'https://backend-go-8klm.onrender.com';
```

**بعد التغيير:**
```dart
static const String productionBackend = 'https://your-new-hosting.com';
```

### ⚡ النظام السريع الجديد

التطبيق يعمل بسرعة عالية:

1. **عند بدء التطبيق (مرة واحدة فقط):**
   - يتحقق من السيرفر المحلي بسرعة (500ms فقط)
   - إذا وجده، يستخدمه
   - إذا لم يجده، يذهب للسيرفر المستضاف

2. **بعد ذلك:**
   - يستخدم النتيجة المحفوظة (cache)
   - لا يوجد أي تأخير أو فحص إضافي
   - الأداء مثالي

### 🏠 السيرفر المحلي

للتطوير المحلي، السيرفر المحلي معرف في:

```dart
/// Local development server (localhost)
static const String localBackend = 'http://localhost:8080';

/// Android emulator local server
static const String androidEmulatorBackend = 'http://********:8080';
```

### 🔍 فحص التكوين

لطباعة التكوين الحالي:

```dart
// في الكود
BackendConfig.printServerConfiguration();

// أو
ServerUrls.printConfiguration();
```

### 📊 معلومات إضافية

- **Health Check Timeout:** 500ms فقط (سريع جداً)
- **Cache:** مرة واحدة عند بدء التطبيق
- **API Version:** v1
- **API Base Path:** /api/v1
- **Performance:** لا يوجد تأثير على الأداء

### 🚀 الاستخدام

```dart
// الحصول على عنوان السيرفر الحالي (فوري)
final serverUrl = BackendConfig.baseUrl;

// بناء رابط API (فوري)
final apiUrl = BackendConfig.buildApiUrl('/users');

// فحص صحة السيرفر (فوري)
final healthUrl = BackendConfig.healthUrl;
```

### ⚡ مميزات النظام الجديد

1. **سرعة عالية:** فحص واحد فقط عند بدء التطبيق
2. **لا تأثير على الأداء:** النتيجة محفوظة في الذاكرة
3. **بسيط:** منطق واضح ومفهوم
4. **مرن:** سهل التعديل من مكان واحد
5. **موثوق:** يعمل دائماً حتى لو فشل الفحص

### 🔄 كيفية العمل

```
بدء التطبيق
    ↓
فحص سريع للسيرفر المحلي (500ms)
    ↓
إذا وجد المحلي → استخدمه
إذا لم يجده → استخدم المستضاف
    ↓
احفظ النتيجة في الذاكرة
    ↓
استخدم النتيجة المحفوظة دائماً
```

### ⚡ نصائح سريعة

1. **تغيير الاستضافة:** عدل `productionBackend` فقط
2. **إضافة بيئة جديدة:** أضف متغير جديد في `ServerUrls`
3. **تغيير المنفذ المحلي:** عدل `localBackend`
4. **إعادة فحص:** استخدم `BackendConfig.clearCache()`

### 🎯 الأداء

- **وقت الفحص:** 500ms فقط
- **الاستخدام اللاحق:** فوري (0ms)
- **استهلاك الذاكرة:** ضئيل جداً
- **استهلاك الشبكة:** مرة واحدة فقط

---

## 🎉 تم! 

الآن لديك نظام سريع وبسيط لتغيير عنوان السيرفر من مكان واحد فقط! 🚀 