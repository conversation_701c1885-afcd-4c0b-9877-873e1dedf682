import 'package:flutter/foundation.dart';

/// Configuration class for performance monitoring
class PerformanceConfig {
  /// Whether performance monitoring is enabled
  static bool get isEnabled => !kDebugMode || _forceEnable;
  
  /// Force enable performance monitoring (for testing)
  static bool _forceEnable = false;
  
  /// Enable performance monitoring regardless of build mode
  static void forceEnable() {
    _forceEnable = true;
  }
  
  /// Disable forced performance monitoring
  static void disableForce() {
    _forceEnable = false;
  }
  
  /// Maximum number of performance events to keep in memory
  static const int maxEventsInMemory = 1000;
  
  /// Maximum number of performance metrics to keep
  static const int maxMetricsInMemory = 500;
  
  /// Duration after which old events are automatically cleared
  static const Duration eventRetentionDuration = Duration(hours: 1);
  
  /// Interval for automatic memory usage tracking
  static const Duration memoryTrackingInterval = Duration(minutes: 5);
  
  /// Interval for automatic frame rate tracking
  static const Duration frameRateTrackingInterval = Duration(seconds: 30);
  
  /// Threshold for slow operations (in milliseconds)
  static const int slowOperationThreshold = 1000;
  
  /// Threshold for very slow operations (in milliseconds)
  static const int verySlowOperationThreshold = 5000;
  
  /// Whether to track memory usage automatically
  static bool get trackMemoryUsage => isEnabled;
  
  /// Whether to track frame rate automatically
  static bool get trackFrameRate => isEnabled && kDebugMode;
  
  /// Whether to log performance warnings
  static bool get logPerformanceWarnings => isEnabled;
  
  /// Whether to send performance data to analytics
  static bool get sendToAnalytics => isEnabled && !kDebugMode;
  
  /// Performance monitoring categories
  static const Map<String, bool> categories = {
    'network': true,
    'database': true,
    'ui': true,
    'navigation': true,
    'authentication': true,
    'file_operations': true,
    'image_processing': true,
    'search': true,
    'cache': true,
    'background_tasks': true,
  };
  
  /// Check if a category is enabled for monitoring
  static bool isCategoryEnabled(String category) {
    return isEnabled && (categories[category] ?? false);
  }
  
  /// Performance alert thresholds
  static const Map<String, int> alertThresholds = {
    'app_startup': 3000, // 3 seconds
    'screen_load': 2000, // 2 seconds
    'api_call': 5000, // 5 seconds
    'database_query': 1000, // 1 second
    'image_load': 3000, // 3 seconds
    'search_query': 1500, // 1.5 seconds
    'file_upload': 10000, // 10 seconds
    'authentication': 3000, // 3 seconds
  };
  
  /// Get alert threshold for a specific operation
  static int getAlertThreshold(String operation) {
    return alertThresholds[operation] ?? slowOperationThreshold;
  }
  
  /// Check if an operation duration exceeds the alert threshold
  static bool exceedsThreshold(String operation, Duration duration) {
    final threshold = getAlertThreshold(operation);
    return duration.inMilliseconds > threshold;
  }
  
  /// Get performance level based on duration
  static PerformanceLevel getPerformanceLevel(Duration duration) {
    final ms = duration.inMilliseconds;
    
    if (ms < 100) return PerformanceLevel.excellent;
    if (ms < 500) return PerformanceLevel.good;
    if (ms < slowOperationThreshold) return PerformanceLevel.acceptable;
    if (ms < verySlowOperationThreshold) return PerformanceLevel.slow;
    return PerformanceLevel.verySlow;
  }
}

/// Performance levels for operations
enum PerformanceLevel {
  excellent,
  good,
  acceptable,
  slow,
  verySlow,
}

/// Extension methods for PerformanceLevel
extension PerformanceLevelX on PerformanceLevel {
  String get displayName {
    switch (this) {
      case PerformanceLevel.excellent:
        return 'Excellent';
      case PerformanceLevel.good:
        return 'Good';
      case PerformanceLevel.acceptable:
        return 'Acceptable';
      case PerformanceLevel.slow:
        return 'Slow';
      case PerformanceLevel.verySlow:
        return 'Very Slow';
    }
  }
  
  bool get isAcceptable {
    return index <= PerformanceLevel.acceptable.index;
  }
  
  bool get needsAttention {
    return index >= PerformanceLevel.slow.index;
  }
}