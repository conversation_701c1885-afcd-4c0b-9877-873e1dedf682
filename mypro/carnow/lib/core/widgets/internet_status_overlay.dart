import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Simple connection status overlay following Forever Plan Architecture
/// Removed enhanced connection provider as it violates the architecture rules
class ConnectionStatusOverlay extends ConsumerWidget {
  const ConnectionStatusOverlay({
    super.key,
    required this.child,
  });

  final Widget child;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Simplified - let API calls handle connection errors directly
    // No complex connection monitoring needed
    return child;
  }
} 