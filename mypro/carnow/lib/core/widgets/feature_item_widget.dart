import 'package:flutter/material.dart';

/// مكون لعرض ميزة منتج كزوج من مفتاح/قيمة
class FeatureItemWidget extends StatelessWidget {
  const FeatureItemWidget({
    required this.name,
    required this.value,
    required this.onDelete,
    super.key,
  });
  final String name;
  final String value;
  final VoidCallback onDelete;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerHighest.withAlpha(
          (0.5 * 255).toInt(),
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          // اسم الميزة
          Expanded(
            flex: 3,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'الميزة',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
                const SizedBox(height: 4),
                Text(name, style: theme.textTheme.titleSmall),
              ],
            ),
          ),

          // فاصل
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8),
            child: Container(
              height: 40,
              width: 1,
              color: theme.colorScheme.outline.withAlpha((0.5 * 255).toInt()),
            ),
          ),

          // قيمة الميزة
          Expanded(
            flex: 5,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'القيمة',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
                const SizedBox(height: 4),
                Text(value, style: theme.textTheme.titleSmall),
              ],
            ),
          ),

          // زر الحذف
          IconButton(
            icon: const Icon(Icons.delete_outline, size: 20),
            color: theme.colorScheme.error,
            onPressed: onDelete,
            tooltip: 'حذف الميزة',
          ),
        ],
      ),
    );
  }
}
