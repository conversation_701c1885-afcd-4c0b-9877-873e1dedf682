import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:shimmer/shimmer.dart';
import '../services/image_optimization_service.dart';

/// Optimized image widget with automatic optimization and caching
class OptimizedImage extends StatelessWidget {
  final String imageUrl;
  final double? width;
  final double? height;
  final BoxFit fit;
  final int quality;
  final bool thumbnail;
  final Widget? placeholder;
  final Widget? errorWidget;
  final Duration fadeInDuration;
  final Duration placeholderFadeInDuration;
  final bool enableMemoryCache;
  final bool enableDiskCache;
  final Map<String, String>? httpHeaders;

  const OptimizedImage({
    super.key,
    required this.imageUrl,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.quality = 85,
    this.thumbnail = false,
    this.placeholder,
    this.errorWidget,
    this.fadeInDuration = const Duration(milliseconds: 300),
    this.placeholderFadeInDuration = const Duration(milliseconds: 300),
    this.enableMemoryCache = true,
    this.enableDiskCache = true,
    this.httpHeaders,
  });

  @override
  Widget build(BuildContext context) {
    if (imageUrl.isEmpty) {
      return _buildErrorWidget();
    }

    final optimizedUrl = ImageOptimizationService.getOptimizedImageUrl(
      imageUrl,
      width: width?.toInt(),
      height: height?.toInt(),
      quality: quality,
      thumbnail: thumbnail,
    );

    return CachedNetworkImage(
      imageUrl: optimizedUrl,
      width: width,
      height: height,
      fit: fit,
      placeholder: (context, url) => _buildPlaceholder(),
      errorWidget: (context, url, error) => _buildErrorWidget(),
      fadeInDuration: fadeInDuration,
      placeholderFadeInDuration: placeholderFadeInDuration,
      memCacheWidth: enableMemoryCache ? width?.toInt() : null,
      memCacheHeight: enableMemoryCache ? height?.toInt() : null,
      maxWidthDiskCache: enableDiskCache ? 1200 : null,
      maxHeightDiskCache: enableDiskCache ? 800 : null,
      httpHeaders: httpHeaders,
      useOldImageOnUrlChange: true,
      filterQuality: FilterQuality.medium,
    );
  }

  Widget _buildPlaceholder() {
    if (placeholder != null) {
      return placeholder!;
    }

    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          color: Colors.grey[300],
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  Widget _buildErrorWidget() {
    if (errorWidget != null) {
      return errorWidget!;
    }

    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Icon(
        Icons.image_not_supported_outlined,
        color: Colors.grey,
        size: 32,
      ),
    );
  }
}

/// Optimized avatar image with circular shape
class OptimizedAvatar extends StatelessWidget {
  final String imageUrl;
  final double radius;
  final int quality;
  final Widget? placeholder;
  final Widget? errorWidget;

  const OptimizedAvatar({
    super.key,
    required this.imageUrl,
    this.radius = 20,
    this.quality = 85,
    this.placeholder,
    this.errorWidget,
  });

  @override
  Widget build(BuildContext context) {
    return ClipOval(
      child: OptimizedImage(
        imageUrl: imageUrl,
        width: radius * 2,
        height: radius * 2,
        fit: BoxFit.cover,
        quality: quality,
        thumbnail: true,
        placeholder: placeholder ?? _buildDefaultPlaceholder(),
        errorWidget: errorWidget ?? _buildDefaultErrorWidget(),
      ),
    );
  }

  Widget _buildDefaultPlaceholder() {
    return Container(
      width: radius * 2,
      height: radius * 2,
      decoration: const BoxDecoration(
        color: Colors.grey,
        shape: BoxShape.circle,
      ),
      child: Icon(Icons.person, color: Colors.white, size: radius),
    );
  }

  Widget _buildDefaultErrorWidget() {
    return Container(
      width: radius * 2,
      height: radius * 2,
      decoration: const BoxDecoration(
        color: Colors.grey,
        shape: BoxShape.circle,
      ),
      child: Icon(Icons.person, color: Colors.white, size: radius),
    );
  }
}

/// Progressive image loading with multiple quality levels
class ProgressiveImage extends StatefulWidget {
  final String imageUrl;
  final double? width;
  final double? height;
  final BoxFit fit;
  final Widget? placeholder;
  final Widget? errorWidget;

  const ProgressiveImage({
    super.key,
    required this.imageUrl,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.placeholder,
    this.errorWidget,
  });

  @override
  State<ProgressiveImage> createState() => _ProgressiveImageState();
}

class _ProgressiveImageState extends State<ProgressiveImage> {
  final bool _lowQualityLoaded = false;

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Low quality image (loads first)
        if (!_lowQualityLoaded)
          OptimizedImage(
            imageUrl: widget.imageUrl,
            width: widget.width,
            height: widget.height,
            fit: widget.fit,
            quality: 30,
            thumbnail: true,
            placeholder: widget.placeholder,
            errorWidget: widget.errorWidget,
          ),

        // High quality image (loads after low quality)
        OptimizedImage(
          imageUrl: widget.imageUrl,
          width: widget.width,
          height: widget.height,
          fit: widget.fit,
          quality: 85,
          placeholder: _lowQualityLoaded ? null : widget.placeholder,
          errorWidget: widget.errorWidget,
        ),
      ],
    );
  }
}

/// Image gallery with lazy loading
class LazyImageGallery extends StatefulWidget {
  final List<String> imageUrls;
  final double itemWidth;
  final double itemHeight;
  final int crossAxisCount;
  final double crossAxisSpacing;
  final double mainAxisSpacing;
  final void Function(String imageUrl)? onImageTap;

  const LazyImageGallery({
    super.key,
    required this.imageUrls,
    this.itemWidth = 150,
    this.itemHeight = 150,
    this.crossAxisCount = 2,
    this.crossAxisSpacing = 8,
    this.mainAxisSpacing = 8,
    this.onImageTap,
  });

  @override
  State<LazyImageGallery> createState() => _LazyImageGalleryState();
}

class _LazyImageGalleryState extends State<LazyImageGallery> {
  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: widget.crossAxisCount,
        crossAxisSpacing: widget.crossAxisSpacing,
        mainAxisSpacing: widget.mainAxisSpacing,
        childAspectRatio: widget.itemWidth / widget.itemHeight,
      ),
      itemCount: widget.imageUrls.length,
      itemBuilder: (context, index) {
        return GestureDetector(
          onTap: () => widget.onImageTap?.call(widget.imageUrls[index]),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: OptimizedImage(
              imageUrl: widget.imageUrls[index],
              width: widget.itemWidth,
              height: widget.itemHeight,
              fit: BoxFit.cover,
              thumbnail: true,
            ),
          ),
        );
      },
    );
  }
}

/// Hero image with zoom functionality
class HeroOptimizedImage extends StatelessWidget {
  final String imageUrl;
  final String heroTag;
  final double? width;
  final double? height;
  final BoxFit fit;
  final Widget? placeholder;
  final Widget? errorWidget;

  const HeroOptimizedImage({
    super.key,
    required this.imageUrl,
    required this.heroTag,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.placeholder,
    this.errorWidget,
  });

  @override
  Widget build(BuildContext context) {
    return Hero(
      tag: heroTag,
      child: GestureDetector(
        onTap: () => _showFullScreenImage(context),
        child: OptimizedImage(
          imageUrl: imageUrl,
          width: width,
          height: height,
          fit: fit,
          placeholder: placeholder,
          errorWidget: errorWidget,
        ),
      ),
    );
  }

  void _showFullScreenImage(BuildContext context) {
    Navigator.of(context).push(
      PageRouteBuilder(
        opaque: false,
        barrierColor: Colors.black87,
        pageBuilder: (context, animation, secondaryAnimation) {
          return Scaffold(
            backgroundColor: Colors.transparent,
            body: GestureDetector(
              onTap: () => Navigator.of(context).pop(),
              child: Center(
                child: Hero(
                  tag: heroTag,
                  child: InteractiveViewer(
                    child: OptimizedImage(
                      imageUrl: imageUrl,
                      fit: BoxFit.contain,
                      quality: 95,
                    ),
                  ),
                ),
              ),
            ),
          );
        },
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return FadeTransition(opacity: animation, child: child);
        },
      ),
    );
  }
}

/// Image with loading progress indicator
class ProgressIndicatorImage extends StatelessWidget {
  final String imageUrl;
  final double? width;
  final double? height;
  final BoxFit fit;

  const ProgressIndicatorImage({
    super.key,
    required this.imageUrl,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
  });

  @override
  Widget build(BuildContext context) {
    return CachedNetworkImage(
      imageUrl: ImageOptimizationService.getOptimizedImageUrl(imageUrl),
      width: width,
      height: height,
      fit: fit,
      progressIndicatorBuilder: (context, url, downloadProgress) {
        return Container(
          width: width,
          height: height,
          decoration: BoxDecoration(
            color: Colors.grey[200],
            borderRadius: BorderRadius.circular(8),
          ),
          child: Center(
            child: CircularProgressIndicator(
              value: downloadProgress.progress,
              strokeWidth: 2,
            ),
          ),
        );
      },
      errorWidget: (context, url, error) {
        return Container(
          width: width,
          height: height,
          decoration: BoxDecoration(
            color: Colors.grey[200],
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Icon(Icons.error_outline, color: Colors.red, size: 32),
        );
      },
    );
  }
}
