import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logging/logging.dart';



/// Widget to display network error messages with retry functionality
class NetworkErrorWidget extends ConsumerWidget {
  const NetworkErrorWidget({
    super.key,
    required this.onRetry,
    this.title,
    this.message,
    this.showRetryButton = true,
    this.retryButtonText,
  });

  final VoidCallback onRetry;
  final String? title;
  final String? message;
  final bool showRetryButton;
  final String? retryButtonText;

  static final _logger = Logger('NetworkErrorWidget');

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final locale = Localizations.localeOf(context);
    final isArabic = locale.languageCode == 'ar';

    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // خطأ الشبكة أيقونة
          Icon(
            Icons.cloud_off_outlined,
            size: 64,
            color: theme.colorScheme.error,
          ),
          const SizedBox(height: 16),
          
          // عنوان الخطأ
          Text(
            title ?? (isArabic ? 'مشكلة في الاتصال' : 'Connection Problem'),
            style: theme.textTheme.headlineSmall?.copyWith(
              color: theme.colorScheme.onSurface,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          
          // رسالة الخطأ
          Text(
            message ?? (isArabic 
              ? 'تعذر الاتصال بالخادم. تحقق من اتصالك بالإنترنت وحاول مرة أخرى.'
              : 'Unable to connect to server. Please check your internet connection and try again.'),
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
          
          if (showRetryButton) ...[
            const SizedBox(height: 24),
            
            // زر إعادة المحاولة
            FilledButton.icon(
              onPressed: () {
                _logger.info('User requested retry');
                onRetry();
              },
              icon: const Icon(Icons.refresh),
              label: Text(
                retryButtonText ?? (isArabic ? 'إعادة المحاولة' : 'Retry'),
              ),
              style: FilledButton.styleFrom(
                padding: const EdgeInsets.symmetric(
                  horizontal: 32,
                  vertical: 12,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }
}

/// Compact version of network error widget for inline use
class CompactNetworkErrorWidget extends ConsumerWidget {
  const CompactNetworkErrorWidget({
    super.key,
    required this.onRetry,
    this.message,
  });

  final VoidCallback onRetry;
  final String? message;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final locale = Localizations.localeOf(context);
    final isArabic = locale.languageCode == 'ar';

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.errorContainer,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: theme.colorScheme.error.withValues(alpha: 0.2),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.cloud_off_outlined,
            size: 20,
            color: theme.colorScheme.error,
          ),
          const SizedBox(width: 12),
          
          Expanded(
            child: Text(
              message ?? (isArabic ? 'مشكلة في الاتصال' : 'Connection issue'),
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onErrorContainer,
              ),
            ),
          ),
          
          const SizedBox(width: 8),
          
          // زر إعادة المحاولة المدمج
          TextButton.icon(
            onPressed: onRetry,
            icon: Icon(
              Icons.refresh,
              size: 16,
              color: theme.colorScheme.primary,
            ),
            label: Text(
              isArabic ? 'إعادة' : 'Retry',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.primary,
                fontWeight: FontWeight.w500,
              ),
            ),
            style: TextButton.styleFrom(
              padding: const EdgeInsets.symmetric(
                horizontal: 8,
                vertical: 4,
              ),
              minimumSize: Size.zero,
              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
            ),
          ),
        ],
      ),
    );
  }
}

/// Shows a network error dialog
void showNetworkErrorDialog(
  BuildContext context, {
  required VoidCallback onRetry,
  String? title,
  String? message,
}) {
  showDialog(
    context: context,
    builder: (context) => AlertDialog(
      content: NetworkErrorWidget(
        onRetry: () {
          Navigator.of(context).pop();
          onRetry();
        },
        title: title,
        message: message,
      ),
    ),
  );
}

/// Shows a network error bottom sheet
void showNetworkErrorBottomSheet(
  BuildContext context, {
  required VoidCallback onRetry,
  String? title,
  String? message,
}) {
  showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    shape: const RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
    ),
    builder: (context) => Container(
      padding: EdgeInsets.only(
        bottom: MediaQuery.of(context).viewInsets.bottom,
      ),
      child: NetworkErrorWidget(
        onRetry: () {
          Navigator.of(context).pop();
          onRetry();
        },
        title: title,
        message: message,
      ),
    ),
  );
}