import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:carnow/core/theme/app_colors.dart';

/// Custom Text Field Widget with consistent styling
class CustomTextField extends StatefulWidget {
  const CustomTextField({
    super.key,
    this.controller,
    this.label,
    this.hint,
    this.prefixIcon,
    this.suffixIcon,
    this.onSuffixIconPressed,
    this.validator,
    this.keyboardType,
    this.inputFormatters,
    this.obscureText = false,
    this.enabled = true,
    this.maxLines = 1,
    this.maxLength,
    this.onTap,
    this.onChanged,
    this.onSubmitted,
    this.focusNode,
    this.textCapitalization = TextCapitalization.none,
    this.textDirection,
    this.readOnly = false,
    this.fillColor,
    this.contentPadding,
  });
  final TextEditingController? controller;
  final String? label;
  final String? hint;
  final IconData? prefixIcon;
  final IconData? suffixIcon;
  final VoidCallback? onSuffixIconPressed;
  final String? Function(String?)? validator;
  final TextInputType? keyboardType;
  final List<TextInputFormatter>? inputFormatters;
  final bool obscureText;
  final bool enabled;
  final int? maxLines;
  final int? maxLength;
  final VoidCallback? onTap;
  final void Function(String)? onChanged;
  final void Function(String)? onSubmitted;
  final FocusNode? focusNode;
  final TextCapitalization textCapitalization;
  final TextDirection? textDirection;
  final bool readOnly;
  final Color? fillColor;
  final EdgeInsetsGeometry? contentPadding;

  @override
  State<CustomTextField> createState() => _CustomTextFieldState();
}

class _CustomTextFieldState extends State<CustomTextField> {
  late bool _obscureText;
  bool _isFocused = false;

  @override
  void initState() {
    super.initState();
    _obscureText = widget.obscureText;

    // Add focus listener if focusNode is provided
    widget.focusNode?.addListener(_onFocusChange);
  }

  @override
  void dispose() {
    widget.focusNode?.removeListener(_onFocusChange);
    super.dispose();
  }

  void _onFocusChange() {
    setState(() {
      _isFocused = widget.focusNode?.hasFocus ?? false;
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.label != null) ...[
          Text(
            widget.label!,
            style: theme.textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
              color: _isFocused ? AppColors.primary : Colors.grey.shade700,
            ),
          ),
          const SizedBox(height: 8),
        ],
        TextFormField(
          controller: widget.controller,
          validator: widget.validator,
          keyboardType: widget.keyboardType,
          inputFormatters: widget.inputFormatters,
          obscureText: _obscureText,
          enabled: widget.enabled,
          maxLines: widget.maxLines,
          maxLength: widget.maxLength,
          onTap: widget.onTap,
          onChanged: widget.onChanged,
          onFieldSubmitted: widget.onSubmitted,
          focusNode: widget.focusNode,
          textCapitalization: widget.textCapitalization,
          textDirection: widget.textDirection,
          readOnly: widget.readOnly,
          style: theme.textTheme.bodyLarge?.copyWith(
            color: widget.enabled ? Colors.black87 : Colors.grey.shade600,
          ),
          decoration: InputDecoration(
            hintText: widget.hint,
            hintStyle: theme.textTheme.bodyLarge?.copyWith(
              color: Colors.grey.shade500,
            ),

            // Prefix icon
            prefixIcon: widget.prefixIcon != null
                ? Icon(
                    widget.prefixIcon,
                    color: _isFocused
                        ? AppColors.primary
                        : Colors.grey.shade600,
                    size: 20,
                  )
                : null,

            // Suffix icon (with password toggle functionality)
            suffixIcon: _buildSuffixIcon(),

            // Border styling
            border: _buildBorder(Colors.grey.shade300),
            enabledBorder: _buildBorder(Colors.grey.shade300),
            focusedBorder: _buildBorder(AppColors.primary),
            errorBorder: _buildBorder(Colors.red),
            focusedErrorBorder: _buildBorder(Colors.red),
            disabledBorder: _buildBorder(Colors.grey.shade200),

            // Fill styling
            filled: true,
            fillColor:
                widget.fillColor ??
                (widget.enabled ? Colors.grey.shade50 : Colors.grey.shade100),

            // Content padding
            contentPadding:
                widget.contentPadding ??
                const EdgeInsets.symmetric(horizontal: 16, vertical: 16),

            // Counter styling
            counterStyle: theme.textTheme.bodySmall?.copyWith(
              color: Colors.grey.shade600,
            ),

            // Error styling
            errorStyle: theme.textTheme.bodySmall?.copyWith(color: Colors.red),

            // Helper text styling
            helperStyle: theme.textTheme.bodySmall?.copyWith(
              color: Colors.grey.shade600,
            ),
          ),
        ),
      ],
    );
  }

  Widget? _buildSuffixIcon() {
    if (widget.obscureText) {
      // Password toggle icon
      return IconButton(
        icon: Icon(
          _obscureText ? Icons.visibility_off : Icons.visibility,
          color: Colors.grey.shade600,
          size: 20,
        ),
        onPressed: () {
          setState(() {
            _obscureText = !_obscureText;
          });
        },
      );
    } else if (widget.suffixIcon != null) {
      // Custom suffix icon
      return IconButton(
        icon: Icon(widget.suffixIcon, color: Colors.grey.shade600, size: 20),
        onPressed: widget.onSuffixIconPressed,
      );
    }
    return null;
  }

  OutlineInputBorder _buildBorder(Color color) => OutlineInputBorder(
    borderRadius: BorderRadius.circular(12),
    borderSide: BorderSide(color: color, width: 1.5),
  );
}

/// Specialized text fields for common use cases

class EmailTextField extends StatelessWidget {
  const EmailTextField({
    super.key,
    this.controller,
    this.validator,
    this.onChanged,
    this.enabled = true,
  });
  final TextEditingController? controller;
  final String? Function(String?)? validator;
  final void Function(String)? onChanged;
  final bool enabled;

  @override
  Widget build(BuildContext context) => CustomTextField(
    controller: controller,
    label: 'البريد الإلكتروني',
    hint: '<EMAIL>',
    prefixIcon: Icons.email_outlined,
    keyboardType: TextInputType.emailAddress,
    validator: validator ?? _defaultEmailValidator,
    onChanged: onChanged,
    enabled: enabled,
  );

  String? _defaultEmailValidator(String? value) {
    if (value == null || value.isEmpty) {
      return 'البريد الإلكتروني مطلوب';
    }
    if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
      return 'البريد الإلكتروني غير صحيح';
    }
    return null;
  }
}

class PasswordTextField extends StatelessWidget {
  const PasswordTextField({
    super.key,
    this.controller,
    this.label,
    this.validator,
    this.onChanged,
    this.enabled = true,
  });
  final TextEditingController? controller;
  final String? label;
  final String? Function(String?)? validator;
  final void Function(String)? onChanged;
  final bool enabled;

  @override
  Widget build(BuildContext context) => CustomTextField(
    controller: controller,
    label: label ?? 'كلمة المرور',
    hint: '••••••••',
    prefixIcon: Icons.lock_outlined,
    obscureText: true,
    validator: validator ?? _defaultPasswordValidator,
    onChanged: onChanged,
    enabled: enabled,
  );

  String? _defaultPasswordValidator(String? value) {
    if (value == null || value.isEmpty) {
      return 'كلمة المرور مطلوبة';
    }
    if (value.length < 6) {
      return 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
    }
    return null;
  }
}

class PhoneTextField extends StatelessWidget {
  const PhoneTextField({
    super.key,
    this.controller,
    this.validator,
    this.onChanged,
    this.enabled = true,
  });
  final TextEditingController? controller;
  final String? Function(String?)? validator;
  final void Function(String)? onChanged;
  final bool enabled;

  @override
  Widget build(BuildContext context) => CustomTextField(
    controller: controller,
    label: 'رقم الهاتف',
    hint: '05xxxxxxxx',
    prefixIcon: Icons.phone_outlined,
    keyboardType: TextInputType.phone,
    inputFormatters: [
      FilteringTextInputFormatter.digitsOnly,
      LengthLimitingTextInputFormatter(10),
    ],
    validator: validator ?? _defaultPhoneValidator,
    onChanged: onChanged,
    enabled: enabled,
  );

  String? _defaultPhoneValidator(String? value) {
    if (value == null || value.isEmpty) {
      return 'رقم الهاتف مطلوب';
    }
    if (value.length != 10) {
      return 'رقم الهاتف يجب أن يكون 10 أرقام';
    }
    if (!value.startsWith('05')) {
      return 'رقم الهاتف يجب أن يبدأ بـ 05';
    }
    return null;
  }
}

class SearchTextField extends StatelessWidget {
  const SearchTextField({
    super.key,
    this.controller,
    this.hint,
    this.onChanged,
    this.onSubmitted,
    this.onClear,
    this.enabled = true,
  });
  final TextEditingController? controller;
  final String? hint;
  final void Function(String)? onChanged;
  final void Function(String)? onSubmitted;
  final VoidCallback? onClear;
  final bool enabled;

  @override
  Widget build(BuildContext context) => CustomTextField(
    controller: controller,
    hint: hint ?? 'البحث...',
    prefixIcon: Icons.search,
    suffixIcon: (controller?.text.isNotEmpty ?? false) ? Icons.clear : null,
    onSuffixIconPressed: onClear ?? () => controller?.clear(),
    onChanged: onChanged,
    onSubmitted: onSubmitted,
    enabled: enabled,
    textCapitalization: TextCapitalization.words,
  );
}
