import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

class SkeletonLoading extends StatelessWidget {
  const SkeletonLoading({
    required this.width,
    required this.height,
    super.key,
    this.borderRadius = 8,
    this.shape = BoxShape.rectangle,
  });
  final double width;
  final double height;
  final double borderRadius;
  final BoxShape shape;

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Shimmer.fromColors(
      baseColor: isDark ? Colors.grey.shade800 : Colors.grey.shade300,
      highlightColor: isDark ? Colors.grey.shade700 : Colors.grey.shade100,
      child: Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          color: isDark ? Colors.grey.shade800 : Colors.grey.shade300,
          borderRadius: shape == BoxShape.rectangle
              ? BorderRadius.circular(borderRadius)
              : null,
          shape: shape,
        ),
      ),
    );
  }
}

class ProductCardSkeleton extends StatelessWidget {
  const ProductCardSkeleton({super.key});

  @override
  Widget build(BuildContext context) => Card(
    clipBehavior: Clip.antiAlias,
    elevation: 1.5,
    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
    child: const Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Image placeholder
        SkeletonLoading(width: double.infinity, height: 120, borderRadius: 0),
        Padding(
          padding: EdgeInsets.all(8),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Title placeholder
              SkeletonLoading(width: double.infinity, height: 16),
              SizedBox(height: 8),
              // Price placeholder
              SkeletonLoading(width: 80, height: 16),
              SizedBox(height: 16),
              // Badge placeholder
              SkeletonLoading(width: 60, height: 16, borderRadius: 4),
            ],
          ),
        ),
      ],
    ),
  );
}

/// شاشة هيكل تحميل الصفحة الرئيسية
///
/// تُستخدم كعنصر نائب أثناء انتظار بيانات الصفحة الرئيسية؛ تعرض وميض
/// شيمر لشرائح الفلاتر، لافتة ترويجية، الفئات، والمنتجات.
class HomeScreenSkeleton extends StatelessWidget {
  const HomeScreenSkeleton({super.key});

  @override
  Widget build(BuildContext context) => ListView(
    children: [
      const SizedBox(height: 16),
      // Filter chips
      SizedBox(
        height: 40,
        child: ListView.builder(
          scrollDirection: Axis.horizontal,
          itemCount: 5,
          padding: const EdgeInsets.symmetric(horizontal: 16),
          itemBuilder: (context, index) => Container(
            margin: const EdgeInsets.only(right: 8),
            child: const SkeletonLoading(
              width: 80,
              height: 32,
              borderRadius: 16,
            ),
          ),
        ),
      ),
      const SizedBox(height: 16),
      // Promotional banner
      const Padding(
        padding: EdgeInsets.symmetric(horizontal: 16),
        child: SkeletonLoading(
          width: double.infinity,
          height: 150,
          borderRadius: 12,
        ),
      ),
      const SizedBox(height: 24),
      // Section header
      const Padding(
        padding: EdgeInsets.symmetric(horizontal: 16),
        child: SkeletonLoading(width: 150, height: 24),
      ),
      const SizedBox(height: 16),
      // Categories
      SizedBox(
        height: 100,
        child: ListView.builder(
          scrollDirection: Axis.horizontal,
          itemCount: 4,
          padding: const EdgeInsets.symmetric(horizontal: 16),
          itemBuilder: (context, index) => Container(
            margin: const EdgeInsets.only(right: 16),
            child: const Column(
              children: [
                SkeletonLoading(width: 56, height: 56, shape: BoxShape.circle),
                SizedBox(height: 8),
                SkeletonLoading(width: 60, height: 14),
              ],
            ),
          ),
        ),
      ),
      const SizedBox(height: 24),
      // Products grid
      const Padding(
        padding: EdgeInsets.symmetric(horizontal: 16),
        child: SkeletonLoading(width: 150, height: 24),
      ),
      const SizedBox(height: 16),
      GridView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        padding: const EdgeInsets.symmetric(horizontal: 16),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 0.7,
        ),
        itemCount: 4,
        itemBuilder: (context, index) => const ProductCardSkeleton(),
      ),
    ],
  );
}
