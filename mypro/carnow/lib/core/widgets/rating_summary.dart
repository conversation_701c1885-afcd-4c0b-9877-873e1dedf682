import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';

import '../../core/theme/app_theme.dart';
import '../../features/ratings/models/rating_model.dart';
import '../../features/ratings/providers/ratings_provider.dart';
import 'star_rating.dart';

class RatingSummary extends ConsumerWidget {
  const RatingSummary({required this.productId, super.key});
  final int productId;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final ratingsAsyncValue = ref.watch(ratingsNotifierProvider(productId));
    final statsAsyncValue = ref.watch(productRatingStatsProvider(productId));
    final theme = Theme.of(context);

    return Card(
      margin: const EdgeInsets.all(AppTheme.spacingM),
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'تقييمات العملاء',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppTheme.spacingM),
            statsAsyncValue.when(
              data: (stats) => _buildStatsSection(context, stats),
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stack) => Text(
                'خطأ في تحميل إحصائيات التقييمات: $error',
                style: TextStyle(color: theme.colorScheme.error),
              ),
            ),
            const SizedBox(height: AppTheme.spacingL),
            const Divider(),
            const SizedBox(height: AppTheme.spacingM),
            Text(
              'آراء العملاء',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: AppTheme.spacingS),
            ratingsAsyncValue.when(
              data: (ratings) => _buildRatingsList(context, ratings),
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stack) => Text(
                'خطأ في تحميل التقييمات: $error',
                style: TextStyle(color: theme.colorScheme.error),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatsSection(BuildContext context, Map<String, dynamic> stats) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;
    final colorScheme = theme.colorScheme;

    final averageRating = stats['average'] as double? ?? 0;
    final totalCount = stats['count'] as int? ?? 0;
    final ratingsBreakdown = stats['distribution'] as Map<int, int>? ?? {};

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Overall rating display
        Expanded(
          flex: 2,
          child: Column(
            children: [
              Text(
                averageRating.toStringAsFixed(1),
                style: textTheme.headlineLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: colorScheme.primary,
                ),
              ),
              const SizedBox(height: AppTheme.spacingS),
              StarRating(rating: averageRating, size: 24, showRating: false),
              const SizedBox(height: AppTheme.spacingS),
              Text(
                '$totalCount تقييم',
                style: textTheme.bodyMedium?.copyWith(
                  color: colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ),
        ),

        // Rating breakdown bars
        Expanded(
          flex: 3,
          child: Column(
            children: List.generate(5, (index) {
              final starCount = 5 - index;
              final count = ratingsBreakdown[starCount] ?? 0;
              final percentage = totalCount > 0
                  ? (count / totalCount).toDouble()
                  : 0.0;

              return Padding(
                padding: const EdgeInsets.only(bottom: AppTheme.spacingXXS),
                child: Row(
                  children: [
                    Text('$starCount', style: textTheme.bodySmall),
                    const SizedBox(width: AppTheme.spacingXXS),
                    Icon(Icons.star, size: 16, color: colorScheme.primary),
                    const SizedBox(width: AppTheme.spacingS),
                    Expanded(
                      child: LinearProgressIndicator(
                        value: percentage,
                        backgroundColor: colorScheme.surfaceContainerHighest,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          colorScheme.primary,
                        ),
                      ),
                    ),
                    const SizedBox(width: AppTheme.spacingS),
                    SizedBox(
                      width: 30,
                      child: Text(
                        '$count',
                        style: textTheme.bodySmall,
                        textAlign: TextAlign.end,
                      ),
                    ),
                  ],
                ),
              );
            }),
          ),
        ),
      ],
    );
  }

  Widget _buildRatingsList(BuildContext context, List<RatingModel> ratings) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;
    final colorScheme = theme.colorScheme;
    if (ratings.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(AppTheme.spacingXL),
        child: Column(
          children: [
            Icon(
              Icons.rate_review_outlined,
              size: 48,
              color: colorScheme.onSurface.withAlpha((0.4 * 255).toInt()),
            ),
            const SizedBox(height: AppTheme.spacingM),
            Text(
              'لا توجد تقييمات بعد',
              style: textTheme.titleMedium?.copyWith(
                color: colorScheme.onSurfaceVariant,
              ),
            ),
            const SizedBox(height: AppTheme.spacingS),
            Text(
              'كن أول من يقيم هذا المنتج',
              style: textTheme.bodyMedium?.copyWith(
                color: colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
      );
    }

    return Column(
      children:
          ratings
              .take(3)
              .map((rating) => _buildRatingItem(context, rating))
              .toList()
            ..addAll([
              if (ratings.length > 3)
                Padding(
                  padding: const EdgeInsets.only(top: AppTheme.spacingS),
                  child: TextButton(
                    onPressed: () {
                      _showAllRatings(context, ratings);
                    },
                    child: Text('عرض جميع التقييمات (${ratings.length})'),
                  ),
                ),
            ]),
    );
  }

  Widget _buildRatingItem(BuildContext context, RatingModel rating) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;
    final colorScheme = theme.colorScheme;

    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingM),
      padding: const EdgeInsets.all(AppTheme.spacingS),
      decoration: BoxDecoration(
        color: colorScheme.surfaceContainerHighest.withAlpha(
          (0.3 * 255).toInt(),
        ),
        borderRadius: BorderRadius.circular(AppTheme.radiusM),
        border: Border.all(
          color: colorScheme.outline.withAlpha((0.1 * 255).toInt()),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              StarRating(
                rating: rating.rating.toDouble(),
                size: 16,
                showRating: false,
              ),
              const SizedBox(width: AppTheme.spacingS),
              Text(
                _formatDate(rating.createdAt),
                style: textTheme.bodySmall?.copyWith(
                  color: colorScheme.onSurfaceVariant,
                ),
              ),
              const Spacer(),
              const Icon(
                Icons.verified_user,
                size: 16,
                color: AppTheme.success,
              ),
              const SizedBox(width: 4),
              Text(
                'مشتري موثق',
                style: textTheme.bodySmall?.copyWith(color: AppTheme.success),
              ),
            ],
          ),
          if (rating.comment != null && rating.comment!.isNotEmpty) ...[
            const SizedBox(height: AppTheme.spacingS),
            Text(rating.comment!, style: textTheme.bodyMedium),
          ],
        ],
      ),
    );
  }

  void _showAllRatings(BuildContext context, List<RatingModel> ratings) {
    showModalBottomSheet<void>(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(AppTheme.radiusL),
        ),
      ),
      builder: (BuildContext context) {
        return DraggableScrollableSheet(
          expand: false,
          initialChildSize: 0.8,
          maxChildSize: 0.9,
          builder: (BuildContext context, ScrollController scrollController) {
            return Column(
              children: [
                Padding(
                  padding: const EdgeInsets.all(AppTheme.spacingM),
                  child: Text(
                    'جميع التقييمات (${ratings.length})',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                ),
                Expanded(
                  child: ListView.builder(
                    controller: scrollController,
                    itemCount: ratings.length,
                    itemBuilder: (context, index) {
                      return Padding(
                        padding: const EdgeInsets.symmetric(
                          horizontal: AppTheme.spacingM,
                          vertical: AppTheme.spacingS,
                        ),
                        child: _buildRatingItem(context, ratings[index]),
                      );
                    },
                  ),
                ),
              ],
            );
          },
        );
      },
    );
  }

  String _formatDate(DateTime? date) {
    if (date == null) {
      return 'تاريخ غير معروف';
    }
    return DateFormat.yMMMd('ar').format(date);
  }
}
