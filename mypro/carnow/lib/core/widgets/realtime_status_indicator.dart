import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../services/websocket_service.dart';
import '../services/optimistic_updates_service.dart';

/// Real-time connection status indicator widget
class RealtimeStatusIndicator extends ConsumerWidget {
  final bool showLabel;
  final double size;
  final EdgeInsets? padding;

  const RealtimeStatusIndicator({
    super.key,
    this.showLabel = true,
    this.size = 16.0,
    this.padding,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final connectionStateAsync = ref.watch(webSocketConnectionStateProvider);

    return connectionStateAsync.when(
      data: (connectionState) =>
          _buildStatusIndicator(context, connectionState),
      loading: () => _buildLoadingIndicator(),
      error: (error, stack) => _buildErrorIndicator(context),
    );
  }

  Widget _buildStatusIndicator(
    BuildContext context,
    WebSocketConnectionState connectionState,
  ) {
    final theme = Theme.of(context);

    Color statusColor;
    IconData statusIcon;
    String statusText;

    switch (connectionState) {
      case WebSocketConnectionState.connected:
        statusColor = Colors.green;
        statusIcon = Icons.wifi;
        statusText = 'Connected';
        break;
      case WebSocketConnectionState.connecting:
        statusColor = Colors.orange;
        statusIcon = Icons.wifi_find;
        statusText = 'Connecting...';
        break;
      case WebSocketConnectionState.reconnecting:
        statusColor = Colors.orange;
        statusIcon = Icons.wifi_find;
        statusText = 'Reconnecting...';
        break;
      case WebSocketConnectionState.disconnected:
        statusColor = Colors.grey;
        statusIcon = Icons.wifi_off;
        statusText = 'Disconnected';
        break;
      case WebSocketConnectionState.error:
        statusColor = Colors.red;
        statusIcon = Icons.wifi_off;
        statusText = 'Connection Error';
        break;
    }

    return Container(
      padding:
          padding ?? const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(statusIcon, color: statusColor, size: size),
          if (showLabel) ...[
            const SizedBox(width: 4.0),
            Text(
              statusText,
              style: theme.textTheme.bodySmall?.copyWith(
                color: statusColor,
                fontSize: 12.0,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildLoadingIndicator() {
    return Container(
      padding:
          padding ?? const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: size,
            height: size,
            child: const CircularProgressIndicator(strokeWidth: 2.0),
          ),
          if (showLabel) ...[
            const SizedBox(width: 4.0),
            const Text('Loading...', style: TextStyle(fontSize: 12.0)),
          ],
        ],
      ),
    );
  }

  Widget _buildErrorIndicator(BuildContext context) {
    return Container(
      padding:
          padding ?? const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(Icons.error_outline, color: Colors.red, size: size),
          if (showLabel) ...[
            const SizedBox(width: 4.0),
            const Text(
              'Error',
              style: TextStyle(color: Colors.red, fontSize: 12.0),
            ),
          ],
        ],
      ),
    );
  }
}

/// Optimistic update status indicator
class OptimisticUpdateIndicator extends ConsumerWidget {
  final String entityType;
  final String entityId;
  final Widget child;

  const OptimisticUpdateIndicator({
    super.key,
    required this.entityType,
    required this.entityId,
    required this.child,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final optimisticService = ref.watch(optimisticUpdatesServiceProvider);
    final hasPendingUpdates = optimisticService.hasPendingUpdates(
      entityType,
      entityId,
    );

    return Stack(
      children: [
        child,
        if (hasPendingUpdates)
          Positioned(
            top: 0,
            right: 0,
            child: Container(
              padding: const EdgeInsets.all(2.0),
              decoration: const BoxDecoration(
                color: Colors.orange,
                shape: BoxShape.circle,
              ),
              child: const Icon(Icons.sync, color: Colors.white, size: 12.0),
            ),
          ),
      ],
    );
  }
}

/// Real-time data sync indicator
class DataSyncIndicator extends ConsumerWidget {
  final bool showDetails;

  const DataSyncIndicator({super.key, this.showDetails = false});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);

    return StreamBuilder<OptimisticUpdate>(
      stream: ref.watch(optimisticUpdatesServiceProvider).updateStream,
      builder: (context, snapshot) {
        if (!snapshot.hasData) {
          return const SizedBox.shrink();
        }

        final update = snapshot.data!;
        final isError = update.error != null;
        final isRollback = update.operation == 'rollback';

        Color indicatorColor;
        IconData indicatorIcon;
        String message;

        if (isError) {
          indicatorColor = Colors.red;
          indicatorIcon = Icons.error;
          message = 'Sync failed: ${update.error}';
        } else if (isRollback) {
          indicatorColor = Colors.orange;
          indicatorIcon = Icons.undo;
          message = 'Changes reverted';
        } else {
          indicatorColor = Colors.green;
          indicatorIcon = Icons.check_circle;
          message = 'Changes synced';
        }

        return AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
          decoration: BoxDecoration(
            color: indicatorColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(20.0),
            border: Border.all(color: indicatorColor.withOpacity(0.3)),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(indicatorIcon, color: indicatorColor, size: 16.0),
              if (showDetails) ...[
                const SizedBox(width: 8.0),
                Text(
                  message,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: indicatorColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ],
          ),
        );
      },
    );
  }
}

/// Real-time notification badge
class RealtimeNotificationBadge extends ConsumerWidget {
  final Widget child;
  final String? userId;

  const RealtimeNotificationBadge({
    super.key,
    required this.child,
    this.userId,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return StreamBuilder<Map<String, dynamic>>(
      stream: ref.watch(systemMessagesStreamProvider),
      builder: (context, snapshot) {
        final hasNotification = snapshot.hasData;

        return Stack(
          children: [
            child,
            if (hasNotification)
              Positioned(
                top: 0,
                right: 0,
                child: Container(
                  padding: const EdgeInsets.all(4.0),
                  decoration: const BoxDecoration(
                    color: Colors.red,
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.notifications,
                    color: Colors.white,
                    size: 12.0,
                  ),
                ),
              ),
          ],
        );
      },
    );
  }
}

/// Live data indicator for lists
class LiveDataIndicator extends ConsumerWidget {
  final String dataType;
  final Widget child;

  const LiveDataIndicator({
    super.key,
    required this.dataType,
    required this.child,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);

    return StreamBuilder<Map<String, dynamic>>(
      stream: _getStreamForDataType(ref, dataType),
      builder: (context, snapshot) {
        final hasUpdate = snapshot.hasData;

        return Column(
          children: [
            if (hasUpdate)
              Container(
                width: double.infinity,
                padding: const EdgeInsets.symmetric(
                  horizontal: 16.0,
                  vertical: 8.0,
                ),
                decoration: BoxDecoration(
                  color: Colors.blue.withOpacity(0.1),
                  border: Border(
                    bottom: BorderSide(
                      color: Colors.blue.withOpacity(0.3),
                      width: 1.0,
                    ),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(Icons.update, color: Colors.blue, size: 16.0),
                    const SizedBox(width: 8.0),
                    Text(
                      'New updates available',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: Colors.blue,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const Spacer(),
                    TextButton(
                      onPressed: () {
                        // Refresh data
                        // This would typically trigger a refresh of the data provider
                      },
                      child: const Text('Refresh'),
                    ),
                  ],
                ),
              ),
            Expanded(child: child),
          ],
        );
      },
    );
  }

  Stream<Map<String, dynamic>> _getStreamForDataType(
    WidgetRef ref,
    String dataType,
  ) {
    switch (dataType) {
      case 'products':
        return ref.watch(productUpdatesStreamProvider);
      case 'orders':
        return ref.watch(orderUpdatesStreamProvider);
      case 'wallet':
        return ref.watch(walletUpdatesStreamProvider);
      case 'inventory':
        return ref.watch(inventoryUpdatesStreamProvider);
      default:
        return const Stream.empty();
    }
  }
}

/// Connection quality indicator
class ConnectionQualityIndicator extends ConsumerWidget {
  const ConnectionQualityIndicator({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final connectionStateAsync = ref.watch(webSocketConnectionStateProvider);

    return connectionStateAsync.when(
      data: (connectionState) {
        final isConnected =
            connectionState == WebSocketConnectionState.connected;

        return AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          width: 8.0,
          height: 8.0,
          decoration: BoxDecoration(
            color: isConnected ? Colors.green : Colors.red,
            shape: BoxShape.circle,
            boxShadow: isConnected
                ? [
                    BoxShadow(
                      color: Colors.green.withOpacity(0.5),
                      blurRadius: 4.0,
                      spreadRadius: 1.0,
                    ),
                  ]
                : null,
          ),
        );
      },
      loading: () => Container(
        width: 8.0,
        height: 8.0,
        decoration: const BoxDecoration(
          color: Colors.grey,
          shape: BoxShape.circle,
        ),
      ),
      error: (error, stack) => Container(
        width: 8.0,
        height: 8.0,
        decoration: const BoxDecoration(
          color: Colors.red,
          shape: BoxShape.circle,
        ),
      ),
    );
  }
}

/// Typing indicator for real-time chat or comments
class TypingIndicator extends StatefulWidget {
  final List<String> typingUsers;

  const TypingIndicator({super.key, required this.typingUsers});

  @override
  State<TypingIndicator> createState() => _TypingIndicatorState();
}

class _TypingIndicatorState extends State<TypingIndicator>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _animation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.repeat();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.typingUsers.isEmpty) {
      return const SizedBox.shrink();
    }

    final theme = Theme.of(context);
    final typingText = widget.typingUsers.length == 1
        ? '${widget.typingUsers.first} is typing...'
        : '${widget.typingUsers.length} people are typing...';

    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
          child: Row(
            children: [
              ...List.generate(3, (index) {
                return AnimatedContainer(
                  duration: const Duration(milliseconds: 300),
                  margin: const EdgeInsets.only(right: 4.0),
                  width: 6.0,
                  height: 6.0,
                  decoration: BoxDecoration(
                    color: Colors.grey.withOpacity(
                      0.3 + (0.7 * (((_animation.value + index * 0.3) % 1.0))),
                    ),
                    shape: BoxShape.circle,
                  ),
                );
              }),
              const SizedBox(width: 8.0),
              Text(
                typingText,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                  fontStyle: FontStyle.italic,
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
