import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../providers/connectivity_provider.dart';

/// A widget that shows a banner when the device is offline
class NetworkSensitive extends ConsumerWidget {
  /// Creates a network sensitive widget
  const NetworkSensitive({
    required this.child,
    super.key,
    this.showOfflineBanner = true,
  });

  /// The child widget to display
  final Widget child;

  /// Whether to show an offline banner when there's no connection
  final bool showOfflineBanner;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final connectivity = ref.watch(connectivityProvider);

    return connectivity.when(
      data: (results) {
        // Consider offline when every connectivity result equals "none".
        final isOffline =
            results.isEmpty ||
            results.every((r) => r == ConnectivityResult.none);

        return Column(
          children: [
            if (isOffline && showOfflineBanner) _buildOfflineBanner(context),
            Expanded(child: child),
          ],
        );
      },
      loading: () => child,
      error: (_, stack) => child,
    );
  }

  Widget _buildOfflineBanner(BuildContext context) => Container(
    width: double.infinity,
    color: Theme.of(context).colorScheme.errorContainer,
    padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
    child: Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const Icon(Icons.wifi_off, size: 16),
        const SizedBox(width: 8),
        Text(
          'أنت غير متصل بالإنترنت',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Theme.of(context).colorScheme.onErrorContainer,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    ),
  );
}
