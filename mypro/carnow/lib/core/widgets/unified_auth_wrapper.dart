import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../auth/auth_models.dart';
import '../auth/unified_auth_provider.dart';
import '../auth/auth_provider_initializer.dart';
import '../app/carnow_app.dart';
import '../../features/auth/screens/new_login_screen.dart';

/// AuthWrapper مبسط يستخدم UnifiedAuthProvider
/// Simplified AuthWrapper using UnifiedAuthProvider
class UnifiedAuthWrapper extends ConsumerWidget {
  const UnifiedAuthWrapper({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // مراقبة حالة المصادقة من SafeAuthStateProvider
    // Watch auth state from SafeAuthStateProvider
    final authState = ref.watch(safeAuthStateProvider);

    return authState.when(
      initial: () => const Center(child: CircularProgressIndicator()),
      loading: (message, operation) => const Center(child: CircularProgressIndicator()),
      authenticated: (user, token, refreshToken, tokenExpiry, sessionStart) {
        return const CarNowApp();
      },
      unauthenticated: (reason) => const NewLoginScreen(),
      error: (message, errorCode, errorType, isRecoverable, originalException) {
        return Scaffold(
          body: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 64,
                  color: Theme.of(context).colorScheme.error,
                ),
                const SizedBox(height: 16),
                Text(
                  message,
                  style: Theme.of(context).textTheme.titleMedium,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                FilledButton(
                  onPressed: () {
                    // إعادة تحميل النظام
                    ref.invalidate(unifiedAuthProviderProvider);
                  },
                  child: const Text('إعادة المحاولة'),
                ),
              ],
            ),
          ),
        );
      },
      emailVerificationPending: (email, sentAt) => const NewLoginScreen(),
      sessionExpired: (expiredAt, autoRefreshAttempted) => const NewLoginScreen(),
    );
  }
}
