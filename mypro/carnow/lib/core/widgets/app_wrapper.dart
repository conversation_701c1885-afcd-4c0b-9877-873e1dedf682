import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'backend_status_banner.dart';

/// App wrapper that includes backend status monitoring
class AppWrapper extends ConsumerWidget {
  final Widget child;
  
  const AppWrapper({super.key, required this.child});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Column(
      children: [
        // Backend status banner (only shows when there are issues)
        const BackendStatusBanner(),
        
        // Main app content
        Expanded(child: child),
      ],
    );
  }
}

/// Overlay widget for showing backend status in floating manner
class BackendStatusOverlay extends ConsumerWidget {
  final Widget child;
  
  const BackendStatusOverlay({super.key, required this.child});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Stack(
      children: [
        // Main app content
        child,
        
        // Floating status banner at top
        Positioned(
          top: MediaQuery.of(context).padding.top,
          left: 0,
          right: 0,
          child: const BackendStatusBanner(),
        ),
      ],
    );
  }
} 