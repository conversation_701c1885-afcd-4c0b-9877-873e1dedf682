import 'package:flutter/material.dart';

import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../auth/unified_auth_provider.dart';
import '../providers/users_provider.dart';
import '../../core/widgets/auth_wrapper.dart';
import '../../features/auth/screens/new_login_screen.dart';
import '../../shared/providers/location_provider.dart';

/// Example widget showing how to use the unified authentication system
/// This widget can be used as a template for other auth-dependent features
class AuthDependentWidget extends ConsumerWidget {
  const AuthDependentWidget({
    required this.authenticatedBuilder,
    super.key,
    this.unauthenticatedWidget,
    this.title,
  });
  final Widget Function(BuildContext context, String userId)
  authenticatedBuilder;
  final Widget? unauthenticatedWidget;
  final String? title;

  @override
  Widget build(BuildContext context, WidgetRef ref) => AuthStatusBuilder(
    authenticatedBuilder: (context) {
      final user = ref.watch(currentUserProvider);
      final userId = user?.id;
      if (userId != null) {
        return authenticatedBuilder(context, userId);
      }
      return const Center(child: Text('خطأ في تحديد هوية المستخدم'));
    },
    unauthenticatedBuilder: (context) =>
        unauthenticatedWidget ?? _buildDefaultUnauthenticatedWidget(context),
    loadingBuilder: (context) =>
        const Center(child: CircularProgressIndicator()),
    errorBuilder: (context) => const Center(child: Text('خطأ في المصادقة')),
  );

  Widget _buildDefaultUnauthenticatedWidget(BuildContext context) => Center(
    child: Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(Icons.lock_outline, size: 64, color: Colors.grey.shade400),
        const SizedBox(height: 16),
        Text(
          title ?? 'يتطلب تسجيل الدخول',
          style: Theme.of(context).textTheme.headlineSmall,
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 8),
        Text(
          'يرجى تسجيل الدخول للوصول إلى هذه الميزة',
          style: Theme.of(
            context,
          ).textTheme.bodyMedium?.copyWith(color: Colors.grey.shade600),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 24),
        FilledButton(
          onPressed: () {
            Navigator.of(context).push<void>(
              MaterialPageRoute<void>(
                builder: (context) => const NewLoginScreen(),
              ),
            );
          },
          child: const Text('تسجيل الدخول'),
        ),
      ],
    ),
  );
}

/// Widget that shows user profile information when authenticated
class UserProfileWidget extends ConsumerWidget {
  const UserProfileWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) => AuthDependentWidget(
    title: 'الملف الشخصي',
    authenticatedBuilder: (context, userId) {
      final userProfileAsync = ref.watch(currentUserStreamProvider);
      final userProfile = userProfileAsync.value;

      if (userProfile == null) {
        return const Center(child: Text('لا توجد بيانات للملف الشخصي'));
      }

      return Card(
        margin: const EdgeInsets.all(16),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                children: [
                  CircleAvatar(
                    radius: 30,
                    child: Text(
                      userProfile.name?.substring(0, 1).toUpperCase() ?? 'U',
                      style: const TextStyle(fontSize: 24),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          userProfile.name ?? 'مستخدم',
                          style: Theme.of(context).textTheme.titleLarge,
                        ),
                        if (userProfile.email != null) ...[
                          const SizedBox(height: 4),
                          Text(
                            userProfile.email!,
                            style: Theme.of(context).textTheme.bodyMedium
                                ?.copyWith(color: Colors.grey.shade600),
                          ),
                        ],
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              const Divider(),
              const SizedBox(height: 16),
              if (userProfile.phone != null)
                _buildProfileDetailRow(Icons.phone, userProfile.phone!),
              if (userProfile.cityId != null)
                Consumer(
                  builder: (context, ref, child) {
                    final cityAsync = ref.watch(
                      cityByIdProvider(userProfile.cityId!),
                    );
                    return cityAsync.when(
                      data: (city) => city != null
                          ? _buildProfileDetailRow(
                              Icons.location_city,
                              city.nameArabic,
                            )
                          : const SizedBox.shrink(),
                      loading: () => const Padding(
                        padding: EdgeInsets.symmetric(vertical: 8),
                        child: CircularProgressIndicator(),
                      ),
                      error: (e, st) => _buildProfileDetailRow(
                        Icons.error_outline,
                        'خطأ في تحميل المدينة',
                      ),
                    );
                  },
                ),
              if (userProfile.address != null &&
                  userProfile.address!.isNotEmpty)
                _buildProfileDetailRow(
                  Icons.maps_home_work_outlined,
                  userProfile.address!,
                ),
            ],
          ),
        ),
      );
    },
  );

  Widget _buildProfileDetailRow(IconData icon, String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Icon(icon, size: 20, color: Colors.grey.shade600),
          const SizedBox(width: 12),
          Expanded(child: Text(text)),
        ],
      ),
    );
  }
}

/// Widget that shows different content based on authentication status
class ConditionalAuthWidget extends ConsumerWidget {
  const ConditionalAuthWidget({
    required this.authenticatedChild,
    required this.unauthenticatedChild,
    super.key,
  });
  final Widget authenticatedChild;
  final Widget unauthenticatedChild;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isAuthenticated = ref.watch(isAuthenticatedProvider);

    return AnimatedSwitcher(
      duration: const Duration(milliseconds: 300),
      child: isAuthenticated ? authenticatedChild : unauthenticatedChild,
    );
  }
}
