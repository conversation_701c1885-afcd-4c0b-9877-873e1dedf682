import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'optimized_image.dart';

/// Lazy loading container that loads content when visible
class LazyLoadingContainer extends StatefulWidget {
  final Widget child;
  final Widget? placeholder;
  final double visibilityThreshold;
  final Duration loadingDelay;
  final VoidCallback? onVisible;

  const LazyLoadingContainer({
    super.key,
    required this.child,
    this.placeholder,
    this.visibilityThreshold = 0.1,
    this.loadingDelay = const Duration(milliseconds: 100),
    this.onVisible,
  });

  @override
  State<LazyLoadingContainer> createState() => _LazyLoadingContainerState();
}

class _LazyLoadingContainerState extends State<LazyLoadingContainer> {
  bool _isVisible = false;
  bool _hasLoaded = false;

  @override
  Widget build(BuildContext context) {
    return VisibilityDetector(
      key: Key('lazy_${widget.hashCode}'),
      onVisibilityChanged: _onVisibilityChanged,
      child: _hasLoaded ? widget.child : _buildPlaceholder(),
    );
  }

  void _onVisibilityChanged(VisibilityInfo info) {
    if (!_hasLoaded && info.visibleFraction >= widget.visibilityThreshold) {
      _isVisible = true;
      widget.onVisible?.call();

      Future.delayed(widget.loadingDelay, () {
        if (mounted && _isVisible) {
          setState(() {
            _hasLoaded = true;
          });
        }
      });
    }
  }

  Widget _buildPlaceholder() {
    return widget.placeholder ??
        Container(
          height: 200,
          color: Colors.grey[200],
          child: const Center(child: CircularProgressIndicator()),
        );
  }
}

/// Lazy loading list view with automatic pagination
class LazyLoadingListView<T> extends ConsumerStatefulWidget {
  final Future<List<T>> Function(int page, int limit) itemLoader;
  final Widget Function(BuildContext context, T item, int index) itemBuilder;
  final Widget? loadingWidget;
  final Widget? errorWidget;
  final Widget? emptyWidget;
  final int itemsPerPage;
  final ScrollController? scrollController;
  final EdgeInsets? padding;
  final bool shrinkWrap;
  final ScrollPhysics? physics;

  const LazyLoadingListView({
    super.key,
    required this.itemLoader,
    required this.itemBuilder,
    this.loadingWidget,
    this.errorWidget,
    this.emptyWidget,
    this.itemsPerPage = 20,
    this.scrollController,
    this.padding,
    this.shrinkWrap = false,
    this.physics,
  });

  @override
  ConsumerState<LazyLoadingListView<T>> createState() =>
      _LazyLoadingListViewState<T>();
}

class _LazyLoadingListViewState<T>
    extends ConsumerState<LazyLoadingListView<T>> {
  final List<T> _items = [];
  late ScrollController _scrollController;
  bool _isLoading = false;
  bool _hasError = false;
  bool _hasMoreItems = true;
  int _currentPage = 0;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _scrollController = widget.scrollController ?? ScrollController();
    _scrollController.addListener(_onScroll);
    _loadInitialItems();
  }

  @override
  void dispose() {
    if (widget.scrollController == null) {
      _scrollController.dispose();
    }
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      _loadMoreItems();
    }
  }

  Future<void> _loadInitialItems() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
      _hasError = false;
      _errorMessage = null;
    });

    try {
      final items = await widget.itemLoader(0, widget.itemsPerPage);
      setState(() {
        _items.clear();
        _items.addAll(items);
        _currentPage = 0;
        _hasMoreItems = items.length >= widget.itemsPerPage;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _hasError = true;
        _errorMessage = e.toString();
        _isLoading = false;
      });
    }
  }

  Future<void> _loadMoreItems() async {
    if (_isLoading || !_hasMoreItems) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final nextPage = _currentPage + 1;
      final items = await widget.itemLoader(nextPage, widget.itemsPerPage);

      setState(() {
        _items.addAll(items);
        _currentPage = nextPage;
        _hasMoreItems = items.length >= widget.itemsPerPage;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      // Show error snackbar for pagination errors
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load more items: $e'),
            action: SnackBarAction(label: 'Retry', onPressed: _loadMoreItems),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_hasError && _items.isEmpty) {
      return _buildErrorWidget();
    }

    if (_items.isEmpty && _isLoading) {
      return _buildLoadingWidget();
    }

    if (_items.isEmpty) {
      return _buildEmptyWidget();
    }

    return RefreshIndicator(
      onRefresh: _loadInitialItems,
      child: ListView.builder(
        controller: _scrollController,
        padding: widget.padding,
        shrinkWrap: widget.shrinkWrap,
        physics: widget.physics,
        itemCount: _items.length + (_hasMoreItems ? 1 : 0),
        itemBuilder: (context, index) {
          if (index >= _items.length) {
            return _buildLoadingIndicator();
          }

          return widget.itemBuilder(context, _items[index], index);
        },
      ),
    );
  }

  Widget _buildLoadingWidget() {
    return widget.loadingWidget ??
        const Center(child: CircularProgressIndicator());
  }

  Widget _buildErrorWidget() {
    return widget.errorWidget ??
        Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error_outline, size: 64, color: Colors.red),
              const SizedBox(height: 16),
              Text(
                'Error loading items',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              if (_errorMessage != null) ...[
                const SizedBox(height: 8),
                Text(
                  _errorMessage!,
                  style: Theme.of(context).textTheme.bodyMedium,
                  textAlign: TextAlign.center,
                ),
              ],
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: _loadInitialItems,
                child: const Text('Retry'),
              ),
            ],
          ),
        );
  }

  Widget _buildEmptyWidget() {
    return widget.emptyWidget ?? const Center(child: Text('No items found'));
  }

  Widget _buildLoadingIndicator() {
    return Container(
      padding: const EdgeInsets.all(16),
      alignment: Alignment.center,
      child: const CircularProgressIndicator(),
    );
  }
}

/// Lazy loading grid view
class LazyLoadingGridView<T> extends ConsumerStatefulWidget {
  final Future<List<T>> Function(int page, int limit) itemLoader;
  final Widget Function(BuildContext context, T item, int index) itemBuilder;
  final int crossAxisCount;
  final double crossAxisSpacing;
  final double mainAxisSpacing;
  final double childAspectRatio;
  final Widget? loadingWidget;
  final Widget? errorWidget;
  final Widget? emptyWidget;
  final int itemsPerPage;
  final ScrollController? scrollController;
  final EdgeInsets? padding;
  final bool shrinkWrap;
  final ScrollPhysics? physics;

  const LazyLoadingGridView({
    super.key,
    required this.itemLoader,
    required this.itemBuilder,
    this.crossAxisCount = 2,
    this.crossAxisSpacing = 8,
    this.mainAxisSpacing = 8,
    this.childAspectRatio = 1,
    this.loadingWidget,
    this.errorWidget,
    this.emptyWidget,
    this.itemsPerPage = 20,
    this.scrollController,
    this.padding,
    this.shrinkWrap = false,
    this.physics,
  });

  @override
  ConsumerState<LazyLoadingGridView<T>> createState() =>
      _LazyLoadingGridViewState<T>();
}

class _LazyLoadingGridViewState<T>
    extends ConsumerState<LazyLoadingGridView<T>> {
  final List<T> _items = [];
  late ScrollController _scrollController;
  bool _isLoading = false;
  bool _hasError = false;
  bool _hasMoreItems = true;
  int _currentPage = 0;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _scrollController = widget.scrollController ?? ScrollController();
    _scrollController.addListener(_onScroll);
    _loadInitialItems();
  }

  @override
  void dispose() {
    if (widget.scrollController == null) {
      _scrollController.dispose();
    }
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      _loadMoreItems();
    }
  }

  Future<void> _loadInitialItems() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
      _hasError = false;
      _errorMessage = null;
    });

    try {
      final items = await widget.itemLoader(0, widget.itemsPerPage);
      setState(() {
        _items.clear();
        _items.addAll(items);
        _currentPage = 0;
        _hasMoreItems = items.length >= widget.itemsPerPage;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _hasError = true;
        _errorMessage = e.toString();
        _isLoading = false;
      });
    }
  }

  Future<void> _loadMoreItems() async {
    if (_isLoading || !_hasMoreItems) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final nextPage = _currentPage + 1;
      final items = await widget.itemLoader(nextPage, widget.itemsPerPage);

      setState(() {
        _items.addAll(items);
        _currentPage = nextPage;
        _hasMoreItems = items.length >= widget.itemsPerPage;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_hasError && _items.isEmpty) {
      return _buildErrorWidget();
    }

    if (_items.isEmpty && _isLoading) {
      return _buildLoadingWidget();
    }

    if (_items.isEmpty) {
      return _buildEmptyWidget();
    }

    return RefreshIndicator(
      onRefresh: _loadInitialItems,
      child: GridView.builder(
        controller: _scrollController,
        padding: widget.padding,
        shrinkWrap: widget.shrinkWrap,
        physics: widget.physics,
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: widget.crossAxisCount,
          crossAxisSpacing: widget.crossAxisSpacing,
          mainAxisSpacing: widget.mainAxisSpacing,
          childAspectRatio: widget.childAspectRatio,
        ),
        itemCount: _items.length + (_hasMoreItems ? 1 : 0),
        itemBuilder: (context, index) {
          if (index >= _items.length) {
            return _buildLoadingIndicator();
          }

          return widget.itemBuilder(context, _items[index], index);
        },
      ),
    );
  }

  Widget _buildLoadingWidget() {
    return widget.loadingWidget ??
        const Center(child: CircularProgressIndicator());
  }

  Widget _buildErrorWidget() {
    return widget.errorWidget ??
        Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error_outline, size: 64, color: Colors.red),
              const SizedBox(height: 16),
              Text(
                'Error loading items',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              if (_errorMessage != null) ...[
                const SizedBox(height: 8),
                Text(
                  _errorMessage!,
                  style: Theme.of(context).textTheme.bodyMedium,
                  textAlign: TextAlign.center,
                ),
              ],
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: _loadInitialItems,
                child: const Text('Retry'),
              ),
            ],
          ),
        );
  }

  Widget _buildEmptyWidget() {
    return widget.emptyWidget ?? const Center(child: Text('No items found'));
  }

  Widget _buildLoadingIndicator() {
    return Container(
      padding: const EdgeInsets.all(16),
      alignment: Alignment.center,
      child: const CircularProgressIndicator(),
    );
  }
}

/// Lazy loading image with intersection observer
class LazyLoadingImage extends StatefulWidget {
  final String imageUrl;
  final double? width;
  final double? height;
  final BoxFit fit;
  final Widget? placeholder;
  final Widget? errorWidget;
  final double visibilityThreshold;

  const LazyLoadingImage({
    super.key,
    required this.imageUrl,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.placeholder,
    this.errorWidget,
    this.visibilityThreshold = 0.1,
  });

  @override
  State<LazyLoadingImage> createState() => _LazyLoadingImageState();
}

class _LazyLoadingImageState extends State<LazyLoadingImage> {
  bool _shouldLoad = false;

  @override
  Widget build(BuildContext context) {
    return VisibilityDetector(
      key: Key('lazy_image_${widget.imageUrl}'),
      onVisibilityChanged: (info) {
        if (!_shouldLoad &&
            info.visibleFraction >= widget.visibilityThreshold) {
          setState(() {
            _shouldLoad = true;
          });
        }
      },
      child: _shouldLoad
          ? OptimizedImage(
              imageUrl: widget.imageUrl,
              width: widget.width,
              height: widget.height,
              fit: widget.fit,
              placeholder: widget.placeholder,
              errorWidget: widget.errorWidget,
            )
          : _buildPlaceholder(),
    );
  }

  Widget _buildPlaceholder() {
    return widget.placeholder ??
        Container(
          width: widget.width,
          height: widget.height,
          color: Colors.grey[200],
          child: const Icon(Icons.image_outlined, color: Colors.grey),
        );
  }
}
