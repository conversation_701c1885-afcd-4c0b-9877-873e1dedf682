import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:logger/logger.dart';
import '../theme/app_theme.dart';

final _logger = Logger(
  printer: PrettyPrinter(methodCount: 0, errorMethodCount: 5, lineLength: 50),
);

/// Error Boundary محسن للتعامل مع أخطاء التطبيق
class ErrorBoundary<T> extends ConsumerWidget {
  const ErrorBoundary({
    required this.asyncValue,
    required this.dataBuilder,
    super.key,
    this.onRetry,
    this.loadingWidget,
    this.emptyWidget,
    this.errorTitle,
    this.emptyMessage,
    this.showErrorDetails = false,
  });
  final AsyncValue<T> asyncValue;
  final Widget Function(T data) dataBuilder;
  final VoidCallback? onRetry;
  final Widget? loadingWidget;
  final Widget? emptyWidget;
  final String? errorTitle;
  final String? emptyMessage;
  final bool showErrorDetails;

  @override
  Widget build(BuildContext context, WidgetRef ref) => asyncValue.when(
    data: (data) {
      // التحقق من البيانات الفارغة
      if (data is List && data.isEmpty) {
        return emptyWidget ?? _buildEmptyState(context);
      }
      if (data == null) {
        return emptyWidget ?? _buildEmptyState(context);
      }
      return dataBuilder(data);
    },
    loading: () => loadingWidget ?? _buildLoadingState(context),
    error: (error, stackTrace) {
      _logger.e(
        'ErrorBoundary caught error: $error',
        error: error,
        stackTrace: stackTrace,
      );
      return _buildErrorState(context, error, stackTrace);
    },
  );

  Widget _buildLoadingState(BuildContext context) => const Center(
    child: Padding(
      padding: EdgeInsets.all(AppTheme.spacingL),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(color: AppTheme.primary),
          SizedBox(height: AppTheme.spacingM),
          Text(
            'جاري التحميل...',
            style: TextStyle(color: AppTheme.onSurfaceVariant, fontSize: 16),
          ),
        ],
      ),
    ),
  );

  Widget _buildEmptyState(BuildContext context) => Center(
    child: Padding(
      padding: const EdgeInsets.all(AppTheme.spacingL),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.inbox_outlined, size: 64, color: AppTheme.onSurface),
          const SizedBox(height: AppTheme.spacingM),
          Text(
            emptyMessage ?? 'لا توجد بيانات للعرض',
            style: const TextStyle(
              color: AppTheme.onSurfaceVariant,
              fontSize: 16,
            ),
            textAlign: TextAlign.center,
          ),
          if (onRetry != null) ...[
            const SizedBox(height: AppTheme.spacingL),
            ElevatedButton.icon(
              onPressed: onRetry,
              icon: const Icon(Icons.refresh),
              label: const Text('إعادة المحاولة'),
            ),
          ],
        ],
      ),
    ),
  );

  Widget _buildErrorState(
    BuildContext context,
    Object error,
    StackTrace? stackTrace,
  ) {
    final errorMessage = _getErrorMessage(error);

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingL),
        child: Card(
          child: Padding(
            padding: const EdgeInsets.all(AppTheme.spacingL),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(
                  Icons.error_outline,
                  size: 64,
                  color: AppTheme.error,
                ),
                const SizedBox(height: AppTheme.spacingM),
                Text(
                  errorTitle ?? 'حدث خطأ',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.error,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: AppTheme.spacingS),
                Text(
                  errorMessage,
                  style: const TextStyle(
                    color: AppTheme.onSurfaceVariant,
                    fontSize: 14,
                  ),
                  textAlign: TextAlign.center,
                ),
                if (showErrorDetails && stackTrace != null) ...[
                  const SizedBox(height: AppTheme.spacingM),
                  ExpansionTile(
                    title: const Text('تفاصيل الخطأ'),
                    children: [
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(AppTheme.spacingS),
                        decoration: BoxDecoration(
                          color: Colors.grey.shade100,
                          borderRadius: BorderRadius.circular(AppTheme.radiusS),
                        ),
                        child: Text(
                          stackTrace.toString(),
                          style: const TextStyle(
                            fontFamily: 'monospace',
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
                if (onRetry != null) ...[
                  const SizedBox(height: AppTheme.spacingL),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      ElevatedButton.icon(
                        onPressed: onRetry,
                        icon: const Icon(Icons.refresh),
                        label: const Text('إعادة المحاولة'),
                      ),
                      OutlinedButton.icon(
                        onPressed: () => Navigator.of(context).pop(),
                        icon: const Icon(Icons.close),
                        label: const Text('إغلاق'),
                      ),
                    ],
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  String _getErrorMessage(Object error) {
    if (error.toString().contains('SocketException')) {
      return 'تحقق من اتصالك بالإنترنت وحاول مرة أخرى';
    } else if (error.toString().contains('TimeoutException')) {
      return 'انتهت مهلة الاتصال، حاول مرة أخرى';
    } else if (error.toString().contains('FormatException')) {
      return 'خطأ في تنسيق البيانات';
    } else if (error.toString().contains('Unauthorized')) {
      return 'غير مصرح لك بالوصول، يرجى تسجيل الدخول مرة أخرى';
    } else {
      return 'حدث خطأ غير متوقع، يرجى المحاولة مرة أخرى';
    }
  }
}

/// Error Boundary مبسط للاستخدام السريع
class SimpleErrorBoundary<T> extends ConsumerWidget {
  const SimpleErrorBoundary({
    required this.data,
    required this.builder,
    super.key,
    this.onRetry,
  });
  final AsyncValue<T> data;
  final Widget Function(T) builder;
  final VoidCallback? onRetry;

  @override
  Widget build(BuildContext context, WidgetRef ref) => ErrorBoundary<T>(
    asyncValue: data,
    dataBuilder: builder,
    onRetry: onRetry,
  );
}

/// Loading Widget محسن
class CustomLoadingWidget extends StatelessWidget {
  const CustomLoadingWidget({super.key, this.message, this.size});
  final String? message;
  final double? size;

  @override
  Widget build(BuildContext context) => Center(
    child: Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        SizedBox(
          width: size ?? 40,
          height: size ?? 40,
          child: const CircularProgressIndicator(
            color: AppTheme.primary,
            strokeWidth: 3,
          ),
        ),
        if (message != null) ...[
          const SizedBox(height: AppTheme.spacingM),
          Text(
            message!,
            style: const TextStyle(
              color: AppTheme.onSurfaceVariant,
              fontSize: 14,
            ),
          ),
        ],
      ],
    ),
  );
}

/// Loading widget with a specific message
class LoadingWithMessage extends StatelessWidget {
  const LoadingWithMessage({super.key, required this.message});
  final String message;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primary),
          ),
          if (message.isNotEmpty) ...[
            const SizedBox(height: AppTheme.spacingL),
            Text(
              message,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: AppTheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }
}

/// Error Widget محسن
class CustomErrorWidget extends StatelessWidget {
  const CustomErrorWidget({
    super.key,
    this.error,
    this.stackTrace,
    this.onRetry,
    this.showDetails = false,
  });

  final Object? error;
  final StackTrace? stackTrace;
  final VoidCallback? onRetry;
  final bool showDetails;

  @override
  Widget build(BuildContext context) => Center(
    child: Padding(
      padding: const EdgeInsets.all(AppTheme.spacingL),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.warning_amber_rounded,
            color: AppTheme.errorContainer,
            size: 60,
          ),
          const SizedBox(height: AppTheme.spacingM),
          Text(
            'حدث خطأ غير متوقع',
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(color: AppTheme.error),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppTheme.spacingS),
          if (showDetails)
            ExpansionTile(
              title: const Text('التفاصيل'),
              children: [
                Container(
                  padding: const EdgeInsets.all(AppTheme.spacingM),
                  decoration: BoxDecoration(
                    color: AppTheme.errorContainer.withAlpha(
                      (0.1 * 255).toInt(),
                    ),
                    borderRadius: BorderRadius.circular(AppTheme.radiusM),
                  ),
                  child: SelectableText(
                    '${error?.toString() ?? 'غير معروف'}\n\n${stackTrace?.toString() ?? ''}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppTheme.onSurfaceVariant,
                    ),
                  ),
                ),
              ],
            ),
          if (onRetry != null) ...[
            const SizedBox(height: AppTheme.spacingL),
            ElevatedButton.icon(
              onPressed: onRetry,
              style: FilledButton.styleFrom(
                backgroundColor: AppTheme.primary,
                foregroundColor: AppTheme.onPrimary,
              ),
              icon: const Icon(Icons.refresh),
              label: const Text('إعادة المحاولة'),
            ),
          ],
        ],
      ),
    ),
  );
}

/// Simple Error Widget
class SimpleErrorWidget extends StatelessWidget {
  const SimpleErrorWidget(this.errorMessage, {super.key, this.onRetry});
  final String errorMessage;
  final VoidCallback? onRetry;

  @override
  Widget build(BuildContext context) => Padding(
    padding: const EdgeInsets.all(AppTheme.spacingL),
    child: Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const Icon(Icons.error_outline, color: AppTheme.error, size: 48),
        const SizedBox(height: AppTheme.spacingM),
        Text(
          errorMessage,
          textAlign: TextAlign.center,
          style: const TextStyle(color: AppTheme.onSurfaceVariant),
        ),
        if (onRetry != null) ...[
          const SizedBox(height: AppTheme.spacingL),
          FilledButton(onPressed: onRetry, child: const Text('إعادة المحاولة')),
        ],
      ],
    ),
  );
}

// Fallback Error Widget
class FallbackErrorWidget extends StatelessWidget {
  const FallbackErrorWidget({
    super.key,
    required this.error,
    required this.stackTrace,
  });

  final Object error;
  final StackTrace stackTrace;

  @override
  Widget build(BuildContext context) {
    return Material(
      child: Directionality(
        textDirection: TextDirection.ltr,
        child: Container(
          color: const Color(0xFFfcf3f3),
          padding: const EdgeInsets.all(AppTheme.spacingM),
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Unhandled Application Error',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFFd9534f),
                  ),
                ),
                const SizedBox(height: AppTheme.spacingL),
                _buildSection(
                  context,
                  title: 'Error Details',
                  content: error.toString(),
                  icon: Icons.error_outline,
                ),
                const SizedBox(height: AppTheme.spacingM),
                _buildSection(
                  context,
                  title: 'Stack Trace',
                  content: stackTrace.toString(),
                  icon: Icons.code,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSection(
    BuildContext context, {
    required String title,
    required String content,
    required IconData icon,
  }) {
    return Card(
      color: Colors.white,
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppTheme.radiusL),
        side: const BorderSide(color: Color(0xFFeeeeee)),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingL),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: const Color(0xFFd9534f)),
                const SizedBox(width: AppTheme.spacingS),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF333333),
                  ),
                ),
              ],
            ),
            const Divider(height: AppTheme.spacingL),
            Container(
              padding: const EdgeInsets.all(AppTheme.spacingS),
              decoration: BoxDecoration(
                color: const Color(0xFFf7f7f9),
                borderRadius: BorderRadius.circular(AppTheme.radiusS),
              ),
              child: SelectableText(
                content,
                style: const TextStyle(
                  fontFamily: 'monospace',
                  fontSize: 13,
                  color: AppTheme.onSurfaceVariant,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
