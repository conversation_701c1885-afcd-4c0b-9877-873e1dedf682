import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../services/backend_resilience_service.dart';

/// Banner widget that shows backend status to users
class BackendStatusBanner extends HookConsumerWidget {
  const BackendStatusBanner({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final resilienceService = useMemoized(() => BackendResilienceService());
    final currentStatus = useState(resilienceService.currentStatus);
    
    useEffect(() {
      void statusListener() {
        currentStatus.value = resilienceService.currentStatus;
      }
      
      resilienceService.addStatusListener(statusListener);
      return () => resilienceService.removeStatusListener(statusListener);
    }, []);

    // Don't show banner if backend is healthy
    if (currentStatus.value == BackendStatus.healthy) {
      return const SizedBox.shrink();
    }

    return Material(
      elevation: 2,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: _getBackgroundColor(currentStatus.value),
          border: Border(
            bottom: BorderSide(
              color: _getBorderColor(currentStatus.value),
              width: 1,
            ),
          ),
        ),
        child: Row(
          children: [
            Icon(
              _getStatusIcon(currentStatus.value),
              color: _getIconColor(currentStatus.value),
              size: 20,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    resilienceService.getStatusMessage(),
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      color: _getTextColor(currentStatus.value),
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  if (resilienceService.getRecoverySuggestion().isNotEmpty) ...[
                    const SizedBox(height: 4),
                    Text(
                      resilienceService.getRecoverySuggestion(),
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: _getTextColor(currentStatus.value).withAlpha((255 * 0.8).round()),
                      ),
                    ),
                  ],
                ],
              ),
            ),
            if (currentStatus.value == BackendStatus.unavailable ||
                currentStatus.value == BackendStatus.degraded) ...[
              const SizedBox(width: 8),
              _buildRetryButton(context, resilienceService),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildRetryButton(BuildContext context, BackendResilienceService service) {
    return TextButton.icon(
      onPressed: () async {
        await service.checkBackendHealth();
      },
      icon: const Icon(Icons.refresh, size: 16),
      label: const Text('إعادة المحاولة'),
      style: TextButton.styleFrom(
        foregroundColor: _getTextColor(service.currentStatus),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        minimumSize: Size.zero,
        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
      ),
    );
  }

  IconData _getStatusIcon(BackendStatus status) {
    switch (status) {
      case BackendStatus.healthy:
        return Icons.check_circle;
      case BackendStatus.degraded:
        return Icons.warning;
      case BackendStatus.unavailable:
        return Icons.error;
      case BackendStatus.unknown:
        return Icons.help;
    }
  }

  Color _getBackgroundColor(BackendStatus status) {
    switch (status) {
      case BackendStatus.healthy:
        return Colors.green.shade50;
      case BackendStatus.degraded:
        return Colors.orange.shade50;
      case BackendStatus.unavailable:
        return Colors.red.shade50;
      case BackendStatus.unknown:
        return Colors.grey.shade50;
    }
  }

  Color _getBorderColor(BackendStatus status) {
    switch (status) {
      case BackendStatus.healthy:
        return Colors.green.shade200;
      case BackendStatus.degraded:
        return Colors.orange.shade200;
      case BackendStatus.unavailable:
        return Colors.red.shade200;
      case BackendStatus.unknown:
        return Colors.grey.shade200;
    }
  }

  Color _getIconColor(BackendStatus status) {
    switch (status) {
      case BackendStatus.healthy:
        return Colors.green.shade600;
      case BackendStatus.degraded:
        return Colors.orange.shade600;
      case BackendStatus.unavailable:
        return Colors.red.shade600;
      case BackendStatus.unknown:
        return Colors.grey.shade600;
    }
  }

  Color _getTextColor(BackendStatus status) {
    switch (status) {
      case BackendStatus.healthy:
        return Colors.green.shade800;
      case BackendStatus.degraded:
        return Colors.orange.shade800;
      case BackendStatus.unavailable:
        return Colors.red.shade800;
      case BackendStatus.unknown:
        return Colors.grey.shade800;
    }
  }
}

/// Compact status indicator for use in app bars or small spaces
class BackendStatusIndicator extends HookConsumerWidget {
  const BackendStatusIndicator({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final resilienceService = useMemoized(() => BackendResilienceService());
    final currentStatus = useState(resilienceService.currentStatus);
    
    useEffect(() {
      void statusListener() {
        currentStatus.value = resilienceService.currentStatus;
      }
      
      resilienceService.addStatusListener(statusListener);
      return () => resilienceService.removeStatusListener(statusListener);
    }, []);

    // Only show indicator if there's an issue
    if (currentStatus.value == BackendStatus.healthy) {
      return const SizedBox.shrink();
    }

    return Tooltip(
      message: resilienceService.getStatusMessage(),
      child: Container(
        width: 8,
        height: 8,
        decoration: BoxDecoration(
          color: _getIndicatorColor(currentStatus.value),
          shape: BoxShape.circle,
        ),
      ),
    );
  }

  Color _getIndicatorColor(BackendStatus status) {
    switch (status) {
      case BackendStatus.healthy:
        return Colors.green;
      case BackendStatus.degraded:
        return Colors.orange;
      case BackendStatus.unavailable:
        return Colors.red;
      case BackendStatus.unknown:
        return Colors.grey;
    }
  }
}

/// Provider for backend resilience service
final backendResilienceProvider = Provider<BackendResilienceService>((ref) {
  return BackendResilienceService();
});