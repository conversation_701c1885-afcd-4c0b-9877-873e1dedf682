import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';

/// A lightweight wrapper around [CachedNetworkImage] providing
/// sane defaults (fade-in, placeholder, error icon) and optional
/// border-radius support.
///
/// ```dart
/// const NetworkImageCached(
///   'https://example.com/my.jpg',
///   width: 120,
///   height: 120,
///   borderRadius: BorderRadius.all(Radius.circular(8)),
/// );
/// ```
class NetworkImageCached extends StatelessWidget {
  const NetworkImageCached(
    this.url, {
    super.key,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.borderRadius = BorderRadius.zero,
    this.errorIcon = Icons.broken_image_outlined,
  });

  /// Remote image URL to fetch.
  final String url;

  /// Desired widget width. If null, expands as much as possible.
  final double? width;

  /// Desired widget height. If null, expands as much as possible.
  final double? height;

  /// How the image should be inscribed into the space allocated during layout.
  final BoxFit fit;

  /// Corner radius to clip image with.
  final BorderRadius borderRadius;

  /// Icon shown when the image fails to load.
  final IconData errorIcon;

  @override
  Widget build(BuildContext context) {
    Widget image = CachedNetworkImage(
      imageUrl: url,
      width: width,
      height: height,
      fit: fit,
      placeholder: (context, url) => _buildPlaceholder(context),
      errorWidget: (context, url, error) => _buildError(context),
      fadeInDuration: const Duration(milliseconds: 150),
      memCacheWidth: width?.round(),
      memCacheHeight: height?.round(),
    );

    if (borderRadius != BorderRadius.zero) {
      image = ClipRRect(borderRadius: borderRadius, child: image);
    }

    return image;
  }

  Widget _buildPlaceholder(BuildContext context) => Container(
    width: width,
    height: height,
    alignment: Alignment.center,
    color: Theme.of(context).colorScheme.surfaceContainerHighest,
    child: const SizedBox(
      width: 24,
      height: 24,
      child: CircularProgressIndicator(strokeWidth: 1.8),
    ),
  );

  Widget _buildError(BuildContext context) => Container(
    width: width,
    height: height,
    alignment: Alignment.center,
    color: Theme.of(context).colorScheme.surfaceContainerHighest,
    child: Icon(errorIcon, color: Theme.of(context).colorScheme.error),
  );
}
