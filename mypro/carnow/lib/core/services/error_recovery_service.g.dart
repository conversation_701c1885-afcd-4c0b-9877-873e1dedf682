// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'error_recovery_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$errorRecoveryServiceHash() =>
    r'497f25eba0e88d0f3e6e5f80fb26d5abb5c2e00a';

/// Provider لخدمة استرداد الأخطاء
///
/// Copied from [errorRecoveryService].
@ProviderFor(errorRecoveryService)
final errorRecoveryServiceProvider =
    AutoDisposeFutureProvider<ErrorRecoveryService>.internal(
      errorRecoveryService,
      name: r'errorRecoveryServiceProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$errorRecoveryServiceHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef ErrorRecoveryServiceRef =
    AutoDisposeFutureProviderRef<ErrorRecoveryService>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
