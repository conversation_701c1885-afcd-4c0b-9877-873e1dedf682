import 'dart:convert';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:logging/logging.dart';

part 'dead_letter_queue_service.g.dart';

final _logger = Logger('DeadLetterQueueService');

/// Dead Letter Queue service to handle operations that failed after all retry attempts
/// Stores failed operations for later processing or analysis
@riverpod
DeadLetterQueueService deadLetterQueueService(DeadLetterQueueServiceRef ref) {
  return DeadLetterQueueService();
}

class DeadLetterQueueService {
  static const String _queueKey = 'carnow_dead_letter_queue';
  static const int _maxQueueSize = 100;

  /// Add a failed operation to the dead letter queue
  Future<void> addFailedOperation(
    String operationName,
    String errorMessage,
    int attemptCount, {
    Map<String, dynamic>? operationData,
    String? endpoint,
    String? userId,
  }) async {
    try {
      final queue = await _getQueue();

      final failedOperation = FailedOperation(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        operationName: operationName,
        errorMessage: errorMessage,
        attemptCount: attemptCount,
        timestamp: DateTime.now(),
        status: FailedOperationStatus.pending,
        operationData: operationData,
        endpoint: endpoint,
        userId: userId,
      );

      queue.add(failedOperation);

      // Keep only the most recent operations
      if (queue.length > _maxQueueSize) {
        queue.removeRange(0, queue.length - _maxQueueSize);
      }

      await _saveQueue(queue);
      _logger.warning(
        'Added failed operation to dead letter queue: $operationName',
      );
    } catch (error) {
      _logger.severe('Failed to add operation to dead letter queue: $error');
    }
  }

  /// Get all failed operations from the queue
  Future<List<FailedOperation>> getFailedOperations() async {
    try {
      return await _getQueue();
    } catch (e) {
      _logger.severe('Failed to get failed operations: $e');
      return [];
    }
  }

  /// Get failed operations by status
  Future<List<FailedOperation>> getFailedOperationsByStatus(
    FailedOperationStatus status,
  ) async {
    try {
      final queue = await _getQueue();
      return queue.where((op) => op.status == status).toList();
    } catch (e) {
      _logger.severe('Failed to get failed operations by status: $e');
      return [];
    }
  }

  /// Mark a failed operation as processed
  Future<void> markAsProcessed(String operationId) async {
    try {
      final queue = await _getQueue();
      final index = queue.indexWhere((op) => op.id == operationId);

      if (index != -1) {
        queue[index] = queue[index].copyWith(
          status: FailedOperationStatus.processed,
          processedAt: DateTime.now(),
        );
        await _saveQueue(queue);
        _logger.info('Marked operation as processed: $operationId');
      }
    } catch (e) {
      _logger.severe('Failed to mark operation as processed: $e');
    }
  }

  /// Mark a failed operation as ignored
  Future<void> markAsIgnored(String operationId) async {
    try {
      final queue = await _getQueue();
      final index = queue.indexWhere((op) => op.id == operationId);

      if (index != -1) {
        queue[index] = queue[index].copyWith(
          status: FailedOperationStatus.ignored,
          processedAt: DateTime.now(),
        );
        await _saveQueue(queue);
        _logger.info('Marked operation as ignored: $operationId');
      }
    } catch (e) {
      _logger.severe('Failed to mark operation as ignored: $e');
    }
  }

  /// Retry a failed operation
  Future<bool> retryOperation(String operationId) async {
    try {
      final queue = await _getQueue();
      final index = queue.indexWhere((op) => op.id == operationId);

      if (index != -1) {
        final operation = queue[index];

        // Update retry count
        queue[index] = operation.copyWith(
          attemptCount: operation.attemptCount + 1,
          status: FailedOperationStatus.retrying,
        );
        await _saveQueue(queue);

        _logger.info('Retrying operation: ${operation.operationName}');

        // Here you would implement the actual retry logic
        // For now, we'll just simulate the execution
        await Future.delayed(const Duration(seconds: 1));

        // Simulate 70% success rate for retries
        final success = DateTime.now().millisecond % 10 < 7;

        if (success) {
          await markAsProcessed(operationId);
          return true;
        } else {
          queue[index] = queue[index].copyWith(
            status: FailedOperationStatus.failed,
            errorMessage: '${operation.errorMessage} (retry failed)',
          );
          await _saveQueue(queue);
          return false;
        }
      }

      return false;
    } catch (e) {
      _logger.severe('Failed to retry operation: $e');
      return false;
    }
  }

  /// Clear all processed and ignored operations
  Future<void> clearProcessedOperations() async {
    try {
      final queue = await _getQueue();
      final filteredQueue = queue
          .where(
            (op) =>
                op.status != FailedOperationStatus.processed &&
                op.status != FailedOperationStatus.ignored,
          )
          .toList();

      await _saveQueue(filteredQueue);
      _logger.info('Cleared processed operations from dead letter queue');
    } catch (e) {
      _logger.severe('Failed to clear processed operations: $e');
    }
  }

  /// Clear all operations from the queue
  Future<void> clearAllOperations() async {
    try {
      await _saveQueue([]);
      _logger.info('Cleared all operations from dead letter queue');
    } catch (e) {
      _logger.severe('Failed to clear all operations: $e');
    }
  }

  /// Get queue statistics
  Future<DeadLetterQueueStats> getQueueStats() async {
    try {
      final queue = await _getQueue();

      final stats = DeadLetterQueueStats(
        totalOperations: queue.length,
        pendingOperations: queue
            .where((op) => op.status == FailedOperationStatus.pending)
            .length,
        processedOperations: queue
            .where((op) => op.status == FailedOperationStatus.processed)
            .length,
        ignoredOperations: queue
            .where((op) => op.status == FailedOperationStatus.ignored)
            .length,
        failedOperations: queue
            .where((op) => op.status == FailedOperationStatus.failed)
            .length,
        retryingOperations: queue
            .where((op) => op.status == FailedOperationStatus.retrying)
            .length,
        oldestOperation: queue.isNotEmpty ? queue.first.timestamp : null,
        newestOperation: queue.isNotEmpty ? queue.last.timestamp : null,
      );

      return stats;
    } catch (e) {
      _logger.severe('Failed to get queue stats: $e');
      return const DeadLetterQueueStats(
        totalOperations: 0,
        pendingOperations: 0,
        processedOperations: 0,
        ignoredOperations: 0,
        failedOperations: 0,
        retryingOperations: 0,
      );
    }
  }

  /// Get the queue from storage
  Future<List<FailedOperation>> _getQueue() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final queueJson = prefs.getString(_queueKey);

      if (queueJson == null) return [];

      final List<dynamic> queueList = jsonDecode(queueJson);
      return queueList.map((json) => FailedOperation.fromJson(json)).toList();
    } catch (e) {
      _logger.severe('Failed to get queue from storage: $e');
      return [];
    }
  }

  /// Save the queue to storage
  Future<void> _saveQueue(List<FailedOperation> queue) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final queueJson = jsonEncode(queue.map((op) => op.toJson()).toList());
      await prefs.setString(_queueKey, queueJson);
    } catch (e) {
      _logger.severe('Failed to save queue to storage: $e');
    }
  }
}

/// Represents a failed operation in the dead letter queue
class FailedOperation {
  const FailedOperation({
    required this.id,
    required this.operationName,
    required this.errorMessage,
    required this.attemptCount,
    required this.timestamp,
    required this.status,
    this.operationData,
    this.endpoint,
    this.userId,
    this.processedAt,
  });

  final String id;
  final String operationName;
  final String errorMessage;
  final int attemptCount;
  final DateTime timestamp;
  final FailedOperationStatus status;
  final Map<String, dynamic>? operationData;
  final String? endpoint;
  final String? userId;
  final DateTime? processedAt;

  factory FailedOperation.fromJson(Map<String, dynamic> json) {
    return FailedOperation(
      id: json['id'] as String,
      operationName: json['operationName'] as String,
      errorMessage: json['errorMessage'] as String,
      attemptCount: json['attemptCount'] as int,
      timestamp: DateTime.parse(json['timestamp'] as String),
      status: FailedOperationStatus.values.byName(json['status'] as String),
      operationData: json['operationData'] as Map<String, dynamic>?,
      endpoint: json['endpoint'] as String?,
      userId: json['userId'] as String?,
      processedAt: json['processedAt'] != null
          ? DateTime.parse(json['processedAt'] as String)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'operationName': operationName,
      'errorMessage': errorMessage,
      'attemptCount': attemptCount,
      'timestamp': timestamp.toIso8601String(),
      'status': status.name,
      'operationData': operationData,
      'endpoint': endpoint,
      'userId': userId,
      'processedAt': processedAt?.toIso8601String(),
    };
  }

  FailedOperation copyWith({
    String? id,
    String? operationName,
    String? errorMessage,
    int? attemptCount,
    DateTime? timestamp,
    FailedOperationStatus? status,
    Map<String, dynamic>? operationData,
    String? endpoint,
    String? userId,
    DateTime? processedAt,
  }) {
    return FailedOperation(
      id: id ?? this.id,
      operationName: operationName ?? this.operationName,
      errorMessage: errorMessage ?? this.errorMessage,
      attemptCount: attemptCount ?? this.attemptCount,
      timestamp: timestamp ?? this.timestamp,
      status: status ?? this.status,
      operationData: operationData ?? this.operationData,
      endpoint: endpoint ?? this.endpoint,
      userId: userId ?? this.userId,
      processedAt: processedAt ?? this.processedAt,
    );
  }
}

/// Status of a failed operation
enum FailedOperationStatus { pending, retrying, processed, ignored, failed }

/// Statistics for the dead letter queue
class DeadLetterQueueStats {
  const DeadLetterQueueStats({
    required this.totalOperations,
    required this.pendingOperations,
    required this.processedOperations,
    required this.ignoredOperations,
    required this.failedOperations,
    required this.retryingOperations,
    this.oldestOperation,
    this.newestOperation,
  });

  final int totalOperations;
  final int pendingOperations;
  final int processedOperations;
  final int ignoredOperations;
  final int failedOperations;
  final int retryingOperations;
  final DateTime? oldestOperation;
  final DateTime? newestOperation;

  double get successRate {
    if (totalOperations == 0) return 0.0;
    return processedOperations / totalOperations;
  }

  double get failureRate {
    if (totalOperations == 0) return 0.0;
    return (failedOperations + pendingOperations) / totalOperations;
  }
}
