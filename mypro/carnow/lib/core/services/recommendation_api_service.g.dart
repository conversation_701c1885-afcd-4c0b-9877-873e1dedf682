// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'recommendation_api_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$recommendationApiServiceHash() =>
    r'5decf36803008b8e9eed886e88c3fe9ba1ca14e7';

/// Simple Recommendation API Service following Forever Plan Architecture
/// Flutter (UI Only) → Go API → Supabase (Data Only)
///
/// Copied from [recommendationApiService].
@ProviderFor(recommendationApiService)
final recommendationApiServiceProvider =
    AutoDisposeProvider<RecommendationApiService>.internal(
      recommendationApiService,
      name: r'recommendationApiServiceProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$recommendationApiServiceHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef RecommendationApiServiceRef =
    AutoDisposeProviderRef<RecommendationApiService>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
