// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'supabase_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$supabaseServiceHash() => r'cd458afb9d5de897b56c06998379ecfee4ca2fcf';

/// SupabaseService for Forever Plan Architecture (DEPRECATED)
/// Flutter (UI Only) → Go API → Supabase (Data Only)
///
/// 🚨 DEPRECATED: This service is deprecated for Forever Plan compliance
/// New code MUST use:
/// - SimpleAuthSystem for authentication
/// - SimpleApiClient for ALL data operations
///
/// This service remains only for app initialization compatibility
/// DEPRECATED: Legacy provider - DO NOT USE
///
/// Copied from [supabaseService].
@ProviderFor(supabaseService)
final supabaseServiceProvider = AutoDisposeProvider<SupabaseService>.internal(
  supabaseService,
  name: r'supabaseServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$supabaseServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef SupabaseServiceRef = AutoDisposeProviderRef<SupabaseService>;
String _$supabaseClientHash() => r'8a41af1cc89d55b7474294fc25eb920ab66c80f7';

/// DEPRECATED: Legacy client provider - FORBIDDEN FOR NEW CODE
///
/// Copied from [supabaseClient].
@ProviderFor(supabaseClient)
final supabaseClientProvider = AutoDisposeProvider<Never>.internal(
  supabaseClient,
  name: r'supabaseClientProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$supabaseClientHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef SupabaseClientRef = AutoDisposeProviderRef<Never>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
