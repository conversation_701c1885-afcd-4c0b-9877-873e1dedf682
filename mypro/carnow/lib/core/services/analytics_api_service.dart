import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:logging/logging.dart';

import '../networking/simple_api_client.dart';

part 'analytics_api_service.g.dart';

final _logger = Logger('AnalyticsApiService');

/// Simple Analytics API Service following Forever Plan Architecture
/// Flutter (UI Only) → Go API → Supabase (Data Only)
@riverpod
AnalyticsApiService analyticsApiService(Ref ref) {
  final apiClient = ref.watch(simpleApiClientProvider);
  return AnalyticsApiService(apiClient);
}

class AnalyticsApiService {
  const AnalyticsApiService(this._apiClient);
  
  final SimpleApiClient _apiClient;

  /// Get overview analytics data
  Future<Map<String, dynamic>> getOverviewData() async {
    try {
      _logger.info('Fetching overview analytics data...');
      final response = await _apiClient.get('/analytics/overview');
      return response.data as Map<String, dynamic>;
    } catch (e) {
      _logger.severe('Error fetching overview data: $e');
      throw Exception('فشل في تحميل البيانات العامة: $e');
    }
  }

  /// Get users analytics data
  Future<Map<String, dynamic>> getUsersData() async {
    try {
      _logger.info('Fetching users analytics data...');
      final response = await _apiClient.get('/analytics/users');
      return response.data as Map<String, dynamic>;
    } catch (e) {
      _logger.severe('Error fetching users data: $e');
      throw Exception('فشل في تحميل بيانات المستخدمين: $e');
    }
  }

  /// Get products analytics data
  Future<Map<String, dynamic>> getProductsData() async {
    try {
      _logger.info('Fetching products analytics data...');
      final response = await _apiClient.get('/analytics/products');
      return response.data as Map<String, dynamic>;
    } catch (e) {
      _logger.severe('Error fetching products data: $e');
      throw Exception('فشل في تحميل بيانات المنتجات: $e');
    }
  }

  /// Get sales analytics data
  Future<Map<String, dynamic>> getSalesData() async {
    try {
      _logger.info('Fetching sales analytics data...');
      final response = await _apiClient.get('/analytics/sales');
      return response.data as Map<String, dynamic>;
    } catch (e) {
      _logger.severe('Error fetching sales data: $e');
      throw Exception('فشل في تحميل بيانات المبيعات: $e');
    }
  }

  /// Get engagement analytics data
  Future<Map<String, dynamic>> getEngagementData() async {
    try {
      _logger.info('Fetching engagement analytics data...');
      final response = await _apiClient.get('/analytics/engagement');
      return response.data as Map<String, dynamic>;
    } catch (e) {
      _logger.severe('Error fetching engagement data: $e');
      throw Exception('فشل في تحميل بيانات التفاعل: $e');
    }
  }
} 