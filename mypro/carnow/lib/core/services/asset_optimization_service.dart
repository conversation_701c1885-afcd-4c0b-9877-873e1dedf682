import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../config/backend_config.dart';

/// Service for optimizing and managing application assets
class AssetOptimizationService {
  static const String _assetCacheKey = 'asset_cache_v1';
  static const String _assetVersionKey = 'asset_version';
  static const Duration _cacheExpiry = Duration(hours: 24);

  /// Preload critical assets
  static Future<void> preloadCriticalAssets() async {
    final criticalAssets = [
      'assets/images/logo.png',
      'assets/images/placeholder.png',
      'assets/icons/google_logo.svg',
    ];

    final futures = criticalAssets.map((asset) async {
      try {
        await rootBundle.load(asset);
        debugPrint('✅ Preloaded asset: $asset');
      } catch (e) {
        debugPrint('❌ Failed to preload asset $asset: $e');
      }
    });

    await Future.wait(futures);
  }

  /// Load and cache remote assets
  static Future<Map<String, dynamic>> loadRemoteAssets() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cachedData = prefs.getString(_assetCacheKey);
      final cachedVersion = prefs.getString(_assetVersionKey);

      // Check if cache is valid
      if (cachedData != null && cachedVersion != null) {
        final cacheTime = DateTime.tryParse(cachedVersion);
        if (cacheTime != null &&
            DateTime.now().difference(cacheTime) < _cacheExpiry) {
          return json.decode(cachedData);
        }
      }

      // Fetch fresh assets from backend
      final freshAssets = await _fetchRemoteAssets();

      // Cache the fresh data
      await prefs.setString(_assetCacheKey, json.encode(freshAssets));
      await prefs.setString(_assetVersionKey, DateTime.now().toIso8601String());

      return freshAssets;
    } catch (e) {
      debugPrint('Error loading remote assets: $e');
      return {};
    }
  }

  /// Fetch remote assets from backend
  static Future<Map<String, dynamic>> _fetchRemoteAssets() async {
    // This would typically fetch from your backend API
    // For now, return a mock structure
    return {
      'fonts': {
        'cairo_regular':
            '${BackendConfig.baseUrl}/assets/fonts/cairo-regular.woff2',
        'cairo_bold': '${BackendConfig.baseUrl}/assets/fonts/cairo-bold.woff2',
      },
      'icons': {
        'app_icon': '${BackendConfig.baseUrl}/assets/icons/app-icon.png',
        'notification_icon':
            '${BackendConfig.baseUrl}/assets/icons/notification.png',
      },
      'images': {
        'splash_background':
            '${BackendConfig.baseUrl}/assets/images/splash-bg.jpg',
        'onboarding_1':
            '${BackendConfig.baseUrl}/assets/images/onboarding-1.jpg',
        'onboarding_2':
            '${BackendConfig.baseUrl}/assets/images/onboarding-2.jpg',
        'onboarding_3':
            '${BackendConfig.baseUrl}/assets/images/onboarding-3.jpg',
      },
      'version': DateTime.now().millisecondsSinceEpoch,
    };
  }

  /// Clear asset cache
  static Future<void> clearAssetCache() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_assetCacheKey);
    await prefs.remove(_assetVersionKey);
  }

  /// Get asset cache size
  static Future<int> getAssetCacheSize() async {
    final prefs = await SharedPreferences.getInstance();
    final cachedData = prefs.getString(_assetCacheKey);
    return cachedData?.length ?? 0;
  }

  /// Optimize SVG assets
  static Future<String> optimizeSvgAsset(String svgContent) async {
    // Remove unnecessary whitespace and comments
    String optimized = svgContent
        .replaceAll(RegExp(r'<!--.*?-->', dotAll: true), '')
        .replaceAll(RegExp(r'\s+'), ' ')
        .trim();

    // Remove unnecessary attributes
    optimized = optimized
        .replaceAll(RegExp(r'xmlns:.*?=".*?"'), '')
        .replaceAll(RegExp(r'xml:space="preserve"'), '')
        .replaceAll(RegExp(r'enable-background=".*?"'), '');

    return optimized;
  }

  /// Compress and optimize images
  static Future<Uint8List?> compressImage(
    Uint8List imageBytes, {
    int quality = 85,
    int? maxWidth,
    int? maxHeight,
  }) async {
    try {
      // This is a simplified version - in production you might use
      // a more sophisticated image compression library
      return imageBytes; // Placeholder implementation
    } catch (e) {
      debugPrint('Error compressing image: $e');
      return null;
    }
  }

  /// Generate responsive image sizes
  static Map<String, String> generateResponsiveImageUrls(
    String baseUrl, {
    List<int> sizes = const [300, 600, 900, 1200],
  }) {
    final responsiveUrls = <String, String>{};

    for (final size in sizes) {
      responsiveUrls['${size}w'] =
          '${BackendConfig.baseUrl}/api/v1/images/resize?url=${Uri.encodeComponent(baseUrl)}&w=$size';
    }

    return responsiveUrls;
  }

  /// Preload fonts
  static Future<void> preloadFonts() async {
    final fontAssets = [
      'fonts/Cairo/Cairo-regular.ttf',
      'fonts/Cairo/Cairo-600.ttf',
      'fonts/Cairo/Cairo-700.ttf',
    ];

    final futures = fontAssets.map((font) async {
      try {
        await rootBundle.load(font);
        debugPrint('✅ Preloaded font: $font');
      } catch (e) {
        debugPrint('❌ Failed to preload font $font: $e');
      }
    });

    await Future.wait(futures);
  }

  /// Get asset loading statistics
  static Future<AssetLoadingStats> getLoadingStats() async {
    final prefs = await SharedPreferences.getInstance();
    final cacheSize = await getAssetCacheSize();
    final lastUpdate = prefs.getString(_assetVersionKey);

    return AssetLoadingStats(
      cacheSize: cacheSize,
      lastUpdate: lastUpdate != null ? DateTime.parse(lastUpdate) : null,
      totalAssets: 0, // Would be calculated based on actual assets
      cachedAssets: cacheSize > 0 ? 1 : 0,
    );
  }
}

/// Asset loading statistics
class AssetLoadingStats {
  final int cacheSize;
  final DateTime? lastUpdate;
  final int totalAssets;
  final int cachedAssets;

  const AssetLoadingStats({
    required this.cacheSize,
    this.lastUpdate,
    required this.totalAssets,
    required this.cachedAssets,
  });

  double get cacheHitRate => totalAssets > 0 ? cachedAssets / totalAssets : 0.0;

  bool get isCacheExpired =>
      lastUpdate == null ||
      DateTime.now().difference(lastUpdate!) > const Duration(hours: 24);

  Map<String, dynamic> toJson() => {
    'cache_size': cacheSize,
    'last_update': lastUpdate?.toIso8601String(),
    'total_assets': totalAssets,
    'cached_assets': cachedAssets,
    'cache_hit_rate': cacheHitRate,
    'is_cache_expired': isCacheExpired,
  };
}

/// CDN integration service
class CDNService {
  static const String _cdnBaseUrl = 'https://cdn.carnow.app';

  /// Get CDN URL for asset
  static String getCDNUrl(String assetPath) {
    if (assetPath.startsWith('http')) {
      return assetPath; // Already a full URL
    }

    return '$_cdnBaseUrl/$assetPath';
  }

  /// Get optimized CDN URL with transformations
  static String getOptimizedCDNUrl(
    String assetPath, {
    int? width,
    int? height,
    int quality = 85,
    String format = 'auto',
  }) {
    final baseUrl = getCDNUrl(assetPath);
    final params = <String, String>{};

    if (width != null) params['w'] = width.toString();
    if (height != null) params['h'] = height.toString();
    params['q'] = quality.toString();
    params['f'] = format;

    final queryString = params.entries
        .map((e) => '${e.key}=${e.value}')
        .join('&');

    return '$baseUrl?$queryString';
  }

  /// Preload CDN assets
  static Future<void> preloadCDNAssets(List<String> assetPaths) async {
    final futures = assetPaths.map((path) async {
      try {
        final url = getCDNUrl(path);
        // Preload the asset (implementation depends on asset type)
        debugPrint('✅ Preloaded CDN asset: $url');
      } catch (e) {
        debugPrint('❌ Failed to preload CDN asset $path: $e');
      }
    });

    await Future.wait(futures);
  }
}

/// Asset preloading manager
class AssetPreloadingManager {
  static bool _isPreloading = false;
  static final List<String> _preloadedAssets = [];

  /// Start preloading critical assets
  static Future<void> startPreloading() async {
    if (_isPreloading) return;

    _isPreloading = true;

    try {
      // Preload local assets
      await AssetOptimizationService.preloadCriticalAssets();
      await AssetOptimizationService.preloadFonts();

      // Load remote asset configuration
      await AssetOptimizationService.loadRemoteAssets();

      debugPrint('✅ Asset preloading completed');
    } catch (e) {
      debugPrint('❌ Asset preloading failed: $e');
    } finally {
      _isPreloading = false;
    }
  }

  /// Check if asset is preloaded
  static bool isAssetPreloaded(String assetPath) {
    return _preloadedAssets.contains(assetPath);
  }

  /// Get preloading status
  static bool get isPreloading => _isPreloading;

  /// Get preloaded assets count
  static int get preloadedAssetsCount => _preloadedAssets.length;
}

/// Providers for asset optimization
final assetOptimizationServiceProvider = Provider<AssetOptimizationService>(
  (ref) => AssetOptimizationService(),
);

final cdnServiceProvider = Provider<CDNService>((ref) => CDNService());

final assetPreloadingManagerProvider = Provider<AssetPreloadingManager>(
  (ref) => AssetPreloadingManager(),
);

/// Asset loading state provider
final assetLoadingStatsProvider = FutureProvider<AssetLoadingStats>((
  ref,
) async {
  return AssetOptimizationService.getLoadingStats();
});
