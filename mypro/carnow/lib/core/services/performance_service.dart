import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../config/app_config.dart';
import 'analytics_service.dart';

/// Performance Service Provider - Forever Plan Architecture
final performanceServiceProvider = Provider<PerformanceService>((ref) {
  return PerformanceService(ref);
});

/// خدمة مراقبة الأداء (إصدار مبسط)
class PerformanceService {
  PerformanceService(this._ref);
  
  final Ref _ref;
  
  /// Get AnalyticsService from provider
  AnalyticsService get _analytics => _ref.read(analyticsServiceProvider);
  final Map<String, dynamic> _cache = {};
  final Map<String, DateTime> _cacheTimestamps = {};

  // Cache management
  T? getCachedData<T>(String key) {
    final timestamp = _cacheTimestamps[key];
    if (timestamp == null) return null;

    if (DateTime.now().difference(timestamp) > AppConfig.cacheDuration) {
      _cache.remove(key);
      _cacheTimestamps.remove(key);
      return null;
    }

    return _cache[key] as T?;
  }

  void setCachedData<T>(String key, T data) {
    _cache[key] = data;
    _cacheTimestamps[key] = DateTime.now();

    // Clean old cache entries if cache is getting too large
    if (_cache.length > 1000) {
      _cleanOldCache();
    }
  }

  void _cleanOldCache() {
    final now = DateTime.now();
    final keysToRemove = <String>[];

    _cacheTimestamps.forEach((key, timestamp) {
      if (now.difference(timestamp) > AppConfig.cacheDuration) {
        keysToRemove.add(key);
      }
    });

    for (final key in keysToRemove) {
      _cache.remove(key);
      _cacheTimestamps.remove(key);
    }
  }

  // Performance monitoring
  Future<T> measurePerformance<T>(
    String operationName,
    Future<T> Function() operation,
  ) async {
    final stopwatch = Stopwatch()..start();

    try {
      final result = await operation();
      stopwatch.stop();

      await _analytics.logEvent(
        'performance',
        properties: {
          'operation': operationName,
          'duration_ms': stopwatch.elapsedMilliseconds,
          'success': true,
        },
      );

      if (kDebugMode) {
        print(
          'Performance: $operationName took ${stopwatch.elapsedMilliseconds}ms',
        );
      }

      return result;
    } catch (e) {
      stopwatch.stop();

      await _analytics.logEvent(
        'performance',
        properties: {
          'operation': operationName,
          'duration_ms': stopwatch.elapsedMilliseconds,
          'success': false,
          'error': e.toString(),
        },
      );

      rethrow;
    }
  }

  // Database query optimization
  Future<List<Map<String, dynamic>>> optimizedQuery({
    required String table,
    String? select,
    Map<String, dynamic>? filters,
    String? orderBy,
    bool ascending = true,
    int? limit,
    bool useCache = true,
  }) async {
    final cacheKey = _generateCacheKey(
      table,
      select,
      filters,
      orderBy,
      ascending,
      limit,
    );

    if (useCache) {
      final cached = getCachedData<List<Map<String, dynamic>>>(cacheKey);
      if (cached != null) {
        await _analytics.logEvent('cache_hit', properties: {'table': table});
        return cached;
      }
    }

    return measurePerformance('db_query_$table', () async {
      // DEPRECATED: Direct Supabase calls removed - use Go backend API instead
      throw UnimplementedError('Use SimpleApiClient for data fetching instead');
    });
  }

  String _generateCacheKey(
    String table,
    String? select,
    Map<String, dynamic>? filters,
    String? orderBy,
    bool ascending,
    int? limit,
  ) {
    return '$table:${select ?? 'all'}:${filters?.toString() ?? 'no_filters'}:${orderBy ?? 'no_order'}:$ascending:${limit ?? 'no_limit'}';
  }

  // Image optimization
  String optimizeImageUrl(String url, {int? width, int? height, int? quality}) {
    // Add image optimization parameters
    final uri = Uri.parse(url);
    final params = <String, String>{};

    if (width != null) params['w'] = width.toString();
    if (height != null) params['h'] = height.toString();
    if (quality != null) params['q'] = quality.toString();

    if (params.isEmpty) return url;

    return uri
        .replace(queryParameters: {...uri.queryParameters, ...params})
        .toString();
  }

  // Memory management
  void clearCache() {
    _cache.clear();
    _cacheTimestamps.clear();
  }

  Future<void> dispose() async {
    clearCache();
    _traces.clear();
    _analytics.dispose();
  }

  // Performance service is now initialized through provider

  // -------------------------------------------------------------------------
  // Trace management (simple in-memory implementation)
  // -------------------------------------------------------------------------

  final Map<String, _TraceData> _traces = {};

  Future<void> startTrace(String name) async {
    // If a trace with the same name exists, restart it.
    _traces[name] = _TraceData()..start();
  }

  Future<void> stopTrace(String name) async {
    final trace = _traces.remove(name);
    if (trace == null) return;
    trace.stop();
    await _analytics.logEvent(
      'trace',
      properties: {
        'name': name,
        'duration_ms': trace.elapsedMilliseconds,
        'metrics': trace.metrics,
        'attributes': trace.attributes,
      },
    );
  }

  Future<void> putMetric(String traceName, String metricName, int value) async {
    final trace = _traces[traceName];
    if (trace == null) return;
    trace.metrics[metricName] = value;
  }

  Future<void> putAttribute(
    String traceName,
    String attrName,
    String value,
  ) async {
    final trace = _traces[traceName];
    if (trace == null) return;
    trace.attributes[attrName] = value;
  }

  // -------------------------------------------------------------------------
  // High-level helpers expected by the test-suite
  // -------------------------------------------------------------------------

  Future<T> measureOperation<T>(
    String operationName,
    Future<T> Function() operation, {
    Map<String, String>? attributes,
    Map<String, int>? metrics,
  }) async {
    return measurePerformance(operationName, () async {
      final result = await operation();
      // Record extra information (best-effort).
      if ((attributes?.isNotEmpty ?? false) || (metrics?.isNotEmpty ?? false)) {
        await _analytics.logEvent(
          'performance_meta',
          properties: {
            'operation': operationName,
            if (attributes != null) 'attributes': attributes,
            if (metrics != null) 'metrics': metrics,
          },
        );
      }
      return result;
    });
  }

  Future<T> trackApiCall<T>(
    String path,
    Future<T> Function() call, {
    String method = 'GET',
  }) async {
    return measureOperation<T>(
      'api_$method:$path',
      call,
      attributes: {'method': method, 'path': path},
    );
  }

  Future<T> trackDatabaseOperation<T>(
    String operation,
    Future<T> Function() query, {
    required String table,
  }) async {
    return measureOperation<T>(
      'db_$operation:$table',
      query,
      attributes: {'operation': operation, 'table': table},
    );
  }

  Future<void> trackScreenLoad(String screenName) async {
    await measureOperation<void>(
      'screen_load:$screenName',
      () async {},
      attributes: {'screen': screenName},
    );
  }

  Future<void> logCustomPerformance(
    String eventName,
    Map<String, dynamic> data, {
    Duration? duration,
  }) async {
    await _analytics.logEvent(
      'custom_performance',
      properties: {
        'event': eventName,
        if (duration != null) 'duration_ms': duration.inMilliseconds,
        ...data,
      },
    );
  }
}

/// Extension for performance tracking has been removed
/// Use ref.read(performanceServiceProvider) instead

// -------------------------------------------------------------------------
// Internal helper class that models a running trace
// -------------------------------------------------------------------------
class _TraceData {
  final Stopwatch _stopwatch = Stopwatch();
  final Map<String, int> metrics = {};
  final Map<String, String> attributes = {};

  void start() => _stopwatch.start();
  void stop() => _stopwatch.stop();
  int get elapsedMilliseconds => _stopwatch.elapsedMilliseconds;
}
