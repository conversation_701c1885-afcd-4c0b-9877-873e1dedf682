import 'package:flutter/material.dart';
import 'package:logging/logging.dart';
import '../utils/image_utils.dart';

/// Service to handle image loading errors and provide fallback mechanisms
class ImageErrorHandler {
  static final _logger = Logger('ImageErrorHandler');
  static final Map<String, String> _urlCache = {};
  static final Set<String> _failedUrls = {};

  /// Get a reliable image URL with fallbacks
  static String getReliableImageUrl(
    String? originalUrl, {
    String? category,
    int width = 600,
    int height = 400,
  }) {
    // Return cached URL if available
    if (originalUrl != null && _urlCache.containsKey(originalUrl)) {
      return _urlCache[originalUrl]!;
    }

    // Check if URL is valid and not in failed list
    if (originalUrl != null &&
        ImageUtils.isValidImageUrl(originalUrl) &&
        !_failedUrls.contains(originalUrl)) {
      _urlCache[originalUrl] = originalUrl;
      return originalUrl;
    }

    // Generate fallback URL
    final fallbackUrl = ImageUtils.getSafePlaceholderUrl(
      width: width,
      height: height,
      category: category,
    );

    if (originalUrl != null) {
      _urlCache[originalUrl] = fallbackUrl;
      _logger.warning('Using fallback URL for: $originalUrl');
    }

    return fallbackUrl;
  }

  /// Report a failed image URL
  static void reportFailedUrl(String url, Object error) {
    _failedUrls.add(url);
    _urlCache.remove(url);
    _logger.severe('Image load failed for URL: $url, Error: $error');
  }

  /// Clear the cache (useful for testing or memory management)
  static void clearCache() {
    _urlCache.clear();
    _failedUrls.clear();
    _logger.info('Image URL cache cleared');
  }

  /// Get a standardized error widget for automotive parts
  static Widget getAutomotivePartErrorWidget({
    required String partCategory,
    double? width,
    double? height,
    Color? backgroundColor,
  }) {
    return Container(
      width: width,
      height: height,
      color: backgroundColor ?? Colors.grey.shade100,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            _getPartIcon(partCategory),
            size: 32,
            color: Colors.grey.shade600,
          ),
          const SizedBox(height: 8),
          Text(
            _getPartDisplayName(partCategory),
            style: TextStyle(
              color: Colors.grey.shade600,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// Safely convert a nullable [double] dimension to an [int].
  /// Falls back to [defaultValue] when the value is `null`, `NaN`, or infinite.
  static int _sanitizeDimension(double? value, {required int defaultValue}) {
    if (value == null) return defaultValue;
    if (value.isNaN || value.isInfinite) return defaultValue;
    return value.toInt();
  }

  /// Get enhanced cached network image widget with error handling
  static Widget buildRobustImageWidget({
    required String? imageUrl,
    required BuildContext context,
    String? category,
    double? width,
    double? height,
    BoxFit fit = BoxFit.cover,
    Widget? placeholder,
    Widget? customErrorWidget,
  }) {
    final reliableUrl = getReliableImageUrl(
      imageUrl,
      category: category,
      width: _sanitizeDimension(width, defaultValue: 600),
      height: _sanitizeDimension(height, defaultValue: 400),
    );

    return ImageUtils.optimizedNetworkImage(
      imageUrl: reliableUrl,
      context: context,
      width: width,
      height: height,
      fit: fit,
      fallbackCategory: category,
      placeholder: placeholder,
      errorWidget:
          customErrorWidget ??
          (category != null
              ? getAutomotivePartErrorWidget(
                  partCategory: category,
                  width: width,
                  height: height,
                )
              : null),
    );
  }

  static IconData _getPartIcon(String category) {
    switch (category.toLowerCase()) {
      case 'battery':
      case 'batteries':
        return Icons.battery_std;
      case 'engine':
        return Icons.settings;
      case 'transmission':
        return Icons.settings_applications;
      case 'brakes':
        return Icons.circle;
      case 'tires':
      case 'wheels':
        return Icons.circle_outlined;
      case 'filters':
        return Icons.filter_alt;
      case 'lights':
      case 'lighting':
        return Icons.lightbulb;
      case 'electrical':
        return Icons.electrical_services;
      default:
        return Icons.build;
    }
  }

  static String _getPartDisplayName(String category) {
    switch (category.toLowerCase()) {
      case 'battery':
      case 'batteries':
        return 'بطارية';
      case 'engine':
        return 'محرك';
      case 'transmission':
        return 'ناقل حركة';
      case 'brakes':
        return 'فرامل';
      case 'tires':
      case 'wheels':
        return 'إطارات';
      case 'filters':
        return 'فلاتر';
      case 'lights':
      case 'lighting':
        return 'إضاءة';
      case 'electrical':
        return 'كهربائيات';
      default:
        return 'قطعة غيار';
    }
  }
}
