// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'subscription_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$subscriptionServiceHash() =>
    r'8149c05db92f9df1cf445986d8cc82ed64072f08';

/// Provider for SubscriptionService
///
/// Copied from [subscriptionService].
@ProviderFor(subscriptionService)
final subscriptionServiceProvider =
    AutoDisposeProvider<SubscriptionService>.internal(
      subscriptionService,
      name: r'subscriptionServiceProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$subscriptionServiceHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef SubscriptionServiceRef = AutoDisposeProviderRef<SubscriptionService>;
String _$subscriptionServiceWithLoggerHash() =>
    r'7a2cfeafea8751e4d12e24f226e6825e58d6c383';

/// Provider for SubscriptionService with custom logger
///
/// Copied from [subscriptionServiceWithLogger].
@ProviderFor(subscriptionServiceWithLogger)
final subscriptionServiceWithLoggerProvider =
    AutoDisposeProvider<SubscriptionService>.internal(
      subscriptionServiceWithLogger,
      name: r'subscriptionServiceWithLoggerProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$subscriptionServiceWithLoggerHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef SubscriptionServiceWithLoggerRef =
    AutoDisposeProviderRef<SubscriptionService>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
