import 'package:flutter/foundation.dart';
import 'backend_resilience_service.dart';
import 'carnow_api_service.dart';

/// Initializes all backend resilience components
class BackendResilienceInitializer {
  static bool _isInitialized = false;
  static BackendResilienceService? _resilienceService;
  
  /// Initialize all backend resilience services
  static Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      debugPrint('🔗 Initializing Backend Resilience System...');
      
      // 1. Initialize Backend Resilience Service
      _resilienceService = BackendResilienceService();
      await _resilienceService!.initialize();
      debugPrint('✅ Backend Resilience Service initialized');
      
      // 2. Initialize CarnowApiService 
      await CarnowApiService.initialize();
      debugPrint('✅ CarnowApiService initialized');
      
      // 3. Perform initial health check
      final initialStatus = await _resilienceService!.checkBackendHealth();
      debugPrint('🔗 Initial backend status: $initialStatus');
      
      _isInitialized = true;
      debugPrint('✅ Backend Resilience System fully initialized');
      
    } catch (e) {
      debugPrint('❌ Failed to initialize Backend Resilience System: $e');
      // Don't throw, allow app to continue with degraded functionality
    }
  }
  
  /// Get the resilience service instance
  static BackendResilienceService? get resilienceService => _resilienceService;
  
  /// Check if the system is initialized
  static bool get isInitialized => _isInitialized;
  
  /// Dispose all services
  static void dispose() {
    try {
      _resilienceService?.dispose();
      CarnowApiService.disposeService();
      _isInitialized = false;
      debugPrint('🔗 Backend Resilience System disposed');
    } catch (e) {
      debugPrint('❌ Error disposing Backend Resilience System: $e');
    }
  }
  
  /// Force reinitialize (useful for testing or recovery)
  static Future<void> reinitialize() async {
    dispose();
    await initialize();
  }
  
  /// Get current system status
  static Map<String, dynamic> getSystemStatus() {
    if (!_isInitialized || _resilienceService == null) {
      return {
        'initialized': false,
        'backend_status': 'unknown',
        'message': 'System not initialized',
      };
    }
    
    return {
      'initialized': true,
      'backend_status': _resilienceService!.currentStatus.toString(),
      'backend_message': _resilienceService!.getStatusMessage(),
      'outage_duration': _resilienceService!.outageDuration?.inMinutes,
      'is_healthy': _resilienceService!.isHealthy,
      'is_degraded': _resilienceService!.isDegraded,
      'is_unavailable': _resilienceService!.isUnavailable,
    };
  }
} 