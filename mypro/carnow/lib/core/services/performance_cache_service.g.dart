// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'performance_cache_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$performanceCacheServiceHash() =>
    r'1e06385d56f47b83707a4989c1e493f02935b9d5';

/// Provider لخدمة التخزين المؤقت
///
/// Copied from [performanceCacheService].
@ProviderFor(performanceCacheService)
final performanceCacheServiceProvider =
    AutoDisposeProvider<PerformanceCacheService>.internal(
      performanceCacheService,
      name: r'performanceCacheServiceProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$performanceCacheServiceHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef PerformanceCacheServiceRef =
    AutoDisposeProviderRef<PerformanceCacheService>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
