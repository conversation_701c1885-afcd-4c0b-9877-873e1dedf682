// =============================================================================
// Error Handling Service - Forever Plan Architecture
// خدمة معالجة الأخطاء - بنية الخطة الدائمة
// =============================================================================
// Centralized error handling for Flutter (UI Only) → Go API → Supabase (Data Only)

import 'dart:io';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:logging/logging.dart';

final _logger = Logger('ErrorHandlingService');

/// Comprehensive error handling service for CarNow app
/// Handles all types of errors: network, authentication, backend, validation
class ErrorHandlingService {
  static ErrorHandlingService? _instance;
  
  ErrorHandlingService._internal();
  
  factory ErrorHandlingService() {
    return _instance ??= ErrorHandlingService._internal();
  }

  /// Process and categorize errors from different sources
  CarNowError processError(dynamic error, [StackTrace? stackTrace]) {
    _logger.severe('Processing error: $error', error, stackTrace);

    if (error is CarNowError) {
      return error;
    }

    if (error is DioException) {
      return _handleDioError(error);
    }

    if (error is SocketException) {
      return CarNowError.network(
        'No internet connection',
        'Please check your internet connection and try again.',
        error: error,
        stackTrace: stackTrace,
      );
    }

    if (error is FormatException) {
      return CarNowError.validation(
        'Data format error',
        'The data received is in an unexpected format.',
        error: error,
        stackTrace: stackTrace,
      );
    }

    // Generic error fallback
    return CarNowError.unknown(
      'An unexpected error occurred',
      'Please try again. If the problem persists, contact support.',
      error: error,
      stackTrace: stackTrace,
    );
  }

  /// Handle Dio/HTTP errors specifically
  CarNowError _handleDioError(DioException dioError) {
    final response = dioError.response;
    final statusCode = response?.statusCode;

    switch (dioError.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return CarNowError.network(
          'Connection timeout',
          'The request took too long. Please try again.',
          statusCode: statusCode,
          error: dioError,
        );

      case DioExceptionType.badResponse:
        return _handleBadResponse(dioError);

      case DioExceptionType.cancel:
        return CarNowError.network(
          'Request cancelled',
          'The request was cancelled.',
          error: dioError,
        );

      case DioExceptionType.connectionError:
        return CarNowError.network(
          'Connection error',
          'Unable to connect to the server. Please check your connection.',
          error: dioError,
        );

      case DioExceptionType.badCertificate:
        return CarNowError.network(
          'Security error',
          'There was a security issue with the connection.',
          error: dioError,
        );

      case DioExceptionType.unknown:
      default:
        return CarNowError.network(
          'Network error',
          'An unknown network error occurred.',
          error: dioError,
        );
    }
  }

  /// Handle bad HTTP responses (4xx, 5xx)
  CarNowError _handleBadResponse(DioException dioError) {
    final response = dioError.response;
    final statusCode = response?.statusCode ?? 0;
    final data = response?.data;

    // Try to extract error message from Go backend response
    String title = 'Request failed';
    String message = 'An error occurred while processing your request.';

    if (data is Map<String, dynamic>) {
      // Handle Go backend API response format
      if (data.containsKey('error') && data['error'] is String) {
        message = data['error'] as String;
      } else if (data.containsKey('message') && data['message'] is String) {
        message = data['message'] as String;
      }
    }

    switch (statusCode) {
      case 400:
        return CarNowError.validation(
          'Invalid request',
          message.isEmpty ? 'The request contains invalid data.' : message,
          statusCode: statusCode,
          error: dioError,
        );

      case 401:
        return CarNowError.authentication(
          'Authentication required',
          'Please sign in to continue.',
          statusCode: statusCode,
          error: dioError,
        );

      case 403:
        return CarNowError.authorization(
          'Access denied',
          'You do not have permission to access this resource.',
          statusCode: statusCode,
          error: dioError,
        );

      case 404:
        return CarNowError.notFound(
          'Not found',
          'The requested resource was not found.',
          statusCode: statusCode,
          error: dioError,
        );

      case 422:
        return CarNowError.validation(
          'Validation error',
          message.isEmpty ? 'The provided data is invalid.' : message,
          statusCode: statusCode,
          error: dioError,
        );

      case 429:
        return CarNowError.rateLimit(
          'Too many requests',
          'Please wait a moment before trying again.',
          statusCode: statusCode,
          error: dioError,
        );

      case 500:
      case 502:
      case 503:
      case 504:
        return CarNowError.server(
          'Server error',
          'The server is currently unavailable. Please try again later.',
          statusCode: statusCode,
          error: dioError,
        );

      default:
        return CarNowError.unknown(
          title,
          message,
          statusCode: statusCode,
          error: dioError,
        );
    }
  }

  /// Log error for debugging and monitoring
  void logError(CarNowError error) {
    final level = _getLogLevel(error.type);
    _logger.log(level, '${error.type.name}: ${error.title}', error.originalError, error.stackTrace);

    // In production, send to crash reporting service
    if (kReleaseMode && error.type.severity.index >= ErrorSeverity.high.index) {
      _sendToCrashReporting(error);
    }
  }

  Level _getLogLevel(CarNowErrorType type) {
    switch (type.severity) {
      case ErrorSeverity.low:
        return Level.INFO;
      case ErrorSeverity.medium:
        return Level.WARNING;
      case ErrorSeverity.high:
        return Level.SEVERE;
      case ErrorSeverity.critical:
        return Level.SHOUT;
    }
  }

  void _sendToCrashReporting(CarNowError error) {
    // TODO: Integrate with crash reporting service (Firebase Crashlytics, Sentry, etc.)
    debugPrint('🔥 Critical error: ${error.title} - ${error.message}');
  }
}

/// Custom error class for CarNow application
class CarNowError implements Exception {
  final CarNowErrorType type;
  final String title;
  final String message;
  final int? statusCode;
  final dynamic originalError;
  final StackTrace? stackTrace;
  final DateTime timestamp;

  CarNowError._({
    required this.type,
    required this.title,
    required this.message,
    this.statusCode,
    this.originalError,
    this.stackTrace,
  }) : timestamp = DateTime.now();

  // Factory constructors for different error types
  factory CarNowError.network(String title, String message, {
    int? statusCode,
    dynamic error,
    StackTrace? stackTrace,
  }) => CarNowError._(
    type: CarNowErrorType.network,
    title: title,
    message: message,
    statusCode: statusCode,
    originalError: error,
    stackTrace: stackTrace,
  );

  factory CarNowError.authentication(String title, String message, {
    int? statusCode,
    dynamic error,
    StackTrace? stackTrace,
  }) => CarNowError._(
    type: CarNowErrorType.authentication,
    title: title,
    message: message,
    statusCode: statusCode,
    originalError: error,
    stackTrace: stackTrace,
  );

  factory CarNowError.authorization(String title, String message, {
    int? statusCode,
    dynamic error,
    StackTrace? stackTrace,
  }) => CarNowError._(
    type: CarNowErrorType.authorization,
    title: title,
    message: message,
    statusCode: statusCode,
    originalError: error,
    stackTrace: stackTrace,
  );

  factory CarNowError.validation(String title, String message, {
    int? statusCode,
    dynamic error,
    StackTrace? stackTrace,
  }) => CarNowError._(
    type: CarNowErrorType.validation,
    title: title,
    message: message,
    statusCode: statusCode,
    originalError: error,
    stackTrace: stackTrace,
  );

  factory CarNowError.server(String title, String message, {
    int? statusCode,
    dynamic error,
    StackTrace? stackTrace,
  }) => CarNowError._(
    type: CarNowErrorType.server,
    title: title,
    message: message,
    statusCode: statusCode,
    originalError: error,
    stackTrace: stackTrace,
  );

  factory CarNowError.notFound(String title, String message, {
    int? statusCode,
    dynamic error,
    StackTrace? stackTrace,
  }) => CarNowError._(
    type: CarNowErrorType.notFound,
    title: title,
    message: message,
    statusCode: statusCode,
    originalError: error,
    stackTrace: stackTrace,
  );

  factory CarNowError.rateLimit(String title, String message, {
    int? statusCode,
    dynamic error,
    StackTrace? stackTrace,
  }) => CarNowError._(
    type: CarNowErrorType.rateLimit,
    title: title,
    message: message,
    statusCode: statusCode,
    originalError: error,
    stackTrace: stackTrace,
  );

  factory CarNowError.unknown(String title, String message, {
    int? statusCode,
    dynamic error,
    StackTrace? stackTrace,
  }) => CarNowError._(
    type: CarNowErrorType.unknown,
    title: title,
    message: message,
    statusCode: statusCode,
    originalError: error,
    stackTrace: stackTrace,
  );

  @override
  String toString() {
    return 'CarNowError(${type.name}): $title - $message';
  }
}

/// Error type enumeration with severity levels
enum CarNowErrorType {
  network(ErrorSeverity.medium),
  authentication(ErrorSeverity.high),
  authorization(ErrorSeverity.high),
  validation(ErrorSeverity.low),
  server(ErrorSeverity.high),
  notFound(ErrorSeverity.low),
  rateLimit(ErrorSeverity.medium),
  unknown(ErrorSeverity.critical);

  const CarNowErrorType(this.severity);
  final ErrorSeverity severity;
}

/// Error severity levels for prioritization and handling
enum ErrorSeverity {
  low,
  medium,
  high,
  critical;
}

// =============================================================================
// Usage Examples:
// =============================================================================
//
// In providers:
// ```dart
// try {
//   final response = await apiClient.getApi('/products');
//   return response.data;
// } catch (error, stackTrace) {
//   final errorService = ErrorHandlingService();
//   final carNowError = errorService.processError(error, stackTrace);
//   errorService.logError(carNowError);
//   throw carNowError;
// }
// ```
// ============================================================================= 