// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'simple_connection_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$simpleConnectionServiceHash() =>
    r'ef38b616cc9db7e2fe9b0418ad1405e3c3d899be';

/// Simple Connection Service for Forever Plan Architecture
/// Flutter (UI Only) → Go API → Supabase (Data Only)
///
/// Simple connectivity monitoring - NO complex offline features
///
/// Copied from [simpleConnectionService].
@ProviderFor(simpleConnectionService)
final simpleConnectionServiceProvider =
    AutoDisposeProvider<SimpleConnectionService>.internal(
      simpleConnectionService,
      name: r'simpleConnectionServiceProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$simpleConnectionServiceHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef SimpleConnectionServiceRef =
    AutoDisposeProviderRef<SimpleConnectionService>;
String _$connectionStatusHash() => r'dd51a55090e571696de6230baa48a914d2d10e2d';

/// See also [connectionStatus].
@ProviderFor(connectionStatus)
final connectionStatusProvider = AutoDisposeStreamProvider<bool>.internal(
  connectionStatus,
  name: r'connectionStatusProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$connectionStatusHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef ConnectionStatusRef = AutoDisposeStreamProviderRef<bool>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
