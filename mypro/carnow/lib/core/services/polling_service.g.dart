// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'polling_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$pollingServiceHash() => r'k1l2m3n4o5p6q7r8s9t0u1v2w3x4y5z6a7b8c9d0';

/// Polling service provider
///
/// Copied from [pollingService].
@ProviderFor(pollingService)
final pollingServiceProvider = AutoDisposeProvider<PollingService>.internal(
  pollingService,
  name: r'pollingServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$pollingServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef PollingServiceRef = AutoDisposeProviderRef<PollingService>;
String _$productsPollingHash() => r'l2m3n4o5p6q7r8s9t0u1v2w3x4y5z6a7b8c9d0e1';

/// Products polling provider
///
/// Copied from [productsPolling].
@ProviderFor(productsPolling)
final productsPollingProvider =
    AutoDisposeStreamProvider<PollingResult>.internal(
      productsPolling,
      name: r'productsPollingProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$productsPollingHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef ProductsPollingRef = AutoDisposeStreamProviderRef<PollingResult>;
String _$ordersPollingHash() => r'm3n4o5p6q7r8s9t0u1v2w3x4y5z6a7b8c9d0e1f2';

/// Orders polling provider
///
/// Copied from [ordersPolling].
@ProviderFor(ordersPolling)
final ordersPollingProvider = AutoDisposeStreamProvider<PollingResult>.internal(
  ordersPolling,
  name: r'ordersPollingProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$ordersPollingHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef OrdersPollingRef = AutoDisposeStreamProviderRef<PollingResult>;
String _$walletPollingHash() => r'n4o5p6q7r8s9t0u1v2w3x4y5z6a7b8c9d0e1f2g3';

/// Wallet polling provider
///
/// Copied from [walletPolling].
@ProviderFor(walletPolling)
final walletPollingProvider = AutoDisposeStreamProvider<PollingResult>.internal(
  walletPolling,
  name: r'walletPollingProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$walletPollingHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef WalletPollingRef = AutoDisposeStreamProviderRef<PollingResult>;
String _$inventoryPollingHash() => r'o5p6q7r8s9t0u1v2w3x4y5z6a7b8c9d0e1f2g3h4';

/// Inventory polling provider
///
/// Copied from [inventoryPolling].
@ProviderFor(inventoryPolling)
final inventoryPollingProvider =
    AutoDisposeStreamProvider<PollingResult>.internal(
      inventoryPolling,
      name: r'inventoryPollingProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$inventoryPollingHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef InventoryPollingRef = AutoDisposeStreamProviderRef<PollingResult>;
String _$notificationsPollingHash() =>
    r'p6q7r8s9t0u1v2w3x4y5z6a7b8c9d0e1f2g3h4i5';

/// Notifications polling provider
///
/// Copied from [notificationsPolling].
@ProviderFor(notificationsPolling)
final notificationsPollingProvider =
    AutoDisposeStreamProvider<PollingResult>.internal(
      notificationsPolling,
      name: r'notificationsPollingProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$notificationsPollingHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef NotificationsPollingRef = AutoDisposeStreamProviderRef<PollingResult>;
String _$realtimeProductsDataHash() =>
    r'q7r8s9t0u1v2w3x4y5z6a7b8c9d0e1f2g3h4i5j6';

/// Combined real-time data provider that merges WebSocket and polling
///
/// Copied from [realtimeProductsData].
@ProviderFor(realtimeProductsData)
final realtimeProductsDataProvider =
    AutoDisposeStreamProvider<Map<String, dynamic>>.internal(
      realtimeProductsData,
      name: r'realtimeProductsDataProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$realtimeProductsDataHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef RealtimeProductsDataRef =
    AutoDisposeStreamProviderRef<Map<String, dynamic>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
