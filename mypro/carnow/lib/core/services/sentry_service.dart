import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import 'package:logger/logger.dart';

/// Real Sentry service for production error tracking
class SentryService {
  factory SentryService() => _instance;
  SentryService._internal();
  static final _instance = SentryService._internal();

  final Logger _logger = Logger();
  bool _isInitialized = false;

  /// Initialize Sentry with configuration
  Future<void> initialize({
    required String dsn,
    String? environment,
    double? tracesSampleRate,
  }) async {
    if (_isInitialized) {
      _logger.w('Sentry is already initialized');
      return;
    }

    // Validate DSN format
    if (!_isValidDsn(dsn)) {
      _logger.w('Invalid Sentry DSN format, skipping initialization');
      return;
    }

    try {
      await SentryFlutter.init((options) {
        options.dsn = dsn;
        options.environment =
            environment ?? (kDebugMode ? 'debug' : 'production');

        // Performance monitoring
        options.tracesSampleRate = tracesSampleRate ?? (kDebugMode ? 1.0 : 0.1);

        // Error filtering
        options.beforeSend = _beforeSend;

        // Additional configuration
        options.attachStacktrace = true;
        options.enableAutoSessionTracking = true;

        // Debug options
        options.debug = kDebugMode;

        // Release and distribution
        options.release = '1.0.0';
        options.dist = '1';

        // Performance monitoring options
        options.enableAutoPerformanceTracing =
            !kDebugMode; // Disable in debug to reduce noise
        options.enableUserInteractionTracing = !kDebugMode;

        // Privacy settings
        options.sendDefaultPii = false;
        options.attachScreenshot =
            false; // Disable screenshots to avoid privacy issues
        options.attachViewHierarchy = false;

        // Network configuration to help with CSRF issues
        options.maxRequestBodySize = MaxRequestBodySize.small;

        // Add custom transaction filtering
        options.beforeSendTransaction = _beforeSendTransaction;
      });

      _isInitialized = true;
      _logger.i('Sentry initialized successfully');

      // Set initial context
      _setInitialContext();
    } catch (e) {
      _logger.e('Failed to initialize Sentry: $e');
      // Don't rethrow to avoid crashing the app
    }
  }

  /// Validate DSN format
  bool _isValidDsn(String dsn) {
    return dsn.isNotEmpty &&
        dsn.startsWith('https://') &&
        dsn.contains('@') &&
        dsn.contains('sentry.io') &&
        !dsn.contains('your-sentry-dsn');
  }

  /// Filter events before sending to Sentry
  SentryEvent? _beforeSend(SentryEvent event, Hint hint) {
    // Filter out debug-only events in production
    if (!kDebugMode && event.level == SentryLevel.debug) {
      return null;
    }

    // Filter out network errors in debug mode
    if (kDebugMode && _isNetworkError(event)) {
      return null;
    }

    // Filter out Sentry-related errors to prevent recursive reporting
    if (_isSentryError(event)) {
      _logger.w('Filtering out Sentry-related error to prevent recursion');
      return null;
    }

    // Filter out common Flutter framework errors that aren't actionable
    if (_isFrameworkError(event)) {
      return null;
    }

    return event;
  }

  /// Filter transactions before sending
  SentryTransaction? _beforeSendTransaction(
    SentryTransaction transaction,
    Hint hint,
  ) {
    // Don't send debug transactions in production
    if (!kDebugMode &&
        transaction.contexts.trace?.operation.contains('debug') == true) {
      return null;
    }
    return transaction;
  }

  /// Check if an event is a network error
  bool _isNetworkError(SentryEvent event) {
    final exception = event.throwable;
    if (exception == null) return false;

    final exceptionString = exception.toString().toLowerCase();
    return exceptionString.contains('socket') ||
        exceptionString.contains('network') ||
        exceptionString.contains('connection') ||
        exceptionString.contains('timeout') ||
        exceptionString.contains('403') ||
        exceptionString.contains('csrf');
  }

  /// Check if an event is a Sentry-related error
  bool _isSentryError(SentryEvent event) {
    final exception = event.throwable;
    if (exception == null) return false;

    final exceptionString = exception.toString().toLowerCase();
    return exceptionString.contains('sentry') ||
        exceptionString.contains('envelope submission failed') ||
        exceptionString.contains('csrf verification failed');
  }

  /// Check if an event is a common framework error
  bool _isFrameworkError(SentryEvent event) {
    final exception = event.throwable;
    if (exception == null) return false;

    final exceptionString = exception.toString().toLowerCase();
    return exceptionString.contains('looking up a deactivated widget') ||
        exceptionString.contains('ticker') ||
        exceptionString.contains('renderbox was not laid out');
  }

  /// Set initial context
  void _setInitialContext() {
    Sentry.configureScope((scope) {
      scope.setTag('platform', Platform.operatingSystem);
      scope.setTag('app_name', 'CarNow');
      scope.setTag('os_version', Platform.operatingSystemVersion);
    });
  }

  /// Capture an exception with context
  Future<SentryId> captureException(
    dynamic exception, {
    dynamic stackTrace,
    String? hint,
    Map<String, dynamic>? extra,
    Map<String, String>? tags,
    SentryLevel? level,
  }) async {
    if (!_isInitialized) {
      _logger.w('Sentry not initialized, logging exception locally');
      _logger.e(
        'Exception: $exception',
        error: exception,
        stackTrace: stackTrace,
      );
      return const SentryId.empty();
    }

    try {
      return await Sentry.captureException(
        exception,
        stackTrace: stackTrace,
        hint: hint != null ? Hint.withMap({'hint': hint}) : null,
        withScope: (scope) {
          if (extra != null) {
            extra.forEach((key, value) => scope.setTag(key, value.toString()));
          }
          if (tags != null) {
            tags.forEach((key, value) => scope.setTag(key, value));
          }
          if (level != null) {
            scope.level = level;
          }
        },
      );
    } catch (e) {
      _logger.e('Failed to capture exception with Sentry: $e');
      return const SentryId.empty();
    }
  }

  /// Capture a message
  Future<SentryId> captureMessage(
    String message, {
    SentryLevel? level,
    Map<String, dynamic>? extra,
    Map<String, String>? tags,
  }) async {
    if (!_isInitialized) {
      _logger.w('Sentry not initialized, logging message locally');
      _logger.i('Message: $message');
      return const SentryId.empty();
    }

    try {
      return await Sentry.captureMessage(
        message,
        level: level ?? SentryLevel.info,
        withScope: (scope) {
          if (extra != null) {
            extra.forEach((key, value) => scope.setTag(key, value.toString()));
          }
          if (tags != null) {
            tags.forEach((key, value) => scope.setTag(key, value));
          }
        },
      );
    } catch (e) {
      _logger.e('Failed to capture message with Sentry: $e');
      return const SentryId.empty();
    }
  }

  /// Add a breadcrumb
  void addBreadcrumb(
    String message, {
    String? category,
    SentryLevel? level,
    Map<String, dynamic>? data,
  }) {
    if (!_isInitialized) return;

    try {
      Sentry.addBreadcrumb(
        Breadcrumb(
          message: message,
          category: category,
          level: level ?? SentryLevel.info,
          data: data,
          timestamp: DateTime.now(),
        ),
      );
    } catch (e) {
      _logger.e('Failed to add breadcrumb: $e');
    }
  }

  /// Set user context
  void setUser({
    String? id,
    String? email,
    String? username,
    Map<String, dynamic>? extras,
  }) {
    if (!_isInitialized) return;

    try {
      Sentry.configureScope((scope) {
        scope.setUser(
          SentryUser(id: id, email: email, username: username, data: extras),
        );
      });
    } catch (e) {
      _logger.e('Failed to set user context: $e');
    }
  }

  /// Set a tag
  void setTag(String key, String value) {
    if (!_isInitialized) return;

    try {
      Sentry.configureScope((scope) {
        scope.setTag(key, value);
      });
    } catch (e) {
      _logger.e('Failed to set tag: $e');
    }
  }

  /// Start a transaction for performance monitoring
  dynamic startTransaction(String name, String operation) {
    if (!_isInitialized) {
      _logger.w('Sentry not initialized, skipping transaction');
      return null;
    }

    try {
      return Sentry.startTransaction(name, operation, bindToScope: true);
    } catch (e) {
      _logger.e('Failed to start transaction: $e');
      return null;
    }
  }

  /// Close Sentry and flush events
  Future<void> close() async {
    if (!_isInitialized) return;

    try {
      await Sentry.close();
      _isInitialized = false;
      _logger.i('Sentry closed successfully');
    } catch (e) {
      _logger.e('Failed to close Sentry: $e');
    }
  }
}
