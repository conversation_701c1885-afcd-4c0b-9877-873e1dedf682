import 'package:flutter/foundation.dart';
import '../config/backend_config.dart';

/// Advanced image processing service using backend bimg capabilities
class AdvancedImageService {
  /// Convert image format
  static String getConvertedImageUrl(
    String originalUrl, {
    required String format,
  }) {
    if (originalUrl.isEmpty) return originalUrl;

    final baseUrl = BackendConfig.baseUrl;
    final encodedUrl = Uri.encodeComponent(originalUrl);

    return '$baseUrl/api/v1/images/convert?url=$encodedUrl&format=$format';
  }

  /// Crop image to specific dimensions and position
  static String getCroppedImageUrl(
    String originalUrl, {
    required int width,
    required int height,
    int x = 0,
    int y = 0,
  }) {
    if (originalUrl.isEmpty) return originalUrl;

    final baseUrl = BackendConfig.baseUrl;
    final encodedUrl = Uri.encodeComponent(originalUrl);

    return '$baseUrl/api/v1/images/crop?url=$encodedUrl&w=$width&h=$height&x=$x&y=$y';
  }

  /// Rotate image by specified angle
  static String getRotatedImageUrl(String originalUrl, {required int angle}) {
    if (originalUrl.isEmpty) return originalUrl;

    final baseUrl = BackendConfig.baseUrl;
    final encodedUrl = Uri.encodeComponent(originalUrl);

    return '$baseUrl/api/v1/images/rotate?url=$encodedUrl&angle=$angle';
  }

  /// Apply blur effect to image
  static String getBlurredImageUrl(String originalUrl, {double sigma = 1.0}) {
    if (originalUrl.isEmpty) return originalUrl;

    final baseUrl = BackendConfig.baseUrl;
    final encodedUrl = Uri.encodeComponent(originalUrl);

    return '$baseUrl/api/v1/images/blur?url=$encodedUrl&sigma=$sigma';
  }

  /// Apply watermark to image
  static String getWatermarkedImageUrl(
    String originalUrl, {
    required String watermarkUrl,
    double opacity = 0.5,
    String position = 'bottom-right',
  }) {
    if (originalUrl.isEmpty || watermarkUrl.isEmpty) return originalUrl;

    final baseUrl = BackendConfig.baseUrl;
    final encodedUrl = Uri.encodeComponent(originalUrl);
    final encodedWatermarkUrl = Uri.encodeComponent(watermarkUrl);

    return '$baseUrl/api/v1/images/watermark?url=$encodedUrl&watermark=$encodedWatermarkUrl&opacity=$opacity&position=$position';
  }

  /// Get image with multiple transformations
  static String getTransformedImageUrl(
    String originalUrl, {
    String? format,
    int? width,
    int? height,
    int? cropX,
    int? cropY,
    int? rotateAngle,
    double? blurSigma,
    String? watermarkUrl,
    double? watermarkOpacity,
    String? watermarkPosition,
  }) {
    String transformedUrl = originalUrl;

    // Apply transformations in sequence
    if (format != null) {
      transformedUrl = getConvertedImageUrl(transformedUrl, format: format);
    }

    if (width != null && height != null && cropX != null && cropY != null) {
      transformedUrl = getCroppedImageUrl(
        transformedUrl,
        width: width,
        height: height,
        x: cropX,
        y: cropY,
      );
    }

    if (rotateAngle != null) {
      transformedUrl = getRotatedImageUrl(transformedUrl, angle: rotateAngle);
    }

    if (blurSigma != null) {
      transformedUrl = getBlurredImageUrl(transformedUrl, sigma: blurSigma);
    }

    if (watermarkUrl != null) {
      transformedUrl = getWatermarkedImageUrl(
        transformedUrl,
        watermarkUrl: watermarkUrl,
        opacity: watermarkOpacity ?? 0.5,
        position: watermarkPosition ?? 'bottom-right',
      );
    }

    return transformedUrl;
  }

  /// Supported image formats
  static const List<String> supportedFormats = [
    'jpeg',
    'jpg',
    'png',
    'webp',
    'gif',
    'tiff',
  ];

  /// Supported watermark positions
  static const List<String> supportedWatermarkPositions = [
    'top-left',
    'top-right',
    'bottom-left',
    'bottom-right',
    'center',
  ];

  /// Check if format is supported
  static bool isFormatSupported(String format) {
    return supportedFormats.contains(format.toLowerCase());
  }

  /// Check if watermark position is supported
  static bool isWatermarkPositionSupported(String position) {
    return supportedWatermarkPositions.contains(position.toLowerCase());
  }

  /// Get optimal format for web
  static String getOptimalWebFormat() {
    // WebP is optimal for web if supported, otherwise JPEG
    return 'webp';
  }

  /// Get optimal format for mobile
  static String getOptimalMobileFormat() {
    // JPEG is widely supported and efficient for mobile
    return 'jpeg';
  }

  /// Generate responsive image URLs for different screen sizes
  static Map<String, String> generateResponsiveUrls(
    String originalUrl, {
    List<int> sizes = const [300, 600, 900, 1200],
    String format = 'jpeg',
    int quality = 85,
  }) {
    final responsiveUrls = <String, String>{};

    for (final size in sizes) {
      final optimizedUrl = BackendConfig.buildApiUrl(
        '/images/optimize?url=${Uri.encodeComponent(originalUrl)}&w=$size&q=$quality&format=$format',
      );
      responsiveUrls['${size}w'] = optimizedUrl;
    }

    return responsiveUrls;
  }

  /// Generate image srcset for responsive images
  static String generateSrcSet(
    String originalUrl, {
    List<int> sizes = const [300, 600, 900, 1200],
    String format = 'jpeg',
    int quality = 85,
  }) {
    final responsiveUrls = generateResponsiveUrls(
      originalUrl,
      sizes: sizes,
      format: format,
      quality: quality,
    );

    return responsiveUrls.entries
        .map((entry) => '${entry.value} ${entry.key}')
        .join(', ');
  }

  /// Get image dimensions from URL (requires backend call)
  static Future<Map<String, dynamic>?> getImageInfo(String imageUrl) async {
    try {
      final baseUrl = BackendConfig.baseUrl;
      final encodedUrl = Uri.encodeComponent(imageUrl);
      final infoUrl = '$baseUrl/api/v1/images/info?url=$encodedUrl';

      // This would require an HTTP client to fetch the info
      // For now, return null - implement with your HTTP client
      debugPrint('Image info URL: $infoUrl');
      return null;
    } catch (e) {
      debugPrint('Error getting image info: $e');
      return null;
    }
  }

  /// Preload transformed images
  static Future<void> preloadTransformedImages(
    List<String> imageUrls, {
    List<Map<String, dynamic>> transformations = const [],
  }) async {
    final futures = <Future>[];

    for (final url in imageUrls) {
      for (final transformation in transformations) {
        final transformedUrl = getTransformedImageUrl(
          url,
          format: transformation['format'],
          width: transformation['width'],
          height: transformation['height'],
          cropX: transformation['cropX'],
          cropY: transformation['cropY'],
          rotateAngle: transformation['rotateAngle'],
          blurSigma: transformation['blurSigma'],
          watermarkUrl: transformation['watermarkUrl'],
          watermarkOpacity: transformation['watermarkOpacity'],
          watermarkPosition: transformation['watermarkPosition'],
        );

        // Add to preload queue
        futures.add(_preloadSingleImage(transformedUrl));
      }
    }

    await Future.wait(futures);
  }

  static Future<void> _preloadSingleImage(String url) async {
    try {
      // This would preload the image - implement with your image loading library
      debugPrint('Preloading image: $url');
    } catch (e) {
      debugPrint('Failed to preload image $url: $e');
    }
  }
}

/// Image transformation configuration
class ImageTransformation {
  final String? format;
  final int? width;
  final int? height;
  final int? cropX;
  final int? cropY;
  final int? rotateAngle;
  final double? blurSigma;
  final String? watermarkUrl;
  final double? watermarkOpacity;
  final String? watermarkPosition;
  final int quality;

  const ImageTransformation({
    this.format,
    this.width,
    this.height,
    this.cropX,
    this.cropY,
    this.rotateAngle,
    this.blurSigma,
    this.watermarkUrl,
    this.watermarkOpacity,
    this.watermarkPosition,
    this.quality = 85,
  });

  /// Apply transformation to image URL
  String apply(String originalUrl) {
    return AdvancedImageService.getTransformedImageUrl(
      originalUrl,
      format: format,
      width: width,
      height: height,
      cropX: cropX,
      cropY: cropY,
      rotateAngle: rotateAngle,
      blurSigma: blurSigma,
      watermarkUrl: watermarkUrl,
      watermarkOpacity: watermarkOpacity,
      watermarkPosition: watermarkPosition,
    );
  }

  /// Create a copy with modified parameters
  ImageTransformation copyWith({
    String? format,
    int? width,
    int? height,
    int? cropX,
    int? cropY,
    int? rotateAngle,
    double? blurSigma,
    String? watermarkUrl,
    double? watermarkOpacity,
    String? watermarkPosition,
    int? quality,
  }) {
    return ImageTransformation(
      format: format ?? this.format,
      width: width ?? this.width,
      height: height ?? this.height,
      cropX: cropX ?? this.cropX,
      cropY: cropY ?? this.cropY,
      rotateAngle: rotateAngle ?? this.rotateAngle,
      blurSigma: blurSigma ?? this.blurSigma,
      watermarkUrl: watermarkUrl ?? this.watermarkUrl,
      watermarkOpacity: watermarkOpacity ?? this.watermarkOpacity,
      watermarkPosition: watermarkPosition ?? this.watermarkPosition,
      quality: quality ?? this.quality,
    );
  }

  Map<String, dynamic> toJson() => {
    'format': format,
    'width': width,
    'height': height,
    'cropX': cropX,
    'cropY': cropY,
    'rotateAngle': rotateAngle,
    'blurSigma': blurSigma,
    'watermarkUrl': watermarkUrl,
    'watermarkOpacity': watermarkOpacity,
    'watermarkPosition': watermarkPosition,
    'quality': quality,
  };

  factory ImageTransformation.fromJson(Map<String, dynamic> json) {
    return ImageTransformation(
      format: json['format'],
      width: json['width'],
      height: json['height'],
      cropX: json['cropX'],
      cropY: json['cropY'],
      rotateAngle: json['rotateAngle'],
      blurSigma: json['blurSigma']?.toDouble(),
      watermarkUrl: json['watermarkUrl'],
      watermarkOpacity: json['watermarkOpacity']?.toDouble(),
      watermarkPosition: json['watermarkPosition'],
      quality: json['quality'] ?? 85,
    );
  }
}

/// Predefined image transformations for common use cases
class ImageTransformations {
  /// Thumbnail transformation
  static const thumbnail = ImageTransformation(
    width: 300,
    height: 300,
    cropX: 0,
    cropY: 0,
    format: 'jpeg',
    quality: 80,
  );

  /// Profile picture transformation
  static const profilePicture = ImageTransformation(
    width: 200,
    height: 200,
    cropX: 0,
    cropY: 0,
    format: 'jpeg',
    quality: 85,
  );

  /// Banner transformation
  static const banner = ImageTransformation(
    width: 1200,
    height: 400,
    format: 'jpeg',
    quality: 90,
  );

  /// Product image transformation
  static const productImage = ImageTransformation(
    width: 800,
    height: 600,
    format: 'webp',
    quality: 85,
  );

  /// Gallery thumbnail transformation
  static const galleryThumbnail = ImageTransformation(
    width: 150,
    height: 150,
    cropX: 0,
    cropY: 0,
    format: 'jpeg',
    quality: 75,
  );

  /// High quality transformation
  static const highQuality = ImageTransformation(format: 'png', quality: 95);

  /// Web optimized transformation
  static const webOptimized = ImageTransformation(format: 'webp', quality: 80);

  /// Mobile optimized transformation
  static const mobileOptimized = ImageTransformation(
    width: 600,
    format: 'jpeg',
    quality: 75,
  );
}
