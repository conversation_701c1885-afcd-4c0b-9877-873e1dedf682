import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import 'package:carnow/core/config/backend_config.dart';

/// Backend health status
enum BackendStatus {
  healthy,
  degraded,
  unavailable,
  unknown,
}

/// Backend resilience service for handling outages gracefully
class BackendResilienceService {
  static final String _healthCheckUrl =
      CarnowBackendConfig.buildUrlSync(CarnowBackendConfig.healthEndpoint);
  static const String _lastKnownStatusKey = 'backend_last_known_status';
  static const String _lastCheckTimeKey = 'backend_last_check_time';
  static const String _outageStartTimeKey = 'backend_outage_start_time';
  static const Duration _healthCheckInterval = Duration(minutes: 2);
  static const Duration _retryInterval = Duration(seconds: 30);
  
  BackendStatus _currentStatus = BackendStatus.unknown;
  DateTime? _outageStartTime;
  Timer? _healthCheckTimer;
  final List<VoidCallback> _statusListeners = [];
  
  static final BackendResilienceService _instance = BackendResilienceService._internal();
  factory BackendResilienceService() => _instance;
  BackendResilienceService._internal();

  /// Current backend status
  BackendStatus get currentStatus => _currentStatus;
  
  /// Time when current outage started (if any)
  DateTime? get outageStartTime => _outageStartTime;
  
  /// Duration of current outage
  Duration? get outageDuration {
    if (_outageStartTime == null) return null;
    return DateTime.now().difference(_outageStartTime!);
  }
  
  /// Whether backend is available for requests
  bool get isHealthy => _currentStatus == BackendStatus.healthy;
  
  /// Whether we should show degraded service warnings
  bool get isDegraded => _currentStatus == BackendStatus.degraded;
  
  /// Whether backend is completely unavailable
  bool get isUnavailable => _currentStatus == BackendStatus.unavailable;

  /// Initialize the service
  Future<void> initialize() async {
    await _loadStoredStatus();
    await checkBackendHealth();
    _startPeriodicHealthCheck();
  }

  /// Add listener for status changes
  void addStatusListener(VoidCallback listener) {
    _statusListeners.add(listener);
  }

  /// Remove status listener
  void removeStatusListener(VoidCallback listener) {
    _statusListeners.remove(listener);
  }

  /// Check backend health immediately
  Future<BackendStatus> checkBackendHealth() async {
    try {
      final response = await http.get(
        Uri.parse(_healthCheckUrl),
        headers: {'Accept': 'application/json'},
      ).timeout(const Duration(seconds: 10));

      final newStatus = _determineStatusFromResponse(response);
      await _updateStatus(newStatus);
      return newStatus;
    } catch (e) {
      debugPrint('🔗 Backend health check failed: $e');
      await _updateStatus(BackendStatus.unavailable);
      return BackendStatus.unavailable;
    }
  }

  /// Determine status from HTTP response
  BackendStatus _determineStatusFromResponse(http.Response response) {
    if (response.statusCode == 200) {
      return BackendStatus.healthy;
    } else if (response.statusCode >= 500) {
      return BackendStatus.unavailable;
    } else if (response.statusCode >= 400) {
      return BackendStatus.degraded;
    } else {
      return BackendStatus.degraded;
    }
  }

  /// Update status and notify listeners
  Future<void> _updateStatus(BackendStatus newStatus) async {
    final previousStatus = _currentStatus;
    _currentStatus = newStatus;

    // Track outage timing
    if (previousStatus != BackendStatus.unavailable && newStatus == BackendStatus.unavailable) {
      _outageStartTime = DateTime.now();
      await _storeOutageStartTime();
    } else if (previousStatus == BackendStatus.unavailable && newStatus != BackendStatus.unavailable) {
      _outageStartTime = null;
      await _clearOutageStartTime();
    }

    // Store status
    await _storeStatus(newStatus);
    
    // Notify listeners
    for (final listener in _statusListeners) {
      try {
        listener();
      } catch (e) {
        debugPrint('Error notifying status listener: $e');
      }
    }

    // Log status changes
    if (previousStatus != newStatus) {
      debugPrint('🔗 Backend status changed: $previousStatus → $newStatus');
    }
  }

  /// Start periodic health checking
  void _startPeriodicHealthCheck() {
    _healthCheckTimer?.cancel();
    
    final interval = _currentStatus == BackendStatus.unavailable 
        ? _retryInterval 
        : _healthCheckInterval;
    
    _healthCheckTimer = Timer.periodic(interval, (_) {
      checkBackendHealth();
    });
  }

  /// Load stored status from preferences
  Future<void> _loadStoredStatus() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Load last known status
      final statusString = prefs.getString(_lastKnownStatusKey);
      if (statusString != null) {
        _currentStatus = BackendStatus.values.firstWhere(
          (status) => status.toString() == statusString,
          orElse: () => BackendStatus.unknown,
        );
      }
      
      // Load outage start time
      final outageStartString = prefs.getString(_outageStartTimeKey);
      if (outageStartString != null) {
        _outageStartTime = DateTime.parse(outageStartString);
      }
    } catch (e) {
      debugPrint('Error loading stored backend status: $e');
    }
  }

  /// Store current status
  Future<void> _storeStatus(BackendStatus status) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_lastKnownStatusKey, status.toString());
      await prefs.setString(_lastCheckTimeKey, DateTime.now().toIso8601String());
    } catch (e) {
      debugPrint('Error storing backend status: $e');
    }
  }

  /// Store outage start time
  Future<void> _storeOutageStartTime() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      if (_outageStartTime != null) {
        await prefs.setString(_outageStartTimeKey, _outageStartTime!.toIso8601String());
      }
    } catch (e) {
      debugPrint('Error storing outage start time: $e');
    }
  }

  /// Clear outage start time
  Future<void> _clearOutageStartTime() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_outageStartTimeKey);
    } catch (e) {
      debugPrint('Error clearing outage start time: $e');
    }
  }

  /// Get user-friendly status message
  String getStatusMessage() {
    switch (_currentStatus) {
      case BackendStatus.healthy:
        return 'خدمة المحفظة متاحة';
      case BackendStatus.degraded:
        return 'خدمة المحفظة تعمل ببطء';
      case BackendStatus.unavailable:
        final duration = outageDuration;
        if (duration != null) {
          final minutes = duration.inMinutes;
          if (minutes < 60) {
            return 'خدمة المحفظة غير متاحة منذ $minutes دقيقة';
          } else {
            final hours = duration.inHours;
            return 'خدمة المحفظة غير متاحة منذ $hours ساعة';
          }
        }
        return 'خدمة المحفظة غير متاحة حالياً';
      case BackendStatus.unknown:
        return 'جاري فحص حالة خدمة المحفظة...';
    }
  }

  /// Get recovery suggestion for user
  String getRecoverySuggestion() {
    switch (_currentStatus) {
      case BackendStatus.healthy:
        return '';
      case BackendStatus.degraded:
        return 'قد تواجه بطء في الاستجابة. جاري المحاولة...';
      case BackendStatus.unavailable:
        return 'سيتم المحاولة تلقائياً كل 30 ثانية. يمكنك المحاولة لاحقاً.';
      case BackendStatus.unknown:
        return 'يرجى الانتظار...';
    }
  }

  /// Dispose resources
  void dispose() {
    _healthCheckTimer?.cancel();
    _statusListeners.clear();
  }
} 