import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:web_socket_channel/web_socket_channel.dart';
import 'package:web_socket_channel/io.dart';

import '../config/backend_config.dart';
import '../auth/unified_auth_provider.dart';

part 'websocket_service.g.dart';

/// WebSocket message model
class WebSocketMessage {
  final String type;
  final String event;
  final Map<String, dynamic> data;
  final String? userId;
  final DateTime timestamp;
  final Map<String, dynamic>? metadata;

  const WebSocketMessage({
    required this.type,
    required this.event,
    required this.data,
    this.userId,
    required this.timestamp,
    this.metadata,
  });

  factory WebSocketMessage.fromJson(Map<String, dynamic> json) {
    return WebSocketMessage(
      type: json['type'] as String,
      event: json['event'] as String,
      data: json['data'] as Map<String, dynamic>,
      userId: json['user_id'] as String?,
      timestamp: DateTime.parse(json['timestamp'] as String),
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'type': type,
      'event': event,
      'data': data,
      'user_id': userId,
      'timestamp': timestamp.toIso8601String(),
      'metadata': metadata,
    };
  }
}

/// WebSocket connection state
enum WebSocketConnectionState {
  disconnected,
  connecting,
  connected,
  reconnecting,
  error,
}

/// WebSocket service for real-time communication
class WebSocketService {
  final Ref _ref;
  WebSocketChannel? _channel;
  StreamSubscription? _subscription;
  Timer? _reconnectTimer;
  Timer? _heartbeatTimer;

  WebSocketConnectionState _connectionState =
      WebSocketConnectionState.disconnected;
  String? _lastError;
  int _reconnectAttempts = 0;
  static const int _maxReconnectAttempts = 5;
  static const Duration _reconnectDelay = Duration(seconds: 5);
  static const Duration _heartbeatInterval = Duration(seconds: 30);

  // Stream controllers for different message types
  final StreamController<WebSocketMessage> _messageController =
      StreamController.broadcast();
  final StreamController<WebSocketConnectionState> _connectionStateController =
      StreamController.broadcast();
  final StreamController<Map<String, dynamic>> _productUpdatesController =
      StreamController.broadcast();
  final StreamController<Map<String, dynamic>> _orderUpdatesController =
      StreamController.broadcast();
  final StreamController<Map<String, dynamic>> _walletUpdatesController =
      StreamController.broadcast();
  final StreamController<Map<String, dynamic>> _inventoryUpdatesController =
      StreamController.broadcast();
  final StreamController<Map<String, dynamic>> _systemMessagesController =
      StreamController.broadcast();

  WebSocketService(this._ref);

  // Getters for streams
  Stream<WebSocketMessage> get messageStream => _messageController.stream;
  Stream<WebSocketConnectionState> get connectionStateStream =>
      _connectionStateController.stream;
  Stream<Map<String, dynamic>> get productUpdatesStream =>
      _productUpdatesController.stream;
  Stream<Map<String, dynamic>> get orderUpdatesStream =>
      _orderUpdatesController.stream;
  Stream<Map<String, dynamic>> get walletUpdatesStream =>
      _walletUpdatesController.stream;
  Stream<Map<String, dynamic>> get inventoryUpdatesStream =>
      _inventoryUpdatesController.stream;
  Stream<Map<String, dynamic>> get systemMessagesStream =>
      _systemMessagesController.stream;

  // Getters for state
  WebSocketConnectionState get connectionState => _connectionState;
  String? get lastError => _lastError;
  bool get isConnected =>
      _connectionState == WebSocketConnectionState.connected;

  /// Connect to WebSocket server
  Future<void> connect() async {
    if (_connectionState == WebSocketConnectionState.connected ||
        _connectionState == WebSocketConnectionState.connecting) {
      return;
    }

    try {
      _updateConnectionState(WebSocketConnectionState.connecting);

      // Get auth token
      final authState = _ref.read(currentAuthStateProvider);
      String? token;

      if (authState is AuthStateAuthenticated) {
        token = _ref.read(currentAccessTokenProvider);
      }

      if (token == null) {
        throw Exception('No authentication token available');
      }

      // Create WebSocket URL
      final wsUrl = BackendConfig.websocketUrl;
      final uri = Uri.parse('$wsUrl?token=$token');

      debugPrint('🔌 Connecting to WebSocket: $uri');

      // Create WebSocket connection
      _channel = IOWebSocketChannel.connect(uri);

      // Listen to messages
      _subscription = _channel!.stream.listen(
        _handleMessage,
        onError: _handleError,
        onDone: _handleDisconnection,
      );

      _updateConnectionState(WebSocketConnectionState.connected);
      _reconnectAttempts = 0;
      _startHeartbeat();

      debugPrint('✅ WebSocket connected successfully');
    } catch (e) {
      debugPrint('❌ WebSocket connection failed: $e');
      _lastError = e.toString();
      _updateConnectionState(WebSocketConnectionState.error);
      _scheduleReconnect();
    }
  }

  /// Disconnect from WebSocket server
  Future<void> disconnect() async {
    debugPrint('🔌 Disconnecting WebSocket...');

    _reconnectTimer?.cancel();
    _heartbeatTimer?.cancel();

    await _subscription?.cancel();
    await _channel?.sink.close();

    _channel = null;
    _subscription = null;
    _reconnectTimer = null;
    _heartbeatTimer = null;

    _updateConnectionState(WebSocketConnectionState.disconnected);

    debugPrint('✅ WebSocket disconnected');
  }

  /// Send message to server
  void sendMessage(String type, String event, Map<String, dynamic> data) {
    if (!isConnected) {
      debugPrint('⚠️ Cannot send message: WebSocket not connected');
      return;
    }

    final message = WebSocketMessage(
      type: type,
      event: event,
      data: data,
      timestamp: DateTime.now(),
    );

    try {
      _channel?.sink.add(jsonEncode(message.toJson()));
      debugPrint('📤 Sent WebSocket message: $type/$event');
    } catch (e) {
      debugPrint('❌ Failed to send WebSocket message: $e');
    }
  }

  /// Subscribe to specific data type updates
  void subscribe(String dataType) {
    sendMessage('client', 'subscribe', {'type': dataType});
    debugPrint('✅ Subscribed to $dataType updates');
  }

  /// Unsubscribe from specific data type updates
  void unsubscribe(String dataType) {
    sendMessage('client', 'unsubscribe', {'type': dataType});
    debugPrint('✅ Unsubscribed from $dataType updates');
  }

  /// Handle incoming messages
  void _handleMessage(dynamic rawMessage) {
    try {
      final messageData =
          jsonDecode(rawMessage as String) as Map<String, dynamic>;
      final message = WebSocketMessage.fromJson(messageData);

      debugPrint(
        '📥 Received WebSocket message: ${message.type}/${message.event}',
      );

      // Emit to general message stream
      _messageController.add(message);

      // Route to specific streams based on message type and event
      _routeMessage(message);
    } catch (e) {
      debugPrint('❌ Failed to parse WebSocket message: $e');
    }
  }

  /// Route messages to appropriate streams
  void _routeMessage(WebSocketMessage message) {
    switch (message.type) {
      case 'product':
        _productUpdatesController.add(message.data);
        break;

      case 'order':
        _orderUpdatesController.add(message.data);
        break;

      case 'wallet':
        _walletUpdatesController.add(message.data);
        break;

      case 'inventory':
        _inventoryUpdatesController.add(message.data);
        break;

      case 'system':
        if (message.event == 'ping') {
          // Respond to ping with pong
          sendMessage('client', 'pong', {
            'timestamp': DateTime.now().toIso8601String(),
          });
        } else {
          _systemMessagesController.add(message.data);
        }
        break;

      case 'data_change':
        // Handle data change events for optimistic updates
        _handleDataChange(message);
        break;

      default:
        debugPrint('⚠️ Unknown message type: ${message.type}');
    }
  }

  /// Handle data change events for optimistic updates
  void _handleDataChange(WebSocketMessage message) {
    final data = message.data;
    final entityType = data['entity_type'] as String?;

    switch (entityType) {
      case 'product':
        _productUpdatesController.add(data);
        break;
      case 'order':
        _orderUpdatesController.add(data);
        break;
      default:
        debugPrint('⚠️ Unknown entity type in data change: $entityType');
    }
  }

  /// Handle WebSocket errors
  void _handleError(dynamic error) {
    debugPrint('❌ WebSocket error: $error');
    _lastError = error.toString();
    _updateConnectionState(WebSocketConnectionState.error);
    _scheduleReconnect();
  }

  /// Handle WebSocket disconnection
  void _handleDisconnection() {
    debugPrint('🔌 WebSocket disconnected');
    _updateConnectionState(WebSocketConnectionState.disconnected);
    _scheduleReconnect();
  }

  /// Update connection state and notify listeners
  void _updateConnectionState(WebSocketConnectionState newState) {
    if (_connectionState != newState) {
      _connectionState = newState;
      _connectionStateController.add(newState);
      debugPrint('🔄 WebSocket state changed to: $newState');
    }
  }

  /// Schedule reconnection attempt
  void _scheduleReconnect() {
    if (_reconnectAttempts >= _maxReconnectAttempts) {
      debugPrint('❌ Max reconnection attempts reached');
      _updateConnectionState(WebSocketConnectionState.error);
      return;
    }

    if (_reconnectTimer?.isActive == true) {
      return; // Already scheduled
    }

    _reconnectAttempts++;
    _updateConnectionState(WebSocketConnectionState.reconnecting);

    final delay = Duration(
      seconds: _reconnectDelay.inSeconds * _reconnectAttempts,
    );
    debugPrint(
      '🔄 Scheduling reconnection attempt $_reconnectAttempts in ${delay.inSeconds}s',
    );

    _reconnectTimer = Timer(delay, () {
      debugPrint('🔄 Attempting to reconnect...');
      connect();
    });
  }

  /// Start heartbeat to keep connection alive
  void _startHeartbeat() {
    _heartbeatTimer?.cancel();
    _heartbeatTimer = Timer.periodic(_heartbeatInterval, (timer) {
      if (isConnected) {
        sendMessage('client', 'ping', {
          'timestamp': DateTime.now().toIso8601String(),
        });
      } else {
        timer.cancel();
      }
    });
  }

  /// Dispose resources
  void dispose() {
    debugPrint('🗑️ Disposing WebSocket service');

    disconnect();

    _messageController.close();
    _connectionStateController.close();
    _productUpdatesController.close();
    _orderUpdatesController.close();
    _walletUpdatesController.close();
    _inventoryUpdatesController.close();
    _systemMessagesController.close();
  }
}

/// WebSocket service provider
@riverpod
WebSocketService webSocketService(Ref ref) {
  final service = WebSocketService(ref);

  // Auto-connect when authenticated
  ref.listen(currentAuthStateProvider, (previous, next) {
    if (next is AuthStateAuthenticated) {
      service.connect();
    } else {
      service.disconnect();
    }
  });

  // Dispose when provider is disposed
  ref.onDispose(() {
    service.dispose();
  });

  return service;
}

/// Connection state provider
@riverpod
Stream<WebSocketConnectionState> webSocketConnectionState(Ref ref) {
  final service = ref.watch(webSocketServiceProvider);
  return service.connectionStateStream;
}

/// Product updates stream provider
@riverpod
Stream<Map<String, dynamic>> productUpdatesStream(Ref ref) {
  final service = ref.watch(webSocketServiceProvider);
  return service.productUpdatesStream;
}

/// Order updates stream provider
@riverpod
Stream<Map<String, dynamic>> orderUpdatesStream(Ref ref) {
  final service = ref.watch(webSocketServiceProvider);
  return service.orderUpdatesStream;
}

/// Wallet updates stream provider
@riverpod
Stream<Map<String, dynamic>> walletUpdatesStream(Ref ref) {
  final service = ref.watch(webSocketServiceProvider);
  return service.walletUpdatesStream;
}

/// Inventory updates stream provider
@riverpod
Stream<Map<String, dynamic>> inventoryUpdatesStream(Ref ref) {
  final service = ref.watch(webSocketServiceProvider);
  return service.inventoryUpdatesStream;
}

/// System messages stream provider
@riverpod
Stream<Map<String, dynamic>> systemMessagesStream(Ref ref) {
  final service = ref.watch(webSocketServiceProvider);
  return service.systemMessagesStream;
}
