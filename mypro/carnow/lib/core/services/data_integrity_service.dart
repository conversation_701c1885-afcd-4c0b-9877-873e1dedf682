import 'package:logging/logging.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../utils/database_maintenance_utils.dart';
import 'package:riverpod/riverpod.dart';

part 'data_integrity_service.g.dart';

/// Service for handling data integrity issues across the application
class DataIntegrityService {
  DataIntegrityService(this._ref);

  final Ref _ref;
  static final _logger = Logger('DataIntegrityService');

  /// Fix a specific product's data integrity issues
  Future<bool> fixProductDataIntegrity(String productId) async {
    try {
      _logger.info('Attempting to fix data integrity for product: $productId');

      // Fix null seller_id issue
      final success = await DatabaseMaintenanceUtils.fixProductWithNullSellerId(
        productId,
        ref: _ref,
      );

      if (success) {
        _logger.info('Successfully fixed product data integrity: $productId');
      } else {
        _logger.warning('Failed to fix product data integrity: $productId');
      }

      return success;
    } catch (e) {
      _logger.severe('Error fixing product data integrity for $productId: $e');
      return false;
    }
  }

  /// Run a comprehensive fix for all data integrity issues
  Future<Map<String, dynamic>> runComprehensiveDataFix() async {
    try {
      _logger.info('Starting comprehensive data integrity fix...');

      // Generate health report first
      final healthReport =
          await DatabaseMaintenanceUtils.getProductsHealthReport(ref: _ref);
      _logger.info(
        'Current health score: ${healthReport['health_score_percentage']}%',
      );

      // Fix orphaned products
      final fixReport = await DatabaseMaintenanceUtils.fixAllOrphanedProducts(ref: _ref);

      // Generate new health report
      final newHealthReport =
          await DatabaseMaintenanceUtils.getProductsHealthReport(ref: _ref);

      final result = {
        'initial_health_score': healthReport['health_score_percentage'],
        'final_health_score': newHealthReport['health_score_percentage'],
        'fix_details': fixReport,
        'improvement':
            (newHealthReport['health_score_percentage'] as int) -
            (healthReport['health_score_percentage'] as int),
      };

      _logger.info(
        'Comprehensive data fix completed. Health score improved by ${result['improvement']}%',
      );
      return result;
    } catch (e) {
      _logger.severe('Error in comprehensive data fix: $e');
      rethrow;
    }
  }

  /// Quick health check and fix for critical issues
  Future<void> quickHealthCheckAndFix() async {
    try {
      _logger.info('Running quick health check...');

      final orphanedProducts =
          await DatabaseMaintenanceUtils.findProductsWithNullSellerId(ref: _ref);

      if (orphanedProducts.isNotEmpty) {
        _logger.warning(
          'Found ${orphanedProducts.length} products with null seller_id',
        );

        // Auto-fix if less than 10 products
        if (orphanedProducts.length <= 10) {
          _logger.info('Auto-fixing small number of orphaned products...');
          await DatabaseMaintenanceUtils.fixAllOrphanedProducts(ref: _ref);
        } else {
          _logger.warning(
            'Large number of orphaned products found. Manual review recommended.',
          );
        }
      } else {
        _logger.info('No data integrity issues found');
      }
    } catch (e) {
      _logger.severe('Error in quick health check: $e');
    }
  }

  /// Get health report for UI display
  Future<Map<String, dynamic>> getHealthReport() async {
    try {
      return await DatabaseMaintenanceUtils.getProductsHealthReport(ref: _ref);
    } catch (e) {
      _logger.severe('Error getting health report: $e');
      rethrow;
    }
  }

  /// Log data integrity report to console
  Future<void> printDataIntegrityReport() async {
    try {
      final report = await DatabaseMaintenanceUtils.getProductsHealthReport(ref: _ref);
      DatabaseMaintenanceUtils.printHealthReport(report);
    } catch (e) {
      _logger.severe('Error generating data integrity report: $e');
    }
  }
}

@Riverpod(keepAlive: true)
DataIntegrityService dataIntegrityService(Ref ref) {
  return DataIntegrityService(ref);
}
