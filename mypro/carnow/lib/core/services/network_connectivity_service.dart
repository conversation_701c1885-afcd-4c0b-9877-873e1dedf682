import 'package:logging/logging.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

part 'network_connectivity_service.g.dart';

final _logger = Logger('NetworkConnectivityService');

/// Network connectivity service for managing backend connectivity issues
/// Provides fallback mechanisms and connectivity monitoring
class NetworkConnectivityService {
  static final NetworkConnectivityService _instance = NetworkConnectivityService._internal();
  factory NetworkConnectivityService() => _instance;
  NetworkConnectivityService._internal();

  bool _isBackendAvailable = true;
  DateTime? _lastBackendCheck;
  static const Duration _checkInterval = Duration(minutes: 5);

  /// Check if backend is currently available
  bool get isBackendAvailable => _isBackendAvailable;

  /// Get last backend check time
  DateTime? get lastBackendCheck => _lastBackendCheck;

  /// Update backend availability status
  void updateBackendStatus(bool isAvailable) {
    _isBackendAvailable = isAvailable;
    _lastBackendCheck = DateTime.now();
    
    if (!isAvailable) {
      _logger.warning('Backend connectivity issue detected');
    } else {
      _logger.info('Backend connectivity restored');
    }
  }

  /// Check if we should attempt backend connection
  bool shouldAttemptBackendConnection() {
    if (_lastBackendCheck == null) return true;
    
    final timeSinceLastCheck = DateTime.now().difference(_lastBackendCheck!);
    return timeSinceLastCheck > _checkInterval;
  }

  /// Determine if error is network-related
  bool isNetworkError(String errorMessage) {
    final message = errorMessage.toLowerCase();
    return message.contains('network request failed') ||
           message.contains('timeout') ||
           message.contains('connection') ||
           message.contains('socket') ||
           message.contains('unreachable') ||
           message.contains('no internet');
  }

  /// Get user-friendly error message
  String getUserFriendlyErrorMessage(String errorMessage) {
    if (isNetworkError(errorMessage)) {
      return 'Connection issue detected. Showing offline data.';
    }
    return errorMessage;
  }
}

/// Provider for network connectivity service
@riverpod
NetworkConnectivityService networkConnectivityService(Ref ref) {
  return NetworkConnectivityService();
}

/// Provider for backend availability status
@riverpod
bool isBackendAvailable(Ref ref) {
  return ref.watch(networkConnectivityServiceProvider).isBackendAvailable;
} 