import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../networking/simple_api_client.dart';
import '../models/api_response.dart';

part 'polling_service.g.dart';

/// Polling configuration for different data types
class PollingConfig {
  final Duration interval;
  final bool enabled;
  final int maxRetries;
  final Duration retryDelay;

  const PollingConfig({
    required this.interval,
    this.enabled = true,
    this.maxRetries = 3,
    this.retryDelay = const Duration(seconds: 5),
  });

  static const PollingConfig products = PollingConfig(
    interval: Duration(seconds: 30), // Poll products every 30 seconds
  );

  static const PollingConfig orders = PollingConfig(
    interval: Duration(seconds: 15), // Poll orders every 15 seconds
  );

  static const PollingConfig wallet = PollingConfig(
    interval: Duration(seconds: 10), // Poll wallet every 10 seconds
  );

  static const PollingConfig inventory = PollingConfig(
    interval: Duration(seconds: 20), // Poll inventory every 20 seconds
  );

  static const PollingConfig notifications = PollingConfig(
    interval: Duration(seconds: 60), // Poll notifications every minute
  );
}

/// Polling result with metadata
class PollingResult<T> {
  final T data;
  final DateTime timestamp;
  final bool hasChanges;
  final String? error;
  final int attempt;

  const PollingResult({
    required this.data,
    required this.timestamp,
    this.hasChanges = false,
    this.error,
    this.attempt = 1,
  });

  PollingResult<T> copyWith({
    T? data,
    DateTime? timestamp,
    bool? hasChanges,
    String? error,
    int? attempt,
  }) {
    return PollingResult<T>(
      data: data ?? this.data,
      timestamp: timestamp ?? this.timestamp,
      hasChanges: hasChanges ?? this.hasChanges,
      error: error ?? this.error,
      attempt: attempt ?? this.attempt,
    );
  }
}

/// Polling service for frequently changing data
class PollingService {
  final Ref _ref;
  final Map<String, Timer> _timers = {};
  final Map<String, StreamController<PollingResult>> _controllers = {};
  final Map<String, dynamic> _lastData = {};
  final Map<String, int> _retryAttempts = {};

  PollingService(this._ref);

  /// Start polling for a specific data type
  void startPolling<T>(
    String dataType,
    Future<T> Function() fetchFunction,
    PollingConfig config,
  ) {
    if (!config.enabled) {
      debugPrint('⚠️ Polling disabled for $dataType');
      return;
    }

    // Stop existing polling if any
    stopPolling(dataType);

    // Create stream controller
    _controllers[dataType] = StreamController<PollingResult>.broadcast();

    debugPrint(
      '🔄 Starting polling for $dataType (interval: ${config.interval})',
    );

    // Start immediate fetch
    _fetchData(dataType, fetchFunction, config);

    // Start periodic polling
    _timers[dataType] = Timer.periodic(config.interval, (timer) {
      _fetchData(dataType, fetchFunction, config);
    });
  }

  /// Stop polling for a specific data type
  void stopPolling(String dataType) {
    _timers[dataType]?.cancel();
    _timers.remove(dataType);

    _controllers[dataType]?.close();
    _controllers.remove(dataType);

    _lastData.remove(dataType);
    _retryAttempts.remove(dataType);

    debugPrint('🛑 Stopped polling for $dataType');
  }

  /// Get polling stream for a data type
  Stream<PollingResult>? getPollingStream(String dataType) {
    return _controllers[dataType]?.stream;
  }

  /// Check if polling is active for a data type
  bool isPolling(String dataType) {
    return _timers.containsKey(dataType) && _timers[dataType]!.isActive;
  }

  /// Fetch data and emit to stream
  Future<void> _fetchData<T>(
    String dataType,
    Future<T> Function() fetchFunction,
    PollingConfig config,
  ) async {
    try {
      final data = await fetchFunction();
      final timestamp = DateTime.now();

      // Check if data has changed
      final hasChanges = _hasDataChanged(dataType, data);

      if (hasChanges) {
        _lastData[dataType] = data;
        debugPrint('✅ Polling update for $dataType: changes detected');
      }

      // Reset retry attempts on success
      _retryAttempts[dataType] = 0;

      // Emit result
      final result = PollingResult<T>(
        data: data,
        timestamp: timestamp,
        hasChanges: hasChanges,
      );

      _controllers[dataType]?.add(result);
    } catch (error) {
      debugPrint('❌ Polling error for $dataType: $error');

      final attempts = (_retryAttempts[dataType] ?? 0) + 1;
      _retryAttempts[dataType] = attempts;

      // Emit error result
      final result = PollingResult<T>(
        data: _lastData[dataType] as T? ?? {} as T,
        timestamp: DateTime.now(),
        error: error.toString(),
        attempt: attempts,
      );

      _controllers[dataType]?.add(result);

      // Schedule retry if within limits
      if (attempts < config.maxRetries) {
        Timer(config.retryDelay, () {
          _fetchData(dataType, fetchFunction, config);
        });
      } else {
        debugPrint('❌ Max retry attempts reached for $dataType');
        stopPolling(dataType);
      }
    }
  }

  /// Check if data has changed since last fetch
  bool _hasDataChanged(String dataType, dynamic newData) {
    final lastData = _lastData[dataType];

    if (lastData == null) {
      return true; // First fetch
    }

    // Simple comparison - in production, you might want more sophisticated comparison
    return newData.toString() != lastData.toString();
  }

  /// Get polling statistics
  Map<String, dynamic> getPollingStats() {
    return {
      'active_polls': _timers.keys.toList(),
      'retry_attempts': Map.from(_retryAttempts),
      'last_update_times': _lastData.keys.map((key) {
        return {'data_type': key, 'has_data': _lastData[key] != null};
      }).toList(),
      'timestamp': DateTime.now().toIso8601String(),
    };
  }

  /// Dispose all polling timers and controllers
  void dispose() {
    debugPrint('🗑️ Disposing polling service');

    for (final timer in _timers.values) {
      timer.cancel();
    }
    _timers.clear();

    for (final controller in _controllers.values) {
      controller.close();
    }
    _controllers.clear();

    _lastData.clear();
    _retryAttempts.clear();
  }
}

/// Polling service provider
@riverpod
PollingService pollingService(Ref ref) {
  final service = PollingService(ref);

  ref.onDispose(() {
    service.dispose();
  });

  return service;
}

/// Products polling provider
@riverpod
Stream<PollingResult> productsPolling(Ref ref) {
  final pollingService = ref.watch(pollingServiceProvider);
  final apiClient = ref.watch(simpleApiClientProvider);

  // Start polling for products
  pollingService.startPolling<Map<String, dynamic>>('products', () async {
    final response = await apiClient.get<Map<String, dynamic>>(
      '/api/v1/products',
    );
    return response.when(
      success: (data) => data,
      error: (error) => throw Exception(error),
    );
  }, PollingConfig.products);

  return pollingService.getPollingStream('products')!;
}

/// Orders polling provider
@riverpod
Stream<PollingResult> ordersPolling(Ref ref) {
  final pollingService = ref.watch(pollingServiceProvider);
  final apiClient = ref.watch(simpleApiClientProvider);

  pollingService.startPolling<Map<String, dynamic>>('orders', () async {
    final response = await apiClient.get<Map<String, dynamic>>(
      '/api/v1/orders',
    );
    return response.when(
      success: (data) => data,
      error: (error) => throw Exception(error),
    );
  }, PollingConfig.orders);

  return pollingService.getPollingStream('orders')!;
}

/// Wallet polling provider
@riverpod
Stream<PollingResult> walletPolling(Ref ref) {
  final pollingService = ref.watch(pollingServiceProvider);
  final apiClient = ref.watch(simpleApiClientProvider);

  pollingService.startPolling<Map<String, dynamic>>('wallet', () async {
    final response = await apiClient.get<Map<String, dynamic>>(
      '/api/v1/wallets/user',
    );
    return response.when(
      success: (data) => data,
      error: (error) => throw Exception(error),
    );
  }, PollingConfig.wallet);

  return pollingService.getPollingStream('wallet')!;
}

/// Inventory polling provider
@riverpod
Stream<PollingResult> inventoryPolling(Ref ref) {
  final pollingService = ref.watch(pollingServiceProvider);
  final apiClient = ref.watch(simpleApiClientProvider);

  pollingService.startPolling<Map<String, dynamic>>('inventory', () async {
    final response = await apiClient.get<Map<String, dynamic>>(
      '/api/v1/seller/inventory',
    );
    return response.when(
      success: (data) => data,
      error: (error) => throw Exception(error),
    );
  }, PollingConfig.inventory);

  return pollingService.getPollingStream('inventory')!;
}

/// Notifications polling provider
@riverpod
Stream<PollingResult> notificationsPolling(Ref ref) {
  final pollingService = ref.watch(pollingServiceProvider);
  final apiClient = ref.watch(simpleApiClientProvider);

  pollingService.startPolling<Map<String, dynamic>>('notifications', () async {
    final response = await apiClient.get<Map<String, dynamic>>(
      '/api/v1/notifications',
    );
    return response.when(
      success: (data) => data,
      error: (error) => throw Exception(error),
    );
  }, PollingConfig.notifications);

  return pollingService.getPollingStream('notifications')!;
}

/// Combined real-time data provider that merges WebSocket and polling
@riverpod
Stream<Map<String, dynamic>> realtimeProductsData(Ref ref) async* {
  // Listen to both WebSocket updates and polling results
  final wsStream = ref.watch(productUpdatesStreamProvider);
  final pollingStream = ref.watch(productsPollingProvider);

  await for (final wsData in wsStream) {
    yield {
      'source': 'websocket',
      'data': wsData,
      'timestamp': DateTime.now().toIso8601String(),
    };
  }

  // Note: In a real implementation, you'd merge these streams properly
  // This is a simplified example
}

/// Adaptive polling that adjusts frequency based on activity
class AdaptivePollingService extends PollingService {
  final Map<String, int> _activityScores = {};
  final Map<String, Duration> _currentIntervals = {};

  AdaptivePollingService(super.ref);

  /// Start adaptive polling that adjusts frequency based on data changes
  void startAdaptivePolling<T>(
    String dataType,
    Future<T> Function() fetchFunction,
    PollingConfig baseConfig,
  ) {
    _currentIntervals[dataType] = baseConfig.interval;
    _activityScores[dataType] = 0;

    startPolling(dataType, fetchFunction, baseConfig);

    // Listen to results and adjust polling frequency
    getPollingStream(dataType)?.listen((result) {
      _adjustPollingFrequency(dataType, result, fetchFunction, baseConfig);
    });
  }

  /// Adjust polling frequency based on activity
  void _adjustPollingFrequency<T>(
    String dataType,
    PollingResult result,
    Future<T> Function() fetchFunction,
    PollingConfig baseConfig,
  ) {
    final currentScore = _activityScores[dataType] ?? 0;

    if (result.hasChanges) {
      // Increase activity score and decrease interval (poll more frequently)
      _activityScores[dataType] = (currentScore + 1).clamp(0, 10);
    } else {
      // Decrease activity score and increase interval (poll less frequently)
      _activityScores[dataType] = (currentScore - 1).clamp(0, 10);
    }

    final score = _activityScores[dataType]!;
    final baseInterval = baseConfig.interval.inSeconds;

    // Calculate new interval based on activity score
    final newIntervalSeconds = (baseInterval * (1.0 - (score * 0.1)))
        .round()
        .clamp(5, baseInterval * 2);
    final newInterval = Duration(seconds: newIntervalSeconds);

    // Only restart polling if interval changed significantly
    if ((_currentIntervals[dataType]!.inSeconds - newIntervalSeconds).abs() >
        5) {
      _currentIntervals[dataType] = newInterval;

      debugPrint(
        '🔄 Adjusting polling frequency for $dataType: ${newInterval.inSeconds}s (score: $score)',
      );

      // Restart with new interval
      final newConfig = PollingConfig(
        interval: newInterval,
        enabled: baseConfig.enabled,
        maxRetries: baseConfig.maxRetries,
        retryDelay: baseConfig.retryDelay,
      );

      startPolling(dataType, fetchFunction, newConfig);
    }
  }
}
