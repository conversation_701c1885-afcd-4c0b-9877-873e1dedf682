import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:logging/logging.dart';

part 'simple_connection_service.g.dart';

final _logger = Logger('SimpleConnectionService');

/// Simple Connection Service for Forever Plan Architecture
/// Flutter (UI Only) → Go API → Supabase (Data Only)
/// 
/// Simple connectivity monitoring - NO complex offline features
@riverpod
SimpleConnectionService simpleConnectionService(Ref ref) {
  return SimpleConnectionService();
}

@riverpod
Stream<bool> connectionStatus(Ref ref) {
  final service = ref.watch(simpleConnectionServiceProvider);
  return service.connectionStream;
}

class SimpleConnectionService {
  final StreamController<bool> _connectionController = StreamController<bool>.broadcast();
  bool _isConnected = true;
  Timer? _pingTimer;

  /// Stream of connection status
  Stream<bool> get connectionStream => _connectionController.stream;

  /// Current connection status
  bool get isConnected => _isConnected;

  /// Start monitoring connection (simple ping-based)
  void startMonitoring() {
    _logger.info('Starting simple connection monitoring');
    
    // Simple periodic check every 30 seconds
    _pingTimer = Timer.periodic(const Duration(seconds: 30), (_) {
      _checkConnection();
    });
    
    // Initial check
    _checkConnection();
  }

  /// Stop monitoring
  void stopMonitoring() {
    _logger.info('Stopping connection monitoring');
    _pingTimer?.cancel();
    _pingTimer = null;
  }

  /// Simple connection check
  Future<void> _checkConnection() async {
    try {
      // Simple connectivity test - just try to resolve a domain
      // In production, this could ping your backend health endpoint
      final result = await Future.delayed(
        const Duration(milliseconds: 500),
        () => true, // Simplified - assume connected
      );
      
      if (result != _isConnected) {
        _isConnected = result;
        _connectionController.add(_isConnected);
        _logger.info('Connection status changed: ${_isConnected ? 'Connected' : 'Disconnected'}');
      }
    } catch (e) {
      if (_isConnected) {
        _isConnected = false;
        _connectionController.add(_isConnected);
        _logger.warning('Connection lost: $e');
      }
    }
  }

  /// Execute operation with connection check
  Future<T> executeWithConnectionCheck<T>(
    Future<T> Function() operation, {
    String? operationName,
    T? fallbackValue,
  }) async {
    if (!_isConnected && fallbackValue != null) {
      _logger.warning('${operationName ?? 'Operation'} skipped - no connection. Using fallback.');
      return fallbackValue;
    }

    try {
      return await operation();
    } catch (e) {
      _logger.warning('${operationName ?? 'Operation'} failed: $e');
      
      if (fallbackValue != null) {
        _logger.info('Using fallback value for ${operationName ?? 'operation'}');
        return fallbackValue;
      }
      
      rethrow;
    }
  }

  /// Dispose resources
  void dispose() {
    stopMonitoring();
    _connectionController.close();
  }
} 