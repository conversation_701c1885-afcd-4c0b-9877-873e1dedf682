// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'websocket_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$webSocketServiceHash() => r'a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0';

/// WebSocket service provider
///
/// Copied from [webSocketService].
@ProviderFor(webSocketService)
final webSocketServiceProvider = AutoDisposeProvider<WebSocketService>.internal(
  webSocketService,
  name: r'webSocketServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$webSocketServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef WebSocketServiceRef = AutoDisposeProviderRef<WebSocketService>;
String _$webSocketConnectionStateHash() =>
    r'b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0u1';

/// Connection state provider
///
/// Copied from [webSocketConnectionState].
@ProviderFor(webSocketConnectionState)
final webSocketConnectionStateProvider =
    AutoDisposeStreamProvider<WebSocketConnectionState>.internal(
      webSocketConnectionState,
      name: r'webSocketConnectionStateProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$webSocketConnectionStateHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef WebSocketConnectionStateRef =
    AutoDisposeStreamProviderRef<WebSocketConnectionState>;
String _$productUpdatesStreamHash() =>
    r'c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0u1v2';

/// Product updates stream provider
///
/// Copied from [productUpdatesStream].
@ProviderFor(productUpdatesStream)
final productUpdatesStreamProvider =
    AutoDisposeStreamProvider<Map<String, dynamic>>.internal(
      productUpdatesStream,
      name: r'productUpdatesStreamProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$productUpdatesStreamHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef ProductUpdatesStreamRef =
    AutoDisposeStreamProviderRef<Map<String, dynamic>>;
String _$orderUpdatesStreamHash() =>
    r'd4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0u1v2w3';

/// Order updates stream provider
///
/// Copied from [orderUpdatesStream].
@ProviderFor(orderUpdatesStream)
final orderUpdatesStreamProvider =
    AutoDisposeStreamProvider<Map<String, dynamic>>.internal(
      orderUpdatesStream,
      name: r'orderUpdatesStreamProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$orderUpdatesStreamHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef OrderUpdatesStreamRef =
    AutoDisposeStreamProviderRef<Map<String, dynamic>>;
String _$walletUpdatesStreamHash() =>
    r'e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0u1v2w3x4';

/// Wallet updates stream provider
///
/// Copied from [walletUpdatesStream].
@ProviderFor(walletUpdatesStream)
final walletUpdatesStreamProvider =
    AutoDisposeStreamProvider<Map<String, dynamic>>.internal(
      walletUpdatesStream,
      name: r'walletUpdatesStreamProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$walletUpdatesStreamHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef WalletUpdatesStreamRef =
    AutoDisposeStreamProviderRef<Map<String, dynamic>>;
String _$inventoryUpdatesStreamHash() =>
    r'f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0u1v2w3x4y5';

/// Inventory updates stream provider
///
/// Copied from [inventoryUpdatesStream].
@ProviderFor(inventoryUpdatesStream)
final inventoryUpdatesStreamProvider =
    AutoDisposeStreamProvider<Map<String, dynamic>>.internal(
      inventoryUpdatesStream,
      name: r'inventoryUpdatesStreamProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$inventoryUpdatesStreamHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef InventoryUpdatesStreamRef =
    AutoDisposeStreamProviderRef<Map<String, dynamic>>;
String _$systemMessagesStreamHash() =>
    r'g7h8i9j0k1l2m3n4o5p6q7r8s9t0u1v2w3x4y5z6';

/// System messages stream provider
///
/// Copied from [systemMessagesStream].
@ProviderFor(systemMessagesStream)
final systemMessagesStreamProvider =
    AutoDisposeStreamProvider<Map<String, dynamic>>.internal(
      systemMessagesStream,
      name: r'systemMessagesStreamProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$systemMessagesStreamHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef SystemMessagesStreamRef =
    AutoDisposeStreamProviderRef<Map<String, dynamic>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
