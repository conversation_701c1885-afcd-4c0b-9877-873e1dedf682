import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import '../utils/unified_logger.dart';
import '../utils/resource_manager.dart';

/// Performance Monitor لمراقبة أداء التطبيق
class PerformanceMonitor {
  PerformanceMonitor._() {
    _eventController = ResourceManager.registerController(
      StreamController<PerformanceEvent>.broadcast(),
    );
  }

  static final PerformanceMonitor _instance = PerformanceMonitor._();
  static PerformanceMonitor get instance => _instance;

  final Map<String, Stopwatch> _activeTimers = {};
  final Map<String, PerformanceMetric> _metrics = {};
  final List<PerformanceEvent> _events = [];
  late final StreamController<PerformanceEvent> _eventController;

  /// Stream للاستماع لأحداث الأداء
  Stream<PerformanceEvent> get eventStream => _eventController.stream;

  /// بدء قياس الأداء
  void startTimer(String name, {String? description}) {
    if (_activeTimers.containsKey(name)) {
      UnifiedLogger.debug('Timer $name is already running');
      return;
    }

    final stopwatch = Stopwatch()..start();
    _activeTimers[name] = stopwatch;

    final event = PerformanceEvent(
      name: name,
      type: PerformanceEventType.timerStart,
      timestamp: DateTime.now(),
      description: description,
    );

    _events.add(event);
    _eventController.add(event);

    UnifiedLogger.debug('⏱️ Timer started: $name');
  }

  /// إيقاف قياس الأداء وحفظ النتيجة
  Duration? stopTimer(String name, {String? description}) {
    final stopwatch = _activeTimers.remove(name);
    if (stopwatch == null) {
      if (kDebugMode) {
        UnifiedLogger.debug('Timer $name was not found');
      }
      return null;
    }

    stopwatch.stop();
    final duration = stopwatch.elapsed;

    // حفظ المقياس
    final metric = PerformanceMetric(
      name: name,
      duration: duration,
      timestamp: DateTime.now(),
      description: description,
    );

    _metrics[name] = metric;

    // إضافة الحدث
    final event = PerformanceEvent(
      name: name,
      type: PerformanceEventType.timerEnd,
      timestamp: DateTime.now(),
      duration: duration,
      description: description,
    );

    _events.add(event);
    _eventController.add(event);

    UnifiedLogger.debug('⏹️ Timer stopped: $name (${duration.inMilliseconds}ms)');

    return duration;
  }

  /// قياس الأداء باستخدام دالة
  Future<T> measureAsync<T>(
    String name,
    Future<T> Function() operation, {
    String? description,
  }) async {
    startTimer(name, description: description);
    try {
      final result = await operation();
      return result;
    } finally {
      stopTimer(name);
    }
  }

  /// قياس الأداء لعملية متزامنة
  T measure<T>(String name, T Function() operation, {String? description}) {
    startTimer(name, description: description);
    try {
      final result = operation();
      return result;
    } finally {
      stopTimer(name);
    }
  }

  /// تسجيل حدث أداء مخصص
  void logEvent({
    required String name,
    required PerformanceEventType type,
    Duration? duration,
    String? description,
    Map<String, dynamic>? metadata,
  }) {
    final event = PerformanceEvent(
      name: name,
      type: type,
      timestamp: DateTime.now(),
      duration: duration,
      description: description,
      metadata: metadata,
    );

    _events.add(event);
    _eventController.add(event);

    UnifiedLogger.debug(
      'Event: $name - Type: $type ${duration != null ? '- Duration: ${duration.inMilliseconds}ms' : ''}',
    );
  }

  /// تسجيل تحميل صفحة
  void logPageLoad(String pageName, Duration loadTime, {String? description}) {
    logEvent(
      name: pageName,
      type: PerformanceEventType.pageLoad,
      duration: loadTime,
      description: description ?? 'Page load time',
    );
  }

  /// تسجيل استعلام API
  void logApiCall(
    String endpoint,
    Duration responseTime, {
    bool success = true,
    String? description,
    Map<String, dynamic>? metadata,
  }) {
    logEvent(
      name: endpoint,
      type: success
          ? PerformanceEventType.apiSuccess
          : PerformanceEventType.apiError,
      duration: responseTime,
      description: description ?? 'API call',
      metadata: metadata,
    );
  }

  /// تسجيل استخدام الذاكرة
  void logMemoryUsage({String? description}) {
    // في Flutter، معلومات الذاكرة محدودة، لكن يمكننا تسجيل الحدث
    logEvent(
      name: 'memory_check',
      type: PerformanceEventType.memoryUsage,
      description: description ?? 'Memory usage check',
    );
  }

  /// الحصول على مقياس محدد
  PerformanceMetric? getMetric(String name) => _metrics[name];

  /// الحصول على جميع المقاييس
  Map<String, PerformanceMetric> getAllMetrics() => Map.unmodifiable(_metrics);

  /// الحصول على الأحداث الأخيرة
  List<PerformanceEvent> getRecentEvents({int limit = 50}) {
    final sortedEvents = _events.toList()
      ..sort((a, b) => b.timestamp.compareTo(a.timestamp));
    return sortedEvents.take(limit).toList();
  }

  /// الحصول على إحصائيات الأداء
  PerformanceStats getStats() {
    final now = DateTime.now();
    final last24Hours = now.subtract(const Duration(hours: 24));

    final recentEvents = _events
        .where((event) => event.timestamp.isAfter(last24Hours))
        .toList();

    final apiCalls = recentEvents
        .where(
          (e) =>
              e.type == PerformanceEventType.apiSuccess ||
              e.type == PerformanceEventType.apiError,
        )
        .toList();

    final pageLoads = recentEvents
        .where((e) => e.type == PerformanceEventType.pageLoad)
        .toList();

    return PerformanceStats(
      totalEvents: _events.length,
      recentEvents: recentEvents.length,
      totalApiCalls: apiCalls.length,
      successfulApiCalls: apiCalls
          .where((e) => e.type == PerformanceEventType.apiSuccess)
          .length,
      failedApiCalls: apiCalls
          .where((e) => e.type == PerformanceEventType.apiError)
          .length,
      totalPageLoads: pageLoads.length,
      averagePageLoadTime: pageLoads.isNotEmpty
          ? Duration(
              milliseconds:
                  pageLoads
                      .where((e) => e.duration != null)
                      .map((e) => e.duration!.inMilliseconds)
                      .fold(0, (a, b) => a + b) ~/
                  pageLoads.where((e) => e.duration != null).length,
            )
          : Duration.zero,
      averageApiResponseTime: apiCalls.isNotEmpty
          ? Duration(
              milliseconds:
                  apiCalls
                      .where((e) => e.duration != null)
                      .map((e) => e.duration!.inMilliseconds)
                      .fold(0, (a, b) => a + b) ~/
                  apiCalls.where((e) => e.duration != null).length,
            )
          : Duration.zero,
    );
  }

  /// مسح الأحداث القديمة
  void clearOldEvents({Duration maxAge = const Duration(days: 7)}) {
    final cutoff = DateTime.now().subtract(maxAge);
    _events.removeWhere((event) => event.timestamp.isBefore(cutoff));

    UnifiedLogger.debug('Cleared old events before $cutoff');
  }

  /// مسح جميع البيانات
  void clear() {
    _activeTimers.clear();
    _metrics.clear();
    _events.clear();

    UnifiedLogger.debug('Cleared all performance data');
  }

  /// إنهاء المراقب
  void dispose() {
    _activeTimers.clear();
    _metrics.clear();
    _events.clear();
    ResourceManager.closeController(_eventController);
    UnifiedLogger.info('Performance monitor disposed');
  }


}

/// مقياس الأداء
class PerformanceMetric {
  const PerformanceMetric({
    required this.name,
    required this.duration,
    required this.timestamp,
    this.description,
  });
  final String name;
  final Duration duration;
  final DateTime timestamp;
  final String? description;

  @override
  String toString() => 'PerformanceMetric($name: ${duration.inMilliseconds}ms)';
}

/// حدث الأداء
class PerformanceEvent {
  const PerformanceEvent({
    required this.name,
    required this.type,
    required this.timestamp,
    this.duration,
    this.description,
    this.metadata,
  });
  final String name;
  final PerformanceEventType type;
  final DateTime timestamp;
  final Duration? duration;
  final String? description;
  final Map<String, dynamic>? metadata;

  @override
  String toString() =>
      'PerformanceEvent($name: $type ${duration != null ? '${duration!.inMilliseconds}ms' : ''})';
}

/// أنواع أحداث الأداء
enum PerformanceEventType {
  timerStart,
  timerEnd,
  pageLoad,
  apiSuccess,
  apiError,
  memoryUsage,
  userAction,
  custom,
}

/// إحصائيات الأداء
class PerformanceStats {
  const PerformanceStats({
    required this.totalEvents,
    required this.recentEvents,
    required this.totalApiCalls,
    required this.successfulApiCalls,
    required this.failedApiCalls,
    required this.totalPageLoads,
    required this.averagePageLoadTime,
    required this.averageApiResponseTime,
  });
  final int totalEvents;
  final int recentEvents;
  final int totalApiCalls;
  final int successfulApiCalls;
  final int failedApiCalls;
  final int totalPageLoads;
  final Duration averagePageLoadTime;
  final Duration averageApiResponseTime;

  double get apiSuccessRate =>
      totalApiCalls > 0 ? successfulApiCalls / totalApiCalls : 0;
  double get apiFailureRate =>
      totalApiCalls > 0 ? failedApiCalls / totalApiCalls : 0;

  @override
  String toString() =>
      '''PerformanceStats:
  Total Events: $totalEvents
  Recent Events (24h): $recentEvents
  API Calls: $totalApiCalls (Success: $successfulApiCalls, Failed: $failedApiCalls)
  API Success Rate: ${(apiSuccessRate * 100).toStringAsFixed(1)}%
  Page Loads: $totalPageLoads
  Avg Page Load Time: ${averagePageLoadTime.inMilliseconds}ms
  Avg API Response Time: ${averageApiResponseTime.inMilliseconds}ms''';
}

/// Widget wrapper لقياس أداء التحميل
class PerformanceWidget extends StatefulWidget {
  const PerformanceWidget({
    required this.child,
    required this.name,
    super.key,
    this.description,
  });
  final Widget child;
  final String name;
  final String? description;

  @override
  State<PerformanceWidget> createState() => _PerformanceWidgetState();
}

class _PerformanceWidgetState extends State<PerformanceWidget> {
  @override
  void initState() {
    super.initState();
    PerformanceMonitor.instance.startTimer(
      'widget_${widget.name}',
      description: widget.description ?? 'Widget build time',
    );
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // قياس وقت إعداد الـ dependencies
    SchedulerBinding.instance.addPostFrameCallback((_) {
      PerformanceMonitor.instance.stopTimer('widget_${widget.name}');
    });
  }

  @override
  Widget build(BuildContext context) => widget.child;
}
