import 'dart:ui' as ui;
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../config/backend_config.dart';

/// Service for optimizing images and assets for better performance
class ImageOptimizationService {
  static const int _maxImageWidth = 1200;
  static const int _maxImageHeight = 800;
  static const int _thumbnailSize = 300;
  static const int _compressionQuality = 85;

  /// Optimize image for display
  static Future<Uint8List?> optimizeImage(
    Uint8List imageBytes, {
    int? maxWidth,
    int? maxHeight,
    int quality = _compressionQuality,
  }) async {
    try {
      final ui.Codec codec = await ui.instantiateImageCodec(
        imageBytes,
        targetWidth: maxWidth ?? _maxImageWidth,
        targetHeight: maxHeight ?? _maxImageHeight,
      );

      final ui.FrameInfo frameInfo = await codec.getNextFrame();
      final ui.Image image = frameInfo.image;

      final ByteData? byteData = await image.toByteData(
        format: ui.ImageByteFormat.png,
      );

      image.dispose();
      codec.dispose();

      return byteData?.buffer.asUint8List();
    } catch (e) {
      debugPrint('Error optimizing image: $e');
      return null;
    }
  }

  /// Generate thumbnail from image
  static Future<Uint8List?> generateThumbnail(
    Uint8List imageBytes, {
    int size = _thumbnailSize,
  }) async {
    return optimizeImage(imageBytes, maxWidth: size, maxHeight: size);
  }

  /// Get optimized image URL from backend
  static String getOptimizedImageUrl(
    String originalUrl, {
    int? width,
    int? height,
    int quality = _compressionQuality,
    bool thumbnail = false,
  }) {
    if (originalUrl.isEmpty) return originalUrl;

    // If it's already an optimized URL, return as is
    if (originalUrl.contains('/optimized/')) {
      return originalUrl;
    }

    final baseUrl = BackendConfig.baseUrl;
    final params = <String, String>{};

    if (width != null) params['w'] = width.toString();
    if (height != null) params['h'] = height.toString();
    params['q'] = quality.toString();
    if (thumbnail) params['thumbnail'] = 'true';

    final queryString = params.entries
        .map((e) => '${e.key}=${e.value}')
        .join('&');

    // Encode the original URL
    final encodedUrl = Uri.encodeComponent(originalUrl);

    return '$baseUrl/api/v1/images/optimize?url=$encodedUrl&$queryString';
  }

  /// Preload critical images
  static Future<void> preloadImages(
    List<String> imageUrls,
    BuildContext context,
  ) async {
    final futures = imageUrls.map((url) async {
      try {
        await precacheImage(CachedNetworkImageProvider(url), context);
      } catch (e) {
        debugPrint('Failed to preload image $url: $e');
      }
    });

    await Future.wait(futures);
  }

  /// Clear image cache
  static Future<void> clearImageCache() async {
    await CachedNetworkImage.evictFromCache('');
    // Clear memory cache
    PaintingBinding.instance.imageCache.clear();
    PaintingBinding.instance.imageCache.clearLiveImages();
  }

  /// Get cache size information
  static Future<Map<String, dynamic>> getCacheInfo() async {
    final imageCache = PaintingBinding.instance.imageCache;
    return {
      'memory_cache_count': imageCache.currentSize,
      'memory_cache_size_bytes': imageCache.currentSizeBytes,
      'live_image_count': imageCache.liveImageCount,
    };
  }
}

/// Provider for image optimization service
final imageOptimizationServiceProvider = Provider<ImageOptimizationService>(
  (ref) => ImageOptimizationService(),
);

/// Image cache configuration
class ImageCacheConfig {
  static void configure() {
    // Configure memory cache
    final imageCache = PaintingBinding.instance.imageCache;
    imageCache.maximumSize = 100; // Maximum number of images
    imageCache.maximumSizeBytes = 50 * 1024 * 1024; // 50MB
  }
}

/// Image loading states
enum ImageLoadingState { loading, loaded, error, cached }

/// Image metadata for optimization
class ImageMetadata {
  final String url;
  final int? width;
  final int? height;
  final int quality;
  final bool thumbnail;
  final DateTime lastModified;

  const ImageMetadata({
    required this.url,
    this.width,
    this.height,
    this.quality = 85,
    this.thumbnail = false,
    required this.lastModified,
  });

  Map<String, dynamic> toJson() => {
    'url': url,
    'width': width,
    'height': height,
    'quality': quality,
    'thumbnail': thumbnail,
    'last_modified': lastModified.toIso8601String(),
  };

  factory ImageMetadata.fromJson(Map<String, dynamic> json) => ImageMetadata(
    url: json['url'],
    width: json['width'],
    height: json['height'],
    quality: json['quality'] ?? 85,
    thumbnail: json['thumbnail'] ?? false,
    lastModified: DateTime.parse(json['last_modified']),
  );
}

/// Image optimization statistics
class ImageOptimizationStats {
  final int totalImages;
  final int optimizedImages;
  final int cacheHits;
  final int cacheMisses;
  final double averageLoadTime;
  final int totalBytesLoaded;
  final int totalBytesSaved;

  const ImageOptimizationStats({
    required this.totalImages,
    required this.optimizedImages,
    required this.cacheHits,
    required this.cacheMisses,
    required this.averageLoadTime,
    required this.totalBytesLoaded,
    required this.totalBytesSaved,
  });

  double get cacheHitRate => totalImages > 0 ? cacheHits / totalImages : 0.0;

  double get optimizationRate =>
      totalImages > 0 ? optimizedImages / totalImages : 0.0;

  double get compressionRatio =>
      totalBytesLoaded > 0 ? totalBytesSaved / totalBytesLoaded : 0.0;

  Map<String, dynamic> toJson() => {
    'total_images': totalImages,
    'optimized_images': optimizedImages,
    'cache_hits': cacheHits,
    'cache_misses': cacheMisses,
    'average_load_time': averageLoadTime,
    'total_bytes_loaded': totalBytesLoaded,
    'total_bytes_saved': totalBytesSaved,
    'cache_hit_rate': cacheHitRate,
    'optimization_rate': optimizationRate,
    'compression_ratio': compressionRatio,
  };
}
