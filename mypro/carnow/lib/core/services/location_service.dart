import 'package:carnow/core/models/city_model.dart';
import 'package:flutter/foundation.dart';
import '../networking/simple_api_client.dart';

class LocationService {
  LocationService(this._apiClient);
  final SimpleApiClient _apiClient;

  Future<List<City>> getCities() async {
    try {
      final response = await _apiClient.get('/api/v1/cities');

      if (response.isSuccess && response.data != null) {
        // Handle null or non-list responses gracefully
        if (response.data is! List) {
          debugPrint('Warning: API returned non-list data for cities: ${response.data.runtimeType}');
          return [];
        }
        
        // The response is a List<Map<String, dynamic>>
        final cities = (response.data as List<dynamic>)
            .map((json) => City.fromJson(json as Map<String, dynamic>))
            .toList();

        // Sort cities alphabetically by Arabic name
        cities.sort((a, b) => a.nameArabic.compareTo(b.nameArabic));

        return cities;
      } else {
        debugPrint('Error fetching cities: ${response.error}');
        throw Exception('Failed to fetch cities: ${response.error}');
      }
    } catch (e) {
      debugPrint('An unexpected error occurred: $e');
      rethrow;
    }
  }
}
