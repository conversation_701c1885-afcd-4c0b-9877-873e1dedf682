import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:logging/logging.dart';

import '../errors/app_error.dart';
import '../errors/result.dart';

part 'graceful_degradation_service.g.dart';

final _logger = Logger('GracefulDegradationService');

/// Simple Graceful Degradation Service - Forever Plan Compliant
///
/// Features (Forever Plan Approved):
/// - Basic fallback mechanisms when services are unavailable
/// - Simple connectivity checking
/// - Feature availability based on network status
/// - User-friendly error messages
/// 
/// What this service DOES NOT do (Forever Plan Compliance):
/// ❌ Complex offline queueing
/// ❌ Persistent data caching
/// ❌ Automatic retry mechanisms
/// ❌ Sync between databases
@riverpod
GracefulDegradationService gracefulDegradationService(
  GracefulDegradationServiceRef ref,
) {
  return GracefulDegradationService(ref: ref);
}

class GracefulDegradationService {
  GracefulDegradationService({required this.ref});

  final Ref ref;
  final Connectivity _connectivity = Connectivity();

  /// Execute operation with simple graceful degradation (Forever Plan Compliant)
  Future<Result<T>> executeWithGracefulDegradation<T>({
    required Future<T> Function() primaryOperation,
    required String operationName,
    Future<T> Function()? fallbackOperation,
    T? defaultValue,
  }) async {
    try {
      // Try primary operation first
      _logger.info('Attempting primary operation: $operationName');
      final result = await primaryOperation();
      return Result.success(result);
    } catch (primaryError) {
      _logger.warning(
        'Primary operation failed for $operationName: $primaryError',
      );

      // Try fallback operation if available
      if (fallbackOperation != null) {
        try {
          _logger.info('Attempting fallback operation: $operationName');
          final fallbackResult = await fallbackOperation();
          return Result.success(fallbackResult);
        } catch (fallbackError) {
          _logger.warning(
            'Fallback operation failed for $operationName: $fallbackError',
          );
        }
      }

      // Use default value if available
      if (defaultValue != null) {
        _logger.info('Using default value for $operationName');
        return Result.success(defaultValue);
      }

      // All fallbacks failed, return error
      final appError = primaryError is AppError
          ? primaryError
          : AppError.unexpected(
              message: 'Operation failed: $operationName',
              originalError: primaryError,
            );

      return Result.failure(appError);
    }
  }

  /// Get degraded functionality based on current conditions (Simple)
  Future<DegradationLevel> getDegradationLevel() async {
    try {
      final connectivityResults = await _connectivity.checkConnectivity();
      final isOnline = connectivityResults.isNotEmpty && 
          connectivityResults.first != ConnectivityResult.none;
      
      return isOnline ? DegradationLevel.normal : DegradationLevel.offline;
    } catch (e) {
      _logger.warning('Failed to check connectivity: $e');
      return DegradationLevel.offline;
    }
  }

  /// Check if a feature should be available in current degradation level (Simple)
  Future<bool> isFeatureAvailable(String featureName) async {
    final level = await getDegradationLevel();

    switch (level) {
      case DegradationLevel.normal:
        return true;
      case DegradationLevel.offline:
        return _isFeatureAvailableOffline(featureName);
    }
  }

  /// Get user-friendly message for current degradation level (Simple)
  Future<String> getDegradationMessage() async {
    final level = await getDegradationLevel();

    switch (level) {
      case DegradationLevel.normal:
        return 'جميع الميزات متاحة';
      case DegradationLevel.offline:
        return 'أنت غير متصل بالإنترنت - يرجى التحقق من اتصالك';
    }
  }

  /// Check if feature is available offline
  bool _isFeatureAvailableOffline(String featureName) {
    // Define which features are available offline
    const offlineFeatures = {
      'view_cached_products',
      'view_profile',
      'view_cached_orders',
      'settings',
    };

    return offlineFeatures.contains(featureName);
  }
}

/// Simple levels of service degradation (Forever Plan Compliant)
enum DegradationLevel {
  normal, // All features available
  offline, // No internet connection
}

/// Feature availability checker
class FeatureAvailability {
  const FeatureAvailability({
    required this.isAvailable,
    required this.reason,
    this.alternativeAction,
  });

  final bool isAvailable;
  final String reason;
  final String? alternativeAction;

  static const available = FeatureAvailability(
    isAvailable: true,
    reason: 'Feature is available',
  );

  static const unavailableOffline = FeatureAvailability(
    isAvailable: false,
    reason: 'هذه الميزة غير متاحة في وضع عدم الاتصال',
    alternativeAction: 'يرجى الاتصال بالإنترنت لاستخدام هذه الميزة',
  );


}

/// Extension for easy feature checking
extension FeatureCheckExtension on Ref {
  /// Check if a feature is available (Simple)
  Future<FeatureAvailability> checkFeatureAvailability(String featureName) async {
    final service = read(gracefulDegradationServiceProvider);
    final isAvailable = await service.isFeatureAvailable(featureName);

    if (isAvailable) {
      return FeatureAvailability.available;
    } else {
      return FeatureAvailability.unavailableOffline;
    }
  }

  /// Execute with simple graceful degradation (Forever Plan Compliant)
  Future<Result<T>> executeWithGracefulDegradation<T>({
    required Future<T> Function() primaryOperation,
    required String operationName,
    Future<T> Function()? fallbackOperation,
    T? defaultValue,
  }) {
    final service = read(gracefulDegradationServiceProvider);
    return service.executeWithGracefulDegradation(
      primaryOperation: primaryOperation,
      operationName: operationName,
      fallbackOperation: fallbackOperation,
      defaultValue: defaultValue,
    );
  }
}
