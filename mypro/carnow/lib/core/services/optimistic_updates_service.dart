import 'dart:async';
import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../networking/simple_api_client.dart';
import '../models/api_response.dart';
import 'websocket_service.dart';

part 'optimistic_updates_service.g.dart';

/// Optimistic update operation
class OptimisticUpdate {
  final String id;
  final String entityType;
  final String entityId;
  final String operation; // create, update, delete
  final Map<String, dynamic> originalData;
  final Map<String, dynamic> optimisticData;
  final DateTime timestamp;
  final int clientVersion;
  final bool isApplied;
  final String? error;

  const OptimisticUpdate({
    required this.id,
    required this.entityType,
    required this.entityId,
    required this.operation,
    required this.originalData,
    required this.optimisticData,
    required this.timestamp,
    required this.clientVersion,
    this.isApplied = false,
    this.error,
  });

  OptimisticUpdate copyWith({
    String? id,
    String? entityType,
    String? entityId,
    String? operation,
    Map<String, dynamic>? originalData,
    Map<String, dynamic>? optimisticData,
    DateTime? timestamp,
    int? clientVersion,
    bool? isApplied,
    String? error,
  }) {
    return OptimisticUpdate(
      id: id ?? this.id,
      entityType: entityType ?? this.entityType,
      entityId: entityId ?? this.entityId,
      operation: operation ?? this.operation,
      originalData: originalData ?? this.originalData,
      optimisticData: optimisticData ?? this.optimisticData,
      timestamp: timestamp ?? this.timestamp,
      clientVersion: clientVersion ?? this.clientVersion,
      isApplied: isApplied ?? this.isApplied,
      error: error ?? this.error,
    );
  }
}

/// Conflict resolution result
class ConflictResolution {
  final String entityId;
  final String entityType;
  final int clientVersion;
  final int serverVersion;
  final String conflictType;
  final String resolution; // server_wins, client_wins, merge
  final Map<String, dynamic>? mergedData;
  final DateTime timestamp;

  const ConflictResolution({
    required this.entityId,
    required this.entityType,
    required this.clientVersion,
    required this.serverVersion,
    required this.conflictType,
    required this.resolution,
    this.mergedData,
    required this.timestamp,
  });

  factory ConflictResolution.fromJson(Map<String, dynamic> json) {
    return ConflictResolution(
      entityId: json['entity_id'] as String,
      entityType: json['entity_type'] as String,
      clientVersion: json['client_version'] as int,
      serverVersion: json['server_version'] as int,
      conflictType: json['conflict_type'] as String,
      resolution: json['resolution'] as String,
      mergedData: json['merged_data'] as Map<String, dynamic>?,
      timestamp: DateTime.parse(json['timestamp'] as String),
    );
  }
}

/// Optimistic updates service for handling client-side optimistic updates
class OptimisticUpdatesService {
  final Ref _ref;
  final Map<String, OptimisticUpdate> _pendingUpdates = {};
  final Map<String, Map<String, dynamic>> _entityVersions = {};
  final StreamController<OptimisticUpdate> _updateController =
      StreamController.broadcast();
  final StreamController<ConflictResolution> _conflictController =
      StreamController.broadcast();

  OptimisticUpdatesService(this._ref) {
    _listenToWebSocketUpdates();
  }

  // Getters for streams
  Stream<OptimisticUpdate> get updateStream => _updateController.stream;
  Stream<ConflictResolution> get conflictStream => _conflictController.stream;

  /// Apply optimistic update
  Future<void> applyOptimisticUpdate({
    required String entityType,
    required String entityId,
    required String operation,
    required Map<String, dynamic> originalData,
    required Map<String, dynamic> optimisticData,
  }) async {
    final updateId =
        '${entityType}_${entityId}_${DateTime.now().millisecondsSinceEpoch}';
    final clientVersion = _getNextClientVersion(entityType, entityId);

    final update = OptimisticUpdate(
      id: updateId,
      entityType: entityType,
      entityId: entityId,
      operation: operation,
      originalData: originalData,
      optimisticData: optimisticData,
      timestamp: DateTime.now(),
      clientVersion: clientVersion,
      isApplied: true,
    );

    // Store pending update
    _pendingUpdates[updateId] = update;

    // Emit update for UI to react
    _updateController.add(update);

    debugPrint(
      '✅ Applied optimistic update: $entityType/$entityId ($operation)',
    );

    // Send to server for confirmation
    _sendOptimisticUpdateToServer(update);
  }

  /// Send optimistic update to server
  Future<void> _sendOptimisticUpdateToServer(OptimisticUpdate update) async {
    try {
      final apiClient = _ref.read(simpleApiClientProvider);

      final requestData = {
        'entity_type': update.entityType,
        'entity_id': update.entityId,
        'client_version': update.clientVersion,
        'update_data': update.optimisticData,
        'operation': update.operation,
      };

      final response = await apiClient.post<Map<String, dynamic>>(
        '/api/v1/optimistic-updates',
        data: requestData,
      );

      response.when(
        success: (data) {
          _handleServerConfirmation(update.id, data);
        },
        error: (error) {
          _handleServerError(update.id, error);
        },
      );
    } catch (e) {
      debugPrint('❌ Failed to send optimistic update to server: $e');
      _handleServerError(update.id, e.toString());
    }
  }

  /// Handle server confirmation of optimistic update
  void _handleServerConfirmation(
    String updateId,
    Map<String, dynamic> serverResponse,
  ) {
    final update = _pendingUpdates[updateId];
    if (update == null) return;

    // Check if there's a conflict
    if (serverResponse.containsKey('conflict')) {
      final conflictData = serverResponse['conflict'] as Map<String, dynamic>;
      final conflict = ConflictResolution.fromJson(conflictData);

      debugPrint('⚠️ Conflict detected for update $updateId');
      _handleConflict(update, conflict);
    } else {
      // Update confirmed, remove from pending
      _pendingUpdates.remove(updateId);

      // Update entity version
      final serverVersion = serverResponse['version'] as int?;
      if (serverVersion != null) {
        _updateEntityVersion(update.entityType, update.entityId, serverVersion);
      }

      debugPrint('✅ Optimistic update confirmed: $updateId');
    }
  }

  /// Handle server error for optimistic update
  void _handleServerError(String updateId, String error) {
    final update = _pendingUpdates[updateId];
    if (update == null) return;

    // Mark update as failed
    final failedUpdate = update.copyWith(error: error);
    _pendingUpdates[updateId] = failedUpdate;

    // Emit failed update for UI to handle
    _updateController.add(failedUpdate);

    debugPrint('❌ Optimistic update failed: $updateId - $error');

    // Schedule retry or rollback
    _scheduleRetryOrRollback(failedUpdate);
  }

  /// Handle conflict resolution
  void _handleConflict(OptimisticUpdate update, ConflictResolution conflict) {
    // Emit conflict for UI to handle
    _conflictController.add(conflict);

    // Apply resolution based on strategy
    switch (conflict.resolution) {
      case 'server_wins':
        _rollbackOptimisticUpdate(update.id);
        if (conflict.mergedData != null) {
          _applyServerData(
            update.entityType,
            update.entityId,
            conflict.mergedData!,
          );
        }
        break;

      case 'client_wins':
        // Keep optimistic update, server will be updated
        _pendingUpdates.remove(update.id);
        break;

      case 'merge':
        _rollbackOptimisticUpdate(update.id);
        if (conflict.mergedData != null) {
          _applyServerData(
            update.entityType,
            update.entityId,
            conflict.mergedData!,
          );
        }
        break;
    }

    // Update entity version
    _updateEntityVersion(
      update.entityType,
      update.entityId,
      conflict.serverVersion,
    );

    debugPrint(
      '✅ Conflict resolved: ${conflict.resolution} for ${update.entityType}/${update.entityId}',
    );
  }

  /// Rollback optimistic update
  void _rollbackOptimisticUpdate(String updateId) {
    final update = _pendingUpdates[updateId];
    if (update == null) return;

    // Create rollback update
    final rollbackUpdate = OptimisticUpdate(
      id: '${updateId}_rollback',
      entityType: update.entityType,
      entityId: update.entityId,
      operation: 'rollback',
      originalData: update.optimisticData,
      optimisticData: update.originalData,
      timestamp: DateTime.now(),
      clientVersion: update.clientVersion,
      isApplied: true,
    );

    // Remove original update
    _pendingUpdates.remove(updateId);

    // Emit rollback update
    _updateController.add(rollbackUpdate);

    debugPrint('🔄 Rolled back optimistic update: $updateId');
  }

  /// Apply server data after conflict resolution
  void _applyServerData(
    String entityType,
    String entityId,
    Map<String, dynamic> serverData,
  ) {
    final serverUpdate = OptimisticUpdate(
      id: '${entityType}_${entityId}_server_${DateTime.now().millisecondsSinceEpoch}',
      entityType: entityType,
      entityId: entityId,
      operation: 'server_update',
      originalData: {},
      optimisticData: serverData,
      timestamp: DateTime.now(),
      clientVersion: 0, // Server version
      isApplied: true,
    );

    _updateController.add(serverUpdate);

    debugPrint('✅ Applied server data: $entityType/$entityId');
  }

  /// Schedule retry or rollback for failed update
  void _scheduleRetryOrRollback(OptimisticUpdate failedUpdate) {
    // For now, just rollback failed updates
    // In a more sophisticated implementation, you could implement retry logic
    Timer(const Duration(seconds: 5), () {
      _rollbackOptimisticUpdate(failedUpdate.id);
    });
  }

  /// Get next client version for entity
  int _getNextClientVersion(String entityType, String entityId) {
    final key = '${entityType}_$entityId';
    final currentVersion = _entityVersions[key]?['client_version'] as int? ?? 0;
    final nextVersion = currentVersion + 1;

    _entityVersions[key] = {
      'client_version': nextVersion,
      'server_version': _entityVersions[key]?['server_version'] ?? 0,
    };

    return nextVersion;
  }

  /// Update entity version from server
  void _updateEntityVersion(
    String entityType,
    String entityId,
    int serverVersion,
  ) {
    final key = '${entityType}_$entityId';
    _entityVersions[key] = {
      'client_version': _entityVersions[key]?['client_version'] ?? 0,
      'server_version': serverVersion,
    };
  }

  /// Listen to WebSocket updates for real-time synchronization
  void _listenToWebSocketUpdates() {
    final webSocketService = _ref.read(webSocketServiceProvider);

    // Listen to product updates
    webSocketService.productUpdatesStream.listen((data) {
      _handleWebSocketUpdate('product', data);
    });

    // Listen to order updates
    webSocketService.orderUpdatesStream.listen((data) {
      _handleWebSocketUpdate('order', data);
    });

    // Listen to wallet updates
    webSocketService.walletUpdatesStream.listen((data) {
      _handleWebSocketUpdate('wallet', data);
    });
  }

  /// Handle WebSocket updates for real-time synchronization
  void _handleWebSocketUpdate(String entityType, Map<String, dynamic> data) {
    final entityId = data['id'] as String?;
    if (entityId == null) return;

    // Check if we have pending updates for this entity
    final pendingUpdates = _pendingUpdates.values
        .where(
          (update) =>
              update.entityType == entityType && update.entityId == entityId,
        )
        .toList();

    if (pendingUpdates.isNotEmpty) {
      // We have pending updates, check for conflicts
      for (final pendingUpdate in pendingUpdates) {
        _checkForConflictWithWebSocketUpdate(pendingUpdate, data);
      }
    } else {
      // No pending updates, apply server update directly
      final serverUpdate = OptimisticUpdate(
        id: '${entityType}_${entityId}_ws_${DateTime.now().millisecondsSinceEpoch}',
        entityType: entityType,
        entityId: entityId,
        operation: 'websocket_update',
        originalData: {},
        optimisticData: data,
        timestamp: DateTime.now(),
        clientVersion: 0, // Server version
        isApplied: true,
      );

      _updateController.add(serverUpdate);
    }
  }

  /// Check for conflicts with WebSocket updates
  void _checkForConflictWithWebSocketUpdate(
    OptimisticUpdate pendingUpdate,
    Map<String, dynamic> serverData,
  ) {
    // Simple conflict detection based on data differences
    bool hasConflict = false;

    for (final key in pendingUpdate.optimisticData.keys) {
      if (serverData.containsKey(key)) {
        final clientValue = pendingUpdate.optimisticData[key];
        final serverValue = serverData[key];

        if (clientValue != serverValue) {
          hasConflict = true;
          break;
        }
      }
    }

    if (hasConflict) {
      // Create conflict resolution
      final conflict = ConflictResolution(
        entityId: pendingUpdate.entityId,
        entityType: pendingUpdate.entityType,
        clientVersion: pendingUpdate.clientVersion,
        serverVersion:
            DateTime.now().millisecondsSinceEpoch, // Use timestamp as version
        conflictType: 'websocket_conflict',
        resolution: 'server_wins', // Default resolution
        mergedData: serverData,
        timestamp: DateTime.now(),
      );

      _handleConflict(pendingUpdate, conflict);
    }
  }

  /// Get pending updates for an entity
  List<OptimisticUpdate> getPendingUpdates(String entityType, String entityId) {
    return _pendingUpdates.values
        .where(
          (update) =>
              update.entityType == entityType && update.entityId == entityId,
        )
        .toList();
  }

  /// Check if entity has pending updates
  bool hasPendingUpdates(String entityType, String entityId) {
    return _pendingUpdates.values.any(
      (update) =>
          update.entityType == entityType && update.entityId == entityId,
    );
  }

  /// Get merged data for entity (original + optimistic updates)
  Map<String, dynamic> getMergedEntityData(
    String entityType,
    String entityId,
    Map<String, dynamic> originalData,
  ) {
    final pendingUpdates = getPendingUpdates(entityType, entityId);

    if (pendingUpdates.isEmpty) {
      return originalData;
    }

    // Apply optimistic updates in chronological order
    Map<String, dynamic> mergedData = Map.from(originalData);

    for (final update
        in pendingUpdates..sort((a, b) => a.timestamp.compareTo(b.timestamp))) {
      if (update.isApplied && update.error == null) {
        mergedData.addAll(update.optimisticData);
      }
    }

    return mergedData;
  }

  /// Clear all pending updates (useful for logout or reset)
  void clearPendingUpdates() {
    _pendingUpdates.clear();
    _entityVersions.clear();
    debugPrint('🗑️ Cleared all pending optimistic updates');
  }

  /// Dispose resources
  void dispose() {
    _updateController.close();
    _conflictController.close();
    clearPendingUpdates();
  }
}

/// Optimistic updates service provider
@riverpod
OptimisticUpdatesService optimisticUpdatesService(Ref ref) {
  final service = OptimisticUpdatesService(ref);

  ref.onDispose(() {
    service.dispose();
  });

  return service;
}

/// Pending updates stream provider
@riverpod
Stream<OptimisticUpdate> optimisticUpdatesStream(Ref ref) {
  final service = ref.watch(optimisticUpdatesServiceProvider);
  return service.updateStream;
}

/// Conflicts stream provider
@riverpod
Stream<ConflictResolution> conflictsStream(Ref ref) {
  final service = ref.watch(optimisticUpdatesServiceProvider);
  return service.conflictStream;
}
