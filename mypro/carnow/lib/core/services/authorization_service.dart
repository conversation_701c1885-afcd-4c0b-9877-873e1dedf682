import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../networking/simple_api_client.dart';
import '../../../core/auth/unified_auth_provider.dart';

part 'authorization_service.g.dart';

@riverpod
class AuthorizationService extends _$AuthorizationService {
  @override
  FutureOr<bool> build(String resource, String action) async {
    final currentUser = ref.read(currentUserProvider);
    final userId = currentUser?.id;

    if (userId == null) return false;

    // التحقق من الصلاحيات حسب المورد والإجراء
    switch (resource) {
      case 'vehicles':
        return _checkVehiclePermission(userId, action);
      case 'parts':
        return _checkPartPermission(userId, action);
      case 'bids':
        return _checkBidPermission(userId, action);
      case 'cart':
        return _checkCartPermission(userId, action);
      case 'orders':
        return _checkOrderPermission(userId, action);
      default:
        return false;
    }
  }

  Future<bool> _checkVehiclePermission(String userId, String action) async {
    // المستخدمون يمكنهم إدارة مركباتهم الخاصة
    return true;
  }

  Future<bool> _checkPartPermission(String userId, String action) async {
    final apiClient = ref.read(simpleApiClientProvider);

    // التحقق مما إذا كان المستخدم بائعاً
    try {
      final response = await apiClient.get('/users/$userId/seller-profile');
      final sellerProfile = response.data;

      switch (action) {
        case 'create':
        case 'update':
        case 'delete':
          return sellerProfile != null && sellerProfile['is_approved'] == true;
        case 'read':
          return true;
        default:
          return false;
      }
    } catch (e) {
      // If seller profile doesn't exist, user is not a seller
      switch (action) {
        case 'read':
          return true;
        default:
          return false;
      }
    }
  }

  Future<bool> _checkBidPermission(String userId, String action) async {
    // المستخدمون يمكنهم تقديم عروض وإدارة عروضهم الخاصة
    return true;
  }

  Future<bool> _checkCartPermission(String userId, String action) async {
    // المستخدمون يمكنهم إدارة سلة التسوق الخاصة بهم
    return true;
  }

  Future<bool> _checkOrderPermission(String userId, String action) async {
    // المستخدمون يمكنهم عرض طلباتهم الخاصة
    // البائعون يمكنهم عرض وتحديث الطلبات التي تخصهم
    return true;
  }
}
