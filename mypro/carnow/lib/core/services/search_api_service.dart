import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logging/logging.dart';

import '../networking/simple_api_client.dart';
import '../errors/app_error.dart';

part 'search_api_service.g.dart';

final _logger = Logger('SearchApiService');

/// Simple Search API Service following Forever Plan Architecture
/// Flutter (UI Only) → Go API → Supabase (Data Only)
@riverpod
SearchApiService searchApiService(Ref ref) {
  final apiClient = ref.watch(simpleApiClientProvider);
  return SearchApiService(apiClient);
}

class SearchApiService {
  const SearchApiService(this._apiClient);
  
  final SimpleApiClient _apiClient;

  /// Get search suggestions
  Future<List<String>> getSearchSuggestions(String query) async {
    try {
      _logger.info('Fetching search suggestions for: $query');
      final response = await _apiClient.get<List<dynamic>>('/search/suggestions?q=$query');
      
      if (!response.isSuccess || response.data == null) {
        _logger.warning('Failed to fetch search suggestions: ${response.error}');
        throw const AppError.network(
          message: 'Failed to fetch search suggestions',
          code: 'search_suggestions_fetch_failed',
        );
      }
      
      return List<String>.from(response.data!);
    } catch (e) {
      _logger.severe('Error fetching search suggestions: $e');
      throw AppError.network(
        message: 'Error fetching search suggestions',
        code: 'search_suggestions_fetch_error',
        originalError: e,
      );
    }
  }

  /// Search products
  Future<List<Map<String, dynamic>>> searchProducts(String query) async {
    try {
      _logger.info('Searching products for: $query');
      final response = await _apiClient.get<List<dynamic>>('/search/products?q=$query');
      
      if (!response.isSuccess || response.data == null) {
        _logger.warning('Failed to search products: ${response.error}');
        throw const AppError.network(
          message: 'Failed to search products',
          code: 'product_search_failed',
        );
      }
      
      return List<Map<String, dynamic>>.from(response.data!);
    } catch (e) {
      _logger.severe('Error searching products: $e');
      throw AppError.network(
        message: 'Error searching products',
        code: 'product_search_error',
        originalError: e,
      );
    }
  }

  /// Get popular searches
  Future<List<String>> getPopularSearches() async {
    try {
      _logger.info('Fetching popular searches...');
      final response = await _apiClient.get<List<dynamic>>('/search/popular');
      
      if (!response.isSuccess || response.data == null) {
        _logger.warning('Failed to fetch popular searches: ${response.error}');
        throw const AppError.network(
          message: 'Failed to fetch popular searches',
          code: 'popular_searches_fetch_failed',
        );
      }
      
      return List<String>.from(response.data!);
    } catch (e) {
      _logger.severe('Error fetching popular searches: $e');
      throw AppError.network(
        message: 'Error fetching popular searches',
        code: 'popular_searches_fetch_error',
        originalError: e,
      );
    }
  }

  /// Track search query for analytics
  Future<void> trackSearch(String query, {String? userId}) async {
    try {
      _logger.info('Tracking search: $query');
      final payload = {
        'query': query,
        if (userId != null) 'user_id': userId,
        'timestamp': DateTime.now().toIso8601String(),
      };
      
      final response = await _apiClient.post<Map<String, dynamic>>(
        '/search/track',
        data: payload,
      );
      
      if (!response.isSuccess) {
        _logger.warning('Failed to track search: ${response.error}');
        // Don't throw error for analytics tracking failures
      }
    } catch (e) {
      _logger.warning('Error tracking search: $e');
      // Don't throw error for analytics tracking failures
    }
  }
} 