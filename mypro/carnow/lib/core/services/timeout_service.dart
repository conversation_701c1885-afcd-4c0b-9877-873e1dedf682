import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:logging/logging.dart';

import '../errors/app_error.dart';
import '../errors/result.dart';

part 'timeout_service.g.dart';

final _logger = Logger('TimeoutService');

/// Timeout Service for Phase 3.2.1: Retry and Timeout Logic
///
/// Features:
/// - Configurable timeout values for different operations
/// - Timeout monitoring and statistics
/// - Graceful timeout handling
/// - Operation cancellation support
@riverpod
TimeoutService timeoutService(TimeoutServiceRef ref) {
  return TimeoutService();
}

class TimeoutService {
  final Map<String, Timer> _activeTimers = {};
  final Map<String, Completer<void>> _activeOperations = {};
  final TimeoutStatistics _statistics = TimeoutStatistics();

  /// Execute operation with timeout
  Future<Result<T>> executeWithTimeout<T>(
    Future<T> Function() operation, {
    required String operationName,
    required Duration timeout,
    String? operationId,
    Map<String, dynamic>? operationData,
    void Function()? onTimeout,
  }) async {
    operationId ??= '${operationName}_${DateTime.now().millisecondsSinceEpoch}';

    final completer = Completer<void>();
    _activeOperations[operationId] = completer;

    final startTime = DateTime.now();

    try {
      _logger.info(
        'Starting operation $operationName with timeout ${timeout.inSeconds}s',
      );

      // Set up timeout timer
      final timer = Timer(timeout, () {
        if (!completer.isCompleted) {
          _logger.warning(
            'Operation $operationName timed out after ${timeout.inSeconds}s',
          );

          // Record timeout statistics
          _recordTimeout(
            operationName,
            timeout,
            DateTime.now().difference(startTime),
          );

          // Call timeout callback
          if (onTimeout != null) {
            onTimeout();
          }

          completer.complete();
        }
      });

      _activeTimers[operationId] = timer;

      // Execute operation with timeout
      final result = await Future.any([
        operation(),
        completer.future.then<T>(
          (_) => throw TimeoutException(
            'Operation $operationName timed out',
            timeout,
          ),
        ),
      ]);

      // Operation completed successfully
      final duration = DateTime.now().difference(startTime);
      _recordSuccess(operationName, duration);

      _logger.info(
        'Operation $operationName completed in ${duration.inMilliseconds}ms',
      );

      return Result.success(result);
    } catch (error, stackTrace) {
      final duration = DateTime.now().difference(startTime);

      if (error is TimeoutException) {
        _logger.severe(
          'Operation $operationName timed out after ${duration.inMilliseconds}ms',
        );

        final appError = AppError.timeout(
          message: 'Operation timed out: $operationName',
          code: 'OPERATION_TIMEOUT',
          originalError: error,
          data: {
            if (operationData != null) ...operationData,
            'operation': operationName,
            'timeout_seconds': timeout.inSeconds,
            'actual_duration_ms': duration.inMilliseconds,
          },
        );

        return Result.failure(appError);
      } else {
        _logger.severe('Operation $operationName failed: $error');
        _recordFailure(operationName, duration);

        final appError = AppError.unexpected(
          message: 'Operation failed: $operationName',
          code: 'OPERATION_FAILED',
          originalError: error,
          data: {
            if (operationData != null) ...operationData,
            'operation': operationName,
            'duration_ms': duration.inMilliseconds,
            'stackTrace': stackTrace.toString(),
          },
        );

        return Result.failure(appError);
      }
    } finally {
      // Clean up
      _cleanup(operationId);
    }
  }

  /// Execute multiple operations with individual timeouts
  Future<Result<List<T>>> executeMultipleWithTimeout<T>(
    List<Future<T> Function()> operations, {
    required String operationName,
    required Duration timeout,
    bool failFast = false,
    Map<String, dynamic>? operationData,
  }) async {
    _logger.info(
      'Starting ${operations.length} operations with timeout ${timeout.inSeconds}s each',
    );

    final results = <T>[];
    final errors = <AppError>[];

    for (int i = 0; i < operations.length; i++) {
      final result = await executeWithTimeout<T>(
        operations[i],
        operationName: '${operationName}_$i',
        timeout: timeout,
        operationData: operationData,
      );

      result.when(
        success: (value) => results.add(value),
        failure: (error) {
          errors.add(error);
          if (failFast) {
            return Result.failure(error);
          }
        },
      );
    }

    if (errors.isNotEmpty && failFast) {
      return Result.failure(errors.first);
    } else if (errors.isNotEmpty) {
      return Result.failure(
        AppError.unexpected(
          message: 'Some operations failed: $operationName',
          code: 'PARTIAL_FAILURE',
          data: {
            if (operationData != null) ...operationData,
            'total_operations': operations.length,
            'successful_operations': results.length,
            'failed_operations': errors.length,
            'errors': errors.map((e) => e.message).toList(),
          },
        ),
      );
    }

    return Result.success(results);
  }

  /// Cancel an active operation
  bool cancelOperation(String operationId) {
    final completer = _activeOperations[operationId];
    if (completer != null && !completer.isCompleted) {
      completer.complete();
      _cleanup(operationId);
      _logger.info('Cancelled operation: $operationId');
      return true;
    }
    return false;
  }

  /// Cancel all active operations
  void cancelAllOperations() {
    final operationIds = List<String>.from(_activeOperations.keys);
    for (final operationId in operationIds) {
      cancelOperation(operationId);
    }
    _logger.info('Cancelled all active operations');
  }

  /// Get list of active operations
  List<String> getActiveOperations() {
    return _activeOperations.keys.toList();
  }

  /// Get timeout statistics
  TimeoutStatistics getStatistics() {
    return _statistics;
  }

  /// Clean up resources for an operation
  void _cleanup(String operationId) {
    final timer = _activeTimers.remove(operationId);
    timer?.cancel();

    final completer = _activeOperations.remove(operationId);
    if (completer != null && !completer.isCompleted) {
      completer.complete();
    }
  }

  /// Record successful operation
  void _recordSuccess(String operationName, Duration duration) {
    _statistics._recordSuccess(operationName, duration);
  }

  /// Record failed operation
  void _recordFailure(String operationName, Duration duration) {
    _statistics._recordFailure(operationName, duration);
  }

  /// Record timeout
  void _recordTimeout(
    String operationName,
    Duration timeout,
    Duration actualDuration,
  ) {
    _statistics._recordTimeout(operationName, timeout, actualDuration);
  }

  /// Dispose resources
  void dispose() {
    cancelAllOperations();
    _activeTimers.clear();
    _activeOperations.clear();
  }
}

/// Timeout statistics for monitoring
class TimeoutStatistics {
  int _totalOperations = 0;
  int _successfulOperations = 0;
  int _failedOperations = 0;
  int _timedOutOperations = 0;
  Duration _totalDuration = Duration.zero;
  Duration _totalTimeoutDuration = Duration.zero;
  DateTime _lastOperationTime = DateTime.now();
  final Map<String, int> _operationCounts = {};
  final Map<String, int> _timeoutCounts = {};

  /// Record successful operation
  void _recordSuccess(String operationName, Duration duration) {
    _totalOperations++;
    _successfulOperations++;
    _totalDuration += duration;
    _lastOperationTime = DateTime.now();
    _operationCounts[operationName] =
        (_operationCounts[operationName] ?? 0) + 1;
  }

  /// Record failed operation
  void _recordFailure(String operationName, Duration duration) {
    _totalOperations++;
    _failedOperations++;
    _totalDuration += duration;
    _lastOperationTime = DateTime.now();
    _operationCounts[operationName] =
        (_operationCounts[operationName] ?? 0) + 1;
  }

  /// Record timeout
  void _recordTimeout(
    String operationName,
    Duration timeout,
    Duration actualDuration,
  ) {
    _totalOperations++;
    _timedOutOperations++;
    _totalDuration += actualDuration;
    _totalTimeoutDuration += timeout;
    _lastOperationTime = DateTime.now();
    _operationCounts[operationName] =
        (_operationCounts[operationName] ?? 0) + 1;
    _timeoutCounts[operationName] = (_timeoutCounts[operationName] ?? 0) + 1;
  }

  // Getters
  int get totalOperations => _totalOperations;
  int get successfulOperations => _successfulOperations;
  int get failedOperations => _failedOperations;
  int get timedOutOperations => _timedOutOperations;
  DateTime get lastOperationTime => _lastOperationTime;
  Map<String, int> get operationCounts => Map.unmodifiable(_operationCounts);
  Map<String, int> get timeoutCounts => Map.unmodifiable(_timeoutCounts);

  double get successRate {
    if (_totalOperations == 0) return 0.0;
    return _successfulOperations / _totalOperations;
  }

  double get failureRate {
    if (_totalOperations == 0) return 0.0;
    return _failedOperations / _totalOperations;
  }

  double get timeoutRate {
    if (_totalOperations == 0) return 0.0;
    return _timedOutOperations / _totalOperations;
  }

  Duration get averageDuration {
    if (_totalOperations == 0) return Duration.zero;
    return Duration(
      microseconds: (_totalDuration.inMicroseconds / _totalOperations).round(),
    );
  }

  Duration get averageTimeoutDuration {
    if (_timedOutOperations == 0) return Duration.zero;
    return Duration(
      microseconds: (_totalTimeoutDuration.inMicroseconds / _timedOutOperations)
          .round(),
    );
  }
}

/// Timeout configuration presets
class TimeoutPresets {
  /// Quick operations (UI interactions, local operations)
  static const quick = Duration(seconds: 2);

  /// Standard operations (API calls, file operations)
  static const standard = Duration(seconds: 30);

  /// Long operations (large file uploads, complex calculations)
  static const long = Duration(minutes: 2);

  /// Critical operations (authentication, payment processing)
  static const critical = Duration(seconds: 10);

  /// Background operations (sync, backup)
  static const background = Duration(minutes: 5);

  /// Network operations (API calls, downloads)
  static const network = Duration(seconds: 30);

  /// Database operations
  static const database = Duration(seconds: 10);

  /// File operations
  static const file = Duration(seconds: 60);
}

/// Extension to make timeout service easier to use with Ref
extension TimeoutServiceExtension on Ref {
  /// Execute operation with timeout using the timeout service
  Future<Result<T>> executeWithTimeout<T>(
    Future<T> Function() operation, {
    required String operationName,
    Duration timeout = TimeoutPresets.standard,
    String? operationId,
    Map<String, dynamic>? operationData,
    void Function()? onTimeout,
  }) {
    final timeoutService = read(timeoutServiceProvider);
    return timeoutService.executeWithTimeout<T>(
      operation,
      operationName: operationName,
      timeout: timeout,
      operationId: operationId,
      operationData: operationData,
      onTimeout: onTimeout,
    );
  }

  /// Cancel operation using the timeout service
  bool cancelOperation(String operationId) {
    final timeoutService = read(timeoutServiceProvider);
    return timeoutService.cancelOperation(operationId);
  }

  /// Get timeout statistics
  TimeoutStatistics getTimeoutStatistics() {
    final timeoutService = read(timeoutServiceProvider);
    return timeoutService.getStatistics();
  }
}
