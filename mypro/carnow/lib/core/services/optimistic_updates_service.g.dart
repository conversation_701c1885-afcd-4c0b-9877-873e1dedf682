// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'optimistic_updates_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$optimisticUpdatesServiceHash() =>
    r'h8i9j0k1l2m3n4o5p6q7r8s9t0u1v2w3x4y5z6a7';

/// Optimistic updates service provider
///
/// Copied from [optimisticUpdatesService].
@ProviderFor(optimisticUpdatesService)
final optimisticUpdatesServiceProvider =
    AutoDisposeProvider<OptimisticUpdatesService>.internal(
      optimisticUpdatesService,
      name: r'optimisticUpdatesServiceProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$optimisticUpdatesServiceHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef OptimisticUpdatesServiceRef =
    AutoDisposeProviderRef<OptimisticUpdatesService>;
String _$optimisticUpdatesStreamHash() =>
    r'i9j0k1l2m3n4o5p6q7r8s9t0u1v2w3x4y5z6a7b8';

/// Pending updates stream provider
///
/// Copied from [optimisticUpdatesStream].
@ProviderFor(optimisticUpdatesStream)
final optimisticUpdatesStreamProvider =
    AutoDisposeStreamProvider<OptimisticUpdate>.internal(
      optimisticUpdatesStream,
      name: r'optimisticUpdatesStreamProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$optimisticUpdatesStreamHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef OptimisticUpdatesStreamRef =
    AutoDisposeStreamProviderRef<OptimisticUpdate>;
String _$conflictsStreamHash() => r'j0k1l2m3n4o5p6q7r8s9t0u1v2w3x4y5z6a7b8c9';

/// Conflicts stream provider
///
/// Copied from [conflictsStream].
@ProviderFor(conflictsStream)
final conflictsStreamProvider =
    AutoDisposeStreamProvider<ConflictResolution>.internal(
      conflictsStream,
      name: r'conflictsStreamProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$conflictsStreamHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef ConflictsStreamRef = AutoDisposeStreamProviderRef<ConflictResolution>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
