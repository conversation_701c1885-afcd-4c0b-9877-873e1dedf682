import 'package:dio/dio.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:logging/logging.dart';

import '../interceptors/error_interceptor.dart';
import '../errors/unified_error_handler.dart';
import '../errors/app_error.dart';

part 'error_interceptor_service.g.dart';

/// خدمة إدارة Error Interceptors
/// Service for managing Error Interceptors
class ErrorInterceptorService {
  ErrorInterceptorService(this._errorHandler) {
    _networkInterceptor = ErrorInterceptorFactory.createNetworkInterceptor(
      _errorHandler,
    );
    // Supabase interceptor removed - Forever Plan Compliance
    _logger.info('Error interceptor service initialized');
  }

  static final Logger _logger = Logger('ErrorInterceptorService');

  final UnifiedErrorHandler _errorHandler;
  late final UnifiedErrorInterceptor _networkInterceptor;
  // SupabaseErrorInterceptor removed - Forever Plan Compliance

  /// إعداد Dio مع Error Interceptor
  /// Setup Dio with Error Interceptor
  Dio setupDioWithErrorHandling({
    BaseOptions? options,
    List<Interceptor>? additionalInterceptors,
  }) {
    final dio = Dio(options ?? BaseOptions());

    // إضافة Error Interceptor كأول interceptor
    dio.interceptors.add(_networkInterceptor);

    // إضافة interceptors إضافية إذا وجدت
    if (additionalInterceptors != null) {
      dio.interceptors.addAll(additionalInterceptors);
    }

    // إضافة logging interceptor في بيئة التطوير
    if (const bool.fromEnvironment('dart.vm.product') == false) {
      dio.interceptors.add(
        LogInterceptor(
          requestBody: true,
          responseBody: true,
          logPrint: (obj) => _logger.info(obj.toString()),
        ),
      );
    }

    _logger.info('Dio configured with error handling');
    return dio;
  }

  // handleSupabaseError removed - Forever Plan Compliance
  // All database operations now go through Go API only

  /// تنفيذ عملية Supabase مع معالجة الأخطاء
  /// Execute Supabase operation with error handling
  Future<T> executeSupabaseOperation<T>(
    Future<T> Function() operation, {
    String? operationName,
    Map<String, dynamic>? additionalData,
  }) async {
    try {
      _logger.info(
        'Executing Supabase operation: ${operationName ?? 'unknown'}',
      );
      return await operation();
    } catch (error, stackTrace) {
      _logger.severe(
        'Supabase operation failed: ${operationName ?? 'unknown'}',
        error,
        stackTrace,
      );

      // Use unified error handler directly - Forever Plan Compliance
      final appError = _errorHandler.handleError(error, context: 'api_operation');

      // إضافة معلومات إضافية للخطأ
      final enhancedError = AppError(
        message: appError.message,
        type: appError.type,
        code: appError.code,
        severity: appError.severity,
        originalError: appError.originalError,
        data: {
          if (appError.data != null) ...appError.data!,
          if (additionalData != null) ...additionalData,
          'operation_name': operationName,
          'timestamp': DateTime.now().toIso8601String(),
        },
      );

      throw enhancedError;
    }
  }

  /// تنفيذ طلب HTTP مع معالجة الأخطاء
  /// Execute HTTP request with error handling
  Future<Response<T>> executeHttpRequest<T>(
    Future<Response<T>> Function() request, {
    String? operationName,
    Map<String, dynamic>? additionalData,
  }) async {
    try {
      _logger.info('Executing HTTP request: ${operationName ?? 'unknown'}');
      return await request();
    } catch (error, stackTrace) {
      _logger.severe(
        'HTTP request failed: ${operationName ?? 'unknown'}',
        error,
        stackTrace,
      );

      // إذا كان الخطأ من Dio، فسيتم معالجته بواسطة الـ interceptor
      // إذا لم يكن كذلك، نعالجه هنا
      if (error is! DioException) {
        final appError = _errorHandler.handleError(
          error,
          stackTrace: stackTrace,
          context: 'http_request',
          additionalData: {
            if (additionalData != null) ...additionalData,
            'operation_name': operationName,
          },
        );
        throw appError;
      }

      rethrow;
    }
  }

  /// إنشاء wrapper للعمليات العامة
  /// Create wrapper for general operations
  Future<T> executeWithErrorHandling<T>(
    Future<T> Function() operation, {
    String? operationName,
    String? context,
    Map<String, dynamic>? additionalData,
    bool shouldRetry = false,
    int maxRetries = 3,
    Duration retryDelay = const Duration(seconds: 1),
  }) async {
    return _errorHandler
        .executeWithErrorHandling(
          operation,
          context: context ?? operationName ?? 'general_operation',
          additionalData: {
            if (additionalData != null) ...additionalData,
            'operation_name': operationName,
          },
          shouldRetry: shouldRetry,
          maxRetries: maxRetries,
          retryDelay: retryDelay,
        )
        .then(
          (result) => result.when(
            success: (data) => data,
            failure: (error) => throw error,
          ),
        );
  }

  /// الحصول على Network Interceptor
  UnifiedErrorInterceptor get networkInterceptor => _networkInterceptor;

  // supabaseInterceptor getter removed - Forever Plan Compliance
  // All database operations now go through Go API only
}

/// Provider لخدمة Error Interceptor
@riverpod
ErrorInterceptorService errorInterceptorService(Ref ref) {
  final errorHandler = ref.read(unifiedErrorHandlerProvider);
  return ErrorInterceptorService(errorHandler);
}

/// Provider لـ Dio مع Error Handling
@riverpod
Dio dioWithErrorHandling(Ref ref) {
  final interceptorService = ref.read(errorInterceptorServiceProvider);
  return interceptorService.setupDioWithErrorHandling(
    options: BaseOptions(
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 30),
      sendTimeout: const Duration(seconds: 30),
    ),
  );
}

/// Extension لتسهيل استخدام Error Handling مع Ref
extension ErrorHandlingRef on Ref {
  /// تنفيذ عملية Supabase مع معالجة الأخطاء
  Future<T> executeSupabaseOperation<T>(
    Future<T> Function() operation, {
    String? operationName,
    Map<String, dynamic>? additionalData,
  }) {
    final service = read(errorInterceptorServiceProvider);
    return service.executeSupabaseOperation(
      operation,
      operationName: operationName,
      additionalData: additionalData,
    );
  }

  /// تنفيذ طلب HTTP مع معالجة الأخطاء
  Future<Response<T>> executeHttpRequest<T>(
    Future<Response<T>> Function() request, {
    String? operationName,
    Map<String, dynamic>? additionalData,
  }) {
    final service = read(errorInterceptorServiceProvider);
    return service.executeHttpRequest(
      request,
      operationName: operationName,
      additionalData: additionalData,
    );
  }

  /// تنفيذ عملية عامة مع معالجة الأخطاء
  Future<T> executeWithErrorHandling<T>(
    Future<T> Function() operation, {
    String? operationName,
    String? context,
    Map<String, dynamic>? additionalData,
    bool shouldRetry = false,
    int maxRetries = 3,
    Duration retryDelay = const Duration(seconds: 1),
  }) {
    final service = read(errorInterceptorServiceProvider);
    return service.executeWithErrorHandling(
      operation,
      operationName: operationName,
      context: context,
      additionalData: additionalData,
      shouldRetry: shouldRetry,
      maxRetries: maxRetries,
      retryDelay: retryDelay,
    );
  }
}
