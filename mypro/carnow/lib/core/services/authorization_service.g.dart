// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'authorization_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$authorizationServiceHash() =>
    r'3cb890641bf934324a68702d3120e52a66514636';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

abstract class _$AuthorizationService
    extends BuildlessAutoDisposeAsyncNotifier<bool> {
  late final String resource;
  late final String action;

  FutureOr<bool> build(String resource, String action);
}

/// See also [AuthorizationService].
@ProviderFor(AuthorizationService)
const authorizationServiceProvider = AuthorizationServiceFamily();

/// See also [AuthorizationService].
class AuthorizationServiceFamily extends Family<AsyncValue<bool>> {
  /// See also [AuthorizationService].
  const AuthorizationServiceFamily();

  /// See also [AuthorizationService].
  AuthorizationServiceProvider call(String resource, String action) {
    return AuthorizationServiceProvider(resource, action);
  }

  @override
  AuthorizationServiceProvider getProviderOverride(
    covariant AuthorizationServiceProvider provider,
  ) {
    return call(provider.resource, provider.action);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'authorizationServiceProvider';
}

/// See also [AuthorizationService].
class AuthorizationServiceProvider
    extends AutoDisposeAsyncNotifierProviderImpl<AuthorizationService, bool> {
  /// See also [AuthorizationService].
  AuthorizationServiceProvider(String resource, String action)
    : this._internal(
        () => AuthorizationService()
          ..resource = resource
          ..action = action,
        from: authorizationServiceProvider,
        name: r'authorizationServiceProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$authorizationServiceHash,
        dependencies: AuthorizationServiceFamily._dependencies,
        allTransitiveDependencies:
            AuthorizationServiceFamily._allTransitiveDependencies,
        resource: resource,
        action: action,
      );

  AuthorizationServiceProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.resource,
    required this.action,
  }) : super.internal();

  final String resource;
  final String action;

  @override
  FutureOr<bool> runNotifierBuild(covariant AuthorizationService notifier) {
    return notifier.build(resource, action);
  }

  @override
  Override overrideWith(AuthorizationService Function() create) {
    return ProviderOverride(
      origin: this,
      override: AuthorizationServiceProvider._internal(
        () => create()
          ..resource = resource
          ..action = action,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        resource: resource,
        action: action,
      ),
    );
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<AuthorizationService, bool>
  createElement() {
    return _AuthorizationServiceProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is AuthorizationServiceProvider &&
        other.resource == resource &&
        other.action == action;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, resource.hashCode);
    hash = _SystemHash.combine(hash, action.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin AuthorizationServiceRef on AutoDisposeAsyncNotifierProviderRef<bool> {
  /// The parameter `resource` of this provider.
  String get resource;

  /// The parameter `action` of this provider.
  String get action;
}

class _AuthorizationServiceProviderElement
    extends AutoDisposeAsyncNotifierProviderElement<AuthorizationService, bool>
    with AuthorizationServiceRef {
  _AuthorizationServiceProviderElement(super.provider);

  @override
  String get resource => (origin as AuthorizationServiceProvider).resource;
  @override
  String get action => (origin as AuthorizationServiceProvider).action;
}

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
