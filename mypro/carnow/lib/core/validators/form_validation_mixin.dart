import 'package:flutter/material.dart';
import '../../l10n/app_localizations.dart';
import 'input_validators.dart';
import 'form_validation_service.dart';

/// خليط للتحقق من صحة النماذج يمكن استخدامه مع أي StatefulWidget
mixin FormValidationMixin<T extends StatefulWidget> on State<T> {
  final Map<String, String> _fieldErrors = {};
  final Map<String, String> _sanitizedValues = {};
  final Map<String, GlobalKey<FormFieldState>> _fieldKeys = {};
  bool _isValidating = false;

  /// الحصول على مفتاح حقل معين أو إنشاؤه
  GlobalKey<FormFieldState> getFieldKey(String fieldName) {
    return _fieldKeys.putIfAbsent(fieldName, () => GlobalKey<FormFieldState>());
  }

  /// الحصول على خطأ حقل معين
  String? getFieldError(String fieldName) => _fieldErrors[fieldName];

  /// الحصول على قيمة معقمة لحقل معين
  String? getSanitizedValue(String fieldName) => _sanitizedValues[fieldName];

  /// هل يوجد أخطاء في النموذج
  bool get hasErrors => _fieldErrors.isNotEmpty;

  /// هل النموذج صالح
  bool get isFormValid => _fieldErrors.isEmpty && !_isValidating;

  /// هل يتم التحقق حالياً
  bool get isValidating => _isValidating;

  /// تعيين خطأ لحقل معين
  void setFieldError(String fieldName, String? error) {
    setState(() {
      if (error != null) {
        _fieldErrors[fieldName] = error;
      } else {
        _fieldErrors.remove(fieldName);
      }
    });
  }

  /// تعيين قيمة معقمة لحقل معين
  void setSanitizedValue(String fieldName, String value) {
    setState(() {
      _sanitizedValues[fieldName] = value;
    });
  }

  /// التحقق من صحة حقل واحد
  Future<bool> validateField(
    String fieldName,
    String? value,
    String? Function(String?, AppLocalizations?) validator, {
    bool sanitize = true,
  }) async {
    setState(() {
      _isValidating = true;
    });

    try {
      final l10n = AppLocalizations.of(context);
      String? processedValue = value;

      if (sanitize && value != null) {
        processedValue = InputValidators.sanitizeInput(value);
        setSanitizedValue(fieldName, processedValue);
      }

      final error = validator(processedValue, l10n);
      setFieldError(fieldName, error);

      return error == null;
    } finally {
      setState(() {
        _isValidating = false;
      });
    }
  }

  /// التحقق من صحة نموذج كامل
  ValidationResult validateForm(
    Map<String, String?> formData,
    List<FieldValidationRule> rules,
  ) {
    final l10n = AppLocalizations.of(context);
    final result = FormValidationService.validateForm(formData, rules, l10n);

    setState(() {
      _fieldErrors.clear();
      _fieldErrors.addAll(result.errors);
      _sanitizedValues.clear();
      _sanitizedValues.addAll(result.sanitizedValues);
    });

    return result;
  }

  /// التحقق من صحة النموذج باستخدام GlobalKey
  bool validateFormWithKeys() {
    bool isValid = true;

    for (final entry in _fieldKeys.entries) {
      final fieldState = entry.value.currentState;
      if (fieldState != null) {
        if (!fieldState.validate()) {
          isValid = false;
        }
      }
    }

    return isValid;
  }

  /// مسح جميع الأخطاء
  void clearAllErrors() {
    setState(() {
      _fieldErrors.clear();
      _sanitizedValues.clear();
    });
  }

  /// مسح خطأ حقل معين
  void clearFieldError(String fieldName) {
    setState(() {
      _fieldErrors.remove(fieldName);
      _sanitizedValues.remove(fieldName);
    });
  }

  /// إنشاء validator آمن لحقل معين
  String? Function(String?) createSecureValidator(
    String fieldName,
    String? Function(String?, AppLocalizations?) validator, {
    bool sanitize = true,
  }) {
    return (String? value) {
      final l10n = AppLocalizations.of(context);
      String? processedValue = value;

      if (sanitize && value != null) {
        processedValue = InputValidators.sanitizeInput(value);
        setSanitizedValue(fieldName, processedValue);
      }

      final error = validator(processedValue, l10n);
      setFieldError(fieldName, error);
      return error;
    };
  }

  /// إنشاء onChanged callback آمن
  void Function(String) createSecureOnChanged(
    String fieldName, {
    void Function(String)? onChanged,
    bool sanitize = true,
  }) {
    return (String value) {
      if (sanitize) {
        final sanitized = InputValidators.sanitizeInput(value);
        setSanitizedValue(fieldName, sanitized);
      }

      onChanged?.call(value);
    };
  }

  /// إنشاء InputDecoration مع عرض الأخطاء
  InputDecoration createSecureDecoration({
    String? labelText,
    String? hintText,
    IconData? prefixIcon,
    IconData? suffixIcon,
    String? fieldName,
  }) {
    final theme = Theme.of(context);
    final error = fieldName != null ? getFieldError(fieldName) : null;

    return InputDecoration(
      labelText: labelText,
      hintText: hintText,
      prefixIcon: prefixIcon != null ? Icon(prefixIcon) : null,
      suffixIcon: suffixIcon != null ? Icon(suffixIcon) : null,
      errorText: error,
      border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(
          color: error != null
              ? theme.colorScheme.error.withValues(alpha: 0.5)
              : theme.colorScheme.outline,
        ),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(
          color: error != null
              ? theme.colorScheme.error
              : theme.colorScheme.primary,
        ),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(color: theme.colorScheme.error),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(color: theme.colorScheme.error),
      ),
    );
  }

  /// عرض رسالة خطأ عامة
  void showFormError(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Theme.of(context).colorScheme.error,
        ),
      );
    }
  }

  /// عرض رسالة نجاح
  void showFormSuccess(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(message), backgroundColor: Colors.green),
      );
    }
  }

  @override
  void dispose() {
    _fieldErrors.clear();
    _sanitizedValues.clear();
    _fieldKeys.clear();
    super.dispose();
  }
}

/// خليط للتحقق من صحة كلمات المرور
mixin PasswordValidationMixin<T extends StatefulWidget> on State<T> {
  String? _password;

  /// تعيين كلمة المرور
  void setPassword(String? password) {
    _password = password;
  }

  /// التحقق من تطابق كلمات المرور
  String? validatePasswordConfirmation(String? value) {
    return InputValidators.validatePasswordConfirmation(_password, value);
  }

  /// التحقق من قوة كلمة المرور
  PasswordStrength getPasswordStrength(String? password) {
    if (password == null || password.isEmpty) {
      return PasswordStrength.none;
    }

    int score = 0;

    // Length check
    if (password.length >= 8) score++;
    if (password.length >= 12) score++;

    // Character variety checks
    if (RegExp(r'[a-z]').hasMatch(password)) score++;
    if (RegExp(r'[A-Z]').hasMatch(password)) score++;
    if (RegExp(r'[0-9]').hasMatch(password)) score++;
    if (RegExp(r'[!@#$%^&*(),.?":{}|<>]').hasMatch(password)) score++;

    // Penalty for common patterns
    if (RegExp(r'(.)\1{2,}').hasMatch(password)) score--; // Repeated characters
    if (RegExp(r'(012|123|234|345|456|567|678|789|890)').hasMatch(password))
      score--; // Sequential numbers
    if (RegExp(
      r'(abc|bcd|cde|def|efg|fgh|ghi|hij|ijk|jkl|klm|lmn|mno|nop|opq|pqr|qrs|rst|stu|tuv|uvw|vwx|wxy|xyz)',
    ).hasMatch(password.toLowerCase()))
      score--; // Sequential letters

    if (score <= 2) return PasswordStrength.weak;
    if (score <= 4) return PasswordStrength.medium;
    if (score <= 5) return PasswordStrength.strong;
    return PasswordStrength.veryStrong;
  }

  /// الحصول على لون قوة كلمة المرور
  Color getPasswordStrengthColor(PasswordStrength strength) {
    switch (strength) {
      case PasswordStrength.none:
        return Colors.grey;
      case PasswordStrength.weak:
        return Colors.red;
      case PasswordStrength.medium:
        return Colors.orange;
      case PasswordStrength.strong:
        return Colors.blue;
      case PasswordStrength.veryStrong:
        return Colors.green;
    }
  }

  /// الحصول على نص قوة كلمة المرور
  String getPasswordStrengthText(
    PasswordStrength strength,
    AppLocalizations? l10n,
  ) {
    switch (strength) {
      case PasswordStrength.none:
        return l10n?.passwordStrengthNone ?? 'No password';
      case PasswordStrength.weak:
        return l10n?.passwordStrengthWeak ?? 'Weak';
      case PasswordStrength.medium:
        return l10n?.passwordStrengthMedium ?? 'Medium';
      case PasswordStrength.strong:
        return l10n?.passwordStrengthStrong ?? 'Strong';
      case PasswordStrength.veryStrong:
        return l10n?.passwordStrengthVeryStrong ?? 'Very Strong';
    }
  }
}

/// تعداد قوة كلمة المرور
enum PasswordStrength { none, weak, medium, strong, veryStrong }

/// خليط للتحقق من صحة الملفات المرفوعة
mixin FileValidationMixin<T extends StatefulWidget> on State<T> {
  static const List<String> _allowedImageExtensions = [
    'jpg',
    'jpeg',
    'png',
    'gif',
    'webp',
  ];

  static const List<String> _allowedDocumentExtensions = [
    'pdf',
    'doc',
    'docx',
    'txt',
  ];

  static const int _maxFileSize = 10 * 1024 * 1024; // 10MB

  /// التحقق من صحة ملف الصورة
  String? validateImageFile(String? filePath, AppLocalizations? l10n) {
    if (filePath == null || filePath.isEmpty) {
      return l10n?.requiredField ?? 'This field is required';
    }

    final extension = filePath.split('.').last.toLowerCase();
    if (!_allowedImageExtensions.contains(extension)) {
      return l10n?.invalidImageFormat ?? 'Invalid image format';
    }

    return null;
  }

  /// التحقق من صحة ملف المستند
  String? validateDocumentFile(String? filePath, AppLocalizations? l10n) {
    if (filePath == null || filePath.isEmpty) {
      return l10n?.requiredField ?? 'This field is required';
    }

    final extension = filePath.split('.').last.toLowerCase();
    if (!_allowedDocumentExtensions.contains(extension)) {
      return l10n?.invalidDocumentFormat ?? 'Invalid document format';
    }

    return null;
  }

  /// التحقق من حجم الملف
  String? validateFileSize(int? fileSize, AppLocalizations? l10n) {
    if (fileSize == null) {
      return l10n?.requiredField ?? 'This field is required';
    }

    if (fileSize > _maxFileSize) {
      return l10n?.fileTooLarge ?? 'File is too large';
    }

    return null;
  }
}
