import 'package:flutter/material.dart';
import '../../l10n/app_localizations.dart';

/// معالج أخطاء التحقق من صحة النماذج
class ValidationErrorHandler {
  /// عرض خطأ التحقق في SnackBar
  static void showValidationError(
    BuildContext context,
    String message, {
    Duration duration = const Duration(seconds: 4),
  }) {
    if (!context.mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(Icons.error_outline, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            Expanded(
              child: Text(message, style: const TextStyle(color: Colors.white)),
            ),
          ],
        ),
        backgroundColor: Theme.of(context).colorScheme.error,
        duration: duration,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }

  /// عرض رسالة نجاح
  static void showSuccessMessage(
    BuildContext context,
    String message, {
    Duration duration = const Duration(seconds: 3),
  }) {
    if (!context.mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(
              Icons.check_circle_outline,
              color: Colors.white,
              size: 20,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(message, style: const TextStyle(color: Colors.white)),
            ),
          ],
        ),
        backgroundColor: Colors.green,
        duration: duration,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }

  /// عرض تحذير أمني
  static void showSecurityWarning(
    BuildContext context,
    String message, {
    Duration duration = const Duration(seconds: 5),
  }) {
    if (!context.mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.security, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            Expanded(
              child: Text(message, style: const TextStyle(color: Colors.white)),
            ),
          ],
        ),
        backgroundColor: Colors.orange,
        duration: duration,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }

  /// عرض حوار خطأ مفصل
  static Future<void> showDetailedErrorDialog(
    BuildContext context,
    String title,
    String message, {
    List<String>? details,
  }) async {
    if (!context.mounted) return;

    return showDialog<void>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Row(
            children: [
              Icon(
                Icons.error_outline,
                color: Theme.of(context).colorScheme.error,
              ),
              const SizedBox(width: 8),
              Text(title),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(message),
              if (details != null && details.isNotEmpty) ...[
                const SizedBox(height: 16),
                const Text(
                  'التفاصيل:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                ...details.map(
                  (detail) => Padding(
                    padding: const EdgeInsets.only(left: 16, bottom: 4),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text('• '),
                        Expanded(child: Text(detail)),
                      ],
                    ),
                  ),
                ),
              ],
            ],
          ),
          actions: <Widget>[
            TextButton(
              child: const Text('موافق'),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }

  /// عرض حوار تأكيد الأمان
  static Future<bool> showSecurityConfirmationDialog(
    BuildContext context,
    String title,
    String message,
  ) async {
    if (!context.mounted) return false;

    return await showDialog<bool>(
          context: context,
          builder: (BuildContext context) {
            return AlertDialog(
              title: Row(
                children: [
                  Icon(Icons.security, color: Colors.orange),
                  const SizedBox(width: 8),
                  Text(title),
                ],
              ),
              content: Text(message),
              actions: <Widget>[
                TextButton(
                  child: const Text('إلغاء'),
                  onPressed: () {
                    Navigator.of(context).pop(false);
                  },
                ),
                FilledButton(
                  style: FilledButton.styleFrom(backgroundColor: Colors.orange),
                  child: const Text('متابعة'),
                  onPressed: () {
                    Navigator.of(context).pop(true);
                  },
                ),
              ],
            );
          },
        ) ??
        false;
  }

  /// تحويل أخطاء التحقق إلى رسائل مفهومة
  static String getReadableErrorMessage(
    String errorCode,
    AppLocalizations? l10n,
  ) {
    switch (errorCode) {
      case 'VALIDATION_ERROR':
        return l10n?.validationError ?? 'Validation error occurred';
      case 'SECURITY_ERROR':
        return l10n?.securityError ?? 'Security issue detected';
      case 'INPUT_TOO_LONG':
        return l10n?.inputTooLong ?? 'Input is too long';
      case 'INVALID_INPUT':
        return l10n?.invalidInput ?? 'Invalid input detected';
      case 'REQUIRED_FIELD':
        return l10n?.requiredField ?? 'This field is required';
      case 'INVALID_EMAIL':
        return l10n?.invalidEmail ?? 'Please enter a valid email';
      case 'INVALID_PHONE':
        return l10n?.invalidPhoneNumber ?? 'Please enter a valid phone number';
      case 'PASSWORD_TOO_SHORT':
        return l10n?.passwordTooShort ?? 'Password is too short';
      case 'PASSWORD_TOO_WEAK':
        return l10n?.passwordTooCommon ?? 'Password is too weak';
      case 'PASSWORDS_DO_NOT_MATCH':
        return l10n?.passwordsDoNotMatch ?? 'Passwords do not match';
      default:
        return l10n?.unknownError ?? 'An unknown error occurred';
    }
  }

  /// إنشاء widget لعرض أخطاء التحقق
  static Widget buildErrorWidget(
    String? error, {
    IconData icon = Icons.error_outline,
    Color? color,
  }) {
    if (error == null || error.isEmpty) {
      return const SizedBox.shrink();
    }

    return Builder(
      builder: (context) {
        final theme = Theme.of(context);
        return Container(
          padding: const EdgeInsets.all(12),
          margin: const EdgeInsets.only(top: 8),
          decoration: BoxDecoration(
            color: (color ?? theme.colorScheme.error).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: (color ?? theme.colorScheme.error).withValues(alpha: 0.3),
            ),
          ),
          child: Row(
            children: [
              Icon(icon, color: color ?? theme.colorScheme.error, size: 20),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  error,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: color ?? theme.colorScheme.error,
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  /// إنشاء widget لعرض تحذيرات الأمان
  static Widget buildSecurityWarningWidget(
    String warning, {
    VoidCallback? onDismiss,
  }) {
    return Builder(
      builder: (context) {
        final theme = Theme.of(context);
        return Container(
          padding: const EdgeInsets.all(12),
          margin: const EdgeInsets.only(top: 8),
          decoration: BoxDecoration(
            color: Colors.orange.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
          ),
          child: Row(
            children: [
              const Icon(Icons.security, color: Colors.orange, size: 20),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  warning,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: Colors.orange.shade700,
                  ),
                ),
              ),
              if (onDismiss != null) ...[
                const SizedBox(width: 8),
                IconButton(
                  icon: const Icon(Icons.close, size: 16),
                  color: Colors.orange,
                  onPressed: onDismiss,
                  constraints: const BoxConstraints(
                    minWidth: 24,
                    minHeight: 24,
                  ),
                  padding: EdgeInsets.zero,
                ),
              ],
            ],
          ),
        );
      },
    );
  }

  /// إنشاء widget لعرض نصائح التحقق
  static Widget buildValidationTipsWidget(List<String> tips, {String? title}) {
    if (tips.isEmpty) return const SizedBox.shrink();

    return Builder(
      builder: (context) {
        final theme = Theme.of(context);
        return Container(
          padding: const EdgeInsets.all(12),
          margin: const EdgeInsets.only(top: 8),
          decoration: BoxDecoration(
            color: theme.colorScheme.primary.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: theme.colorScheme.primary.withValues(alpha: 0.3),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (title != null) ...[
                Row(
                  children: [
                    Icon(
                      Icons.lightbulb_outline,
                      color: theme.colorScheme.primary,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      title,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.primary,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
              ],
              ...tips.map(
                (tip) => Padding(
                  padding: const EdgeInsets.only(bottom: 4),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '• ',
                        style: TextStyle(color: theme.colorScheme.primary),
                      ),
                      Expanded(
                        child: Text(
                          tip,
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.primary.withValues(alpha: 0.8),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
