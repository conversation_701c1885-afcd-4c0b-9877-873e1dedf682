# Flutter Input Validation System

This comprehensive validation system provides secure, client-side input validation with automatic sanitization and security threat detection.

## Features

- **Security-First Approach**: Automatic detection and prevention of XSS, SQL injection, and other security threats
- **Input Sanitization**: Automatic cleaning of dangerous content while preserving valid data
- **Comprehensive Validation**: Support for all common input types (email, phone, password, etc.)
- **Localization Support**: Full Arabic and English localization
- **Real-time Validation**: Debounced validation with visual feedback
- **Password Strength Analysis**: Advanced password strength checking with visual indicators
- **Form Integration**: Easy integration with existing forms through mixins and providers

## Quick Start

### 1. Using Secure Text Fields

Replace your existing `TextFormField` widgets with secure alternatives:

```dart
// Instead of TextFormField
SecureEmailField(
  controller: emailController,
  onChanged: (value) => print('Email: $value'),
)

// Or use the generic SecureTextField
SecureTextField(
  controller: nameController,
  label: 'Full Name',
  validator: InputValidators.validateName,
  showSecurityIndicator: true,
)
```

### 2. Using Validation Mixins

Add validation capabilities to your existing forms:

```dart
class MyFormScreen extends StatefulWidget {
  @override
  _MyFormScreenState createState() => _MyFormScreenState();
}

class _MyFormScreenState extends State<MyFormScreen> 
    with FormValidationMixin {
  
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Form(
      child: Column(
        children: [
          TextFormField(
            controller: _nameController,
            key: getFieldKey('name'),
            validator: createSecureValidator(
              'name',
              InputValidators.validateName,
            ),
            decoration: createSecureDecoration(
              labelText: 'Name',
              fieldName: 'name',
            ),
          ),
          // Submit button
          ElevatedButton(
            onPressed: isFormValid ? _submitForm : null,
            child: Text('Submit'),
          ),
        ],
      ),
    );
  }

  void _submitForm() {
    if (validateFormWithKeys()) {
      // Form is valid, proceed with submission
      final sanitizedName = getSanitizedValue('name');
      // Use sanitized values for API calls
    }
  }
}
```

### 3. Using Form Validation Service

For complex forms with multiple validation rules:

```dart
class RegistrationForm extends StatefulWidget {
  @override
  _RegistrationFormState createState() => _RegistrationFormState();
}

class _RegistrationFormState extends State<RegistrationForm> {
  final _validationProvider = FormValidationProvider();
  
  void _submitForm() {
    final formData = {
      'name': nameController.text,
      'email': emailController.text,
      'password': passwordController.text,
      'phone': phoneController.text,
    };

    final result = _validationProvider.validateForm(
      formData,
      FormValidationService.registrationFormRules,
      AppLocalizations.of(context),
    );

    if (result.isValid) {
      // Use result.sanitizedValues for API calls
      _registerUser(result.sanitizedValues);
    } else {
      // Show errors - they're automatically displayed in the UI
      ValidationErrorHandler.showValidationError(
        context,
        'Please fix the errors below',
      );
    }
  }
}
```

## Available Validators

### Basic Validators
- `validateName()` - Names with international character support
- `validateEmail()` - RFC-compliant email validation
- `validatePhone()` - International phone number formats
- `validatePassword()` - Strong password requirements
- `validateLocation()` - Address and location validation

### Financial Validators
- `validateAmount()` - Monetary amounts with min/max limits
- `validateBankAccount()` - Bank account numbers and IBAN

### Specialized Validators
- `validateVIN()` - Vehicle identification numbers
- `validateURL()` - Web URLs with security checks
- `validatePostalCode()` - International postal codes

### Security Functions
- `sanitizeInput()` - Clean dangerous content from input
- `containsDangerousContent()` - Detect security threats
- `validateSecureInput()` - Combined security validation

## Security Features

### XSS Prevention
The system automatically detects and removes:
- Script tags and JavaScript code
- Event handlers (onclick, onload, etc.)
- Data URLs and JavaScript protocols
- CSS expressions and imports

### SQL Injection Prevention
Detects common SQL injection patterns:
- SQL keywords (SELECT, INSERT, DROP, etc.)
- Boolean logic attacks (OR 1=1)
- Comment sequences (-- and /* */)
- Quote-based attacks

### Input Sanitization
- Removes null bytes and control characters
- Normalizes whitespace
- Preserves valid international characters
- Maintains data integrity while ensuring security

## Password Security

### Strength Requirements
- Minimum 8 characters
- At least one uppercase letter
- At least one lowercase letter
- At least one number
- At least one special character
- Protection against common passwords

### Password Strength Indicator
```dart
class PasswordField extends StatefulWidget {
  @override
  _PasswordFieldState createState() => _PasswordFieldState();
}

class _PasswordFieldState extends State<PasswordField> 
    with PasswordValidationMixin {
  
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        SecurePasswordField(
          onChanged: (value) => setPassword(value),
        ),
        // Password strength indicator
        LinearProgressIndicator(
          value: getPasswordStrength(password).index / 4,
          color: getPasswordStrengthColor(getPasswordStrength(password)),
        ),
        Text(getPasswordStrengthText(
          getPasswordStrength(password),
          AppLocalizations.of(context),
        )),
      ],
    );
  }
}
```

## Error Handling

### Displaying Errors
```dart
// Show validation error in SnackBar
ValidationErrorHandler.showValidationError(
  context,
  'Please enter a valid email address',
);

// Show security warning
ValidationErrorHandler.showSecurityWarning(
  context,
  'Potentially unsafe content detected',
);

// Show detailed error dialog
ValidationErrorHandler.showDetailedErrorDialog(
  context,
  'Validation Failed',
  'Multiple errors were found in your input',
  details: ['Email format is invalid', 'Password is too weak'],
);
```

### Custom Error Widgets
```dart
// Display inline error
ValidationErrorHandler.buildErrorWidget(
  'This field is required',
)

// Display security warning
ValidationErrorHandler.buildSecurityWarningWidget(
  'Potentially unsafe content detected',
  onDismiss: () => clearSecurityWarning(),
)

// Display validation tips
ValidationErrorHandler.buildValidationTipsWidget(
  [
    'Use at least 8 characters',
    'Include uppercase and lowercase letters',
    'Add numbers and special characters',
  ],
  title: 'Password Requirements',
)
```

## Integration with Existing Forms

### Minimal Changes Required
You can integrate the validation system with minimal changes to existing forms:

1. **Replace TextFormField validators:**
```dart
// Before
TextFormField(
  validator: (value) {
    if (value?.isEmpty ?? true) return 'Required';
    return null;
  },
)

// After
TextFormField(
  validator: (value) => InputValidators.validateName(
    value, 
    AppLocalizations.of(context),
  ),
)
```

2. **Add security indicators:**
```dart
TextFormField(
  inputFormatters: [
    SanitizingTextInputFormatter(), // Add automatic sanitization
  ],
  decoration: InputDecoration(
    suffixIcon: Icon(
      InputValidators.containsDangerousContent(controller.text)
        ? Icons.security
        : Icons.verified_user,
    ),
  ),
)
```

## Best Practices

### 1. Always Sanitize User Input
```dart
// Before sending to API
final sanitizedData = {
  'name': InputValidators.sanitizeInput(nameController.text),
  'description': InputValidators.sanitizeInput(descriptionController.text),
};
```

### 2. Use Appropriate Validators
```dart
// Use specific validators for better UX
SecureEmailField(), // For email inputs
SecurePhoneField(), // For phone numbers
SecureAmountField(minAmount: 10, maxAmount: 1000), // For monetary amounts
```

### 3. Provide Visual Feedback
```dart
SecureTextField(
  showSecurityIndicator: true, // Show security status
  autoValidate: true, // Real-time validation
)
```

### 4. Handle Errors Gracefully
```dart
try {
  await submitForm(sanitizedData);
  ValidationErrorHandler.showSuccessMessage(context, 'Form submitted successfully');
} catch (e) {
  ValidationErrorHandler.showValidationError(context, e.toString());
}
```

## Testing

The validation system includes comprehensive tests:

```bash
flutter test test/core/validators/
```

Test coverage includes:
- All validation functions
- Security threat detection
- Input sanitization
- Edge cases and error conditions

## Localization

The system supports full localization with Arabic and English translations. Add new languages by extending the ARB files:

```json
// lib/l10n/app_fr.arb
{
  "invalidEmail": "Veuillez saisir une adresse e-mail valide",
  "passwordTooShort": "Le mot de passe est trop court"
}
```

## Performance Considerations

- **Debounced Validation**: Real-time validation is debounced to prevent excessive processing
- **Efficient Regex**: Optimized regular expressions for fast validation
- **Minimal Memory Usage**: Validators are stateless and memory-efficient
- **Lazy Loading**: Validation rules are loaded only when needed

## Migration Guide

### From Basic Validation
```dart
// Old approach
String? validateEmail(String? value) {
  if (value?.isEmpty ?? true) return 'Required';
  if (!value!.contains('@')) return 'Invalid email';
  return null;
}

// New approach
InputValidators.validateEmail(value, AppLocalizations.of(context))
```

### From Custom Validators
```dart
// Old approach
class CustomValidator {
  static String? validateName(String? value) {
    // Custom validation logic
  }
}

// New approach - extend existing validators
class CustomValidators extends InputValidators {
  static String? validateBusinessName(String? value, AppLocalizations? l10n) {
    // First run standard validation
    final baseError = InputValidators.validateName(value, l10n);
    if (baseError != null) return baseError;
    
    // Add custom business logic
    if (value!.length < 5) return 'Business name too short';
    return null;
  }
}
```

This validation system provides a robust, secure foundation for all form validation needs while maintaining ease of use and integration with existing code.