import 'dart:async';
import 'package:logging/logging.dart';

import 'package:app_links/app_links.dart';

/// Handles deep link processing and routing for the CarNow application.
class DeepLinkHandler {
  static final _logger = Logger('DeepLinkHandler');
  static AppLinks? _appLinks;
  static StreamSubscription<Uri>? _linkSubscription;
  static bool _isInitialized = false;
  static bool _isInitializing = false;
  static Completer<void>? _isInitializedr;

  /// Initialize deep link handling system with improved error handling.
  static Future<void> initialize() async {
    // Prevent multiple simultaneous initializations
    if (_isInitialized) {
      _logger.info('Deep link handler already initialized');
      return;
    }

    if (_isInitializing) {
      _logger.info('Deep link handler initialization in progress, waiting...');
      await _isInitializedr?.future;
      return;
    }

    _isInitializing = true;
    _isInitializedr = Completer<void>();

    try {
      _logger.info('Initializing deep link handling');

      // Try to create AppLinks instance with retry logic
      if (!await _initializeAppLinks()) {
        _logger.warning('Failed to initialize AppLinks, deep linking disabled');
        _completeInitialization(false);
        return;
      }

      // Handle initial link with extended timeout and better error handling
      await _handleInitialLink();

      // Set up listener for future deep links
      _setupLinkListener();

      _completeInitialization(true);
      _logger.info('Deep link handling initialized successfully');
    } catch (e, stackTrace) {
      _logger.severe(
        'Critical error during deep link initialization: $e',
        e,
        stackTrace,
      );
      _completeInitialization(false);
    }
  }

  /// Initialize AppLinks with retry logic.
  static Future<bool> _initializeAppLinks() async {
    const maxRetries = 3;
    const retryDelay = Duration(milliseconds: 500);

    for (int attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        _appLinks = AppLinks();
        _logger.info(
          'AppLinks instance created successfully on attempt $attempt',
        );
        return true;
      } catch (e, stackTrace) {
        _logger.warning(
          'Failed to create AppLinks instance (attempt $attempt/$maxRetries): $e',
          e,
          stackTrace,
        );

        if (attempt < maxRetries) {
          await Future.delayed(retryDelay);
        }
      }
    }

    return false;
  }

  /// Complete initialization process.
  static void _completeInitialization(bool success) {
    _isInitialized = success;
    _isInitializing = false;

    if (!_isInitializedr!.isCompleted) {
      _isInitializedr!.complete();
    }
    _isInitializedr = null;
  }

  /// Handle the initial deep link when app starts with improved timeout and error handling.
  static Future<void> _handleInitialLink() async {
    if (_appLinks == null) {
      _logger.warning('AppLinks not available for initial link handling');
      return;
    }

    try {
      // Increased timeout and better error handling
      final initialUri = await _appLinks!.getInitialLink().timeout(
        const Duration(seconds: 5), // Increased from 2 to 5 seconds
        onTimeout: () {
          _logger.info(
            'Timeout getting initial app link after 5 seconds - continuing without initial link',
          );
          return null;
        },
      );

      if (initialUri != null) {
        _logger.info('Initial deep link detected: $initialUri');

        // Process the link with error handling
        _processDeepLinkSafely(initialUri.toString(), isInitialLink: true);
      } else {
        _logger.info('No initial deep link detected');
      }
    } catch (e, stackTrace) {
      _logger.warning('Error getting initial app link: $e', e, stackTrace);
    }
  }

  /// Set up listener for future deep links with improved error handling.
  static void _setupLinkListener() {
    if (_appLinks == null) {
      _logger.warning('AppLinks not available for listener setup');
      return;
    }

    _logger.info('Setting up listener for future deep links');

    try {
      _linkSubscription = _appLinks!.uriLinkStream.listen(
        (uri) {
          _logger.info('Received deep link: $uri');
          _processDeepLinkSafely(uri.toString(), isInitialLink: false);
        },
        onError: (Object error, StackTrace stackTrace) {
          _logger.warning(
            'Error from appLinks.uriLinkStream: $error',
            error,
            stackTrace,
          );
        },
        onDone: () {
          _logger.info('Deep link stream closed');
        },
      );

      _logger.info('Deep link listener setup completed');
    } catch (e, stackTrace) {
      _logger.warning(
        'Failed to initialize deep link listener: $e',
        e,
        stackTrace,
      );
    }
  }

  /// Process deep links safely with comprehensive error handling.
  static void _processDeepLinkSafely(
    String url, {
    required bool isInitialLink,
  }) {
    // Use scheduleMicrotask for better performance and error isolation
    scheduleMicrotask(() async {
      try {
        await _processDeepLink(url, isInitialLink: isInitialLink);
      } catch (e, stackTrace) {
        _logger.severe('Error in deep link processing: $e', e, stackTrace);
      }
    });
  }

  /// Process and route deep links to appropriate services with enhanced validation.
  static Future<void> _processDeepLink(
    String url, {
    required bool isInitialLink,
  }) async {
    _logger.info('Processing deep link (initial: $isInitialLink): $url');

    try {
      if (url.isEmpty) {
        _logger.warning('Empty deep link URL received, ignoring');
        return;
      }

      final uri = Uri.tryParse(url);
      if (uri == null) {
        _logger.warning('Invalid URI format: $url');
        return;
      }

      _logger
        ..info('Deep link parsed successfully')
        ..info('Link pattern: ${uri.scheme}://${uri.host}${uri.path}')
        ..info('Query parameters: ${uri.queryParameters}')
        ..info('Fragment: ${uri.fragment}');

      // Check if this is a Google authentication link
      if (_isGoogleAuthLink(uri)) {
        await _handleGoogleAuthLink(url, isInitialLink: isInitialLink);
      } else {
        _logger.info('Link not related to authentication - ignoring for now');
        // Handle other types of deep links here in the future
      }
    } catch (e, stackTrace) {
      _logger.severe('Error processing deep link: $e', e, stackTrace);
    }
  }

  /// Handle Google authentication deep links with improved reliability.
  static Future<void> _handleGoogleAuthLink(
    String url, {
    required bool isInitialLink,
  }) async {
    _logger.info('Detected Google auth deep link (initial: $isInitialLink)');

    try {
      // Process the authentication deep link
      // Note: Authentication is handled automatically via SimpleAuthSystem
      _logger.info('Processing OAuth deep link');

      // Give time for the auth process to complete
      if (isInitialLink) {
        await Future.delayed(const Duration(milliseconds: 500));
        _logger.info('Initial deep link processed');
      }
    } catch (e, stackTrace) {
      _logger.severe(
        'Error processing deep link: $e',
        e,
        stackTrace,
      );
    }
  }

  /// Wait for authentication system to be ready.
  static Future<void> _waitForAuthInitialization() async {
    // Give time for authentication system to initialize
    await Future.delayed(const Duration(milliseconds: 100));
    _logger.info('Authentication system ready for deep link processing.');
  }

  /// Log authentication result after initial deep link.
  static Future<void> _verifyAuthenticationSuccess() async {
    _logger.info('Deep link authentication process completed');

    try {
      // Give some time for the auth process to complete
      await Future.delayed(const Duration(seconds: 1));
      
      // Authentication verification is handled by SimpleAuthSystem
      _logger.info('Authentication state will be updated by SimpleAuthSystem');
    } catch (e, stackTrace) {
      _logger.warning(
        'Error verifying authentication success: $e',
        e,
        stackTrace,
      );
    }
  }

  /// Check if the URI is a Google authentication link with enhanced validation.
  static bool _isGoogleAuthLink(Uri uri) {
    try {
      // Check for direct app schema match
      final matchesAppSchema =
          uri.scheme == 'com.example.carnowx' && 
          (uri.host == 'login-callback' || uri.host == 'auth-callback');

      if (matchesAppSchema) {
        _logger.info('✅ Direct app schema match for Google auth: ${uri.scheme}://${uri.host}');
        return true;
      }

      // Check for OAuth parameters in query string
      final hasAuthParams =
          uri.queryParameters.containsKey('access_token') ||
          uri.queryParameters.containsKey('refresh_token') ||
          uri.queryParameters.containsKey('code') ||
          uri.queryParameters.containsKey('state') ||
          uri.queryParameters.containsKey('token_type');

      if (hasAuthParams) {
        _logger.info('✅ Auth parameters detected in query string');
        return true;
      }

      // Check fragment for auth data
      if (uri.fragment.isNotEmpty) {
        final fragmentContainsAuth =
            uri.fragment.contains('access_token=') ||
            uri.fragment.contains('token_type=') ||
            uri.fragment.contains('state=') ||
            uri.fragment.contains('refresh_token=');

        if (fragmentContainsAuth) {
          _logger.info('✅ Auth data detected in URI fragment');
          return true;
        }
      }

      // Check for Supabase callback patterns
      final isSupabaseCallback =
          uri.path.contains('/auth/v1/callback') ||
          uri.path.contains('/auth/callback') ||
          uri.path.contains('/auth/v1/oauth/callback');

      if (isSupabaseCallback) {
        _logger.info('✅ Supabase auth callback path detected: ${uri.path}');
        return true;
      }

      // Additional check for common OAuth error patterns
      final hasErrorParams =
          uri.queryParameters.containsKey('error') ||
          uri.queryParameters.containsKey('error_description') ||
          uri.queryParameters.containsKey('error_code');

      if (hasErrorParams) {
        _logger.warning('⚠️ OAuth error parameters detected');
        return true;
      }

      return false;
    } catch (e, stackTrace) {
      _logger.warning('Error in _isGoogleAuthLink: $e', e, stackTrace);
      return false;
    }
  }

  /// Get initialization status.
  static bool get isInitialized => _isInitialized;

  /// Dispose deep link handler resources with improved cleanup.
  static void dispose() {
    if (!_isInitialized && !_isInitializing) {
      _logger.info('Deep link handler was not initialized, nothing to dispose');
      return;
    }

    _logger.info('Disposing deep link handler resources...');

    try {
      _linkSubscription?.cancel();
      _linkSubscription = null;
      _logger.info('Cancelled link subscription successfully');

      // Complete any pending initialization
      if (_isInitializedr != null && !_isInitializedr!.isCompleted) {
        _isInitializedr!.complete();
      }
      _isInitializedr = null;

      _appLinks = null;
      _isInitialized = false;
      _isInitializing = false;

      _logger.info('Deep link handler disposed successfully');
    } catch (e, stackTrace) {
      _logger.warning(
        'Error during deep link handler disposal: $e',
        e,
        stackTrace,
      );
    }
  }

  /// Safely process deep links with additional validation.
  static Future<void> processLink(String link) async {
    try {
      await _processDeepLink(link, isInitialLink: false);
    } catch (e, stackTrace) {
      _logger.severe('Error processing link: $link. Error: $e', e, stackTrace);
    }
  }
}
