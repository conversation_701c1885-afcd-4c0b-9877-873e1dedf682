import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:logger/logger.dart';

/// <PERSON>les optimized image precaching for the CarNow application.
/// This service uses simple scheduling to prevent frame drops
/// and performance issues during app startup.
class ImagePrecachingService {
  static final _logger = Logger();
  static bool _isPrecachingStarted = false;
  static final Set<String> _precachedImages = <String>{};

  /// Minimal list of only critical images for immediate use
  static const _criticalImages = [
    'assets/images/logo.png', // Only logo for splash/loading screens
  ];

  /// Additional images that can be loaded later
  static const _secondaryImages = [
    'assets/images/placeholder.png',
    // Add other non-critical images here
  ];

  /// Schedule simplified image precaching with minimal performance impact.
  static void scheduleOptimizedImagePrecaching(BuildContext context) {
    // Prevent multiple precaching initiations
    if (_isPrecachingStarted) {
      _logger.d('Image precaching already started, skipping');
      return;
    }

    _logger.d('Starting simplified image precaching service');
    _isPrecachingStarted = true;

    // Wait 10 seconds after app startup to begin precaching (less contention during first frames)
    Future.delayed(const Duration(seconds: 10), () {
      if (!context.mounted) return;
      _precacheCriticalImagesSimple(context);
    });

    // Schedule secondary images for much later
    Future.delayed(const Duration(seconds: 30), () {
      if (!context.mounted) return;
      _precacheSecondaryImagesSimple(context);
    });
  }

  /// Precache critical images with simple scheduling
  static void _precacheCriticalImagesSimple(BuildContext context) {
    for (int i = 0; i < _criticalImages.length; i++) {
      final imagePath = _criticalImages[i];

      // Skip if already precached
      if (_precachedImages.contains(imagePath)) {
        continue;
      }

      // Simple delay between images
      Future.delayed(Duration(seconds: i), () {
        if (!context.mounted) return;
        _precacheSingleImageSimple(imagePath, context, isCritical: true);
      });
    }
  }

  /// Precache secondary images with longer delays
  static void _precacheSecondaryImagesSimple(BuildContext context) {
    for (int i = 0; i < _secondaryImages.length; i++) {
      final imagePath = _secondaryImages[i];

      // Skip if already precached
      if (_precachedImages.contains(imagePath)) {
        continue;
      }

      // Much longer delays between secondary images
      Future.delayed(Duration(seconds: 10 + (i * 5)), () {
        if (!context.mounted) return;
        _precacheSingleImageSimple(imagePath, context);
      });
    }
  }

  /// Simple image precaching with basic error handling
  static void _precacheSingleImageSimple(
    String imagePath,
    BuildContext context, {
    bool isCritical = false,
  }) {
    // Prevent multiple context checks and add defensive programming
    try {
      // Early return if context is not mounted or image already cached
      if (!context.mounted || _precachedImages.contains(imagePath)) {
        return;
      }

      final imageProvider = AssetImage(imagePath);

      // Use unawaited to prevent blocking and add timeout
      Future.delayed(const Duration(milliseconds: 100), () async {
        if (!context.mounted) return;

        try {
          await precacheImage(imageProvider, context).timeout(
            const Duration(seconds: 5),
          ); // Add timeout to prevent hanging

          if (context.mounted) {
            _precachedImages.add(imagePath);
            final type = isCritical ? 'critical' : 'secondary';
            _logger.d('Precached $type image: $imagePath');
          }
        } catch (error) {
          final type = isCritical ? 'critical' : 'secondary';
          _logger.w(
            'Failed to precache $type image: $imagePath, Error: $error',
          );
        }
      });
    } catch (e) {
      _logger.w('Exception while precaching image: $imagePath, Error: $e');
    }
  }

  /// Precache network images with advanced scheduling and memory optimization
  static Future<void> precacheNetworkImageOptimized(
    String imageUrl,
    BuildContext context, {
    double? maxWidth,
    double? maxHeight,
    bool isLowPriority = true,
  }) async {
    // Skip if already precached
    if (_precachedImages.contains(imageUrl)) {
      _logger.d('Network image already precached, skipping: $imageUrl');
      return;
    }

    try {
      // Use scheduler for network images too
      if (isLowPriority) {
        return _scheduleNetworkImagePrecaching(
          imageUrl,
          context,
          maxWidth,
          maxHeight,
        );
      }

      await _precacheNetworkImageDirect(imageUrl, context, maxWidth, maxHeight);
    } catch (e) {
      _logger.w('Failed to precache network image: $imageUrl, Error: $e');
    }
  }

  /// Schedule network image precaching with proper priority
  static void _scheduleNetworkImagePrecaching(
    String imageUrl,
    BuildContext context,
    double? maxWidth,
    double? maxHeight,
  ) {
    SchedulerBinding.instance.scheduleTask(() async {
      if (!context.mounted) return;
      await _precacheNetworkImageDirect(imageUrl, context, maxWidth, maxHeight);
    }, Priority.idle);
  }

  /// Direct network image precaching with optimizations
  static Future<void> _precacheNetworkImageDirect(
    String imageUrl,
    BuildContext context,
    double? maxWidth,
    double? maxHeight,
  ) async {
    try {
      final imageProvider = NetworkImage(imageUrl);

      // Only precache if context is still mounted
      if (context.mounted) {
        await precacheImage(imageProvider, context);
        _precachedImages.add(imageUrl);
        _logger.d('Precached network image: $imageUrl');
      }
    } catch (e) {
      _logger.w(
        'Failed to precache network image directly: $imageUrl, Error: $e',
      );
    }
  }

  /// Precache multiple network images in controlled batches with advanced scheduling
  static Future<void> precacheNetworkImagesBatch(
    List<String> imageUrls,
    BuildContext context, {
    int batchSize = 1, // Reduced batch size for better performance
    Duration batchDelay = const Duration(seconds: 3), // Increased delay
  }) async {
    // Filter out already precached images
    final urlsToProcess = imageUrls
        .where((url) => !_precachedImages.contains(url))
        .toList();

    if (urlsToProcess.isEmpty) {
      _logger.d('All network images already precached');
      return;
    }

    for (int i = 0; i < urlsToProcess.length; i += batchSize) {
      if (!context.mounted) return;

      final batch = urlsToProcess.skip(i).take(batchSize);

      // Schedule each batch with idle priority
      // ignore: unawaited_futures
      SchedulerBinding.instance.scheduleTask(() async {
        if (!context.mounted) return;

        // Process batch sequentially to avoid overwhelming the system
        for (final url in batch) {
          if (!context.mounted) return;
          await precacheNetworkImageOptimized(
            url,
            context,
            isLowPriority: false,
          );

          // Small delay between images in the same batch
          await Future.delayed(const Duration(milliseconds: 100));
        }
      }, Priority.idle);

      // Wait between batches to avoid overwhelming the system
      if (i + batchSize < urlsToProcess.length) {
        await Future.delayed(batchDelay);
      }
    }
  }

  /// Clear image cache to free memory when needed
  static void clearImageCache() {
    try {
      PaintingBinding.instance.imageCache.clear();
      _precachedImages.clear();
      _logger.i('Image cache and tracking cleared successfully');
    } catch (e) {
      _logger.w('Failed to clear image cache: $e');
    }
  }

  /// Get image cache statistics with performance metrics
  static Map<String, dynamic> getCacheStats() {
    final cache = PaintingBinding.instance.imageCache;
    return {
      'currentSize': cache.currentSize,
      'currentSizeBytes': cache.currentSizeBytes,
      'maximumSize': cache.maximumSize,
      'maximumSizeBytes': cache.maximumSizeBytes,
      'cacheUtilization': cache.currentSize / cache.maximumSize,
      'precachedImagesCount': _precachedImages.length,
      'precachedImages': _precachedImages.toList(),
    };
  }

  /// Adaptive precaching based on system performance
  static void adaptivePrecaching(BuildContext context) {
    final memoryPressure = _calculateMemoryPressure();

    if (memoryPressure > 0.8) {
      _logger.w(
        'High memory pressure detected, skipping additional precaching',
      );
      return;
    }

    if (memoryPressure > 0.6) {
      _logger.i('Moderate memory pressure, using slower precaching');
      // Use longer delays and smaller batches
      precacheNetworkImagesBatch(
        [], // Empty list for now, can be populated with URLs
        context,
        batchDelay: const Duration(seconds: 5),
      );
    } else {
      _logger.i('Low memory pressure, using normal precaching speed');
      // Use normal precaching parameters
    }
  }

  /// Reset precaching state (for testing)
  static void reset() {
    _isPrecachingStarted = false;
    _precachedImages.clear();
    _logger.d('Image precaching state reset');
  }

  /// Check if image precaching has been started
  static bool get isStarted => _isPrecachingStarted;

  /// Check if a specific image has been precached
  static bool isPrecached(String imagePath) =>
      _precachedImages.contains(imagePath);

  /// Get list of precached images
  static List<String> get precachedImages => _precachedImages.toList();

  /// Get comprehensive status information for debugging
  static Map<String, dynamic> getStatus() {
    return {
      'isStarted': _isPrecachingStarted,
      'precachedImagesCount': _precachedImages.length,
      'precachedImages': _precachedImages.toList(),
      'criticalImages': _criticalImages,
      'secondaryImages': _secondaryImages,
      'timestamp': DateTime.now().toIso8601String(),
      'cacheStats': getCacheStats(),
    };
  }

  /// Print detailed status to console (for debugging)
  static void printStatus() {
    final status = getStatus();
    _logger.i('=== ImagePrecachingService Status ===');
    _logger.i('Started: ${status['isStarted']}');
    _logger.i('Precached images count: ${status['precachedImagesCount']}');
    _logger.i(
      'Cache utilization: ${getCacheStats()['cacheUtilization']?.toStringAsFixed(2)}',
    );
    _logger.i('====================================');
  }

  /// Calculate memory pressure based on cache usage
  static double _calculateMemoryPressure() {
    final cache = PaintingBinding.instance.imageCache;
    if (cache.maximumSizeBytes == 0) return 0;
    return cache.currentSizeBytes / cache.maximumSizeBytes;
  }
}
