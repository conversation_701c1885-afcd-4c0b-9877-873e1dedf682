import 'package:flutter/material.dart';

import '../widgets/skeleton_loading.dart';
import '../theme/app_theme.dart';

/// Simple error app widget displayed during critical initialization errors.
///
/// This widget is shown if the application encounters an unrecoverable error
/// during its startup sequence.
class ErrorApp extends StatelessWidget {
  const ErrorApp({required this.message, super.key});
  final String message;

  @override
  Widget build(BuildContext context) => MaterialApp(
    home: Scaffold(
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(AppTheme.spacingL),
          child: Text(
            'Error: $message\nPlease restart the application.',
            style: const TextStyle(color: Colors.red, fontSize: 16),
            textAlign: TextAlign.center,
          ),
        ),
      ),
    ),
    debugShowCheckedModeBanner: false,
  );
}

/// Loading app widget displayed while the app is initializing.
class LoadingApp extends StatelessWidget {
  const LoadingApp({super.key});

  @override
  Widget build(BuildContext context) => const MaterialApp(
    home: Scaffold(body: SafeArea(child: HomeScreenSkeleton())),
    debugShowCheckedModeBanner: false,
  );
}

/// Error app widget with better error display.
class AppErrorWidget extends StatelessWidget {
  const AppErrorWidget({required this.errorMessage, super.key});
  final String errorMessage;

  @override
  Widget build(BuildContext context) => MaterialApp(
    home: Scaffold(
      body: SafeArea(
        child: Center(
          child: SelectableText.rich(
            TextSpan(
              children: [
                const TextSpan(
                  text: 'فشل تحميل التطبيق:\n',
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 18),
                ),
                TextSpan(
                  text: errorMessage,
                  style: const TextStyle(height: 1.5),
                ),
              ],
            ),
            style: const TextStyle(color: Colors.red),
            textAlign: TextAlign.center,
          ),
        ),
      ),
    ),
    debugShowCheckedModeBanner: false,
  );
}
