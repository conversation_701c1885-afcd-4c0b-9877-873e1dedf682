import 'package:riverpod/riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../models/order_model.dart';
import '../models/product_model.dart';
import '../auth/unified_auth_provider.dart';
import '../auth/auth_models.dart';
import '../networking/simple_api_client.dart';

part 'app_providers.g.dart';

/// Products provider with proper error handling
@riverpod
Future<List<ProductModel>> products(
  Ref ref, {
  String? category,
  String? brand,
  double? minPrice,
  double? maxPrice,
  String? search,
}) async {
  final apiClient = ref.read(simpleApiClientProvider);
  
  // Build query parameters
  final queryParams = <String, dynamic>{
    if (category != null) 'category': category,
    if (brand != null) 'brand': brand,
    if (minPrice != null) 'min_price': minPrice,
    if (maxPrice != null) 'max_price': maxPrice,
    if (search != null) 'search': search,
  };
  
  final response = await apiClient.get<Map<String, dynamic>>(
    '/products',
    queryParameters: queryParams,
  );
  
  if (!response.isSuccess || response.data == null) {
    throw Exception('Failed to fetch products: ${response.error}');
  }
  
  final data = response.data!;
  final productsData = data['data'] as List?;
  
  if (productsData == null) {
    return [];
  }
  
  return productsData
      .map((item) => ProductModel.fromJson(item as Map<String, dynamic>))
      .toList();
}

/// Categories provider
@riverpod
Future<List<String>> categories(Ref ref) async {
  // TEMPORARY FIX: Return static categories until backend is fixed
  // TODO: Switch back to API call when backend categories endpoint is working
  /*
  final apiClient = ref.read(simpleApiClientProvider);
  
  final response = await apiClient.get<Map<String, dynamic>>('/categories');
  
  if (!response.isSuccess || response.data == null) {
    throw Exception('Failed to fetch categories: ${response.error}');
  }
  
  final data = response.data!;
  final categoriesData = data['data'] as List?;
  
  if (categoriesData == null) {
    return [];
  }
  
  return categoriesData.map((item) => item['name'] as String).toList();
  */
  
  // TEMPORARY: Static categories based on parts_categories table data
  return [
    'Engine Parts',
    'Brake System',
    'Suspension',
    'Electrical',
    'Body Parts',
  ];
}

/// Brands provider
@riverpod
Future<List<String>> brands(Ref ref) async {
  final apiClient = ref.read(simpleApiClientProvider);
  
  final response = await apiClient.get<Map<String, dynamic>>('/brands');
  
  if (!response.isSuccess || response.data == null) {
    throw Exception('Failed to fetch brands: ${response.error}');
  }
  
  final data = response.data!;
  final brandsData = data['data'] as List?;
  
  if (brandsData == null) {
    return [];
  }
  
  return brandsData.map((item) => item['name'] as String).toList();
}

/// Product by ID provider
@riverpod
Future<ProductModel?> productById(Ref ref, String id) async {
  final apiClient = ref.read(simpleApiClientProvider);
  
  final response = await apiClient.get<Map<String, dynamic>>('/products/$id');
  
  if (!response.isSuccess || response.data == null) {
    return null;
  }
  
  final data = response.data!;
  final productData = data['data'] as Map<String, dynamic>?;
  
  if (productData == null) {
    return null;
  }
  
  return ProductModel.fromJson(productData);
}

/// Featured products provider
@riverpod
Future<List<ProductModel>> featuredProducts(Ref ref) async {
  final apiClient = ref.read(simpleApiClientProvider);
  
  final response = await apiClient.get<Map<String, dynamic>>('/products/featured');
  
  if (!response.isSuccess || response.data == null) {
    return [];
  }
  
  final data = response.data!;
  final productsData = data['data'] as List?;
  
  if (productsData == null) {
    return [];
  }
  
  return productsData
      .map((item) => ProductModel.fromJson(item as Map<String, dynamic>))
      .toList();
}

/// User orders provider
@riverpod
Future<List<OrderModel>> userOrders(Ref ref) async {
  final authState = ref.watch(unifiedAuthProviderProvider);
  
  // Only fetch orders if user is authenticated
  if (authState is! AuthStateAuthenticated) {
    return [];
  }
  
  final apiClient = ref.read(simpleApiClientProvider);
  
  final response = await apiClient.get<Map<String, dynamic>>('/orders');
  
  if (!response.isSuccess || response.data == null) {
    throw Exception('Failed to fetch orders: ${response.error}');
  }
  
  final data = response.data!;
  final ordersData = data['data'] as List?;
  
  if (ordersData == null) {
    return [];
  }
  
  return ordersData
      .map((item) => OrderModel.fromJson(item as Map<String, dynamic>))
      .toList();
}

/// Order status provider
@riverpod
Future<String> orderStatus(Ref ref, String orderId) async {
  final apiClient = ref.read(simpleApiClientProvider);
  
  final response = await apiClient.get<Map<String, dynamic>>('/orders/$orderId/status');
  
  if (!response.isSuccess || response.data == null) {
    throw Exception('Failed to fetch order status: ${response.error}');
  }
  
  final data = response.data!;
  return data['status'] as String? ?? 'unknown';
}
