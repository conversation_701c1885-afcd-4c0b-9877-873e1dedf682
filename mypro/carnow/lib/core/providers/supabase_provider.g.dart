// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'supabase_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$authTokenHash() => r'142cb4166564e89e2bd95c194cfb22484e3dd956';

/// DEPRECATED: Auth Providers - Forever Plan Architecture Migration
/// Flutter (UI Only) → Go API → Supabase (Data Only)
///
/// These providers are DEPRECATED and should use UnifiedAuthProvider instead
/// ⚠️  WARNING: Migrate to UnifiedAuthProvider for all authentication operations
/// ✅ USE UnifiedAuthProvider for all auth state management
/// DEPRECATED: Get current auth token (JWT) for API calls
/// Use currentAccessTokenProvider instead
///
/// Copied from [authToken].
@ProviderFor(authToken)
final authTokenProvider = AutoDisposeProvider<String?>.internal(
  authToken,
  name: r'authTokenProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$authTokenHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef AuthTokenRef = AutoDisposeProviderRef<String?>;
String _$currentUserIdHash() => r'e1864666fe16a5f6c53838aaed8d25e992d1aad3';

/// DEPRECATED: Current user ID from auth session
/// Use currentUserProvider instead
///
/// Copied from [currentUserId].
@ProviderFor(currentUserId)
final currentUserIdProvider = AutoDisposeProvider<String?>.internal(
  currentUserId,
  name: r'currentUserIdProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$currentUserIdHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CurrentUserIdRef = AutoDisposeProviderRef<String?>;
String _$currentUserHash() => r'298a4589453b593df0dea4505df2e96d983ff70b';

/// DEPRECATED: Simple auth state provider
/// Use UnifiedAuthProvider directly instead
///
/// Copied from [currentUser].
@ProviderFor(currentUser)
final currentUserProvider = AutoDisposeProvider<Object?>.internal(
  currentUser,
  name: r'currentUserProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$currentUserHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CurrentUserRef = AutoDisposeProviderRef<Object?>;
String _$authStateChangesHash() => r'ed1dd97a52975c78cfa9d62e5bf18cb460b1d6fb';

/// DEPRECATED: Auth state changes stream
/// Use UnifiedAuthProvider.authStateChanges instead
///
/// Copied from [authStateChanges].
@ProviderFor(authStateChanges)
final authStateChangesProvider = AutoDisposeStreamProvider<Object>.internal(
  authStateChanges,
  name: r'authStateChangesProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$authStateChangesHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef AuthStateChangesRef = AutoDisposeStreamProviderRef<Object>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
