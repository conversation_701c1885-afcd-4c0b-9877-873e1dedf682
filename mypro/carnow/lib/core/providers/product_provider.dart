import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../networking/simple_api_client.dart';
import '../models/product_model.dart';

part 'product_provider.g.dart';

@riverpod
Future<List<ProductModel>> products(Ref ref) async {
  final apiClient = ref.watch(simpleApiClientProvider);

  try {
    final response = await apiClient.getApi('/products');
    final data = response.data as List;
    return data
        .map<ProductModel>((json) => ProductModel.fromJson(json))
        .toList();
  } catch (e) {
    throw Exception('Failed to fetch products: $e');
  }
}

// New provider: Fetches products with pagination parameters
@riverpod
Future<List<ProductModel>> paginatedProducts(
  Ref ref,
  Map<String, int> params,
) async {
  final apiClient = ref.watch(simpleApiClientProvider);
  final page = params['page'] ?? 0;
  final pageSize = params['pageSize'] ?? 20;

  try {
    final response = await apiClient.getApi(
      '/products',
      queryParameters: {'page': page, 'pageSize': pageSize},
    );
    final data = response.data as List;
    return data
        .map<ProductModel>((json) => ProductModel.fromJson(json))
        .toList();
  } catch (e) {
    throw Exception('Failed to fetch paginated products: $e');
  }
}

@riverpod
Future<ProductModel> product(Ref ref, String productId) async {
  final apiClient = ref.watch(simpleApiClientProvider);

  try {
    final response = await apiClient.getApi('/products/$productId');
    return ProductModel.fromJson(response.data);
  } catch (e) {
    throw Exception('Failed to fetch product: $e');
  }
}

@riverpod
Future<List<ProductModel>> auctionProducts(Ref ref) async {
  final apiClient = ref.watch(simpleApiClientProvider);

  try {
    final response = await apiClient.getApi('/products/auction');
    final data = response.data as List;
    return data
        .map<ProductModel>((json) => ProductModel.fromJson(json))
        .toList();
  } catch (e) {
    throw Exception('Failed to fetch auction products: $e');
  }
}

// New provider: Fetches auction products with pagination
@riverpod
Future<List<ProductModel>> paginatedAuctionProducts(
  Ref ref,
  Map<String, int> params,
) async {
  final apiClient = ref.watch(simpleApiClientProvider);
  final page = params['page'] ?? 0;
  final pageSize = params['pageSize'] ?? 20;

  try {
    final response = await apiClient.getApi(
      '/products/auction',
      queryParameters: {'page': page, 'pageSize': pageSize},
    );
    final data = response.data as List;
    return data
        .map<ProductModel>((json) => ProductModel.fromJson(json))
        .toList();
  } catch (e) {
    throw Exception('Failed to fetch paginated auction products: $e');
  }
}

@riverpod
Future<List<ProductModel>> productsByCategory(
  Ref ref,
  String categoryId,
) async {
  final apiClient = ref.watch(simpleApiClientProvider);

  try {
    final response = await apiClient.getApi('/products/category/$categoryId');
    final data = response.data as List;
    return data
        .map<ProductModel>((json) => ProductModel.fromJson(json))
        .toList();
  } catch (e) {
    throw Exception('Failed to fetch products by category: $e');
  }
}

// New provider: Fetches products by category with pagination
@riverpod
Future<List<ProductModel>> paginatedProductsByCategory(
  Ref ref,
  Map<String, dynamic> params,
) async {
  final apiClient = ref.watch(simpleApiClientProvider);
  final categoryId = params['categoryId'] as String;
  final page = params['page'] as int? ?? 0;
  final pageSize = params['pageSize'] as int? ?? 20;

  try {
    final response = await apiClient.getApi(
      '/products/category/$categoryId',
      queryParameters: {'page': page, 'pageSize': pageSize},
    );
    final data = response.data as List;
    return data
        .map<ProductModel>((json) => ProductModel.fromJson(json))
        .toList();
  } catch (e) {
    throw Exception('Failed to fetch paginated products by category: $e');
  }
}
