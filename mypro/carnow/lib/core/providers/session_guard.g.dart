// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'session_guard.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$sessionGuardHash() => r'ba1878ac4396ea8102e37662275219225f4c72df';

/// DEPRECATED: Session Guard - Forever Plan Architecture Migration
/// This provider has been replaced by UnifiedAuthProvider session management
///
/// Forever Plan: Use UnifiedAuthProvider for all auth operations
/// The session validation is now handled automatically by UnifiedAuthProvider
///
/// Copied from [SessionGuard].
@ProviderFor(SessionGuard)
final sessionGuardProvider = AsyncNotifierProvider<SessionGuard, void>.internal(
  SessionGuard.new,
  name: r'sessionGuardProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$sessionGuardHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SessionGuard = AsyncNotifier<void>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
