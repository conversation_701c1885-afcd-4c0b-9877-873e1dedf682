import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/logger.dart';

final _logger = Logger(
  printer: PrettyPrinter(methodCount: 0, errorMethodCount: 5, lineLength: 50),
);

/// حالة البيانات المقسمة
class PaginatedState<T> {
  const PaginatedState({
    this.items = const [],
    this.isLoading = false,
    this.hasMore = true,
    this.error,
    this.currentPage = 0,
    this.totalCount = 0,
  });
  final List<T> items;
  final bool isLoading;
  final bool hasMore;
  final String? error;
  final int currentPage;
  final int totalCount;

  PaginatedState<T> copyWith({
    List<T>? items,
    bool? isLoading,
    bool? hasMore,
    String? error,
    int? currentPage,
    int? totalCount,
  }) => PaginatedState<T>(
    items: items ?? this.items,
    isLoading: isLoading ?? this.isLoading,
    hasMore: hasMore ?? this.hasMore,
    error: error,
    currentPage: currentPage ?? this.currentPage,
    totalCount: totalCount ?? this.totalCount,
  );

  bool get isEmpty => items.isEmpty && !isLoading;
  bool get isNotEmpty => items.isNotEmpty;
}

/// نتيجة البيانات المقسمة
class PaginatedResult<T> {
  const PaginatedResult({
    required this.items,
    required this.totalCount,
    required this.hasMore,
  });
  final List<T> items;
  final int totalCount;
  final bool hasMore;
}

/// مزود البيانات المقسمة
class PaginatedNotifier<T> extends StateNotifier<PaginatedState<T>> {
  PaginatedNotifier(this._fetcher, {int pageSize = 20})
    : _pageSize = pageSize,
      super(const PaginatedState());
  final Future<PaginatedResult<T>> Function(int page, int limit) _fetcher;
  final int _pageSize;

  /// تحميل الصفحة الأولى
  Future<void> loadFirst() async {
    if (state.isLoading) return;

    state = state.copyWith(isLoading: true, error: null);

    try {
      final result = await _fetcher(1, _pageSize);

      state = state.copyWith(
        items: result.items,
        isLoading: false,
        hasMore: result.hasMore,
        currentPage: 1,
        totalCount: result.totalCount,
      );

      _logger.d('Loaded first page: ${result.items.length} items');
    } catch (e, stackTrace) {
      _logger.e('Failed to load first page', error: e, stackTrace: stackTrace);
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  /// تحميل الصفحة التالية
  Future<void> loadNext() async {
    if (state.isLoading || !state.hasMore) return;

    state = state.copyWith(isLoading: true, error: null);

    try {
      final nextPage = state.currentPage + 1;
      final result = await _fetcher(nextPage, _pageSize);

      state = state.copyWith(
        items: [...state.items, ...result.items],
        isLoading: false,
        hasMore: result.hasMore,
        currentPage: nextPage,
        totalCount: result.totalCount,
      );

      _logger.d('Loaded page $nextPage: ${result.items.length} new items');
    } catch (e, stackTrace) {
      _logger.e('Failed to load next page', error: e, stackTrace: stackTrace);
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  /// إعادة تحميل البيانات
  Future<void> refresh() async {
    state = const PaginatedState();
    await loadFirst();
  }

  /// إضافة عنصر جديد
  void addItem(T item) {
    state = state.copyWith(
      items: [item, ...state.items],
      totalCount: state.totalCount + 1,
    );
  }

  /// تحديث عنصر موجود
  void updateItem(T item, bool Function(T) predicate) {
    final items = state.items
        .map((existing) => predicate(existing) ? item : existing)
        .toList();

    state = state.copyWith(items: items);
  }

  /// حذف عنصر
  void removeItem(bool Function(T) predicate) {
    final items = state.items.where((item) => !predicate(item)).toList();

    state = state.copyWith(
      items: items,
      totalCount: state.totalCount - (state.items.length - items.length),
    );
  }
}

/// Factory لإنشاء مزودي البيانات المقسمة
class PaginatedProviderFactory {
  static StateNotifierProvider<PaginatedNotifier<T>, PaginatedState<T>>
  create<T>(
    String name,
    Future<PaginatedResult<T>> Function(int page, int limit) fetcher, {
    int pageSize = 20,
  }) => StateNotifierProvider<PaginatedNotifier<T>, PaginatedState<T>>(
    (ref) => PaginatedNotifier<T>(fetcher, pageSize: pageSize),
    name: name,
  );
}

/// مزود مساعد للبحث المقسم
class SearchablePaginatedNotifier<T> extends PaginatedNotifier<T> {
  SearchablePaginatedNotifier(this._searchFetcher, {int pageSize = 20})
    : super(
        (page, limit) => _searchFetcher('', page, limit),
        pageSize: pageSize,
      );
  String _currentQuery = '';
  final Future<PaginatedResult<T>> Function(String query, int page, int limit)
  _searchFetcher;

  /// البحث مع إعادة تعيين البيانات
  Future<void> search(String query) async {
    if (_currentQuery == query && state.isNotEmpty) return;

    _currentQuery = query;
    state = const PaginatedState();

    if (query.isEmpty) {
      await loadFirst();
      return;
    }

    state = state.copyWith(isLoading: true, error: null);

    try {
      final result = await _searchFetcher(query, 1, _pageSize);

      state = state.copyWith(
        items: result.items,
        isLoading: false,
        hasMore: result.hasMore,
        currentPage: 1,
        totalCount: result.totalCount,
      );

      _logger.d('Search completed for "$query": ${result.items.length} items');
    } catch (e, stackTrace) {
      _logger.e('Search failed for "$query"', error: e, stackTrace: stackTrace);
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  /// تحميل المزيد من نتائج البحث
  @override
  Future<void> loadNext() async {
    if (state.isLoading || !state.hasMore) return;

    state = state.copyWith(isLoading: true, error: null);

    try {
      final nextPage = state.currentPage + 1;
      final result = await _searchFetcher(_currentQuery, nextPage, _pageSize);

      state = state.copyWith(
        items: [...state.items, ...result.items],
        isLoading: false,
        hasMore: result.hasMore,
        currentPage: nextPage,
        totalCount: result.totalCount,
      );

      _logger.d(
        'Loaded search page $nextPage: ${result.items.length} new items',
      );
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to load next search page',
        error: e,
        stackTrace: stackTrace,
      );
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  String get currentQuery => _currentQuery;
}

/// Factory للبحث المقسم
class SearchablePaginatedProviderFactory {
  static StateNotifierProvider<
    SearchablePaginatedNotifier<T>,
    PaginatedState<T>
  >
  create<T>(
    String name,
    Future<PaginatedResult<T>> Function(String query, int page, int limit)
    fetcher, {
    int pageSize = 20,
  }) =>
      StateNotifierProvider<SearchablePaginatedNotifier<T>, PaginatedState<T>>(
        (ref) => SearchablePaginatedNotifier<T>(fetcher, pageSize: pageSize),
        name: name,
      );
}
