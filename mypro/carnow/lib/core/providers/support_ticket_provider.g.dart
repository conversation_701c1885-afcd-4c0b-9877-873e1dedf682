// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'support_ticket_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$userSupportTicketsHash() =>
    r'271096908ec4ecd9f889b775479c0579e0c87da3';

/// Clean Support Ticket Providers - Forever Plan Architecture
/// Flutter (UI Only) → Go API → Supabase (Data Only)
///
/// FIXED: Removed direct Supabase imports and complex patterns
/// ✅ Uses SimpleApiClient ONLY
/// ✅ Uses SimpleAuthSystem for auth
/// ✅ Simple @riverpod patterns only
/// User's support tickets provider - simple and clean
///
/// Copied from [userSupportTickets].
@ProviderFor(userSupportTickets)
final userSupportTicketsProvider =
    AutoDisposeFutureProvider<List<TicketModel>>.internal(
      userSupportTickets,
      name: r'userSupportTicketsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$userSupportTicketsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef UserSupportTicketsRef = AutoDisposeFutureProviderRef<List<TicketModel>>;
String _$supportTicketByIdHash() => r'aa929a57c69985c9fb02ea0ff2192d24a962b1e9';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// Single support ticket by ID provider
///
/// Copied from [supportTicketById].
@ProviderFor(supportTicketById)
const supportTicketByIdProvider = SupportTicketByIdFamily();

/// Single support ticket by ID provider
///
/// Copied from [supportTicketById].
class SupportTicketByIdFamily extends Family<AsyncValue<TicketModel?>> {
  /// Single support ticket by ID provider
  ///
  /// Copied from [supportTicketById].
  const SupportTicketByIdFamily();

  /// Single support ticket by ID provider
  ///
  /// Copied from [supportTicketById].
  SupportTicketByIdProvider call(String ticketId) {
    return SupportTicketByIdProvider(ticketId);
  }

  @override
  SupportTicketByIdProvider getProviderOverride(
    covariant SupportTicketByIdProvider provider,
  ) {
    return call(provider.ticketId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'supportTicketByIdProvider';
}

/// Single support ticket by ID provider
///
/// Copied from [supportTicketById].
class SupportTicketByIdProvider
    extends AutoDisposeFutureProvider<TicketModel?> {
  /// Single support ticket by ID provider
  ///
  /// Copied from [supportTicketById].
  SupportTicketByIdProvider(String ticketId)
    : this._internal(
        (ref) => supportTicketById(ref as SupportTicketByIdRef, ticketId),
        from: supportTicketByIdProvider,
        name: r'supportTicketByIdProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$supportTicketByIdHash,
        dependencies: SupportTicketByIdFamily._dependencies,
        allTransitiveDependencies:
            SupportTicketByIdFamily._allTransitiveDependencies,
        ticketId: ticketId,
      );

  SupportTicketByIdProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.ticketId,
  }) : super.internal();

  final String ticketId;

  @override
  Override overrideWith(
    FutureOr<TicketModel?> Function(SupportTicketByIdRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: SupportTicketByIdProvider._internal(
        (ref) => create(ref as SupportTicketByIdRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        ticketId: ticketId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<TicketModel?> createElement() {
    return _SupportTicketByIdProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is SupportTicketByIdProvider && other.ticketId == ticketId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, ticketId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin SupportTicketByIdRef on AutoDisposeFutureProviderRef<TicketModel?> {
  /// The parameter `ticketId` of this provider.
  String get ticketId;
}

class _SupportTicketByIdProviderElement
    extends AutoDisposeFutureProviderElement<TicketModel?>
    with SupportTicketByIdRef {
  _SupportTicketByIdProviderElement(super.provider);

  @override
  String get ticketId => (origin as SupportTicketByIdProvider).ticketId;
}

String _$ticketMessagesHash() => r'410e4a12192d5a7c98dfc699e2846317d987eece';

/// Ticket messages provider
///
/// Copied from [ticketMessages].
@ProviderFor(ticketMessages)
const ticketMessagesProvider = TicketMessagesFamily();

/// Ticket messages provider
///
/// Copied from [ticketMessages].
class TicketMessagesFamily
    extends Family<AsyncValue<List<TicketMessageModel>>> {
  /// Ticket messages provider
  ///
  /// Copied from [ticketMessages].
  const TicketMessagesFamily();

  /// Ticket messages provider
  ///
  /// Copied from [ticketMessages].
  TicketMessagesProvider call(String ticketId) {
    return TicketMessagesProvider(ticketId);
  }

  @override
  TicketMessagesProvider getProviderOverride(
    covariant TicketMessagesProvider provider,
  ) {
    return call(provider.ticketId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'ticketMessagesProvider';
}

/// Ticket messages provider
///
/// Copied from [ticketMessages].
class TicketMessagesProvider
    extends AutoDisposeFutureProvider<List<TicketMessageModel>> {
  /// Ticket messages provider
  ///
  /// Copied from [ticketMessages].
  TicketMessagesProvider(String ticketId)
    : this._internal(
        (ref) => ticketMessages(ref as TicketMessagesRef, ticketId),
        from: ticketMessagesProvider,
        name: r'ticketMessagesProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$ticketMessagesHash,
        dependencies: TicketMessagesFamily._dependencies,
        allTransitiveDependencies:
            TicketMessagesFamily._allTransitiveDependencies,
        ticketId: ticketId,
      );

  TicketMessagesProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.ticketId,
  }) : super.internal();

  final String ticketId;

  @override
  Override overrideWith(
    FutureOr<List<TicketMessageModel>> Function(TicketMessagesRef provider)
    create,
  ) {
    return ProviderOverride(
      origin: this,
      override: TicketMessagesProvider._internal(
        (ref) => create(ref as TicketMessagesRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        ticketId: ticketId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<TicketMessageModel>> createElement() {
    return _TicketMessagesProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is TicketMessagesProvider && other.ticketId == ticketId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, ticketId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin TicketMessagesRef
    on AutoDisposeFutureProviderRef<List<TicketMessageModel>> {
  /// The parameter `ticketId` of this provider.
  String get ticketId;
}

class _TicketMessagesProviderElement
    extends AutoDisposeFutureProviderElement<List<TicketMessageModel>>
    with TicketMessagesRef {
  _TicketMessagesProviderElement(super.provider);

  @override
  String get ticketId => (origin as TicketMessagesProvider).ticketId;
}

String _$totalUnreadTicketsCountHash() =>
    r'c7ad4c1b003fa4197d98d987d7c4597e89f69a22';

/// Total unread tickets count provider
///
/// Copied from [totalUnreadTicketsCount].
@ProviderFor(totalUnreadTicketsCount)
final totalUnreadTicketsCountProvider = AutoDisposeFutureProvider<int>.internal(
  totalUnreadTicketsCount,
  name: r'totalUnreadTicketsCountProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$totalUnreadTicketsCountHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef TotalUnreadTicketsCountRef = AutoDisposeFutureProviderRef<int>;
String _$supportTicketActionsHash() =>
    r'bafaf4c27e94783a3956f1f27a683aea05e691a0';

/// Support ticket actions provider
///
/// Copied from [SupportTicketActions].
@ProviderFor(SupportTicketActions)
final supportTicketActionsProvider =
    AutoDisposeAsyncNotifierProvider<
      SupportTicketActions,
      List<TicketModel>
    >.internal(
      SupportTicketActions.new,
      name: r'supportTicketActionsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$supportTicketActionsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$SupportTicketActions = AutoDisposeAsyncNotifier<List<TicketModel>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
