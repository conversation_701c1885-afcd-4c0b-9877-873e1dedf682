// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'clean_categories_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$allCategoriesHash() => r'9cd5aae51ad5a222b7e3bb334fe6c356a68fcc81';

/// Clean Categories Provider - Forever Plan Architecture
/// Flutter (UI Only) → Go API → Supabase (Data Only)
///
/// Simple providers that use SimpleApiClient ONLY
/// No direct Supabase calls, no complex state management
/// All categories provider with fallback
///
/// Copied from [allCategories].
@ProviderFor(allCategories)
final allCategoriesProvider =
    AutoDisposeFutureProvider<List<CategoryModel>>.internal(
      allCategories,
      name: r'allCategoriesProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$allCategoriesHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef AllCategoriesRef = AutoDisposeFutureProviderRef<List<CategoryModel>>;
String _$parentCategoriesHash() => r'4c2f05a4e0cd351e990c3b4f183840bf4a422bcb';

/// Parent categories provider (categories with no parent)
///
/// Copied from [parentCategories].
@ProviderFor(parentCategories)
final parentCategoriesProvider =
    AutoDisposeFutureProvider<List<CategoryModel>>.internal(
      parentCategories,
      name: r'parentCategoriesProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$parentCategoriesHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef ParentCategoriesRef = AutoDisposeFutureProviderRef<List<CategoryModel>>;
String _$subcategoriesHash() => r'd8b52ea2c263ef599be28f3e8b03b9491de98a61';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// Subcategories provider for a given parent category
///
/// Copied from [subcategories].
@ProviderFor(subcategories)
const subcategoriesProvider = SubcategoriesFamily();

/// Subcategories provider for a given parent category
///
/// Copied from [subcategories].
class SubcategoriesFamily extends Family<AsyncValue<List<CategoryModel>>> {
  /// Subcategories provider for a given parent category
  ///
  /// Copied from [subcategories].
  const SubcategoriesFamily();

  /// Subcategories provider for a given parent category
  ///
  /// Copied from [subcategories].
  SubcategoriesProvider call(String parentId) {
    return SubcategoriesProvider(parentId);
  }

  @override
  SubcategoriesProvider getProviderOverride(
    covariant SubcategoriesProvider provider,
  ) {
    return call(provider.parentId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'subcategoriesProvider';
}

/// Subcategories provider for a given parent category
///
/// Copied from [subcategories].
class SubcategoriesProvider
    extends AutoDisposeFutureProvider<List<CategoryModel>> {
  /// Subcategories provider for a given parent category
  ///
  /// Copied from [subcategories].
  SubcategoriesProvider(String parentId)
    : this._internal(
        (ref) => subcategories(ref as SubcategoriesRef, parentId),
        from: subcategoriesProvider,
        name: r'subcategoriesProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$subcategoriesHash,
        dependencies: SubcategoriesFamily._dependencies,
        allTransitiveDependencies:
            SubcategoriesFamily._allTransitiveDependencies,
        parentId: parentId,
      );

  SubcategoriesProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.parentId,
  }) : super.internal();

  final String parentId;

  @override
  Override overrideWith(
    FutureOr<List<CategoryModel>> Function(SubcategoriesRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: SubcategoriesProvider._internal(
        (ref) => create(ref as SubcategoriesRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        parentId: parentId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<CategoryModel>> createElement() {
    return _SubcategoriesProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is SubcategoriesProvider && other.parentId == parentId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, parentId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin SubcategoriesRef on AutoDisposeFutureProviderRef<List<CategoryModel>> {
  /// The parameter `parentId` of this provider.
  String get parentId;
}

class _SubcategoriesProviderElement
    extends AutoDisposeFutureProviderElement<List<CategoryModel>>
    with SubcategoriesRef {
  _SubcategoriesProviderElement(super.provider);

  @override
  String get parentId => (origin as SubcategoriesProvider).parentId;
}

String _$popularCategoriesHash() => r'1abeaa6e514e8679a03a44689f30a89eee5e579c';

/// Popular categories provider (most used categories)
///
/// Copied from [popularCategories].
@ProviderFor(popularCategories)
final popularCategoriesProvider =
    AutoDisposeFutureProvider<List<CategoryModel>>.internal(
      popularCategories,
      name: r'popularCategoriesProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$popularCategoriesHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef PopularCategoriesRef =
    AutoDisposeFutureProviderRef<List<CategoryModel>>;
String _$categoryByIdHash() => r'1edd6af0817adfe31b95325e4156cf19b050d22e';

/// Single category by ID provider
///
/// Copied from [categoryById].
@ProviderFor(categoryById)
const categoryByIdProvider = CategoryByIdFamily();

/// Single category by ID provider
///
/// Copied from [categoryById].
class CategoryByIdFamily extends Family<AsyncValue<CategoryModel?>> {
  /// Single category by ID provider
  ///
  /// Copied from [categoryById].
  const CategoryByIdFamily();

  /// Single category by ID provider
  ///
  /// Copied from [categoryById].
  CategoryByIdProvider call(String categoryId) {
    return CategoryByIdProvider(categoryId);
  }

  @override
  CategoryByIdProvider getProviderOverride(
    covariant CategoryByIdProvider provider,
  ) {
    return call(provider.categoryId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'categoryByIdProvider';
}

/// Single category by ID provider
///
/// Copied from [categoryById].
class CategoryByIdProvider extends AutoDisposeFutureProvider<CategoryModel?> {
  /// Single category by ID provider
  ///
  /// Copied from [categoryById].
  CategoryByIdProvider(String categoryId)
    : this._internal(
        (ref) => categoryById(ref as CategoryByIdRef, categoryId),
        from: categoryByIdProvider,
        name: r'categoryByIdProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$categoryByIdHash,
        dependencies: CategoryByIdFamily._dependencies,
        allTransitiveDependencies:
            CategoryByIdFamily._allTransitiveDependencies,
        categoryId: categoryId,
      );

  CategoryByIdProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.categoryId,
  }) : super.internal();

  final String categoryId;

  @override
  Override overrideWith(
    FutureOr<CategoryModel?> Function(CategoryByIdRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: CategoryByIdProvider._internal(
        (ref) => create(ref as CategoryByIdRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        categoryId: categoryId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<CategoryModel?> createElement() {
    return _CategoryByIdProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is CategoryByIdProvider && other.categoryId == categoryId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, categoryId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin CategoryByIdRef on AutoDisposeFutureProviderRef<CategoryModel?> {
  /// The parameter `categoryId` of this provider.
  String get categoryId;
}

class _CategoryByIdProviderElement
    extends AutoDisposeFutureProviderElement<CategoryModel?>
    with CategoryByIdRef {
  _CategoryByIdProviderElement(super.provider);

  @override
  String get categoryId => (origin as CategoryByIdProvider).categoryId;
}

String _$createCategoryHash() => r'920237f3345b7bfcb7403e6698737e61434cdd8b';

/// Create new category
///
/// Copied from [createCategory].
@ProviderFor(createCategory)
const createCategoryProvider = CreateCategoryFamily();

/// Create new category
///
/// Copied from [createCategory].
class CreateCategoryFamily extends Family<AsyncValue<CategoryModel?>> {
  /// Create new category
  ///
  /// Copied from [createCategory].
  const CreateCategoryFamily();

  /// Create new category
  ///
  /// Copied from [createCategory].
  CreateCategoryProvider call(Map<String, dynamic> categoryData) {
    return CreateCategoryProvider(categoryData);
  }

  @override
  CreateCategoryProvider getProviderOverride(
    covariant CreateCategoryProvider provider,
  ) {
    return call(provider.categoryData);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'createCategoryProvider';
}

/// Create new category
///
/// Copied from [createCategory].
class CreateCategoryProvider extends AutoDisposeFutureProvider<CategoryModel?> {
  /// Create new category
  ///
  /// Copied from [createCategory].
  CreateCategoryProvider(Map<String, dynamic> categoryData)
    : this._internal(
        (ref) => createCategory(ref as CreateCategoryRef, categoryData),
        from: createCategoryProvider,
        name: r'createCategoryProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$createCategoryHash,
        dependencies: CreateCategoryFamily._dependencies,
        allTransitiveDependencies:
            CreateCategoryFamily._allTransitiveDependencies,
        categoryData: categoryData,
      );

  CreateCategoryProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.categoryData,
  }) : super.internal();

  final Map<String, dynamic> categoryData;

  @override
  Override overrideWith(
    FutureOr<CategoryModel?> Function(CreateCategoryRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: CreateCategoryProvider._internal(
        (ref) => create(ref as CreateCategoryRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        categoryData: categoryData,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<CategoryModel?> createElement() {
    return _CreateCategoryProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is CreateCategoryProvider &&
        other.categoryData == categoryData;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, categoryData.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin CreateCategoryRef on AutoDisposeFutureProviderRef<CategoryModel?> {
  /// The parameter `categoryData` of this provider.
  Map<String, dynamic> get categoryData;
}

class _CreateCategoryProviderElement
    extends AutoDisposeFutureProviderElement<CategoryModel?>
    with CreateCategoryRef {
  _CreateCategoryProviderElement(super.provider);

  @override
  Map<String, dynamic> get categoryData =>
      (origin as CreateCategoryProvider).categoryData;
}

String _$updateCategoryHash() => r'39d32ead15210f8bacaea18e42e2bec9e2c5fd73';

/// Update existing category
///
/// Copied from [updateCategory].
@ProviderFor(updateCategory)
const updateCategoryProvider = UpdateCategoryFamily();

/// Update existing category
///
/// Copied from [updateCategory].
class UpdateCategoryFamily extends Family<AsyncValue<CategoryModel?>> {
  /// Update existing category
  ///
  /// Copied from [updateCategory].
  const UpdateCategoryFamily();

  /// Update existing category
  ///
  /// Copied from [updateCategory].
  UpdateCategoryProvider call(
    String categoryId,
    Map<String, dynamic> updateData,
  ) {
    return UpdateCategoryProvider(categoryId, updateData);
  }

  @override
  UpdateCategoryProvider getProviderOverride(
    covariant UpdateCategoryProvider provider,
  ) {
    return call(provider.categoryId, provider.updateData);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'updateCategoryProvider';
}

/// Update existing category
///
/// Copied from [updateCategory].
class UpdateCategoryProvider extends AutoDisposeFutureProvider<CategoryModel?> {
  /// Update existing category
  ///
  /// Copied from [updateCategory].
  UpdateCategoryProvider(String categoryId, Map<String, dynamic> updateData)
    : this._internal(
        (ref) =>
            updateCategory(ref as UpdateCategoryRef, categoryId, updateData),
        from: updateCategoryProvider,
        name: r'updateCategoryProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$updateCategoryHash,
        dependencies: UpdateCategoryFamily._dependencies,
        allTransitiveDependencies:
            UpdateCategoryFamily._allTransitiveDependencies,
        categoryId: categoryId,
        updateData: updateData,
      );

  UpdateCategoryProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.categoryId,
    required this.updateData,
  }) : super.internal();

  final String categoryId;
  final Map<String, dynamic> updateData;

  @override
  Override overrideWith(
    FutureOr<CategoryModel?> Function(UpdateCategoryRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: UpdateCategoryProvider._internal(
        (ref) => create(ref as UpdateCategoryRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        categoryId: categoryId,
        updateData: updateData,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<CategoryModel?> createElement() {
    return _UpdateCategoryProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is UpdateCategoryProvider &&
        other.categoryId == categoryId &&
        other.updateData == updateData;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, categoryId.hashCode);
    hash = _SystemHash.combine(hash, updateData.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin UpdateCategoryRef on AutoDisposeFutureProviderRef<CategoryModel?> {
  /// The parameter `categoryId` of this provider.
  String get categoryId;

  /// The parameter `updateData` of this provider.
  Map<String, dynamic> get updateData;
}

class _UpdateCategoryProviderElement
    extends AutoDisposeFutureProviderElement<CategoryModel?>
    with UpdateCategoryRef {
  _UpdateCategoryProviderElement(super.provider);

  @override
  String get categoryId => (origin as UpdateCategoryProvider).categoryId;
  @override
  Map<String, dynamic> get updateData =>
      (origin as UpdateCategoryProvider).updateData;
}

String _$deleteCategoryHash() => r'c32e0997a07767ef9ca6e8a2122d371173da5ecd';

/// Delete category
///
/// Copied from [deleteCategory].
@ProviderFor(deleteCategory)
const deleteCategoryProvider = DeleteCategoryFamily();

/// Delete category
///
/// Copied from [deleteCategory].
class DeleteCategoryFamily extends Family<AsyncValue<bool>> {
  /// Delete category
  ///
  /// Copied from [deleteCategory].
  const DeleteCategoryFamily();

  /// Delete category
  ///
  /// Copied from [deleteCategory].
  DeleteCategoryProvider call(String categoryId) {
    return DeleteCategoryProvider(categoryId);
  }

  @override
  DeleteCategoryProvider getProviderOverride(
    covariant DeleteCategoryProvider provider,
  ) {
    return call(provider.categoryId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'deleteCategoryProvider';
}

/// Delete category
///
/// Copied from [deleteCategory].
class DeleteCategoryProvider extends AutoDisposeFutureProvider<bool> {
  /// Delete category
  ///
  /// Copied from [deleteCategory].
  DeleteCategoryProvider(String categoryId)
    : this._internal(
        (ref) => deleteCategory(ref as DeleteCategoryRef, categoryId),
        from: deleteCategoryProvider,
        name: r'deleteCategoryProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$deleteCategoryHash,
        dependencies: DeleteCategoryFamily._dependencies,
        allTransitiveDependencies:
            DeleteCategoryFamily._allTransitiveDependencies,
        categoryId: categoryId,
      );

  DeleteCategoryProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.categoryId,
  }) : super.internal();

  final String categoryId;

  @override
  Override overrideWith(
    FutureOr<bool> Function(DeleteCategoryRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: DeleteCategoryProvider._internal(
        (ref) => create(ref as DeleteCategoryRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        categoryId: categoryId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<bool> createElement() {
    return _DeleteCategoryProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is DeleteCategoryProvider && other.categoryId == categoryId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, categoryId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin DeleteCategoryRef on AutoDisposeFutureProviderRef<bool> {
  /// The parameter `categoryId` of this provider.
  String get categoryId;
}

class _DeleteCategoryProviderElement
    extends AutoDisposeFutureProviderElement<bool>
    with DeleteCategoryRef {
  _DeleteCategoryProviderElement(super.provider);

  @override
  String get categoryId => (origin as DeleteCategoryProvider).categoryId;
}

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
