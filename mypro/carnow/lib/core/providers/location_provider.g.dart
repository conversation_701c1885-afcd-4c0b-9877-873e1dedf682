// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'location_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$locationServiceHash() => r'0e1f462a6a32b6a04f4f51b61817f60d65175ba3';

/// See also [locationService].
@ProviderFor(locationService)
final locationServiceProvider = AutoDisposeProvider<LocationService>.internal(
  locationService,
  name: r'locationServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$locationServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef LocationServiceRef = AutoDisposeProviderRef<LocationService>;
String _$citiesHash() => r'f96fb89ef05b9c76a880d20243efbaea1a12cfb2';

/// See also [cities].
@ProviderFor(cities)
final citiesProvider = AutoDisposeFutureProvider<List<City>>.internal(
  cities,
  name: r'citiesProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$citiesHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CitiesRef = AutoDisposeFutureProviderRef<List<City>>;
String _$cityByIdHash() => r'd3bb6f4f747bd773f4de0a86204f2e9aa9534a6a';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [cityById].
@ProviderFor(cityById)
const cityByIdProvider = CityByIdFamily();

/// See also [cityById].
class CityByIdFamily extends Family<AsyncValue<City?>> {
  /// See also [cityById].
  const CityByIdFamily();

  /// See also [cityById].
  CityByIdProvider call(int id) {
    return CityByIdProvider(id);
  }

  @override
  CityByIdProvider getProviderOverride(covariant CityByIdProvider provider) {
    return call(provider.id);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'cityByIdProvider';
}

/// See also [cityById].
class CityByIdProvider extends AutoDisposeFutureProvider<City?> {
  /// See also [cityById].
  CityByIdProvider(int id)
    : this._internal(
        (ref) => cityById(ref as CityByIdRef, id),
        from: cityByIdProvider,
        name: r'cityByIdProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$cityByIdHash,
        dependencies: CityByIdFamily._dependencies,
        allTransitiveDependencies: CityByIdFamily._allTransitiveDependencies,
        id: id,
      );

  CityByIdProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.id,
  }) : super.internal();

  final int id;

  @override
  Override overrideWith(FutureOr<City?> Function(CityByIdRef provider) create) {
    return ProviderOverride(
      origin: this,
      override: CityByIdProvider._internal(
        (ref) => create(ref as CityByIdRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        id: id,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<City?> createElement() {
    return _CityByIdProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is CityByIdProvider && other.id == id;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, id.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin CityByIdRef on AutoDisposeFutureProviderRef<City?> {
  /// The parameter `id` of this provider.
  int get id;
}

class _CityByIdProviderElement extends AutoDisposeFutureProviderElement<City?>
    with CityByIdRef {
  _CityByIdProviderElement(super.provider);

  @override
  int get id => (origin as CityByIdProvider).id;
}

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
