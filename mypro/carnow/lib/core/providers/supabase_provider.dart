import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../core/auth/unified_auth_provider.dart';

part 'supabase_provider.g.dart';

/// DEPRECATED: Auth Providers - Forever Plan Architecture Migration
/// Flutter (UI Only) → Go API → Supabase (Data Only)
/// 
/// These providers are DEPRECATED and should use UnifiedAuthProvider instead
/// ⚠️  WARNING: Migrate to UnifiedAuthProvider for all authentication operations
/// ✅ USE UnifiedAuthProvider for all auth state management

/// DEPRECATED: Get current auth token (JWT) for API calls
/// Use currentAccessTokenProvider instead
@Deprecated('Use currentAccessTokenProvider instead')
@riverpod
String? authToken(AuthTokenRef ref) {
  final accessToken = ref.watch(currentAccessTokenProvider);
  return accessToken;
}

/// DEPRECATED: Current user ID from auth session
/// Use currentUserProvider instead
@Deprecated('Use currentUserProvider instead')
@riverpod
String? currentUserId(CurrentUserIdRef ref) {
  final user = ref.watch(currentUserProvider);
  // Cast to dynamic to access id property since currentUserProvider returns Object?
  return (user as dynamic)?.id as String?;
}

/// DEPRECATED: Simple auth state provider
/// Use UnifiedAuthProvider directly instead
@Deprecated('Use UnifiedAuthProvider directly instead')
@riverpod
Object? currentUser(CurrentUserRef ref) {
  // This provider is deprecated - use UnifiedAuthProvider instead
  throw UnsupportedError(
    'This provider is deprecated. Use UnifiedAuthProvider.user instead.',
  );
}



/// DEPRECATED: Auth state changes stream
/// Use UnifiedAuthProvider.authStateChanges instead
@Deprecated('Use UnifiedAuthProvider.authStateChanges instead')
@riverpod
Stream<Object> authStateChanges(AuthStateChangesRef ref) async* {
  // This provider is no longer functional - use UnifiedAuthProvider
  throw UnsupportedError(
    'This provider is deprecated. Use UnifiedAuthProvider.authStateChanges instead.',
  );
}

// ================================================================================
// MIGRATION GUIDE - How to migrate from these deprecated providers:
// ================================================================================
// 
// OLD: final token = ref.watch(authTokenProvider);
// NEW: final token = ref.read(currentAccessTokenProvider);
//
// OLD: final userId = ref.watch(currentUserIdProvider);
// NEW: final user = ref.watch(currentUserProvider);
//      final userId = user?.id;
//
// OLD: final user = ref.watch(currentUserProvider);
// NEW: final user = ref.watch(currentUserProvider);
//
// OLD: ref.listen(authStateChangesProvider, ...);
// NEW: ref.listen(unifiedAuthProviderProvider, ...);
//
// Forever Plan Rule: Use UnifiedAuthProvider for ALL authentication operations
// ================================================================================ 