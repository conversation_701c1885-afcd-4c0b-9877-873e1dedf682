import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../core/auth/unified_auth_provider.dart';
import '../../../core/auth/auth_models.dart';

/// Provider that returns `true` if the authenticated user has administrator privileges.
/// 
/// Forever Plan Architecture: Uses SimpleAuthSystem for auth state
/// Admin status should be determined by the Go backend API, not client-side metadata
final isAdminProvider = Provider<bool>((ref) {
  // Safely access current user with fallback to prevent initialization race conditions
  User? currentUser;
  try {
    currentUser = ref.watch(currentUserProvider);
  } catch (e) {
    if (kDebugMode) {
      print('AdminProvider: Current user provider not ready, defaulting to false: $e');
    }
    return false;
  }
  
  if (currentUser == null) return false;
  
  // ✅ Temporary admin detection for development
  // TODO: Replace with proper Go backend API call for production
  // Example: GET /api/v1/user/role or check user profile data
  
  // Admin users list (should be moved to backend configuration)
  const adminEmails = {
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
  };
  
  final isAdmin = adminEmails.contains(currentUser.email.toLowerCase());
  
  if (kDebugMode && isAdmin) {
    print('🔧 AdminProvider: User ${currentUser.email} detected as admin');
  }
  
  return isAdmin;
});

// ================================================================================
// TODO: Implement proper admin role checking
// ================================================================================
// 
// The Forever Plan approach should be:
// 1. Make API call to Go backend: GET /api/v1/user/profile  
// 2. Backend returns user data including role/admin status
// 3. Cache the result appropriately
//
// OLD: Check user.userMetadata for admin claims (client-side)
// NEW: Check backend API for user role (server-side verification)
//
// This ensures admin status is always verified by the backend and cannot be
// manipulated on the client side.
// ================================================================================
