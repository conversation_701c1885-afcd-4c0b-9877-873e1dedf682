# دليل منع مشاكل ValueNotifier Disposal

## المشكلة
تحدث أخطاء `ValueNotifier disposal` عندما يتم التخلص من الموفرات (Providers) بشكل مبكر بينما لا تزال الواجهات أو العمليات غير المتزامنة تحاول الوصول إليها.

## الحلول المطبقة

### 1. إضافة `ref.keepAlive()` للموفرات الحرجة
```dart
@riverpod
UserSessionState userSessionState(UserSessionStateRef ref) {
  // منع التخلص المبكر من الموفر الحرج
  ref.keepAlive();
  
  final currentUserAsync = ref.watch(currentUserProvider);
  // باقي الكود...
}
```

### 2. إضافة فحوصات `ref.mounted` للعمليات غير المتزامنة
```dart
@riverpod
Stream<UserModel?> currentUser(Ref ref) {
  ref.keepAlive();
  
  return stream.asyncMap((data) async {
    // فحص ما إذا كان الموفر لا يزال نشطاً
    if (!ref.mounted) return null;
    
    // العمليات غير المتزامنة
    await someAsyncOperation();
    
    // فحص مرة أخرى بعد العملية
    if (!ref.mounted) return null;
    
    return result;
  });
}
```

### 3. تحسين مراقبة الموفرات
تم تحديث `GlobalProviderObserver` لتحذير المطورين عند التخلص من الموفرات الحرجة.

## الموفرات الحرجة التي تحتاج `ref.keepAlive()`

- `userSessionStateProvider`
- `currentUserProvider`
- `unifiedAuthProvider`
- `isAuthenticatedProvider`
- أي موفر يحتوي على:
  - `auth`
  - `session`
  - `user`
  - `supabase`
  - `connection`
  - `theme`
  - `router`

## أفضل الممارسات

### 1. استخدم `ref.keepAlive()` للموفرات الحرجة
```dart
final criticalProvider = Provider<SomeType>((ref) {
  ref.keepAlive(); // منع التخلص المبكر
  return SomeType();
});
```

### 2. فحص `ref.mounted` في العمليات غير المتزامنة
```dart
future.then((result) {
  if (!ref.mounted) return; // تجنب الوصول للموفر المتخلص منه
  // معالجة النتيجة
});
```

### 3. تجنب الوصول للموفرات في `dispose()`
```dart
class MyWidget extends ConsumerStatefulWidget {
  @override
  void dispose() {
    // لا تستخدم ref.read() هنا
    super.dispose();
  }
}
```

### 4. استخدم `ref.read()` بدلاً من `ref.watch()` للعمليات لمرة واحدة
```dart
// جيد للعمليات لمرة واحدة
final supabase = ref.read(supabaseClientProvider);

// جيد للمراقبة المستمرة
final user = ref.watch(currentUserProvider);
```

## التحقق من الحلول

### 1. مراقبة السجلات
ابحث عن رسائل التحذير:
```
⚠️ CRITICAL PROVIDER DISPOSED: [provider_name] - This may cause ValueNotifier disposal errors!
```

### 2. اختبار التنقل
- انتقل بين الشاشات بسرعة
- اختبر تسجيل الدخول/الخروج
- اختبر إغلاق التطبيق أثناء العمليات

### 3. مراقبة الأخطاء
تأكد من عدم ظهور:
```
FlutterError: A ValueNotifier<bool> was used after being disposed
```

## متى تستخدم كل حل

| الحالة | الحل |
|--------|------|
| موفر حرج للمصادقة | `ref.keepAlive()` |
| عملية غير متزامنة طويلة | `ref.mounted` checks |
| موفر مؤقت | لا حاجة لحلول خاصة |
| موفر للواجهة فقط | `ref.keepAlive()` إذا لزم الأمر |

## الصيانة المستقبلية

1. **مراجعة دورية**: تحقق من السجلات للموفرات المتخلص منها
2. **اختبار شامل**: اختبر سيناريوهات التنقل المختلفة
3. **مراقبة الأداء**: تأكد من أن `keepAlive()` لا يؤثر على الذاكرة
4. **تحديث الدليل**: أضف حالات جديدة عند اكتشافها