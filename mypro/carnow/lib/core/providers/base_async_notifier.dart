import 'dart:async';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logging/logging.dart';

import '../errors/app_error.dart';

/// مزود ملاحظات غير متزامن أساسي لإدارة الحالات غير المتزامنة بشكل موحد
/// Base async notifier for standardized async state management
abstract class BaseAsyncNotifier<T> extends AsyncNotifier<T> {
  BaseAsyncNotifier() {
    logger = Logger(runtimeType.toString());
  }

  /// سجل للتسجيل
  /// Logger for recording operations
  late final Logger logger;

  /// تنفيذ عملية غير متزامنة مع معالجة الأخطاء
  /// Execute async operation with error handling
  Future<T> executeOperation<R>({
    required Future<T> Function() operation,
    String? operationName,
    bool shouldLog = true,
    Future<T> Function(Object error, StackTrace stackTrace)? onError,
  }) async {
    try {
      if (shouldLog) {
        logger.info(
          'Executing operation${operationName != null ? ' $operationName' : ''}',
        );
      }

      return await operation();
    } catch (e, stackTrace) {
      if (shouldLog) {
        logger.severe(
          'Error in ${operationName ?? 'operation'}: $e',
          e,
          stackTrace,
        );
      }

      if (onError != null) {
        return onError(e, stackTrace);
      } else {
        // رمي الخطأ مجدداً بعد تسجيله
        // Rethrow the error after logging
        throw e is AppError
            ? e
            : AppErrorFactory.fromError(e, stackTrace: stackTrace);
      }
    }
  }

  /// تعيين الحالة إلى حالة تحميل
  /// Set state to loading
  void setLoading() {
    state = const AsyncValue.loading();
  }

  /// تعيين الحالة إلى حالة بيانات
  /// Set state to data
  void setData(T data) {
    state = AsyncValue.data(data);
  }

  /// تعيين الحالة إلى حالة خطأ
  /// Set state to error
  void setError(Object error, [StackTrace? stackTrace]) {
    state = AsyncValue.error(error, stackTrace ?? StackTrace.current);
  }

  /// إعادة تحميل البيانات
  /// Reload data
  Future<void> reload() async {
    // Instead of calling build() directly, use ref.invalidateSelf()
    // which will trigger the provider to rebuild
    ref.invalidateSelf();
  }
}

/// مزود ملاحظات غير متزامن تلقائي للتخلص للاستخدام مع العناصر التي تحتاج إلى التخلص التلقائي
/// Auto disposable async notifier for use with widgets that need auto disposal
abstract class BaseAutoDisposeAsyncNotifier<T>
    extends AutoDisposeAsyncNotifier<T> {
  BaseAutoDisposeAsyncNotifier() {
    logger = Logger(runtimeType.toString());
  }

  /// سجل للتسجيل
  /// Logger for recording operations
  late final Logger logger;

  /// تنفيذ عملية غير متزامنة مع معالجة الأخطاء
  /// Execute async operation with error handling
  Future<T> executeOperation<R>({
    required Future<T> Function() operation,
    String? operationName,
    bool shouldLog = true,
    Future<T> Function(Object error, StackTrace stackTrace)? onError,
  }) async {
    try {
      if (shouldLog) {
        logger.info(
          'Executing operation${operationName != null ? ' $operationName' : ''}',
        );
      }

      return await operation();
    } catch (e, stackTrace) {
      if (shouldLog) {
        logger.severe(
          'Error in ${operationName ?? 'operation'}: $e',
          e,
          stackTrace,
        );
      }

      if (onError != null) {
        return onError(e, stackTrace);
      } else {
        // رمي الخطأ مجدداً بعد تسجيله
        // Rethrow the error after logging
        throw e is AppError
            ? e
            : AppErrorFactory.fromError(e, stackTrace: stackTrace);
      }
    }
  }

  /// تعيين الحالة إلى حالة تحميل
  /// Set state to loading
  void setLoading() {
    state = const AsyncValue.loading();
  }

  /// تعيين الحالة إلى حالة بيانات
  /// Set state to data
  void setData(T data) {
    state = AsyncValue.data(data);
  }

  /// تعيين الحالة إلى حالة خطأ
  /// Set state to error
  void setError(Object error, [StackTrace? stackTrace]) {
    state = AsyncValue.error(error, stackTrace ?? StackTrace.current);
  }

  /// إعادة تحميل البيانات
  /// Reload data
  Future<void> reload() async {
    // Instead of calling build() directly, use ref.invalidateSelf()
    // which will trigger the provider to rebuild
    ref.invalidateSelf();
  }
}

/// مزود ملاحظات غير متزامن تلقائي للتخلص مع المعلمات
/// Auto disposable async notifier with parameters
abstract class BaseParameterizedAsyncNotifier<T, P>
    extends AutoDisposeAsyncNotifier<T> {
  BaseParameterizedAsyncNotifier() {
    logger = Logger(runtimeType.toString());
  }

  /// سجل للتسجيل
  /// Logger for recording operations
  late final Logger logger;

  /// المعلمات المستخدمة لبناء هذا المزود
  /// Parameters used to build this provider
  P get params;

  /// تنفيذ عملية غير متزامنة مع معالجة الأخطاء
  /// Execute async operation with error handling
  Future<T> executeOperation<R>({
    required Future<T> Function() operation,
    String? operationName,
    bool shouldLog = true,
    Future<T> Function(Object error, StackTrace stackTrace)? onError,
  }) async {
    try {
      if (shouldLog) {
        logger.info(
          'Executing operation${operationName != null ? ' $operationName' : ''}',
        );
      }

      return await operation();
    } catch (e, stackTrace) {
      if (shouldLog) {
        logger.severe(
          'Error in ${operationName ?? 'operation'}: $e',
          e,
          stackTrace,
        );
      }

      if (onError != null) {
        return onError(e, stackTrace);
      } else {
        // رمي الخطأ مجدداً بعد تسجيله
        // Rethrow the error after logging
        throw e is AppError
            ? e
            : AppErrorFactory.fromError(e, stackTrace: stackTrace);
      }
    }
  }
}
