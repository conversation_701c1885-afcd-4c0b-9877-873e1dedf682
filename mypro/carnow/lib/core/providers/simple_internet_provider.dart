import 'dart:async';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'simple_internet_provider.g.dart';

/// Internet connectivity status - simplified
@riverpod
Stream<bool> internetConnection(InternetConnectionRef ref) {
  final connectivity = Connectivity();
  
  return connectivity.onConnectivityChanged.map((results) {
    return results.any((result) => result != ConnectivityResult.none);
  });
}

/// Current internet status - simplified
@riverpod
Future<bool> internetStatus(InternetStatusRef ref) async {
  final connectivity = Connectivity();
  final results = await connectivity.checkConnectivity();
  return results.any((result) => result != ConnectivityResult.none);
}