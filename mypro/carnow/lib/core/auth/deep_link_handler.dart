/// ============================================================================
/// DEEP LINK HANDLER - Forever Plan Architecture
/// ============================================================================
///
/// معالج الروابط العميقة - بنية الخطة الدائمة
/// Deep link handler for OAuth authentication callbacks
///
/// يتعامل مع روابط OAuth callbacks ويسجل المعلومات للتشخيص
/// ============================================================================
library;

import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'simple_oauth_config.dart';

/// نتيجة معالجة OAuth callback
/// OAuth callback processing result
class OAuthCallbackResult {
  final bool isSuccess;
  final String? authCode;
  final String? accessToken;
  final String? state;
  final String? error;

  const OAuthCallbackResult({
    required this.isSuccess,
    this.authCode,
    this.accessToken,
    this.state,
    this.error,
  });

  factory OAuthCallbackResult.success({
    String? authCode,
    String? accessToken,
    String? state,
  }) {
    return OAuthCallbackResult(
      isSuccess: true,
      authCode: authCode,
      accessToken: accessToken,
      state: state,
    );
  }

  factory OAuthCallbackResult.error(String error) {
    return OAuthCallbackResult(isSuccess: false, error: error);
  }
}

/// معالج الروابط العميقة للمصادقة
/// Deep link handler for authentication
class DeepLinkHandler {
  static const MethodChannel _channel = MethodChannel('carnow/deep_links');

  /// تسجيل معلومات deep link للتشخيص
  /// Log deep link information for debugging
  static void logDeepLinkInfo(String url) {
    if (!kDebugMode) return;

    print('=== Deep Link Handler Debug Info ===');
    print('Received URL: $url');
    print('Expected scheme: ${SimpleOAuthConfig.getAppScheme()}');
    print('Expected host: ${SimpleOAuthConfig.getAuthHost()}');

    // تحليل الرابط
    final uri = Uri.tryParse(url);
    if (uri != null) {
      print('Parsed URI:');
      print('  Scheme: ${uri.scheme}');
      print('  Host: ${uri.host}');
      print('  Path: ${uri.path}');
      print('  Query: ${uri.query}');
      print('  Fragment: ${uri.fragment}');

      // التحقق من صحة الرابط
      final isValid = SimpleOAuthConfig.isValidOAuthCallback(url);
      print('Is valid OAuth callback: $isValid');

      if (isValid) {
        final code = SimpleOAuthConfig.extractAuthCodeFromUrl(url);
        final error = SimpleOAuthConfig.extractErrorFromUrl(url);

        if (code != null) {
          final codePreview = code.length > 10
              ? '${code.substring(0, 10)}...'
              : code;
          print('Authorization code found: $codePreview');
        }

        if (error != null) {
          print('OAuth error found: $error');
        }
      }
    } else {
      print('Failed to parse URL');
    }

    print('=====================================');
  }

  /// التحقق من أن deep link يطابق التوقعات (يدعم الآن fallback URLs)
  /// Verify that deep link matches expectations (now supports fallback URLs)
  static bool verifyDeepLink(String url) {
    final uri = Uri.tryParse(url);
    if (uri == null) {
      if (kDebugMode) {
        print('❌ DeepLinkHandler: Invalid URL format: $url');
      }
      return false;
    }

    final expectedScheme = SimpleOAuthConfig.getAppScheme();
    final expectedHost = SimpleOAuthConfig.getAuthHost();
    final expectedHostFallback = SimpleOAuthConfig.getAuthHostFallback();

    // التحقق من scheme
    if (uri.scheme != expectedScheme) {
      if (kDebugMode) {
        print(
          '❌ DeepLinkHandler: Scheme mismatch. Expected: $expectedScheme, Got: ${uri.scheme}',
        );
      }
      return false;
    }

    // التحقق من host (يدعم الآن primary و fallback)
    if (uri.host != expectedHost && uri.host != expectedHostFallback) {
      if (kDebugMode) {
        print(
          '❌ DeepLinkHandler: Host mismatch. Expected: $expectedHost or $expectedHostFallback, Got: ${uri.host}',
        );
      }
      return false;
    }

    if (kDebugMode) {
      final hostType = uri.host == expectedHost ? 'primary' : 'fallback';
      print(
        '✅ DeepLinkHandler: Deep link verification passed ($hostType host)',
      );
    }
    return true;
  }

  /// التحقق من صحة OAuth deep link
  /// Validate OAuth deep link
  bool isValidOAuthDeepLink(String url) {
    return verifyDeepLink(url) && SimpleOAuthConfig.isValidOAuthCallback(url);
  }

  /// معالجة OAuth callback مع إرجاع نتيجة مفصلة
  /// Handle OAuth callback with detailed result
  Future<OAuthCallbackResult> handleOAuthCallback(String url) async {
    logDeepLinkInfo(url);

    if (!verifyDeepLink(url)) {
      return OAuthCallbackResult.error('Invalid deep link scheme');
    }

    final uri = Uri.tryParse(url);
    if (uri == null) {
      return OAuthCallbackResult.error('Failed to parse URL');
    }

    // التحقق من وجود المعاملات المطلوبة أولاً
    final hasCode = uri.queryParameters.containsKey('code');
    final hasAccessToken = uri.queryParameters.containsKey('access_token');
    final hasError = uri.queryParameters.containsKey('error');

    if (!hasCode && !hasAccessToken && !hasError) {
      return OAuthCallbackResult.error('Missing required parameters');
    }

    if (!SimpleOAuthConfig.isValidOAuthCallback(url)) {
      final error = SimpleOAuthConfig.extractErrorFromUrl(url);
      return OAuthCallbackResult.error(error ?? 'Invalid OAuth callback');
    }

    if (hasError) {
      final error = SimpleOAuthConfig.extractErrorFromUrl(url);
      return OAuthCallbackResult.error(error ?? 'OAuth error occurred');
    }

    final authCode = SimpleOAuthConfig.extractAuthCodeFromUrl(url);
    final state = uri.queryParameters['state'];
    final accessToken = uri.queryParameters['access_token'];

    return OAuthCallbackResult.success(
      authCode: authCode,
      accessToken: accessToken,
      state: state,
    );
  }

  /// استخراج authorization code من URL
  /// Extract authorization code from URL
  String? extractAuthCode(String url) {
    return SimpleOAuthConfig.extractAuthCodeFromUrl(url);
  }

  /// استخراج access token من URL
  /// Extract access token from URL
  String? extractAccessToken(String url) {
    final uri = Uri.tryParse(url);
    return uri?.queryParameters['access_token'];
  }

  /// استخراج state parameter من URL
  /// Extract state parameter from URL
  String? extractState(String url) {
    final uri = Uri.tryParse(url);
    return uri?.queryParameters['state'];
  }

  /// استخراج error information من URL
  /// Extract error information from URL
  String? extractError(String url) {
    return SimpleOAuthConfig.extractErrorFromUrl(url);
  }

  /// معالجة OAuth callback (static method for backward compatibility)
  /// Handle OAuth callback (static method for backward compatibility)
  static Map<String, String?> handleOAuthCallbackStatic(String url) {
    logDeepLinkInfo(url);

    if (!verifyDeepLink(url)) {
      return {'success': 'false', 'error': 'Invalid deep link format'};
    }

    if (!SimpleOAuthConfig.isValidOAuthCallback(url)) {
      final error = SimpleOAuthConfig.extractErrorFromUrl(url);
      return {'success': 'false', 'error': error ?? 'Invalid OAuth callback'};
    }

    final code = SimpleOAuthConfig.extractAuthCodeFromUrl(url);
    if (code != null) {
      return {'success': 'true', 'code': code};
    }

    return {'success': 'false', 'error': 'No authorization code found'};
  }

  /// اختبار deep link configuration
  /// Test deep link configuration
  static void testDeepLinkConfiguration() {
    if (!kDebugMode) return;

    print('=== Deep Link Configuration Test ===');

    // اختبار الروابط المختلفة
    final testUrls = [
      'io.supabase.carnow://login-callback/?code=test123',
      'io.supabase.carnow://login-callback/?error=access_denied',
      'http://localhost:3000/?code=test123', // الرابط المشكل
      'https://lpxtghyvxuenyyisrrro.supabase.co/auth/v1/callback?code=test123',
    ];

    for (final url in testUrls) {
      print('\nTesting URL: $url');
      final result = handleOAuthCallbackStatic(url);
      print('Result: $result');
    }

    print('====================================');
  }

  /// إنشاء deep link للاختبار
  /// Generate deep link for testing
  static String generateTestDeepLink({
    String? code,
    String? error,
    String? state,
  }) {
    final baseUrl = SimpleOAuthConfig.buildAuthDeepLink();
    final params = <String, String>{};

    if (code != null) params['code'] = code;
    if (error != null) params['error'] = error;
    if (state != null) params['state'] = state;

    if (params.isEmpty) return baseUrl;

    final query = params.entries
        .map((e) => '${e.key}=${Uri.encodeComponent(e.value)}')
        .join('&');

    return '$baseUrl?$query';
  }
}
