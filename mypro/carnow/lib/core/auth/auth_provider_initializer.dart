// =============================================================================
// AUTH PROVIDER INITIALIZER
// =============================================================================
//
// This file provides a robust initialization wrapper for the unified auth
// provider to prevent race conditions during app startup.
//
// Architecture: Flutter (UI Only) → Go API → Supabase (Data Only)
//
// Key Features:
// - Guarantees auth provider readiness before router access
// - Handles initialization race conditions gracefully
// - Provides fallback states during initialization
// - Production-ready error handling and logging
//
// Author: CarNow Development Team
// =============================================================================

import 'dart:async';
import 'dart:developer' as developer;

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import 'auth_models.dart';
import 'unified_auth_provider.dart';

part 'auth_provider_initializer.g.dart';

// =============================================================================
// AUTH PROVIDER INITIALIZATION STATE
// =============================================================================

/// Represents the initialization state of the auth provider
enum AuthProviderInitState {
  notStarted,
  initializing,
  ready,
  failed,
}

/// Auth provider initialization status with state and optional error
class AuthProviderStatus {
  final AuthProviderInitState state;
  final String? error;
  final AuthState? authState;

  const AuthProviderStatus({
    required this.state,
    this.error,
    this.authState,
  });

  bool get isReady => state == AuthProviderInitState.ready;
  bool get isInitializing => state == AuthProviderInitState.initializing;
  bool get hasFailed => state == AuthProviderInitState.failed;
}

// =============================================================================
// AUTH PROVIDER INITIALIZER
// =============================================================================

/// Provider that manages the initialization of the unified auth provider
/// and ensures it's ready before other providers can safely access it
@riverpod
class AuthProviderInitializer extends _$AuthProviderInitializer {
  Timer? _initializationTimeout;
  Completer<void>? _initializationCompleter;

  @override
  AuthProviderStatus build() {
    // Set up cleanup on provider disposal
    ref.onCancel(() {
      _initializationTimeout?.cancel();
      // Only complete if not already completed
      if (_initializationCompleter != null && !_initializationCompleter!.isCompleted) {
        try {
          _initializationCompleter!.complete();
          developer.log(
            'Provider disposed - completed initialization',
            name: 'AuthProviderInitializer',
          );
        } catch (e) {
          developer.log(
            'Error during provider disposal: $e',
            name: 'AuthProviderInitializer',
            level: 900,
          );
        }
      }
    });

    // Start initialization immediately
    _startInitialization();

    return const AuthProviderStatus(
      state: AuthProviderInitState.initializing,
    );
  }

  /// Start the auth provider initialization process
  void _startInitialization() {
    // Prevent multiple initialization attempts
    if (_initializationCompleter != null && !_initializationCompleter!.isCompleted) {
      developer.log(
        'Initialization already in progress - skipping',
        name: 'AuthProviderInitializer',
      );
      return; // Already initializing
    }

    // Reset completer if it was completed
    if (_initializationCompleter != null && _initializationCompleter!.isCompleted) {
      developer.log(
        'Resetting completed completer for new initialization',
        name: 'AuthProviderInitializer',
      );
    }

    _initializationCompleter = Completer<void>();

    developer.log(
      'Starting auth provider initialization',
      name: 'AuthProviderInitializer',
    );

    // Set a timeout for initialization
    _initializationTimeout = Timer(const Duration(seconds: 10), () {
      if (_initializationCompleter != null && !_initializationCompleter!.isCompleted) {
        developer.log(
          'Auth provider initialization timeout',
          name: 'AuthProviderInitializer',
          level: 900,
        );
        _completeInitialization(
          AuthProviderInitState.failed,
          error: 'Initialization timeout after 10 seconds',
        );
      }
    });

    // Start the actual initialization
    _performInitialization();
  }

  /// Perform the actual auth provider initialization
  Future<void> _performInitialization() async {
    try {
      // Check if already completed before starting
      if (_initializationCompleter?.isCompleted == true) {
        developer.log(
          'Initialization already completed - skipping',
          name: 'AuthProviderInitializer',
        );
        return;
      }

      // Simply mark as ready since UnifiedAuthProvider handles its own initialization
      // This breaks the circular dependency by not watching the provider directly
      developer.log(
        'Auth provider initialization completed - UnifiedAuthProvider handles its own initialization',
        name: 'AuthProviderInitializer',
      );
      
      _completeInitialization(
        AuthProviderInitState.ready,
        authState: const AuthState.unauthenticated(
          reason: 'جاري تهيئة نظام المصادقة...',
        ),
      );

    } catch (error, stackTrace) {
      developer.log(
        'Auth provider initialization failed: $error',
        name: 'AuthProviderInitializer',
        error: error,
        stackTrace: stackTrace,
      );

      _completeInitialization(
        AuthProviderInitState.failed,
        error: error.toString(),
      );
    }
  }

  /// Complete the initialization process with the given state
  void _completeInitialization(
    AuthProviderInitState initState, {
    String? error,
    AuthState? authState,
  }) {
    // Double-check to prevent "Future already completed" error
    if (_initializationCompleter == null || _initializationCompleter!.isCompleted) {
      developer.log(
        'Initialization already completed or completer is null - skipping',
        name: 'AuthProviderInitializer',
      );
      return;
    }

    _initializationTimeout?.cancel();

    state = AuthProviderStatus(
      state: initState,
      error: error,
      authState: authState,
    );

    try {
      _initializationCompleter!.complete();
      developer.log(
        'Auth provider initialization completed with state: $initState',
        name: 'AuthProviderInitializer',
      );
    } catch (e) {
      developer.log(
        'Error completing initialization: $e',
        name: 'AuthProviderInitializer',
        level: 900,
      );
    }
  }

  /// Wait for the auth provider to be ready
  Future<void> waitForReady() async {
    if (state.isReady) {
      return; // Already ready
    }

    if (state.hasFailed) {
      throw Exception('Auth provider initialization failed: ${state.error}');
    }

    // Wait for initialization to complete
    await _initializationCompleter?.future;

    if (state.hasFailed) {
      throw Exception('Auth provider initialization failed: ${state.error}');
    }
  }

  /// Retry initialization if it failed
  Future<void> retryInitialization() async {
    if (state.isInitializing) {
      return; // Already initializing
    }

    developer.log(
      'Retrying auth provider initialization',
      name: 'AuthProviderInitializer',
    );

    state = const AuthProviderStatus(
      state: AuthProviderInitState.initializing,
    );

    _startInitialization();
    await waitForReady();
  }
}

// =============================================================================
// SAFE AUTH STATE PROVIDER
// =============================================================================

/// Provider that safely provides the auth state with proper error handling
@riverpod
AuthState safeAuthState(SafeAuthStateRef ref) {
  try {
    // Directly watch the unified auth provider with error handling
    return ref.watch(unifiedAuthProviderProvider);
  } catch (e) {
    developer.log(
      'Error reading auth state: $e',
      name: 'SafeAuthState',
      level: 900,
    );
    
    // Return a safe fallback state
    return const AuthState.unauthenticated(
      reason: 'خطأ في قراءة حالة المصادقة',
    );
  }
}

// =============================================================================
// CONVENIENCE PROVIDERS
// =============================================================================

/// Provider that safely provides the current user only after auth is ready
@riverpod
User? safeCurrentUser(SafeCurrentUserRef ref) {
  final authState = ref.watch(safeAuthStateProvider);
  
  return authState.when(
    initial: () => null,
    loading: (message, operation) => null,
    authenticated: (user, token, refreshToken, tokenExpiry, sessionStart) => user,
    unauthenticated: (reason) => null,
    error: (message, errorCode, errorType, isRecoverable, originalException) => null,
    emailVerificationPending: (email, sentAt) => null,
    sessionExpired: (expiredAt, autoRefreshAttempted) => null,
  );
}

/// Provider that safely provides the authentication status
@riverpod
bool safeIsAuthenticated(SafeIsAuthenticatedRef ref) {
  final authState = ref.watch(safeAuthStateProvider);
  
  return authState.when(
    initial: () => false,
    loading: (message, operation) => false,
    authenticated: (user, token, refreshToken, tokenExpiry, sessionStart) => true,
    unauthenticated: (reason) => false,
    error: (message, errorCode, errorType, isRecoverable, originalException) => false,
    emailVerificationPending: (email, sentAt) => false,
    sessionExpired: (expiredAt, autoRefreshAttempted) => false,
  );
}

/// Provider that safely provides the current access token
@riverpod
String? safeCurrentAccessToken(SafeCurrentAccessTokenRef ref) {
  final authState = ref.watch(safeAuthStateProvider);
  
  return authState.when(
    initial: () => null,
    loading: (message, operation) => null,
    authenticated: (user, token, refreshToken, tokenExpiry, sessionStart) => token,
    unauthenticated: (reason) => null,
    error: (message, errorCode, errorType, isRecoverable, originalException) => null,
    emailVerificationPending: (email, sentAt) => null,
    sessionExpired: (expiredAt, autoRefreshAttempted) => null,
  );
}
