// =============================================================================
// CARNOW PRODUCTION GOOGLE AUTHENTICATION SERVICE - Forever Plan Architecture
// =============================================================================
//
// This file implements a production-ready Google OAuth authentication service for the CarNow
// unified authentication system following Forever Plan Architecture.
//
// Architecture: Flutter (UI Only) → Go API → Supabase (Data Only)
//
// Key Features:
// - Real Google Sign-In integration using google_sign_in package 7.x
// - Production-ready OAuth flow with proper error handling
// - Integration with UnifiedAuthProvider and Go backend
// - Comprehensive error handling and Arabic localization
// - Secure token management and validation
// - Proper cancellation and timeout handling
//
// Author: CarNow Development Team
// =============================================================================

import 'dart:developer' as developer;
import 'dart:convert';
import 'dart:async';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:http/http.dart' as http;

import 'auth_interfaces.dart';
import 'auth_models.dart';

// =============================================================================
// PRODUCTION GOOGLE AUTHENTICATION SERVICE IMPLEMENTATION
// =============================================================================

/// Production Google authentication service implementing IGoogleAuthService
/// Provides real Google OAuth functionality via google_sign_in package 7.x
class ProductionGoogleAuthService implements IGoogleAuthService {
  // ---------------------------------------------------------------------------
  // Configuration State
  // ---------------------------------------------------------------------------

  bool _isConfigured = false;
  List<String> _configuredScopes = ['email', 'profile'];
  String? _serverClientId;

  // Backend configuration
  static const String _backendBaseUrl = 'https://backend-go-8klm.onrender.com';
  static const Duration _requestTimeout = Duration(seconds: 30);

  @override
  bool get isConfigured => _isConfigured;

  @override
  List<String> get configuredScopes => List.unmodifiable(_configuredScopes);

  // ---------------------------------------------------------------------------
  // Service Initialization
  // ---------------------------------------------------------------------------

  ProductionGoogleAuthService() {
    developer.log(
      'ProductionGoogleAuthService initialized',
      name: 'ProductionGoogleAuthService',
    );
  }

  @override
  Future<void> initialize({
    required String clientId,
    List<String> scopes = const ['email', 'profile'],
  }) async {
    try {
      developer.log(
        'Initializing Google Sign-In with client ID: ${clientId.substring(0, 20)}...',
        name: 'ProductionGoogleAuthService',
      );

      _configuredScopes = List.from(scopes);
      _serverClientId = clientId;

      // Google Sign-In v6.3.0 supports serverClientId in constructor
      // This will properly configure the plugin for Android authentication
      
      developer.log(
        'Google Sign-In configured with scopes: $scopes',
        name: 'ProductionGoogleAuthService',
      );
      developer.log(
        'Server Client ID (Web Client ID): ${clientId.substring(0, 20)}...',
        name: 'ProductionGoogleAuthService',
      );
      
      _isConfigured = true;

      developer.log(
        'Google Sign-In initialized successfully with scopes: $scopes',
        name: 'ProductionGoogleAuthService',
      );
    } catch (error, stackTrace) {
      developer.log(
        'Failed to initialize Google Sign-In: $error',
        name: 'ProductionGoogleAuthService',
        error: error,
        stackTrace: stackTrace,
      );
      _isConfigured = false;
      rethrow;
    }
  }

  // ---------------------------------------------------------------------------
  // Google OAuth Implementation
  // ---------------------------------------------------------------------------

  @override
  Future<GoogleAuthResult> signIn() async {
    try {
      if (!_isConfigured) {
        return GoogleAuthResult.failure(
          error: 'Google Sign-In not configured. Call initialize() first.',
          errorCode: 'not_configured',
        );
      }

      developer.log(
        'Starting Google Sign-In process with v6.3.0',
        name: 'ProductionGoogleAuthService',
      );

      // Create GoogleSignIn instance with serverClientId for v6.3.0
      final GoogleSignIn googleSignIn = GoogleSignIn(
        scopes: _configuredScopes,
        serverClientId: _serverClientId, // Web Client ID for server authentication
      );

      developer.log(
        'Created GoogleSignIn instance with serverClientId: ${_serverClientId?.substring(0, 20)}...',
        name: 'ProductionGoogleAuthService',
      );

      // Attempt Google Sign-In using signIn() method for v6.3.0
      GoogleSignInAccount? googleUser;
      try {
        googleUser = await googleSignIn.signIn();
      } on PlatformException catch (e) {
        developer.log(
          'Google Sign-In platform exception: ${e.code} - ${e.message}',
          name: 'ProductionGoogleAuthService',
          error: e,
        );
        
        if (e.code == 'sign_in_cancelled') {
          return GoogleAuthResult.cancelled();
        }
        rethrow;
      }
      
      // Check if user cancelled sign-in
      if (googleUser == null) {
        developer.log(
          'Google Sign-In cancelled by user',
          name: 'ProductionGoogleAuthService',
        );
        return GoogleAuthResult.cancelled();
      }

      developer.log(
        'Google Sign-In successful for user: ${googleUser.email}',
        name: 'ProductionGoogleAuthService',
      );

      // Step 2: Get authentication details
      final GoogleSignInAuthentication googleAuth = await googleUser.authentication;

      if (googleAuth.idToken == null) {
        developer.log(
          'Failed to get Google ID token',
          name: 'ProductionGoogleAuthService',
        );
        return GoogleAuthResult.failure(
          error: getGoogleAuthErrorMessage('missing_id_token'),
          errorCode: 'missing_id_token',
        );
      }

      developer.log(
        'Google authentication tokens obtained successfully',
        name: 'ProductionGoogleAuthService',
      );

      // Step 3: Exchange real Google ID token with Go backend for JWT
      final backendResponse = await _exchangeGoogleTokenWithBackend(
        googleAuth.idToken!,
      );
      if (backendResponse == null) {
        return GoogleAuthResult.failure(
          error: getGoogleAuthErrorMessage('backend_exchange_failed'),
          errorCode: 'backend_exchange_failed',
        );
      }

      // Step 4: Create GoogleUserInfo from real Google account data
      final googleUserInfo = GoogleUserInfo(
        id: googleUser.id,
        email: googleUser.email,
        displayName: googleUser.displayName,
        photoUrl: googleUser.photoUrl,
        firstName: _extractFirstName(googleUser.displayName),
        lastName: _extractLastName(googleUser.displayName),
        name: googleUser.displayName,
        givenName: _extractFirstName(googleUser.displayName),
        familyName: _extractLastName(googleUser.displayName),
      );

      developer.log(
        'Real Google authentication completed successfully',
        name: 'ProductionGoogleAuthService',
      );

      return GoogleAuthResult.success(
        idToken: googleAuth.idToken!,
        accessToken: '', // Not available in google_sign_in v7.1.1
        userInfo: googleUserInfo,
        expiryDate: DateTime.now().add(const Duration(hours: 1)),
      );
    } on PlatformException catch (error) {
      developer.log(
        'Google Sign-In platform error: ${error.code} - ${error.message}',
        name: 'ProductionGoogleAuthService',
        error: error,
      );

      return GoogleAuthResult.failure(
        error: getGoogleAuthErrorMessage(error.code),
        errorCode: error.code,
      );
    } catch (error, stackTrace) {
      developer.log(
        'Google Sign-In unexpected error: $error',
        name: 'ProductionGoogleAuthService',
        error: error,
        stackTrace: stackTrace,
      );

      return GoogleAuthResult.failure(
        error: getGoogleAuthErrorMessage('unexpected_error'),
        errorCode: 'unexpected_error',
      );
    }
  }

  @override
  Future<void> signOut() async {
    try {
      developer.log(
        'Starting Google Sign-Out',
        name: 'ProductionGoogleAuthService',
      );

      final googleSignIn = GoogleSignIn();
      await googleSignIn.signOut();
      developer.log(
        'Google Sign-Out completed',
        name: 'ProductionGoogleAuthService',
      );
    } catch (error, stackTrace) {
      developer.log(
        'Error during Google Sign-Out: $error',
        name: 'ProductionGoogleAuthService',
        error: error,
        stackTrace: stackTrace,
      );
      // Don't rethrow - sign out should always succeed
    }
  }

  @override
  Future<GoogleAuthResult?> signInSilently() async {
    try {
      developer.log(
        'Attempting silent Google Sign-In',
        name: 'ProductionGoogleAuthService',
      );

      if (!isConfigured) {
        developer.log(
          'Google Sign-In not configured for silent sign-in',
          name: 'ProductionGoogleAuthService',
        );
        return null;
      }

      // Note: In google_sign_in v7.1.1, silent sign-in is not directly supported
      // We'll try to get the current user instead
      // For now, return null as silent sign-in is not available in this version
      developer.log(
        'Silent sign-in not supported in google_sign_in v7.1.1',
        name: 'ProductionGoogleAuthService',
      );
      return null;
    } catch (error, stackTrace) {
      developer.log(
        'Silent Google Sign-In error: $error',
        name: 'ProductionGoogleAuthService',
        error: error,
        stackTrace: stackTrace,
      );
      return null;
    }
  }

  @override
  Future<void> disconnect() async {
    try {
      developer.log(
        'Disconnecting Google account',
        name: 'ProductionGoogleAuthService',
      );

      final googleSignIn = GoogleSignIn();
      await googleSignIn.disconnect();
      developer.log(
        'Google account disconnected',
        name: 'ProductionGoogleAuthService',
      );
    } catch (error, stackTrace) {
      developer.log(
        'Error disconnecting Google account: $error',
        name: 'ProductionGoogleAuthService',
        error: error,
        stackTrace: stackTrace,
      );
      // Don't rethrow - disconnect should always succeed
    }
  }

  @override
  Future<bool> isSignedIn() async {
    try {
      if (!isConfigured) {
        return false;
      }

      // In google_sign_in v7.1.1, we need to try to get authentication
      // to check if user is signed in
      try {
        final googleSignIn = GoogleSignIn();
        return googleSignIn.currentUser != null;
      } catch (e) {
        // If authenticate() throws, user is not signed in
        return false;
      }
    } catch (error) {
      developer.log(
        'Error checking Google sign-in status: $error',
        name: 'ProductionGoogleAuthService',
        error: error,
      );
      return false;
    }
  }

  @override
  Future<GoogleUserInfo?> getCurrentUser() async {
    try {
      if (!isConfigured) {
        return null;
      }

      // In google_sign_in v7.1.1, we need to try to get authentication
      // to check if user is signed in
      try {
        final googleSignIn = GoogleSignIn();
        final currentUser = googleSignIn.currentUser;

        if (currentUser == null) return null;
        
        return GoogleUserInfo(
          id: currentUser.id,
          email: currentUser.email,
          displayName: currentUser.displayName,
          photoUrl: currentUser.photoUrl,
          firstName: _extractFirstName(currentUser.displayName),
          lastName: _extractLastName(currentUser.displayName),
          name: currentUser.displayName,
          givenName: _extractFirstName(currentUser.displayName),
          familyName: _extractLastName(currentUser.displayName),
        );
      } catch (e) {
        // If authenticate() throws, user is not signed in
        return null;
      }
    } catch (error) {
      developer.log(
        'Error getting current Google user: $error',
        name: 'ProductionGoogleAuthService',
        error: error,
      );
      return null;
    }
  }

  @override
  Future<String?> getAccessToken() async {
    try {
      if (!isConfigured) {
        return null;
      }

      // Try to authenticate to get access token
      try {
        final googleSignIn = GoogleSignIn();
        // Note: In google_sign_in v6.3.0, accessToken is not available
        return googleSignIn.currentUser != null ? '' : null;
      } catch (e) {
        // If authenticate() throws, no current user
        return null;
      }
    } catch (error) {
      developer.log(
        'Error getting Google access token: $error',
        name: 'ProductionGoogleAuthService',
        error: error,
      );
      return null;
    }
  }

  @override
  Future<String?> getIdToken() async {
    try {
      if (!isConfigured) {
        return null;
      }

      // Try to authenticate to get ID token
      try {
        final googleSignIn = GoogleSignIn();
        final googleUser = await googleSignIn.signIn();
        if (googleUser == null) return null;
        
        final GoogleSignInAuthentication googleAuth = await googleUser.authentication;
        return googleAuth.idToken;
      } catch (e) {
        // If authenticate() throws, no current user
        return null;
      }
    } catch (error) {
      developer.log(
        'Error getting Google ID token: $error',
        name: 'ProductionGoogleAuthService',
        error: error,
      );
      return null;
    }
  }

  // ---------------------------------------------------------------------------
  // Backend Integration
  // ---------------------------------------------------------------------------

  /// Exchanges Google ID token with Go backend for JWT tokens
  Future<Map<String, dynamic>?> _exchangeGoogleTokenWithBackend(
    String googleIdToken,
  ) async {
    try {
      developer.log(
        'Exchanging Google ID token with backend',
        name: 'ProductionGoogleAuthService',
      );

      final response = await http
          .post(
            Uri.parse('$_backendBaseUrl/api/v1/auth/google'),
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json',
            },
            body: jsonEncode({'id_token': googleIdToken}),
          )
          .timeout(_requestTimeout);

      developer.log(
        'Backend response status: ${response.statusCode}',
        name: 'ProductionGoogleAuthService',
      );

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body) as Map<String, dynamic>;
        developer.log(
          'Backend exchange successful',
          name: 'ProductionGoogleAuthService',
        );
        return responseData;
      } else {
        final errorBody = jsonDecode(response.body) as Map<String, dynamic>?;
        final errorMessage =
            errorBody?['error']?.toString() ?? 'HTTP ${response.statusCode}';
        developer.log(
          'Backend exchange failed: $errorMessage',
          name: 'ProductionGoogleAuthService',
        );
        return null;
      }
    } catch (error, stackTrace) {
      developer.log(
        'Failed to exchange Google token with backend: $error',
        name: 'ProductionGoogleAuthService',
        error: error,
        stackTrace: stackTrace,
      );
      return null;
    }
  }

  // ---------------------------------------------------------------------------
  // Utility Methods
  // ---------------------------------------------------------------------------

  /// Extract first name from display name
  String? _extractFirstName(String? displayName) {
    if (displayName == null || displayName.isEmpty) return null;
    final parts = displayName.split(' ');
    return parts.isNotEmpty ? parts.first : null;
  }

  /// Extract last name from display name
  String? _extractLastName(String? displayName) {
    if (displayName == null || displayName.isEmpty) return null;
    final parts = displayName.split(' ');
    return parts.length > 1 ? parts.skip(1).join(' ') : null;
  }

  /// Convert GoogleUserInfo to CarNow User model
  User convertToCarNowUser(GoogleUserInfo googleUser) {
    return User(
      id: googleUser.id,
      email: googleUser.email,
      firstName: googleUser.firstName ?? 'User',
      lastName: googleUser.lastName ?? '',
      displayName: googleUser.displayName,
      avatarUrl: googleUser.photoUrl,
      authProvider: AuthProvider.google,
      emailVerified: true, // Google accounts are always verified
      isActive: true,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      lastLoginAt: DateTime.now(),
    );
  }

  /// Get user-friendly error message for Google OAuth errors
  String getGoogleAuthErrorMessage(String errorCode) {
    switch (errorCode.toLowerCase()) {
      case 'sign_in_cancelled':
        return 'تم إلغاء تسجيل الدخول بواسطة Google';
      case 'sign_in_failed':
        return 'فشل في تسجيل الدخول بواسطة Google';
      case 'network_error':
        return 'خطأ في الاتصال بالشبكة';
      case 'missing_id_token':
        return 'فشل في الحصول على رمز التعريف من Google';
      case 'backend_exchange_failed':
        return 'فشل في التحقق من بيانات Google مع الخادم';
      case 'timeout_error':
        return 'انتهت مهلة الاتصال بالخادم';
      case 'server_error':
        return 'خطأ في الخادم - يرجى المحاولة لاحقاً';
      case 'unexpected_error':
        return 'حدث خطأ غير متوقع أثناء تسجيل الدخول';
      case 'sign_in_required':
        return 'يجب تسجيل الدخول أولاً';
      case 'account_exists_with_different_credential':
        return 'يوجد حساب بنفس البريد الإلكتروني بطريقة تسجيل دخول مختلفة';
      case 'invalid_credential':
        return 'بيانات الاعتماد غير صحيحة';
      case 'operation_not_allowed':
        return 'تسجيل الدخول بواسطة Google غير مفعل';
      case 'user_disabled':
        return 'تم تعطيل هذا الحساب';
      case 'user_not_found':
        return 'لم يتم العثور على المستخدم';
      case 'wrong_password':
        return 'كلمة المرور غير صحيحة';
      case 'not_configured':
        return 'خدمة تسجيل الدخول بواسطة Google غير مكونة. يرجى استدعاء initialize() أولاً.';
      default:
        return 'حدث خطأ غير متوقع أثناء تسجيل الدخول';
    }
  }
}

// =============================================================================
// RIVERPOD PROVIDERS
// =============================================================================

/// Provider for ProductionGoogleAuthService
final productionGoogleAuthServiceProvider = Provider<IGoogleAuthService>((ref) {
  final service = ProductionGoogleAuthService();
  
  // Auto-initialize the service with the proper client ID
  service.initialize(
    clientId: _getGoogleClientId(),
    scopes: ['email', 'profile'],
  );
  
  return service;
});

/// Provider for Google Sign-In configuration
final googleSignInConfigProvider = Provider<Map<String, dynamic>>((ref) {
  return {
    'client_id': _getGoogleClientId(),
    'scopes': ['email', 'profile'],
  };
});

/// Get Google OAuth Client ID based on platform
String _getGoogleClientId() {
  if (kIsWeb) {
    return '630980378491-vbd1bsp32o4g0664pmt1mhbj9raccnem.apps.googleusercontent.com'; // Web Client ID
  } else if (Platform.isAndroid) {
    return '630980378491-vbd1bsp32o4g0664pmt1mhbj9raccnem.apps.googleusercontent.com'; // Android Client ID
  } else if (Platform.isIOS) {
    return '630980378491-vbd1bsp32o4g0664pmt1mhbj9raccnem.apps.googleusercontent.com'; // iOS Client ID
  } else {
    return '630980378491-vbd1bsp32o4g0664pmt1mhbj9raccnem.apps.googleusercontent.com'; // Default Client ID
  }
}
