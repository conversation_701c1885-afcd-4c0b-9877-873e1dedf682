/// ============================================================================
/// AUTH STATE EXTENSIONS - Clean Helper Methods
/// ============================================================================
/// 
/// Extensions for AuthState to provide convenient helper methods
/// Following Forever Plan Architecture
/// ============================================================================
library;

import '../../../core/auth/auth_models.dart';

/// Extension methods for AuthState - Clean and minimal
extension AuthStateExtensions on AuthState {
  /// Check if currently loading
  bool get isLoading => this is AuthStateLoading;
  
  /// Check if has error
  bool get hasError => this is AuthStateError;
  
  /// طريقة when مشابهة لـ AsyncValue.when
  /// when method similar to AsyncValue.when  
  T when<T>({
    required T Function(AuthState data) data,
    required T Function() loading,
    required T Function(Object error, StackTrace stackTrace) error,
  }) {
    if (isLoading) {
      return loading();
    }
    
    if (hasError) {
      final errorState = this as AuthStateError;
      return error(errorState.message, StackTrace.current);
    }
    
    if (this is AuthStateAuthenticated) {
      return data(this);
    }
    
    return data(this);
  }

  /// طريقة whenOrNull مشابهة لـ AsyncValue.whenOrNull  
  T? whenOrNull<T>({
    T Function()? loading,
    T Function(String error)? error,
    T Function(AuthState)? data,
  }) {
    if (isLoading && loading != null) {
      return loading();
    }
    
    if (hasError && error != null) {
      final errorState = this as AuthStateError;
      return error(errorState.message);
    }
    
    if (this is AuthStateAuthenticated && data != null) {
      return data(this);
    }
    
    return null;
  }

  /// طريقة maybeWhen مشابهة لـ AsyncValue.maybeWhen
  /// maybeWhen method similar to AsyncValue.maybeWhen
  T maybeWhen<T>({
    T Function()? loading,
    T Function(String error)? error,
    T Function()? data,
    required T Function() orElse,
  }) {
    if (isLoading && loading != null) {
      return loading();
    }
    
    if (hasError && error != null) {
      final errorState = this as AuthStateError;
      return error(errorState.message);
    }
    
    if (this is AuthStateAuthenticated && data != null) {
      return data();
    }
    
    return orElse();
  }

  /// محاكاة .valueOrNull للتوافق مع كود AsyncValue
  /// Simulate .valueOrNull for AsyncValue compatibility
  AuthState? get valueOrNull => hasError ? null : this;
  
  /// محاكاة .value لـ AuthState
  /// Simulate .value for AuthState - returns this or throws if error
  AuthState get value {
    if (hasError) {
      final errorState = this as AuthStateError;
      throw Exception(errorState.message);
    }
    return this;
  }
} 