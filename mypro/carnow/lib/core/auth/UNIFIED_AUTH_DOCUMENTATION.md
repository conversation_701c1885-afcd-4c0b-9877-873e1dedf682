# نظام المصادقة الموحد - Unified Authentication System

## المقدمة - Introduction

تم إنشاء نظام مصادقة موحد واحترافي لتطبيق CarNow يدعم جميع طرق تسجيل الدخول ويحل جميع المشاكل السابقة.

A unified and professional authentication system for CarNow app that supports all sign-in methods and solves all previous issues.

## المكونات الأساسية - Core Components

### 1. النظام الأساسي - Core System

#### `UnifiedAuthSystem` (lib/core/auth/unified_auth_system.dart)
- المزود الرئيسي الوحيد للمصادقة
- Single main provider for authentication
- يدير جميع حالات المصادقة
- Manages all authentication states
- يدعم التسجيل التلقائي والفحص الدوري للجلسة
- Supports auto-login and periodic session checks

### 2. بيانات المصادقة - Auth Data

```dart
class AuthData {
  final AuthStatus status;      // حالة المصادقة
  final User? user;            // مستخدم Supabase
  final UserModel? profile;    // بيانات المستخدم من قاعدة البيانات
  final String? error;         // رسالة الخطأ
  final bool isAutoLoginAttempted; // تم محاولة التسجيل التلقائي
  
  // مساعدات للوصول السريع
  bool get isAuthenticated;    // مسجل دخول؟
  bool get isLoading;         // يتم التحميل؟
  bool get hasError;          // يوجد خطأ؟
}
```

### 3. حالات المصادقة - Auth Status

```dart
enum AuthStatus {
  initial,        // أولي
  checking,       // يتم الفحص
  authenticating, // يتم تسجيل الدخول
  authenticated,  // مسجل دخول
  unauthenticated,// غير مسجل دخول
  error,          // خطأ
}
```

## الاستخدام - Usage

### 1. المزودات الأساسية - Core Providers

```dart
// المزود الرئيسي
final authData = ref.watch(unifiedAuthSystemProvider);

// مزودات سريعة
final isAuth = ref.watch(isAuthenticatedProvider);
final isLoading = ref.watch(isAuthLoadingProvider);
final currentUser = ref.watch(currentUserProvider);
final userProfile = ref.watch(currentUserProfileProvider);
```

### 2. عمليات تسجيل الدخول - Sign In Operations

```dart
// تسجيل دخول بالبريد الإلكتروني
final authSystem = ref.read(unifiedAuthSystemProvider.notifier);
final success = await authSystem.signInWithEmail(email, password);

// تسجيل دخول بـ Google
final success = await authSystem.signInWithGoogle();

// تسجيل دخول بـ Facebook
final success = await authSystem.signInWithFacebook();

// تسجيل خروج
await authSystem.signOut();
```

### 3. استخدام المزودات المساعدة - Helper Providers

```dart
// للحصول على دالة تسجيل دخول جاهزة
final googleSignIn = ref.read(googleSignInProviderProvider);
await googleSignIn();

final emailSignIn = ref.read(emailSignInProviderProvider);
await emailSignIn(email, password);

final emailSignUp = ref.read(emailSignUpProviderProvider);
await emailSignUp(email, password, {'full_name': name});
```

## شاشة المصادقة الموحدة - Unified Auth Screen

### `UnifiedAuthScreen` (lib/core/screens/unified_auth_screen.dart)

شاشة مصادقة احترافية ونظيفة تدعم:
- تسجيل الدخول والتسجيل بالبريد الإلكتروني
- تسجيل الدخول بـ Google و Facebook
- التبديل بين تسجيل الدخول وإنشاء حساب جديد
- معالجة الأخطاء بشكل احترافي
- تصميم حديث ومتجاوب

Professional and clean auth screen supporting:
- Email sign-in and sign-up
- Google and Facebook sign-in
- Toggle between sign-in and sign-up modes
- Professional error handling
- Modern and responsive design

### الاستخدام:

```dart
// في التطبيق الخاص بك
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => const UnifiedAuthScreen(),
  ),
);
```

## الامتدادات - Extensions

### 1. امتدادات AuthData

```dart
extension AuthDataExtensions on AuthData {
  // طريقة when مشابهة لـ AsyncValue
  T when<T>({
    required T Function(AuthData) data,
    required T Function() loading,
    required T Function(Object error, StackTrace stackTrace) error,
  });
  
  // محاكاة .value و .valueOrNull
  AuthData get value;
  AuthData? get valueOrNull;
}
```

### 2. امتدادات User

```dart
extension UserExtensions on User? {
  // للتوافق مع AsyncValue
  User? get valueOrNull;
  User? get value;
  AsyncData<User>? get asData;
}
```

## أفضل الممارسات - Best Practices

### 1. استخدام المزودات الصحيحة

```dart
// ✅ صحيح - استخدم المزود الموحد
final authData = ref.watch(unifiedAuthSystemProvider);

// ❌ خطأ - لا تستخدم المزودات القديمة
// final auth = ref.watch(enhancedAuthProvider); // DEPRECATED
```

### 2. معالجة الحالات

```dart
final authData = ref.watch(unifiedAuthSystemProvider);

authData.when(
  data: (data) {
    if (data.isAuthenticated) {
      return HomePage();
    }
    return UnifiedAuthScreen();
  },
  loading: () => LoadingScreen(),
  error: (error, stack) => ErrorScreen(error),
);
```

### 3. تسجيل الخروج الآمن

```dart
Future<void> signOut() async {
  try {
    final authSystem = ref.read(unifiedAuthSystemProvider.notifier);
    await authSystem.signOut();
    // سيتم التوجيه التلقائي إلى شاشة تسجيل الدخول
  } catch (e) {
    // معالجة الخطأ
    showErrorDialog(e.toString());
  }
}
```

## إزالة النظم القديمة - Removing Old Systems

### ملفات محذوفة أو محتقرة:

1. **enhanced_auth_provider.dart** - محتقر، يوجه للنظام الموحد
2. **unified_provider_system.dart** - محتقر، طبقة توافق فقط
3. **auth_aliases.dart** - محتقر، للتوافق العكسي فقط
4. **smart_auth_screen.dart** - استخدم `UnifiedAuthScreen` بدلاً منه

### التحديث من النظم القديمة:

```dart
// القديم
final auth = ref.watch(enhancedAuthProvider);

// الجديد
final authData = ref.watch(unifiedAuthSystemProvider);
```

## استكشاف الأخطاء - Troubleshooting

### 1. خطأ "provider not found"

تأكد من استيراد الملفات الصحيحة:

```dart
import 'package:carnow/core/auth/unified_auth_system.dart';
import 'package:carnow/core/auth/auth_providers.dart';
```

### 2. خطأ في طريقة when

تأكد من استيراد الامتدادات:

```dart
import 'package:carnow/core/auth/auth_data_extensions.dart';
```

### 3. مشاكل التوليد

شغل build_runner:

```bash
flutter pub run build_runner build --delete-conflicting-outputs
```

## أمثلة كاملة - Complete Examples

### 1. شاشة حماية بسيطة

```dart
class ProtectedScreen extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authData = ref.watch(unifiedAuthSystemProvider);
    
    if (!authData.isAuthenticated) {
      return const UnifiedAuthScreen();
    }
    
    return Scaffold(
      appBar: AppBar(
        title: Text('شاشة محمية'),
        actions: [
          IconButton(
            icon: Icon(Icons.logout),
            onPressed: () async {
              final authSystem = ref.read(unifiedAuthSystemProvider.notifier);
              await authSystem.signOut();
            },
          ),
        ],
      ),
      body: Center(
        child: Text('مرحباً ${authData.user?.email}'),
      ),
    );
  }
}
```

### 2. شاشة تسجيل دخول مخصصة

```dart
class CustomLoginScreen extends ConsumerStatefulWidget {
  @override
  ConsumerState<CustomLoginScreen> createState() => _CustomLoginScreenState();
}

class _CustomLoginScreenState extends ConsumerState<CustomLoginScreen> {
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  
  @override
  Widget build(BuildContext context) {
    final authData = ref.watch(unifiedAuthSystemProvider);
    
    return Scaffold(
      body: Column(
        children: [
          TextField(
            controller: _emailController,
            decoration: InputDecoration(labelText: 'البريد الإلكتروني'),
          ),
          TextField(
            controller: _passwordController,
            decoration: InputDecoration(labelText: 'كلمة المرور'),
            obscureText: true,
          ),
          ElevatedButton(
            onPressed: authData.isLoading ? null : _signIn,
            child: authData.isLoading 
                ? CircularProgressIndicator() 
                : Text('تسجيل الدخول'),
          ),
          if (authData.hasError)
            Text(
              authData.error!,
              style: TextStyle(color: Colors.red),
            ),
        ],
      ),
    );
  }
  
  Future<void> _signIn() async {
    final authSystem = ref.read(unifiedAuthSystemProvider.notifier);
    final success = await authSystem.signInWithEmail(
      _emailController.text,
      _passwordController.text,
    );
    
    if (success && mounted) {
      Navigator.of(context).pushReplacementNamed('/home');
    }
  }
}
```

## الخلاصة - Summary

النظام الموحد يوفر:
- **البساطة**: واجهة API واحدة وواضحة
- **الموثوقية**: معالجة أخطاء محسنة وفحص دوري للجلسة
- **المرونة**: دعم لجميع طرق تسجيل الدخول
- **الأداء**: تحسينات في الذاكرة وإدارة الحالة
- **الصيانة**: كود منظم وموثق جيداً

The unified system provides:
- **Simplicity**: Single, clear API interface
- **Reliability**: Enhanced error handling and periodic session checks
- **Flexibility**: Support for all sign-in methods
- **Performance**: Memory and state management optimizations
- **Maintainability**: Well-organized and documented code 