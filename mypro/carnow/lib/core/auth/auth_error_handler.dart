import 'auth_interfaces.dart';

/// Implementation of authentication error handling interface
class AuthErrorHandler implements IAuthErrorHandler {
  static const Map<String, String> _errorMessages = {
    'INVALID_CREDENTIALS': 'Invalid email or password',
    'USER_NOT_FOUND': 'User not found',
    'EMAIL_EXISTS': 'Email already registered',
    'RATE_LIMIT_EXCEEDED': 'Too many attempts, please try again later',
    'INTERNAL_ERROR': 'Internal server error',
    'SERVICE_UNAVAILABLE': 'Service temporarily unavailable',
    'NETWORK_ERROR': 'Network connection error',
    'INVALID_EMAIL': 'Invalid email format',
    'WEAK_PASSWORD': 'Password does not meet requirements',
    'GOOGLE_AUTH_FAILED': 'Google authentication failed',
    'TOKEN_EXPIRED': 'Session expired, please login again',
    'INVALID_TOKEN': 'Invalid authentication token',
  };

  static const Map<String, String> _localizedMessages = {
    'INVALID_CREDENTIALS': 'البريد الإلكتروني أو كلمة المرور غير صحيحة',
    'USER_NOT_FOUND': 'المستخدم غير موجود',
    'EMAIL_EXISTS': 'البريد الإلكتروني مستخدم بالفعل',
    'RATE_LIMIT_EXCEEDED': 'محاولات كثيرة، يرجى المحاولة لاحقاً',
    'INTERNAL_ERROR': 'خطأ في الخادم الداخلي',
    'SERVICE_UNAVAILABLE': 'الخدمة غير متاحة حالياً',
    'NETWORK_ERROR': 'خطأ في الاتصال بالشبكة',
    'INVALID_EMAIL': 'تنسيق البريد الإلكتروني غير صحيح',
    'WEAK_PASSWORD': 'كلمة المرور لا تلبي المتطلبات',
    'GOOGLE_AUTH_FAILED': 'فشل في المصادقة عبر جوجل',
    'TOKEN_EXPIRED': 'انتهت صلاحية الجلسة، يرجى تسجيل الدخول مرة أخرى',
    'INVALID_TOKEN': 'رمز المصادقة غير صالح',
  };

  static const Set<String> _recoverableErrors = {
    'NETWORK_ERROR',
    'SERVICE_UNAVAILABLE',
    'RATE_LIMIT_EXCEEDED',
    'TOKEN_EXPIRED',
  };

  @override
  String handleError(String errorCode, [String? defaultMessage]) {
    return _errorMessages[errorCode] ??
        defaultMessage ??
        'Unknown error occurred';
  }

  @override
  String getLocalizedMessage(String errorCode) {
    return _localizedMessages[errorCode] ??
        _errorMessages[errorCode] ??
        'حدث خطأ غير متوقع';
  }

  @override
  bool isRecoverableError(String errorCode) {
    return _recoverableErrors.contains(errorCode);
  }
}
