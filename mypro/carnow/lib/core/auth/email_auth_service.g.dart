// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'email_auth_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$emailAuthServiceHash() => r'1427522d0e6b51e5dcccbff5f560c73d32f15bf4';

/// Enhanced email authentication service with comprehensive validation
/// and error handling for the CarNow application
///
/// Copied from [EmailAuthService].
@ProviderFor(EmailAuthService)
final emailAuthServiceProvider =
    AutoDisposeNotifierProvider<EmailAuthService, EmailAuthService>.internal(
      EmailAuthService.new,
      name: r'emailAuthServiceProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$emailAuthServiceHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$EmailAuthService = AutoDisposeNotifier<EmailAuthService>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
