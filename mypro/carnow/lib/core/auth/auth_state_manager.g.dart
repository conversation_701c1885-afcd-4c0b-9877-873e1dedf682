// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'auth_state_manager.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$authStateManagerHash() => r'911b7a8a6802a6d822a6a96567089f3bda0b0791';

/// Enhanced authentication state manager with centralized coordination
/// Manages authentication state transitions and provider coordination
///
/// Copied from [AuthStateManager].
@ProviderFor(AuthStateManager)
final authStateManagerProvider =
    AutoDisposeNotifierProvider<AuthStateManager, AuthStateManager>.internal(
      AuthStateManager.new,
      name: r'authStateManagerProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$authStateManagerHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$AuthStateManager = AutoDisposeNotifier<AuthStateManager>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
