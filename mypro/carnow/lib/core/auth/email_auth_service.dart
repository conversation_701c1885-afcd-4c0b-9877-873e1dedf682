// =============================================================================
// CARNOW EMAIL AUTHENTICATION SERVICE
// =============================================================================
// 
// This file implements comprehensive email authentication functionality
// including input validation, password strength validation, and error handling.
//
// Architecture: Flutter (UI Only) → Go API → Supabase (Data Only)
// 
// Key Features:
// - Email format validation with regex patterns
// - Password strength validation with comprehensive rules
// - Input sanitization and security validation
// - Network error handling and retry logic
// - Arabic and English error messages
// - Production-ready logging and debugging
//
// Author: CarNow Development Team
// =============================================================================

import 'dart:developer' as developer;

import 'package:riverpod_annotation/riverpod_annotation.dart';

import 'auth_models.dart';

part 'email_auth_service.g.dart';

// =============================================================================
// EMAIL VALIDATION UTILITIES
// =============================================================================

/// Comprehensive email validation utility class
class EmailValidator {
  // Email regex pattern - RFC 5322 compliant
  static final RegExp _emailRegex = RegExp(
    r'^[a-zA-Z0-9.!#$%&*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$',
  );
  
  // Common disposable email domains to block
  static final Set<String> _disposableDomains = {
    '10minutemail.com',
    'tempmail.org',
    'guerrillamail.com',
    'mailinator.com',
    'throwaway.email',
    'temp-mail.org',
  };
  
  /// Validate email format and domain
  static EmailValidationResult validateEmail(String email) {
    // Basic null/empty check
    if (email.isEmpty) {
      return EmailValidationResult.invalid(
        'البريد الإلكتروني مطلوب',
        EmailValidationError.empty,
      );
    }
    
    // Trim whitespace
    email = email.trim().toLowerCase();
    
    // Length check
    if (email.length > 254) {
      return EmailValidationResult.invalid(
        'البريد الإلكتروني طويل جداً',
        EmailValidationError.tooLong,
      );
    }
    
    // Format validation
    if (!_emailRegex.hasMatch(email)) {
      return EmailValidationResult.invalid(
        'تنسيق البريد الإلكتروني غير صحيح',
        EmailValidationError.invalidFormat,
      );
    }
    
    // Domain extraction and validation
    final parts = email.split('@');
    if (parts.length != 2) {
      return EmailValidationResult.invalid(
        'تنسيق البريد الإلكتروني غير صحيح',
        EmailValidationError.invalidFormat,
      );
    }
    
    final domain = parts[1];
    
    // Check for disposable email domains
    if (_disposableDomains.contains(domain)) {
      return EmailValidationResult.invalid(
        'البريد الإلكتروني المؤقت غير مسموح',
        EmailValidationError.disposableEmail,
      );
    }
    
    // Domain format check
    if (domain.isEmpty || domain.startsWith('.') || domain.endsWith('.')) {
      return EmailValidationResult.invalid(
        'نطاق البريد الإلكتروني غير صحيح',
        EmailValidationError.invalidDomain,
      );
    }
    
    return EmailValidationResult.valid(email);
  }
}

/// Email validation result
class EmailValidationResult {
  final bool isValid;
  final String email;
  final String? errorMessage;
  final EmailValidationError? errorType;
  
  const EmailValidationResult._({
    required this.isValid,
    required this.email,
    this.errorMessage,
    this.errorType,
  });
  
  factory EmailValidationResult.valid(String email) {
    return EmailValidationResult._(
      isValid: true,
      email: email,
    );
  }
  
  factory EmailValidationResult.invalid(
    String errorMessage,
    EmailValidationError errorType,
  ) {
    return EmailValidationResult._(
      isValid: false,
      email: '',
      errorMessage: errorMessage,
      errorType: errorType,
    );
  }
}

/// Email validation error types
enum EmailValidationError {
  empty,
  tooLong,
  invalidFormat,
  invalidDomain,
  disposableEmail,
}

// =============================================================================
// PASSWORD VALIDATION UTILITIES
// =============================================================================

/// Comprehensive password validation utility class
class PasswordValidator {
  /// Minimum password length
  static const int minLength = 8;
  
  /// Maximum password length
  static const int maxLength = 128;
  
  /// Validate password strength and security
  static PasswordValidationResult validatePassword(String password) {
    final errors = <PasswordValidationError>[];
    final suggestions = <String>[];
    
    // Basic null/empty check
    if (password.isEmpty) {
      return PasswordValidationResult.invalid(
        'كلمة المرور مطلوبة',
        [PasswordValidationError.empty],
        ['أدخل كلمة مرور'],
      );
    }
    
    // Length validation
    if (password.length < minLength) {
      errors.add(PasswordValidationError.tooShort);
      suggestions.add('استخدم على الأقل $minLength أحرف');
    }
    
    if (password.length > maxLength) {
      errors.add(PasswordValidationError.tooLong);
      suggestions.add('استخدم أقل من $maxLength حرف');
    }
    
    // Character type validation
    bool hasLowercase = password.contains(RegExp(r'[a-z]'));
    bool hasUppercase = password.contains(RegExp(r'[A-Z]'));
    bool hasDigits = password.contains(RegExp(r'[0-9]'));
    bool hasSpecialChars = password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'));
    
    if (!hasLowercase) {
      errors.add(PasswordValidationError.noLowercase);
      suggestions.add('أضف حروف صغيرة (a-z)');
    }
    
    if (!hasUppercase) {
      errors.add(PasswordValidationError.noUppercase);
      suggestions.add('أضف حروف كبيرة (A-Z)');
    }
    
    if (!hasDigits) {
      errors.add(PasswordValidationError.noDigits);
      suggestions.add('أضف أرقام (0-9)');
    }
    
    if (!hasSpecialChars) {
      errors.add(PasswordValidationError.noSpecialChars);
      suggestions.add('أضف رموز خاصة (!@#\$%^&*)');
    }
    
    // Common password patterns
    if (_isCommonPassword(password)) {
      errors.add(PasswordValidationError.tooCommon);
      suggestions.add('تجنب كلمات المرور الشائعة');
    }
    
    // Sequential characters
    if (_hasSequentialChars(password)) {
      errors.add(PasswordValidationError.sequential);
      suggestions.add('تجنب الأحرف المتتالية (123, abc)');
    }
    
    // Repeated characters
    if (_hasRepeatedChars(password)) {
      errors.add(PasswordValidationError.repeated);
      suggestions.add('تجنب تكرار الأحرف (aaa, 111)');
    }
    
    // Calculate strength score
    int strength = _calculatePasswordStrength(password);
    
    if (errors.isEmpty) {
      return PasswordValidationResult.valid(password, strength);
    } else {
      String errorMessage = 'كلمة المرور ضعيفة: ${suggestions.join('، ')}';
      return PasswordValidationResult.invalid(errorMessage, errors, suggestions);
    }
  }
  
  /// Check if password is commonly used
  static bool _isCommonPassword(String password) {
    final commonPasswords = {
      'password', '123456', '123456789', 'qwerty', 'abc123',
      'password123', 'admin', 'letmein', 'welcome', 'monkey',
      'dragon', 'master', 'shadow', 'superman', 'michael',
    };
    
    return commonPasswords.contains(password.toLowerCase());
  }
  
  /// Check for sequential characters
  static bool _hasSequentialChars(String password) {
    final sequences = ['123', '234', '345', '456', '567', '678', '789',
                      'abc', 'bcd', 'cde', 'def', 'efg', 'fgh', 'ghi'];
    
    final lowerPassword = password.toLowerCase();
    return sequences.any((seq) => lowerPassword.contains(seq));
  }
  
  /// Check for repeated characters
  static bool _hasRepeatedChars(String password) {
    for (int i = 0; i < password.length - 2; i++) {
      if (password[i] == password[i + 1] && password[i] == password[i + 2]) {
        return true;
      }
    }
    return false;
  }
  
  /// Calculate password strength score (0-100)
  static int _calculatePasswordStrength(String password) {
    int score = 0;
    
    // Length bonus
    score += (password.length * 2);
    
    // Character variety bonus
    if (password.contains(RegExp(r'[a-z]'))) score += 10;
    if (password.contains(RegExp(r'[A-Z]'))) score += 10;
    if (password.contains(RegExp(r'[0-9]'))) score += 10;
    if (password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'))) score += 15;
    
    // Unique characters bonus
    final uniqueChars = password.split('').toSet().length;
    score += (uniqueChars * 2);
    
    // Penalties
    if (_isCommonPassword(password)) score -= 20;
    if (_hasSequentialChars(password)) score -= 10;
    if (_hasRepeatedChars(password)) score -= 10;
    
    return score.clamp(0, 100);
  }
}

/// Password validation result
class PasswordValidationResult {
  final bool isValid;
  final String password;
  final int strength;
  final String? errorMessage;
  final List<PasswordValidationError> errors;
  final List<String> suggestions;
  
  const PasswordValidationResult._({
    required this.isValid,
    required this.password,
    required this.strength,
    this.errorMessage,
    required this.errors,
    required this.suggestions,
  });
  
  factory PasswordValidationResult.valid(String password, int strength) {
    return PasswordValidationResult._(
      isValid: true,
      password: password,
      strength: strength,
      errors: [],
      suggestions: [],
    );
  }
  
  factory PasswordValidationResult.invalid(
    String errorMessage,
    List<PasswordValidationError> errors,
    List<String> suggestions,
  ) {
    return PasswordValidationResult._(
      isValid: false,
      password: '',
      strength: 0,
      errorMessage: errorMessage,
      errors: errors,
      suggestions: suggestions,
    );
  }
  
  /// Get password strength description
  String get strengthDescription {
    if (strength >= 80) return 'قوية جداً';
    if (strength >= 60) return 'قوية';
    if (strength >= 40) return 'متوسطة';
    if (strength >= 20) return 'ضعيفة';
    return 'ضعيفة جداً';
  }
  
  /// Get strength color indicator
  String get strengthColor {
    if (strength >= 80) return 'green';
    if (strength >= 60) return 'lightgreen';
    if (strength >= 40) return 'orange';
    if (strength >= 20) return 'red';
    return 'darkred';
  }
}

/// Password validation error types
enum PasswordValidationError {
  empty,
  tooShort,
  tooLong,
  noLowercase,
  noUppercase,
  noDigits,
  noSpecialChars,
  tooCommon,
  sequential,
  repeated,
}

// =============================================================================
// EMAIL AUTHENTICATION SERVICE
// =============================================================================

/// Enhanced email authentication service with comprehensive validation
/// and error handling for the CarNow application
@riverpod
class EmailAuthService extends _$EmailAuthService {
  @override
  EmailAuthService build() {
    // Service initialization
    developer.log(
      'EmailAuthService initialized',
      name: 'EmailAuthService',
    );
    return this;
  }
  
  // ---------------------------------------------------------------------------
  // Email Validation Methods
  // ---------------------------------------------------------------------------
  
  /// Validate email format and domain
  EmailValidationResult validateEmail(String email) {
    developer.log(
      'Validating email format',
      name: 'EmailAuthService',
    );
    
    return EmailValidator.validateEmail(email);
  }
  
  /// Validate password strength and security
  PasswordValidationResult validatePassword(String password) {
    developer.log(
      'Validating password strength',
      name: 'EmailAuthService',
    );
    
    return PasswordValidator.validatePassword(password);
  }
  
  /// Validate password confirmation match
  bool validatePasswordConfirmation(String password, String confirmation) {
    if (password.isEmpty || confirmation.isEmpty) {
      return false;
    }
    
    return password == confirmation;
  }
  
  // ---------------------------------------------------------------------------
  // Name Validation Methods
  // ---------------------------------------------------------------------------
  
  /// Validate first name
  String? validateFirstName(String firstName) {
    if (firstName.isEmpty) {
      return 'الاسم الأول مطلوب';
    }
    
    if (firstName.length < 2) {
      return 'الاسم الأول قصير جداً';
    }
    
    if (firstName.length > 50) {
      return 'الاسم الأول طويل جداً';
    }
    
    // Check for valid characters (letters, spaces, hyphens, apostrophes)
    if (!RegExp(r"^[a-zA-Z\u0600-\u06FF\s\-']+$").hasMatch(firstName)) {
      return 'الاسم الأول يحتوي على أحرف غير صحيحة';
    }
    
    return null;
  }
  
  /// Validate last name
  String? validateLastName(String lastName) {
    if (lastName.isEmpty) {
      return 'اسم العائلة مطلوب';
    }
    
    if (lastName.length < 2) {
      return 'اسم العائلة قصير جداً';
    }
    
    if (lastName.length > 50) {
      return 'اسم العائلة طويل جداً';
    }
    
    // Check for valid characters (letters, spaces, hyphens, apostrophes)
    if (!RegExp(r"^[a-zA-Z\u0600-\u06FF\s\-']+$").hasMatch(lastName)) {
      return 'اسم العائلة يحتوي على أحرف غير صحيحة';
    }
    
    return null;
  }
  
  // ---------------------------------------------------------------------------
  // Comprehensive Form Validation
  // ---------------------------------------------------------------------------
  
  /// Validate complete sign-in form
  SignInValidationResult validateSignInForm({
    required String email,
    required String password,
  }) {
    final errors = <String, String>{};
    
    // Validate email
    final emailResult = validateEmail(email);
    if (!emailResult.isValid) {
      errors['email'] = emailResult.errorMessage!;
    }
    
    // Basic password validation for sign-in (less strict)
    if (password.isEmpty) {
      errors['password'] = 'كلمة المرور مطلوبة';
    }
    
    return SignInValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      validatedEmail: emailResult.isValid ? emailResult.email : '',
    );
  }
  
  /// Validate complete sign-up form
  SignUpValidationResult validateSignUpForm({
    required String firstName,
    required String lastName,
    required String email,
    required String password,
    required String passwordConfirmation,
  }) {
    final errors = <String, String>{};
    
    // Validate first name
    final firstNameError = validateFirstName(firstName);
    if (firstNameError != null) {
      errors['firstName'] = firstNameError;
    }
    
    // Validate last name
    final lastNameError = validateLastName(lastName);
    if (lastNameError != null) {
      errors['lastName'] = lastNameError;
    }
    
    // Validate email
    final emailResult = validateEmail(email);
    if (!emailResult.isValid) {
      errors['email'] = emailResult.errorMessage!;
    }
    
    // Validate password
    final passwordResult = validatePassword(password);
    if (!passwordResult.isValid) {
      errors['password'] = passwordResult.errorMessage!;
    }
    
    // Validate password confirmation
    if (!validatePasswordConfirmation(password, passwordConfirmation)) {
      errors['passwordConfirmation'] = 'كلمات المرور غير متطابقة';
    }
    
    return SignUpValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      validatedEmail: emailResult.isValid ? emailResult.email : '',
      validatedFirstName: firstNameError == null ? firstName.trim() : '',
      validatedLastName: lastNameError == null ? lastName.trim() : '',
      passwordStrength: passwordResult.strength,
    );
  }
  
  // ---------------------------------------------------------------------------
  // Input Sanitization
  // ---------------------------------------------------------------------------
  
  /// Sanitize and normalize email input
  String sanitizeEmail(String email) {
    return email.trim().toLowerCase();
  }
  
  /// Sanitize name input
  String sanitizeName(String name) {
    // Trim whitespace and normalize spaces
    return name.trim().replaceAll(RegExp(r'\s+'), ' ');
  }
  
  // ---------------------------------------------------------------------------
  // Error Message Helpers
  // ---------------------------------------------------------------------------
  
  /// Get user-friendly error message for authentication errors
  String getAuthErrorMessage(AuthErrorType errorType) {
    switch (errorType) {
      case AuthErrorType.invalidCredentials:
        return 'البريد الإلكتروني أو كلمة المرور غير صحيحة';
      case AuthErrorType.userNotFound:
        return 'المستخدم غير موجود';
      case AuthErrorType.emailAlreadyExists:
        return 'البريد الإلكتروني مستخدم بالفعل';
      case AuthErrorType.weakPassword:
        return 'كلمة المرور ضعيفة جداً';
      case AuthErrorType.networkError:
        return 'خطأ في الاتصال بالشبكة';
      case AuthErrorType.serverError:
        return 'خطأ في الخادم، يرجى المحاولة لاحقاً';
      case AuthErrorType.rateLimitExceeded:
        return 'تم تجاوز الحد المسموح، يرجى المحاولة لاحقاً';
      case AuthErrorType.emailNotVerified:
        return 'يرجى التحقق من بريدك الإلكتروني أولاً';
      case AuthErrorType.accountDisabled:
        return 'الحساب معطل، يرجى التواصل مع الدعم';
      case AuthErrorType.sessionExpired:
        return 'انتهت صلاحية الجلسة، يرجى تسجيل الدخول مرة أخرى';
      case AuthErrorType.unknown:
      default:
        return 'حدث خطأ غير متوقع، يرجى المحاولة مرة أخرى';
    }
  }
}

// =============================================================================
// VALIDATION RESULT CLASSES
// =============================================================================

/// Sign-in form validation result
class SignInValidationResult {
  final bool isValid;
  final Map<String, String> errors;
  final String validatedEmail;
  
  const SignInValidationResult({
    required this.isValid,
    required this.errors,
    required this.validatedEmail,
  });
}

/// Sign-up form validation result
class SignUpValidationResult {
  final bool isValid;
  final Map<String, String> errors;
  final String validatedEmail;
  final String validatedFirstName;
  final String validatedLastName;
  final int passwordStrength;
  
  const SignUpValidationResult({
    required this.isValid,
    required this.errors,
    required this.validatedEmail,
    required this.validatedFirstName,
    required this.validatedLastName,
    required this.passwordStrength,
  });
}
