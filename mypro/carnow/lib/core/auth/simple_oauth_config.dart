/// ============================================================================
/// SIMPLE OAUTH CONFIG - Forever Plan Architecture
/// ============================================================================
///
/// إعدادات OAuth البسيطة - بنية الخطة الدائمة
/// Simple OAuth configuration for CarNow authentication
///
/// يدعم Google OAuth فقط (تم حذف Facebook)
/// ============================================================================
library;

import 'package:flutter/foundation.dart';
import '../config/env_config.dart';

/// إعدادات OAuth البسيطة
/// Simple OAuth configuration utility
class SimpleOAuthConfig {
  // ==========================================================================
  // Google OAuth Configuration - إعدادات Google OAuth
  // ==========================================================================

  /// الحصول على Google Client ID حسب النظام الأساسي
  /// Get Google Client ID based on platform
  static String getGoogleClientId() {
    if (kIsWeb) {
      return EnvConfig.googleWebClientId;
    } else {
      // Android/iOS - use Android client ID
      return EnvConfig.googleAndroidClientId;
    }
  }

  /// الحصول على رابط إعادة التوجيه لـ Google OAuth
  /// Get Google OAuth redirect URL
  static String getGoogleRedirectUrl() {
    if (kIsWeb) {
      return EnvConfig.webRedirectUrl;
    } else {
      // Android/iOS - use Supabase callback URL
      return EnvConfig.googleMobileRedirectUrl;
    }
  }

  /// الحصول على رابط إعادة التوجيه الاحتياطي
  /// Get fallback redirect URL
  static String getGoogleRedirectUrlFallback() {
    return EnvConfig.googleMobileRedirectUrlSecondary;
  }

  /// الحصول على قائمة روابط إعادة التوجيه الاحتياطية
  /// Get list of fallback redirect URLs
  static List<String> getGoogleRedirectUrlFallbacks() {
    return [getGoogleRedirectUrl(), getGoogleRedirectUrlFallback()];
  }

  /// الحصول على معاملات Google OAuth
  /// Get Google OAuth parameters
  static Map<String, String> getGoogleOAuthParams() {
    return {
      'client_id': getGoogleClientId(),
      'access_type': 'offline',
      // 'prompt': 'consent', // REMOVED to allow account selection
    };
  }

  // ==========================================================================
  // Deep Link Configuration - إعدادات الروابط العميقة
  // ==========================================================================

  /// الحصول على app scheme
  /// Get app scheme
  static String getAppScheme() {
    return 'io.supabase.carnow';
  }

  /// الحصول على auth host
  /// Get auth host
  static String getAuthHost() {
    return 'login-callback';
  }

  /// الحصول على auth host احتياطي
  /// Get fallback auth host
  static String getAuthHostFallback() {
    return 'auth-callback';
  }

  /// بناء رابط عميق للمصادقة
  /// Build authentication deep link
  static String buildAuthDeepLink() {
    return '${getAppScheme()}://${getAuthHost()}/';
  }

  // ==========================================================================
  // Validation Methods - طرق التحقق
  // ==========================================================================

  /// التحقق من صحة OAuth callback
  /// Validate OAuth callback
  static bool isValidOAuthCallback(String url) {
    final uri = Uri.tryParse(url);
    if (uri == null) return false;

    return uri.scheme == getAppScheme() &&
        (uri.host == getAuthHost() || uri.host == getAuthHostFallback()) &&
        (uri.queryParameters.containsKey('code') ||
            uri.queryParameters.containsKey('error') ||
            uri.queryParameters.containsKey('access_token'));
  }

  /// استخراج authorization code من URL
  /// Extract authorization code from URL
  static String? extractAuthCodeFromUrl(String url) {
    final uri = Uri.tryParse(url);
    return uri?.queryParameters['code'];
  }

  /// استخراج error من URL
  /// Extract error from URL
  static String? extractErrorFromUrl(String url) {
    final uri = Uri.tryParse(url);
    if (uri == null) return null;

    final error = uri.queryParameters['error'];
    final errorDescription = uri.queryParameters['error_description'];

    if (error != null && errorDescription != null) {
      return '$error: $errorDescription';
    }

    return error ?? errorDescription;
  }

  /// التحقق من صحة redirect URL
  /// Validate redirect URL
  static bool validateRedirectUrl(String url) {
    try {
      final uri = Uri.parse(url);
      return uri.scheme.isNotEmpty && uri.host.isNotEmpty;
    } catch (e) {
      return false;
    }
  }

  /// التحقق من صحة إعدادات OAuth
  /// Validate OAuth configuration
  static bool validateOAuthConfig() {
    try {
      final clientId = getGoogleClientId();
      final redirectUrl = getGoogleRedirectUrl();

      if (clientId.isEmpty || redirectUrl.isEmpty) {
        if (kDebugMode) {
          print('❌ SimpleOAuthConfig: Missing client ID or redirect URL');
        }
        return false;
      }

      if (!validateRedirectUrl(redirectUrl)) {
        if (kDebugMode) {
          print('❌ SimpleOAuthConfig: Invalid redirect URL format');
        }
        return false;
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ SimpleOAuthConfig: Validation error: $e');
      }
      return false;
    }
  }

  /// طباعة ملخص إعدادات OAuth
  /// Print OAuth configuration summary
  static void printOAuthConfigSummary() {
    if (!kDebugMode) return;

    print('=== Simple OAuth Configuration ===');
    print('Platform: ${kIsWeb ? 'Web' : 'Mobile'}');
    print('Google Client ID: ${getGoogleClientId()}');
    print('Google Redirect URL: ${getGoogleRedirectUrl()}');
    print('Google Fallback URL: ${getGoogleRedirectUrlFallback()}');
    print('App Scheme: ${getAppScheme()}');
    print('Auth Host: ${getAuthHost()}');
    print('Config Valid: ${validateOAuthConfig()}');
    print('==================================');
  }
}
