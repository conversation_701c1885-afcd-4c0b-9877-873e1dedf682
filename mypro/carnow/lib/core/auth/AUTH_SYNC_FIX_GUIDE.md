# دليل إصلاح مشكلة التزامن في حالة المصادقة

## وصف المشكلة

أحياناً قد تحدث مشكلة حيث يكون المستخدم مسجل دخول فعلياً (الجلسة صالحة) لكن الواجهة تظهر كما لو أنه غير مسجل دخول. هذا يحدث عادة بسبب:

1. **Facebook OAuth Token Expiry** - انتهاء صلاحية token الفيسبوك
2. **Token Refresh Issues** - مشاكل في تحديث التوكن
3. **State Synchronization** - عدم تزامن بين حالة المصادقة والواجهة

## أعراض المشكلة

- الواجهة تظهر كما لو أن المستخدم غير مسجل دخول
- في الـ logs يظهر: `AuthChangeEvent.tokenRefreshed`
- خطأ Facebook: `Invalid OAuth access token signature`
- المستخدم يتم جلبه من قاعدة البيانات بنجاح
- `UserSessionState.authenticatedCompleteProfile` موجود

## الحل المطبق

### 1. تحسين معالجة `tokenRefreshed`

```dart
case AuthChangeEvent.tokenRefreshed:
  if (session?.user != null) {
    // إذا كان المستخدم مسجل دخول، نحدث البروفايل فقط
    if (state.isAuthenticated && state.user?.id == session!.user.id) {
      await _refreshUserProfile(session.user.id);
    } 
    // إذا لم يكن مسجل دخول لكن الجلسة صالحة، نعيد تسجيل الدخول
    else if (_isValidSession(session!)) {
      await _handleAuthSuccess(session.user, skipAutoLogin: true);
    }
  }
```

### 2. إضافة دالة إعادة ضبط الحالة

```dart
/// إعادة تعيين حالة المصادقة بقوة (لحل مشاكل التزامن)
Future<void> forceResetAuthState() async {
  // إيقاف جميع المؤقتات والاشتراكات
  _sessionCheckTimer?.cancel();
  _authSubscription?.cancel();
  
  // فحص الجلسة الحالية
  final currentSession = SupabaseService.staticClient.auth.currentSession;
  
  if (currentSession?.user != null && _isValidSession(currentSession!)) {
    await _handleAuthSuccess(currentSession.user, skipAutoLogin: true);
  } else {
    await _handleSignOut();
  }
  
  // إعادة تشغيل النظام
  await _initializeAuthSystem();
}
```

## كيفية الاستخدام

### الحل التلقائي

النظام الآن يكتشف ويصلح المشكلة تلقائياً، لكن يمكن أيضاً استخدام الحل اليدوي:

```dart
import 'package:carnow/core/auth/auth_state_fix.dart';

// في أي Widget
class MyWidget extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authData = ref.watch(unifiedAuthSystemProvider);
    final authFix = ref.watch(authStateFixProvider);
    
    // إذا كانت هناك مشكلة تزامن
    if (authData.isAuthenticated && authData.user == null) {
      return ElevatedButton(
        onPressed: () async {
          await authFix();
        },
        child: Text('إعادة تحميل حالة المصادقة'),
      );
    }
    
    return YourNormalWidget();
  }
}
```

### الحل اليدوي

```dart
// في أي مكان في التطبيق
final authSystem = ref.read(unifiedAuthSystemProvider.notifier);
await authSystem.forceResetAuthState();
```

## الوقاية من المشكلة

### 1. تنظيف Facebook Token

التطبيق يقوم بتنظيف Facebook tokens تلقائياً عند البدء:

```dart
// في main.dart
await cleanFacebookToken();
```

### 2. فحص دوري للجلسة

النظام يفحص صحة الجلسة كل 15 دقيقة:

```dart
Timer.periodic(Duration(minutes: 15), (_) => _checkSessionValidity());
```

### 3. معالجة Facebook OAuth Errors

```dart
// معالجة خاصة لأخطاء Facebook OAuth
if (error.toString().contains('Invalid OAuth access token signature')) {
  await _cleanupFacebookToken();
}
```

## تسجيل الأخطاء

النظام يسجل جميع الأحداث في الـ logs:

```dart
I/flutter: 📡 تغيير حالة المصادقة: AuthChangeEvent.tokenRefreshed
I/flutter: 🔄 تحديث الجلسة
I/flutter: 🔄 جلسة صالحة أثناء تحديث التوكن، إعادة تسجيل دخول
I/flutter: ✅ تم إصلاح مشكلة حالة المصادقة
```

## الاختبار

لاختبار الحل:

1. قم بتسجيل الدخول بفيسبوك
2. انتظر حتى ينتهي Facebook token
3. راقب الـ logs للتأكد من معالجة `tokenRefreshed`
4. تأكد من أن الواجهة تنعكس حالة المصادقة الصحيحة

## المساعدة

إذا استمرت المشكلة:

1. تحقق من الـ logs
2. استخدم `forceResetAuthState()`
3. تأكد من أن Facebook OAuth معد بشكل صحيح
4. تحقق من صحة Supabase session

## الملاحظات

- هذا الحل آمن ولا يؤثر على أداء التطبيق
- يعمل مع جميع طرق تسجيل الدخول (Email, Google, Facebook)
- يحافظ على جلسة المستخدم ولا يتطلب إعادة تسجيل دخول
- يحل مشكلة التزامن بين المصادقة والواجهة 