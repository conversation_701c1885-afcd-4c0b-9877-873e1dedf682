// =============================================================================
// CARNOW GOOGLE OAUTH AUTHENTICATION SERVICE
// =============================================================================
//
// This file implements simplified Google OAuth authentication functionality
// for the CarNow unified authentication system.
//
// Architecture: Flutter (UI Only) → Go API → Supabase (Data Only)
//
// Key Features:
// - Google Sign-In simulation with proper error handling
// - User profile extraction and conversion
// - Arabic error messages and localization
// - Production-ready logging and debugging
//
// Author: CarNow Development Team
// =============================================================================

import 'package:google_sign_in/google_sign_in.dart';
import 'package:logger/logger.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'google_oauth_service.g.dart';

final _logger = Logger();

/// Google OAuth Service Configuration
/// تكوين خدمة Google OAuth
class GoogleOAuthConfig {
  /// Android client ID for Google Sign-In
  static const String androidClientId =
      '************-vbd1bsp32o4g0664pmt1mhbj9raccnem.apps.googleusercontent.com';

  /// iOS client ID for Google Sign-In
  static const String iosClientId =
      '************-vbd1bsp32o4g0664pmt1mhbj9raccnem.apps.googleusercontent.com';

  /// Web client ID for Google Sign-In (if needed)
  static const String webClientId =
      '************-vbd1bsp32o4g0664pmt1mhbj9raccnem.apps.googleusercontent.com';

  /// OAuth scopes requested from Google
  static const List<String> scopes = ['email', 'profile', 'openid'];

  /// Hosted domain (optional - for organization accounts)
  static const String? hostedDomain = null;

  /// Force code for server auth (recommended for production)
  static const bool forceCodeForRefreshToken = true;

  /// Enable mock mode for development/testing
  /// تفعيل الوضع الوهمي للتطوير والاختبار
  static const bool enableMockMode = false; // ✅ معطل للإنتاج

  /// Enable real Google OAuth for production
  /// تفعيل Google OAuth الحقيقي للإنتاج
  static const bool enableRealOAuth = true; // ✅ مفعل للإنتاج
}

// =============================================================================
// GOOGLE OAUTH SERVICE IMPLEMENTATION
// =============================================================================

/// Enhanced Google OAuth service with comprehensive error handling
/// and integration with the CarNow unified authentication system
@riverpod
class GoogleOAuthService extends _$GoogleOAuthService {
  bool _isConfigured = false;

  @override
  Future<void> build() async {
    await initialize();
  }

  /// Initialize Google OAuth service
  /// تهيئة خدمة Google OAuth
  Future<void> initialize({String? clientId, List<String>? scopes}) async {
    try {
      _logger.i('🚀 Initializing Google OAuth Service...');

      // Check if mock mode is enabled
      if (GoogleOAuthConfig.enableMockMode) {
        _logger.i('🔧 Mock mode enabled - using simulated Google OAuth');
        _isConfigured = true;
        return;
      }

      // Check if real OAuth is enabled
      if (!GoogleOAuthConfig.enableRealOAuth) {
        _logger.w('⚠️ Real OAuth disabled - using mock mode');
        _isConfigured = true;
        return;
      }

      // In google_sign_in v7.1.1, configuration is done via platform-specific files
      // The singleton instance is automatically configured
      _isConfigured = true;

      _logger.i('✅ Google OAuth Service initialized successfully');
    } catch (error, stackTrace) {
      _logger.e(
        '❌ Failed to initialize Google OAuth Service',
        error: error,
        stackTrace: stackTrace,
      );
      _isConfigured = false;
      rethrow;
    }
  }

  /// Check if service is configured
  /// التحقق من تكوين الخدمة
  bool get isConfigured => _isConfigured;

  /// Sign in with Google OAuth
  /// تسجيل الدخول بـ Google OAuth
  Future<GoogleOAuthResult> signIn() async {
    try {
      _logger.i('🔐 Starting Google Sign-In flow...');

      // Check if mock mode is enabled
      if (GoogleOAuthConfig.enableMockMode) {
        return await _mockSignIn();
      }

      // Check if real OAuth is enabled
      if (!GoogleOAuthConfig.enableRealOAuth) {
        _logger.w('⚠️ Real OAuth disabled - using mock mode');
        return await _mockSignIn();
      }

      // Perform real Google Sign-In
      if (!_isConfigured) {
        throw Exception('Google OAuth service not initialized');
      }

      // Attempt Google Sign-In using singleton instance with account selection
      // Configure GoogleSignIn with proper settings to force account selection
      final googleSignIn = GoogleSignIn(
        scopes: GoogleOAuthConfig.scopes,
        hostedDomain: GoogleOAuthConfig.hostedDomain,
        forceCodeForRefreshToken: GoogleOAuthConfig.forceCodeForRefreshToken,
      );
      
      // Sign out first to force account selection
      // إجبار المستخدم على اختيار الحساب بتسجيل الخروج أولاً
      await googleSignIn.signOut();
      
      _logger.i('🔄 Forcing account selection by signing out first');
      
      // Now sign in - this will show account selection
      final googleUser = await googleSignIn.signIn();
      
      if (googleUser == null) {
        throw Exception('Google Sign-In was cancelled by user');
      }

      final auth = await googleUser.authentication;
      final idToken = auth.idToken;

      if (idToken == null) {
        throw GoogleSignInFailedException(
          'Failed to get ID token from Google',
          'missing_id_token',
        );
      }

      _logger.i('✅ Google Sign-In successful for: ${googleUser.email}');

      return GoogleOAuthResult(
        userInfo: GoogleUserInfo(
          id: googleUser.id,
          email: googleUser.email,
          displayName: googleUser.displayName,
          firstName: googleUser.displayName?.split(' ').first,
          lastName: googleUser.displayName?.split(' ').last,
          photoUrl: googleUser.photoUrl,
        ),
        idToken: idToken,
        accessToken: '', // Access token not available in google_sign_in v7.1.1
      );
    } catch (error, stackTrace) {
      _logger.e(
        '❌ Google Sign-In failed',
        error: error,
        stackTrace: stackTrace,
      );

      // Convert platform-specific errors to user-friendly Arabic messages
      if (error.toString().contains('sign_in_canceled') ||
          error.toString().contains('cancelled')) {
        throw GoogleSignInCancelledException();
      } else if (error.toString().contains('network') ||
          error.toString().contains('connection')) {
        throw GoogleSignInNetworkException(
          'خطأ في الاتصال بالشبكة - يرجى التحقق من اتصالك بالإنترنت',
        );
      } else if (error.toString().contains('sign_in_failed')) {
        throw GoogleSignInFailedException(
          'فشل تسجيل الدخول بـ Google - يرجى المحاولة مرة أخرى',
          'sign_in_failed',
        );
      } else {
        throw GoogleSignInFailedException(
          'حدث خطأ غير متوقع أثناء تسجيل الدخول بـ Google',
          'unknown_error',
        );
      }
    }
  }

  /// Mock sign-in for development/testing
  /// تسجيل دخول وهمي للتطوير والاختبار
  Future<GoogleOAuthResult> _mockSignIn() async {
    _logger.i('🎭 Performing mock Google Sign-In...');

    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 500));

    // Generate mock user data
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final mockUser = GoogleUserInfo(
      id: 'mock_google_user_$timestamp',
      email: '<EMAIL>',
      displayName: 'Mock Google User',
      firstName: 'Mock',
      lastName: 'User',
      photoUrl: 'https://via.placeholder.com/150',
    );

    final mockIdToken = 'mock_google_id_token_$timestamp';
    final mockAccessToken = 'mock_access_token_$timestamp';

    _logger.i('✅ Mock Google Sign-In completed for: ${mockUser.email}');

    return GoogleOAuthResult(
      userInfo: mockUser,
      idToken: mockIdToken,
      accessToken: mockAccessToken,
    );
  }

  /// Sign out from Google
  /// تسجيل الخروج من Google
  Future<void> signOut() async {
    try {
      _logger.i('🚪 Signing out from Google...');

      if (GoogleOAuthConfig.enableMockMode) {
        _logger.i('🎭 Mock sign out completed');
        return;
      }

      final googleSignIn = GoogleSignIn();
      await googleSignIn.signOut();
      await googleSignIn.disconnect(); // Also disconnect to fully clear session
      _logger.i('✅ Successfully signed out from Google');
    } catch (error, stackTrace) {
      _logger.e(
        '❌ Google Sign-Out failed',
        error: error,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Check if user is signed in
  /// التحقق من تسجيل دخول المستخدم
  Future<bool> isSignedIn() async {
    try {
      if (GoogleOAuthConfig.enableMockMode) {
        return false; // Mock mode always returns false
      }

      // In google_sign_in v7.1.1, we need to try to get authentication
      // to check if user is signed in
      try {
        final googleSignIn = GoogleSignIn();
        await googleSignIn.signIn();
        return true;
      } catch (e) {
        // If authenticate() throws, user is not signed in
        return false;
      }
    } catch (error, stackTrace) {
      _logger.e(
        '❌ Failed to check sign-in status',
        error: error,
        stackTrace: stackTrace,
      );
      return false;
    }
  }

  /// Get current user
  /// الحصول على المستخدم الحالي
  Future<GoogleUserInfo?> getCurrentUser() async {
    try {
      if (GoogleOAuthConfig.enableMockMode) {
        return null; // Mock mode always returns null
      }

      // In google_sign_in v7.1.1, silent sign-in is not directly supported
      // We'll try to get the current user by attempting authentication
      try {
        final googleSignIn = GoogleSignIn();
        final currentUser = await googleSignIn.signIn();
        
        if (currentUser == null) {
          return null; // User cancelled or sign-in failed
        }
        
        return GoogleUserInfo(
          id: currentUser.id,
          email: currentUser.email,
          displayName: currentUser.displayName ?? '',
          firstName: currentUser.displayName?.split(' ').first ?? '',
          lastName: currentUser.displayName?.split(' ').last ?? '',
          photoUrl: currentUser.photoUrl,
        );
      } catch (e) {
        // If authenticate() throws, no current user
        return null;
      }
    } catch (error, stackTrace) {
      _logger.e(
        '❌ Failed to get current user',
        error: error,
        stackTrace: stackTrace,
      );
      return null;
    }
  }
}

// =============================================================================
// DATA MODELS
// =============================================================================

/// Google OAuth result
/// نتيجة Google OAuth
class GoogleOAuthResult {
  final GoogleUserInfo userInfo;
  final String idToken;
  final String accessToken;

  const GoogleOAuthResult({
    required this.userInfo,
    required this.idToken,
    required this.accessToken,
  });

  @override
  String toString() {
    return 'GoogleOAuthResult(userInfo: $userInfo, idToken: ${idToken.substring(0, 20)}..., accessToken: ${accessToken.substring(0, 20)}...)';
  }
}

/// Google user information
/// معلومات مستخدم Google
class GoogleUserInfo {
  final String id;
  final String email;
  final String? displayName;
  final String? firstName;
  final String? lastName;
  final String? photoUrl;

  const GoogleUserInfo({
    required this.id,
    required this.email,
    this.displayName,
    this.firstName,
    this.lastName,
    this.photoUrl,
  });

  @override
  String toString() {
    return 'GoogleUserInfo(id: $id, email: $email, displayName: $displayName)';
  }
}

// =============================================================================
// GOOGLE OAUTH RESULT CLASSES
// =============================================================================

/// Google token validation result
class GoogleTokenValidationResult {
  final bool isValid;
  final GoogleUserInfo? userInfo;
  final DateTime? expiresAt;
  final String? error;
  final String? errorCode;

  const GoogleTokenValidationResult._({
    required this.isValid,
    this.userInfo,
    this.expiresAt,
    this.error,
    this.errorCode,
  });

  factory GoogleTokenValidationResult.valid({
    required GoogleUserInfo userInfo,
    required DateTime expiresAt,
  }) {
    return GoogleTokenValidationResult._(
      isValid: true,
      userInfo: userInfo,
      expiresAt: expiresAt,
    );
  }

  factory GoogleTokenValidationResult.invalid({
    required String error,
    required String errorCode,
  }) {
    return GoogleTokenValidationResult._(
      isValid: false,
      error: error,
      errorCode: errorCode,
    );
  }
}

/// Google user profile (extended information)
class GoogleUserProfile {
  final String id;
  final String email;
  final String name;
  final String? givenName;
  final String? familyName;
  final String? photoUrl;
  final String? locale;
  final bool verifiedEmail;
  final String? hd; // Hosted domain

  const GoogleUserProfile({
    required this.id,
    required this.email,
    required this.name,
    this.givenName,
    this.familyName,
    this.photoUrl,
    this.locale,
    required this.verifiedEmail,
    this.hd,
  });
}

// =============================================================================
// GOOGLE OAUTH EXCEPTIONS
// =============================================================================

/// Base class for Google Sign-In exceptions
abstract class GoogleSignInException implements Exception {
  final String message;
  final String code;

  const GoogleSignInException(this.message, this.code);

  @override
  String toString() => 'GoogleSignInException: $message (code: $code)';
}

/// Exception thrown when user cancels Google Sign-In
class GoogleSignInCancelledException extends GoogleSignInException {
  const GoogleSignInCancelledException()
    : super('User cancelled Google Sign-In', 'sign_in_cancelled');
}

/// Exception thrown when there's a network error during Google Sign-In
class GoogleSignInNetworkException extends GoogleSignInException {
  const GoogleSignInNetworkException(String message)
    : super(message, 'network_error');
}

/// Exception thrown when Google Sign-In fails for other reasons
class GoogleSignInFailedException extends GoogleSignInException {
  const GoogleSignInFailedException(String message, String code)
    : super(message, code);
}
