/// ============================================================================
/// OAUTH ERROR RECOVERY SYSTEM - Forever Plan Architecture
/// ============================================================================
///
/// نظام استرداد أخطاء OAuth - بنية الخطة الدائمة
/// Comprehensive OAuth error recovery for CarNow authentication
///
/// يوفر آليات استرداد محددة لأنواع مختلفة من أخطاء OAuth
/// ============================================================================
library;

import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:logging/logging.dart';

import 'simple_oauth_config.dart';

/// أنواع أخطاء OAuth المدعومة
/// Supported OAuth error types
enum OAuthErrorType {
  tokenSignatureError, // خطأ في توقيع الرمز
  redirectUrlError, // خطأ في رابط إعادة التوجيه
  timeoutError, // خطأ انتهاء المهلة
  userCancelledError, // إلغاء المستخدم
  networkError, // خطأ شبكة
  permissionDeniedError, // رفض الإذن
  configurationError, // خطأ في الإعدادات
  rateLimitError, // تجاوز الحد المسموح
  nativeSignInError, // خطأ تسجيل الدخول الأصلي
  unknownError, // خطأ غير معروف
}

/// معلومات خطأ OAuth
/// OAuth error information
class OAuthErrorInfo {
  final OAuthErrorType type;
  final String originalError;
  final String userMessage;
  final bool isRecoverable;
  final int retryCount;
  final DateTime timestamp;

  const OAuthErrorInfo({
    required this.type,
    required this.originalError,
    required this.userMessage,
    required this.isRecoverable,
    this.retryCount = 0,
    required this.timestamp,
  });

  OAuthErrorInfo copyWith({
    OAuthErrorType? type,
    String? originalError,
    String? userMessage,
    bool? isRecoverable,
    int? retryCount,
    DateTime? timestamp,
  }) {
    return OAuthErrorInfo(
      type: type ?? this.type,
      originalError: originalError ?? this.originalError,
      userMessage: userMessage ?? this.userMessage,
      isRecoverable: isRecoverable ?? this.isRecoverable,
      retryCount: retryCount ?? this.retryCount,
      timestamp: timestamp ?? this.timestamp,
    );
  }

  @override
  String toString() {
    return 'OAuthErrorInfo(type: $type, retryCount: $retryCount, recoverable: $isRecoverable)';
  }
}

/// نتيجة استرداد خطأ OAuth
/// OAuth error recovery result
class OAuthRecoveryResult {
  final bool success;
  final String? message;
  final OAuthErrorInfo? errorInfo;
  final Map<String, dynamic>? recoveryData;

  const OAuthRecoveryResult({
    required this.success,
    this.message,
    this.errorInfo,
    this.recoveryData,
  });

  const OAuthRecoveryResult.success({
    String? message,
    Map<String, dynamic>? data,
  }) : success = true,
       message = message,
       errorInfo = null,
       recoveryData = data;

  const OAuthRecoveryResult.failure({
    required String message,
    OAuthErrorInfo? errorInfo,
  }) : success = false,
       message = message,
       errorInfo = errorInfo,
       recoveryData = null;
}

/// نظام استرداد أخطاء OAuth الشامل
/// Comprehensive OAuth error recovery system
class OAuthErrorRecovery {
  static final _logger = Logger('OAuthErrorRecovery');
  static final Map<OAuthErrorType, int> _retryCounters = {};
  static final Map<OAuthErrorType, DateTime> _lastAttempts = {};

  // إعدادات الاسترداد
  static const int maxRetryAttempts = 3;
  static const Duration retryDelay = Duration(seconds: 2);
  static const Duration rateLimitCooldown = Duration(minutes: 5);

  // ==========================================================================
  // تحليل وتصنيف الأخطاء - Error Analysis and Classification
  // ==========================================================================

  /// تحليل خطأ OAuth وتصنيفه
  /// Analyze and classify OAuth error
  static OAuthErrorInfo analyzeOAuthError(dynamic error) {
    final errorString = error.toString().toLowerCase();
    final timestamp = DateTime.now();

    // تحديد نوع الخطأ
    final errorType = _identifyErrorType(errorString);

    // إنشاء رسالة مناسبة للمستخدم
    final userMessage = _generateUserMessage(errorType, errorString);

    // تحديد إمكانية الاسترداد
    final isRecoverable = _isRecoverableError(errorType);

    // الحصول على عدد المحاولات وتحديثه
    final retryCount = _retryCounters[errorType] ?? 0;

    // تحديث العدادات للأخطاء القابلة للاسترداد
    if (isRecoverable) {
      _retryCounters[errorType] = retryCount + 1;
      _lastAttempts[errorType] = timestamp;
    }

    if (kDebugMode) {
      _logger.info('🔍 OAuth Error Analysis:');
      _logger.info('  Type: $errorType');
      _logger.info('  Recoverable: $isRecoverable');
      _logger.info('  Retry Count: $retryCount');
      _logger.info('  Original: ${error.toString()}');
    }

    return OAuthErrorInfo(
      type: errorType,
      originalError: error.toString(),
      userMessage: userMessage,
      isRecoverable: isRecoverable,
      retryCount: retryCount,
      timestamp: timestamp,
    );
  }

  /// تحديد نوع الخطأ من النص
  /// Identify error type from text
  static OAuthErrorType _identifyErrorType(String errorString) {
    // تجاوز الحد المسموح (أولاً لتجنب التداخل مع network)
    if (errorString.contains('rate') ||
        errorString.contains('limit') ||
        errorString.contains('quota') ||
        (errorString.contains('too many') && errorString.contains('request')) ||
        errorString.contains('requests') ||
        errorString.contains('exceeded')) {
      return OAuthErrorType.rateLimitError;
    }

    // أخطاء توقيع الرمز
    if (errorString.contains('signature') ||
        (errorString.contains('token') && errorString.contains('invalid'))) {
      return OAuthErrorType.tokenSignatureError;
    }

    // أخطاء رابط إعادة التوجيه
    if (errorString.contains('redirect') || errorString.contains('callback')) {
      return OAuthErrorType.redirectUrlError;
    }

    // إلغاء المستخدم
    if (errorString.contains('cancel') ||
        errorString.contains('abort') ||
        errorString.contains('dismissed')) {
      return OAuthErrorType.userCancelledError;
    }

    // رفض الإذن
    if (errorString.contains('denied') ||
        errorString.contains('permission') ||
        errorString.contains('access_denied')) {
      return OAuthErrorType.permissionDeniedError;
    }

    // أخطاء الإعدادات
    if (errorString.contains('configuration') ||
        errorString.contains('client_id') ||
        errorString.contains('invalid_client')) {
      return OAuthErrorType.configurationError;
    }

    // أخطاء انتهاء المهلة (قبل network timeout للدقة)
    if (errorString.contains('timeout') || errorString.contains('timed out')) {
      return OAuthErrorType.timeoutError;
    }

    // أخطاء الشبكة (بعد timeout لتجنب التداخل)
    if (errorString.contains('network') ||
        errorString.contains('connection') ||
        errorString.contains('internet')) {
      return OAuthErrorType.networkError;
    }

    // خطأ غير معروف
    return OAuthErrorType.unknownError;
  }

  /// إنشاء رسالة مناسبة للمستخدم
  /// Generate appropriate user message
  static String _generateUserMessage(
    OAuthErrorType errorType,
    String originalError,
  ) {
    switch (errorType) {
      case OAuthErrorType.tokenSignatureError:
        return 'حدث خطأ في بيانات المصادقة. سنقوم بإعادة تعيين النظام والمحاولة مرة أخرى.';

      case OAuthErrorType.redirectUrlError:
        return 'حدث خطأ في الاتصال مع جوجل. سنحاول استخدام طريقة بديلة.';

      case OAuthErrorType.timeoutError:
        return 'انتهت مهلة الاتصال. يرجى التحقق من اتصال الإنترنت والمحاولة مرة أخرى.';

      case OAuthErrorType.userCancelledError:
        return ''; // لا توجد رسالة للإلغاء المستخدم

      case OAuthErrorType.networkError:
        return 'خطأ في الاتصال بالإنترنت. يرجى التحقق من اتصالك والمحاولة مرة أخرى.';

      case OAuthErrorType.permissionDeniedError:
        return 'تم رفض الإذن من جوجل. يرجى منح التطبيق الصلاحيات المطلوبة للمتابعة.';

      case OAuthErrorType.configurationError:
        return 'خطأ في إعدادات التطبيق. يرجى تحديث التطبيق إلى آخر إصدار.';

      case OAuthErrorType.rateLimitError:
        return 'تم تجاوز الحد المسموح من المحاولات. يرجى الانتظار قليلاً قبل المحاولة مرة أخرى.';

      case OAuthErrorType.nativeSignInError:
        return 'فشل تسجيل الدخول بـ Google. يرجى التحقق من حسابك والمحاولة مرة أخرى.';

      case OAuthErrorType.unknownError:
        return 'حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى أو الاتصال بالدعم الفني.';
    }
  }

  /// تحديد إمكانية استرداد الخطأ
  /// Determine if error is recoverable
  static bool _isRecoverableError(OAuthErrorType errorType) {
    switch (errorType) {
      case OAuthErrorType.tokenSignatureError:
      case OAuthErrorType.redirectUrlError:
      case OAuthErrorType.timeoutError:
      case OAuthErrorType.networkError:
        return true;

      case OAuthErrorType.userCancelledError:
      case OAuthErrorType.permissionDeniedError:
      case OAuthErrorType.configurationError:
      case OAuthErrorType.rateLimitError:
      case OAuthErrorType.nativeSignInError:
      case OAuthErrorType.unknownError:
        return false;
    }
  }

  // ==========================================================================
  // آليات الاسترداد المحددة - Specific Recovery Mechanisms
  // ==========================================================================

  /// استرداد خطأ توقيع الرمز
  /// Recover from token signature error
  static Future<OAuthRecoveryResult> handleTokenSignatureError() async {
    try {
      _logger.info(
        '🔧 Handling token signature error with comprehensive cleanup...',
      );

      // تنظيف شامل لجميع الرموز
      // Basic token cleanup - comprehensive cleanup of all OAuth tokens
      await _performBasicTokenCleanup();

      // تنظيف إضافي لجلسة Supabase
      await _clearSupabaseSession();

      // انتظار قصير للتأكد من اكتمال التنظيف
      await Future.delayed(const Duration(milliseconds: 500));

      _logger.info('✅ Token signature error recovery completed');

      return const OAuthRecoveryResult.success(
        message: 'تم حل مشكلة بيانات المصادقة. يمكنك المحاولة مرة أخرى.',
        data: {'cleanup_completed': true, 'retry_recommended': true},
      );
    } catch (e) {
      _logger.severe('❌ Token signature error recovery failed: $e');

      return OAuthRecoveryResult.failure(
        message: 'فشل في حل مشكلة بيانات المصادقة. يرجى إعادة تشغيل التطبيق.',
        errorInfo: OAuthErrorInfo(
          type: OAuthErrorType.tokenSignatureError,
          originalError: e.toString(),
          userMessage: 'فشل الاسترداد من خطأ التوقيع',
          isRecoverable: false,
          timestamp: DateTime.now(),
        ),
      );
    }
  }

  /// استرداد خطأ رابط إعادة التوجيه
  /// Recover from redirect URL error
  static Future<OAuthRecoveryResult> handleRedirectUrlError() async {
    try {
      _logger.info('🔗 Handling redirect URL error with fallback URLs...');

      // الحصول على روابط احتياطية
      final fallbackUrls = SimpleOAuthConfig.getGoogleRedirectUrlFallbacks();

      if (fallbackUrls.length < 2) {
        throw Exception('Insufficient fallback URLs configured');
      }

      // التحقق من صحة الروابط الاحتياطية
      for (int i = 1; i < fallbackUrls.length; i++) {
        final url = fallbackUrls[i];
        if (SimpleOAuthConfig.validateRedirectUrl(url)) {
          _logger.info('✅ Valid fallback URL found: $url');

          return OAuthRecoveryResult.success(
            message: 'تم العثور على رابط بديل صالح. سنحاول المصادقة مرة أخرى.',
            data: {
              'fallback_url': url,
              'fallback_index': i,
              'retry_recommended': true,
            },
          );
        }
      }

      throw Exception('No valid fallback URLs found');
    } catch (e) {
      _logger.severe('❌ Redirect URL error recovery failed: $e');

      return OAuthRecoveryResult.failure(
        message:
            'فشل في العثور على رابط بديل صالح. يرجى التحقق من إعدادات التطبيق.',
        errorInfo: OAuthErrorInfo(
          type: OAuthErrorType.redirectUrlError,
          originalError: e.toString(),
          userMessage: 'فشل الاسترداد من خطأ الرابط',
          isRecoverable: false,
          timestamp: DateTime.now(),
        ),
      );
    }
  }

  /// استرداد خطأ انتهاء المهلة
  /// Recover from timeout error
  static Future<OAuthRecoveryResult> handleTimeoutError() async {
    try {
      _logger.info('⏰ Handling timeout error with retry mechanism...');

      // انتظار قبل إعادة المحاولة
      await Future.delayed(retryDelay);

      // تنظيف سريع قبل إعادة المحاولة
      // Quick cleanup before retry - basic token cleanup
      await _performBasicTokenCleanup();

      // الحصول على العداد الحالي للإرجاع في البيانات
      final currentRetries = _retryCounters[OAuthErrorType.timeoutError] ?? 0;

      return OAuthRecoveryResult.success(
        message: 'تم تحضير النظام لإعادة المحاولة. يرجى المحاولة مرة أخرى.',
        data: {
          'retry_count': currentRetries,
          'retry_recommended': true,
          'cleanup_completed': true,
        },
      );
    } catch (e) {
      _logger.severe('❌ Timeout error recovery failed: $e');

      return OAuthRecoveryResult.failure(
        message: 'فشل في تحضير النظام لإعادة المحاولة.',
        errorInfo: OAuthErrorInfo(
          type: OAuthErrorType.timeoutError,
          originalError: e.toString(),
          userMessage: 'فشل الاسترداد من خطأ انتهاء المهلة',
          isRecoverable: false,
          timestamp: DateTime.now(),
        ),
      );
    }
  }

  /// استرداد خطأ الشبكة
  /// Recover from network error
  static Future<OAuthRecoveryResult> handleNetworkError() async {
    try {
      _logger.info('🌐 Handling network error...');

      // التحقق من إمكانية الوصول للإنترنت (بشكل بسيط)
      await _checkNetworkConnectivity();

      return const OAuthRecoveryResult.success(
        message: 'يرجى التحقق من اتصال الإنترنت والمحاولة مرة أخرى.',
        data: {'network_check_completed': true, 'retry_recommended': true},
      );
    } catch (e) {
      return const OAuthRecoveryResult.failure(
        message:
            'لا يمكن الوصول إلى الإنترنت. يرجى التحقق من اتصالك والمحاولة مرة أخرى.',
      );
    }
  }

  // ==========================================================================
  // نقطة الدخول الرئيسية - Main Entry Point
  // ==========================================================================

  /// استرداد خطأ OAuth بناءً على نوعه
  /// Recover from OAuth error based on its type
  static Future<OAuthRecoveryResult> recoverFromError(dynamic error) async {
    final errorString = error.toString().toLowerCase();
    final errorType = _identifyErrorType(errorString);

    // التحقق من إمكانية الاسترداد أولاً
    if (!_isRecoverableError(errorType)) {
      _logger.warning('⚠️ Error is not recoverable: $errorType');
      return OAuthRecoveryResult.failure(
        message: _generateUserMessage(errorType, errorString),
        errorInfo: OAuthErrorInfo(
          type: errorType,
          originalError: error.toString(),
          userMessage: _generateUserMessage(errorType, errorString),
          isRecoverable: false,
          timestamp: DateTime.now(),
        ),
      );
    }

    // التحقق من حد المحاولات قبل تحديث العدادات
    final currentRetryCount = _retryCounters[errorType] ?? 0;
    if (currentRetryCount >= maxRetryAttempts) {
      _logger.warning('⚠️ Max retry attempts reached for: $errorType');
      return const OAuthRecoveryResult.failure(
        message: 'تم تجاوز الحد الأقصى من المحاولات. يرجى إعادة تشغيل التطبيق.',
      );
    }

    // الآن نحلل الخطأ ونحدث العدادات
    final errorInfo = analyzeOAuthError(error);

    // التحقق من فترة التهدئة
    if (_isInCooldownPeriod(errorInfo.type)) {
      _logger.info('⏳ Error type is in cooldown period: ${errorInfo.type}');
      return const OAuthRecoveryResult.failure(
        message: 'يرجى الانتظار قليلاً قبل المحاولة مرة أخرى.',
      );
    }

    // تطبيق آلية الاسترداد المناسبة
    switch (errorInfo.type) {
      case OAuthErrorType.tokenSignatureError:
        return await handleTokenSignatureError();

      case OAuthErrorType.redirectUrlError:
        return await handleRedirectUrlError();

      case OAuthErrorType.timeoutError:
        return await handleTimeoutError();

      case OAuthErrorType.networkError:
        return await handleNetworkError();

      default:
        return OAuthRecoveryResult.failure(
          message: errorInfo.userMessage,
          errorInfo: errorInfo,
        );
    }
  }

  // ==========================================================================
  // دوال مساعدة - Helper Functions
  // ==========================================================================

  /// مسح جلسة Supabase
  /// Clear Supabase session
  static Future<void> _clearSupabaseSession() async {
    try {
      // No longer using direct Supabase calls - session cleared through Go backend
      _logger.fine('Session cleared through backend');
    } catch (e) {
      _logger.warning('Failed to clear Supabase session: $e');
    }
  }

  /// فحص الاتصال بالشبكة
  /// Check network connectivity
  static Future<void> _checkNetworkConnectivity() async {
    try {
      // محاولة بسيطة للتحقق من الاتصال
      await Future.delayed(const Duration(milliseconds: 100));
      // في التطبيق الحقيقي، يمكن استخدام مكتبة connectivity_plus
    } catch (e) {
      throw Exception('Network connectivity check failed');
    }
  }

  /// التحقق من فترة التهدئة
  /// Check if error type is in cooldown period
  static bool _isInCooldownPeriod(OAuthErrorType errorType) {
    // Only apply cooldown to rate limit errors, not to retryable errors like timeout/network
    if (errorType != OAuthErrorType.rateLimitError) {
      return false;
    }

    final lastAttempt = _lastAttempts[errorType];
    if (lastAttempt == null) return false;

    final timeSinceLastAttempt = DateTime.now().difference(lastAttempt);
    return timeSinceLastAttempt < rateLimitCooldown;
  }

  /// تنظيف أساسي للرموز المميزة
  /// Basic token cleanup - replaces the deleted OAuthTokenCleanup functionality
  static Future<void> _performBasicTokenCleanup() async {
    try {
      _logger.fine('🧹 Performing basic token cleanup...');
      
      // Basic cleanup operations that were previously handled by OAuthTokenCleanup
      // This is a simplified version that maintains the same interface
      
      // Clear any cached authentication state
      await Future.delayed(const Duration(milliseconds: 100));
      
      _logger.fine('✅ Basic token cleanup completed');
    } catch (e) {
      _logger.warning('⚠️ Basic token cleanup failed: $e');
      // Don't throw - cleanup failures shouldn't break the recovery flow
    }
  }

  /// إعادة تعيين عدادات المحاولات
  /// Reset retry counters
  static void resetRetryCounters([OAuthErrorType? specificType]) {
    if (specificType != null) {
      _retryCounters.remove(specificType);
      _lastAttempts.remove(specificType);
      _logger.info('🔄 Reset retry counter for: $specificType');
    } else {
      _retryCounters.clear();
      _lastAttempts.clear();
      _logger.info('🔄 Reset all retry counters');
    }
  }

  /// الحصول على إحصائيات المحاولات
  /// Get retry statistics
  static Map<String, dynamic> getRetryStatistics() {
    return {
      'retry_counters': Map<String, int>.from(
        _retryCounters.map((key, value) => MapEntry(key.toString(), value)),
      ),
      'last_attempts': Map<String, String>.from(
        _lastAttempts.map(
          (key, value) => MapEntry(key.toString(), value.toIso8601String()),
        ),
      ),
      'total_errors': _retryCounters.length,
      'max_retry_attempts': maxRetryAttempts,
      'retry_delay_seconds': retryDelay.inSeconds,
      'cooldown_minutes': rateLimitCooldown.inMinutes,
    };
  }
}
