import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'navigation_exports.dart';

/// صفحة عرض شاملة لنظام التنقل الموحد في تطبيق CarNow
///
/// هذه الصفحة تعرض:
/// - جميع مكونات نظام التنقل
/// - أمثلة تفاعلية للاستخدام
/// - أفضل الممارسات
/// - حالات الاستخدام المختلفة
class NavigationShowcase extends ConsumerStatefulWidget {
  const NavigationShowcase({super.key});

  @override
  ConsumerState<NavigationShowcase> createState() => _NavigationShowcaseState();
}

class _NavigationShowcaseState extends ConsumerState<NavigationShowcase>
    with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const UnifiedAppBar(
        title: 'نظام التنقل الموحد - CarNow',
        backgroundColor: Colors.transparent,
      ),
      body: PageView(
        children: [
          _buildOverviewPage(),
          _buildComponentsDemo(),
          _buildAdvancedFeatures(),
          _buildBestPractices(),
        ],
      ),
      bottomNavigationBar: const UnifiedBottomNavigation(
        child: SizedBox.shrink(),
      ),
      floatingActionButton: _buildQuickActionFAB(),
      drawer: _buildNavigationDrawer(),
    );
  }

  Widget _buildOverviewPage() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildWelcomeHeader(),
          const SizedBox(height: 24),
          _buildFeatureCards(),
          const SizedBox(height: 24),
          _buildQuickStartGuide(),
          const SizedBox(height: 24),
          _buildSystemStats(),
        ],
      ),
    );
  }

  Widget _buildWelcomeHeader() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).primaryColor,
            Theme.of(context).primaryColor.withAlpha((0.8 * 255).toInt()),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Theme.of(
              context,
            ).primaryColor.withAlpha((0.3 * 255).toInt()),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(Icons.navigation, color: Colors.white, size: 32),
              SizedBox(width: 12),
              Text(
                'نظام التنقل الموحد',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          const Text(
            'حل شامل ومتطور لإدارة التنقل في تطبيق CarNow',
            style: TextStyle(color: Colors.white70, fontSize: 16),
          ),
          const SizedBox(height: 16),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: [
              _buildBadge('🚀 أداء محسن'),
              _buildBadge('🎨 تصميم موحد'),
              _buildBadge('📱 متجاوب'),
              _buildBadge('🔧 سهل الاستخدام'),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildBadge(String text) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.white.withAlpha((0.2 * 255).toInt()),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Text(
        text,
        style: const TextStyle(color: Colors.white, fontSize: 12),
      ),
    );
  }

  Widget _buildFeatureCards() {
    final features = [
      {
        'icon': Icons.dashboard,
        'title': 'شريط التنقل الموحد',
        'description': 'تصميم متسق عبر جميع الشاشات',
        'color': Colors.blue,
      },
      {
        'icon': Icons.navigation,
        'title': 'التنقل السفلي الذكي',
        'description': 'شارات ومؤشرات تفاعلية',
        'color': Colors.green,
      },
      {
        'icon': Icons.search,
        'title': 'بحث متقدم',
        'description': 'شريط بحث ذكي مع اقتراحات',
        'color': Colors.orange,
      },
      {
        'icon': Icons.code,
        'title': 'API بسيط',
        'description': 'استخدام سهل مع extension methods',
        'color': Colors.purple,
      },
    ];

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
        childAspectRatio: 1.2,
      ),
      itemCount: features.length,
      itemBuilder: (context, index) {
        final feature = features[index];
        return _buildFeatureCard(
          icon: feature['icon'] as IconData,
          title: feature['title'] as String,
          description: feature['description'] as String,
          color: feature['color'] as Color,
        );
      },
    );
  }

  Widget _buildFeatureCard({
    required IconData icon,
    required String title,
    required String description,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha((0.1 * 255).toInt()),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: color.withAlpha((0.1 * 255).toInt()),
              borderRadius: BorderRadius.circular(30),
            ),
            child: Icon(icon, color: color, size: 32),
          ),
          const SizedBox(height: 12),
          Text(
            title,
            style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            description,
            style: TextStyle(color: Colors.grey.shade600, fontSize: 12),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildQuickStartGuide() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '🚀 البدء السريع',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            _buildQuickStartStep(
              '1',
              'استيراد النظام',
              "import 'core/navigation/navigation_exports.dart';",
            ),
            _buildQuickStartStep(
              '2',
              'استخدام التنقل',
              "context.goHome(); // أو context.goToProduct('123');",
            ),
            _buildQuickStartStep(
              '3',
              'إضافة AppBar',
              "UnifiedAppBar(title: 'عنوانك', showSearch: true)",
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () {
                  // Navigate to detailed guide
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text(
                        'يمكنك العثور على الدليل الكامل في README.md',
                      ),
                    ),
                  );
                },
                icon: const Icon(Icons.book),
                label: const Text('اقرأ الدليل الكامل'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickStartStep(String number, String title, String code) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Center(
              child: Text(
                number,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 4),
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    code,
                    style: const TextStyle(
                      fontFamily: 'monospace',
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSystemStats() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '📊 إحصائيات النظام',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(child: _buildStatItem('50+', 'مسار متاح')),
                Expanded(child: _buildStatItem('5', 'مكونات رئيسية')),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(child: _buildStatItem('100%', 'متوافق مع Material 3')),
                Expanded(child: _buildStatItem('⚡', 'أداء محسن')),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String value, String label) {
    return Column(
      children: [
        Text(
          value,
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Theme.of(context).primaryColor,
          ),
        ),
        Text(
          label,
          style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildComponentsDemo() {
    return const NavigationDemoScreen();
  }

  Widget _buildAdvancedFeatures() {
    return const SearchBarDemo();
  }

  Widget _buildBestPractices() {
    return const SimpleDemo();
  }

  Widget _buildQuickActionFAB() {
    return FloatingActionButton.extended(
      onPressed: () {
        showModalBottomSheet(
          context: context,
          isScrollControlled: true,
          backgroundColor: Colors.transparent,
          builder: (context) => _buildQuickActionSheet(),
        );
      },
      icon: const Icon(Icons.apps),
      label: const Text('إجراءات سريعة'),
    );
  }

  Widget _buildQuickActionSheet() {
    return Container(
      height: MediaQuery.of(context).size.height * 0.7,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.symmetric(vertical: 12),
            decoration: BoxDecoration(
              color: Colors.grey.shade300,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const Padding(
            padding: EdgeInsets.all(16),
            child: Text(
              'إجراءات سريعة',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
          ),
          const Expanded(child: NavigationDemoScreen()),
        ],
      ),
    );
  }

  Widget _buildNavigationDrawer() {
    return Drawer(
      child: ListView(
        padding: EdgeInsets.zero,
        children: [
          DrawerHeader(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Theme.of(context).primaryColor,
                  Theme.of(context).primaryColor.withAlpha((0.8 * 255).toInt()),
                ],
              ),
            ),
            child: const Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Icon(Icons.car_repair, color: Colors.white, size: 48),
                SizedBox(height: 8),
                Text(
                  'CarNow',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'نظام التنقل الموحد',
                  style: TextStyle(color: Colors.white70, fontSize: 16),
                ),
              ],
            ),
          ),
          _buildDrawerItem(
            icon: Icons.home,
            title: 'الصفحة الرئيسية',
            onTap: () => Navigator.pop(context),
          ),
          _buildDrawerItem(
            icon: Icons.code,
            title: 'أمثلة الكود',
            onTap: () => Navigator.pop(context),
          ),
          _buildDrawerItem(
            icon: Icons.science,
            title: 'الميزات المتقدمة',
            onTap: () => Navigator.pop(context),
          ),
          _buildDrawerItem(
            icon: Icons.book,
            title: 'أفضل الممارسات',
            onTap: () => Navigator.pop(context),
          ),
          const Divider(),
          _buildDrawerItem(
            icon: Icons.help,
            title: 'المساعدة',
            onTap: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('راجع الملف README.md للحصول على المساعدة'),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildDrawerItem({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
  }) {
    return ListTile(leading: Icon(icon), title: Text(title), onTap: onTap);
  }
}
