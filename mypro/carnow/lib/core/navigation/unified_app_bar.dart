import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../theme/app_theme.dart';
import 'unified_navigation_system.dart';
import '../../features/notifications/services/notification_service.dart';

/// شريط التطبيق الموحد لتطبيق CarNow
///
/// يوفر هذا المكون:
/// - تصميم موحد عبر جميع الشاشات
/// - زر البحث السريع
/// - عداد الإشعارات
/// - قائمة الإجراءات حسب السياق
/// - دعم للوضع الليلي والنهاري
class UnifiedAppBar extends ConsumerWidget implements PreferredSizeWidget {
  const UnifiedAppBar({
    super.key,
    this.title,
    this.showSearch = false,
    this.showNotifications = true,
    this.showCart = false,
    this.actions,
    this.backgroundColor,
    this.centerTitle = true,
    this.elevation = 0,
    this.automaticallyImplyLeading = true,
    this.onSearchTap,
    this.onNotificationTap,
    this.customLeading,
  });

  final String? title;
  final bool showSearch;
  final bool showNotifications;
  final bool showCart;
  final List<Widget>? actions;
  final Color? backgroundColor;
  final bool centerTitle;
  final double elevation;
  final bool automaticallyImplyLeading;
  final VoidCallback? onSearchTap;
  final VoidCallback? onNotificationTap;
  final Widget? customLeading;

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);

    return AppBar(
      title: _buildTitle(context),
      backgroundColor: backgroundColor ?? theme.appBarTheme.backgroundColor,
      foregroundColor: theme.appBarTheme.foregroundColor,
      elevation: elevation,
      centerTitle: centerTitle,
      automaticallyImplyLeading: automaticallyImplyLeading,
      leading: customLeading ?? _buildLeading(context),
      actions: _buildActions(context, ref),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(bottom: Radius.circular(16)),
      ),
    );
  }

  /// بناء عنوان الشريط
  Widget? _buildTitle(BuildContext context) {
    if (title == null) return null;

    return Text(
      title!,
      style: Theme.of(context).textTheme.titleLarge?.copyWith(
        fontWeight: FontWeight.bold,
        color: AppTheme.primary,
      ),
    );
  }

  /// بناء الزر الأيسر (Leading)
  Widget? _buildLeading(BuildContext context) {
    if (!automaticallyImplyLeading) return null;

    // إذا كانت هناك صفحة سابقة، عرض زر الرجوع
    if (ModalRoute.of(context)?.canPop == true) {
      return IconButton(
        icon: const Icon(Icons.arrow_back_ios_new),
        onPressed: () => Navigator.of(context).pop(),
        tooltip: 'رجوع',
      );
    }

    // إذا لم تكن في الصفحة الرئيسية، عرض زر الصفحة الرئيسية
    final currentRoute = ModalRoute.of(context)?.settings.name;
    if (currentRoute != UnifiedNavigationSystem.home) {
      return IconButton(
        icon: const Icon(Icons.home),
        onPressed: () => UnifiedNavigationSystem.goHome(context),
        tooltip: 'الصفحة الرئيسية',
      );
    }

    return null;
  }

  /// بناء قائمة الإجراءات
  List<Widget> _buildActions(BuildContext context, WidgetRef ref) {
    final List<Widget> actionsList = [];

    // زر البحث السريع
    if (showSearch) {
      actionsList.add(
        IconButton(
          icon: const Icon(Icons.search),
          onPressed: onSearchTap ?? () => _handleSearchTap(context),
          tooltip: 'البحث',
        ),
      );
    }

    // زر سلة التسوق
    if (showCart) {
      actionsList.add(_buildCartIcon(context, ref));
    }

    // زر الإشعارات
    if (showNotifications) {
      actionsList.add(_buildNotificationIcon(context, ref));
    }

    // الإجراءات المخصصة
    if (actions != null) {
      actionsList.addAll(actions!);
    }

    return actionsList;
  }

  /// بناء أيقونة سلة التسوق مع العداد
  Widget _buildCartIcon(BuildContext context, WidgetRef ref) {
    return IconButton(
      icon: Badge(
        label: Consumer(
          builder: (context, ref, child) {
            // TODO: ربط عدد العناصر في سلة التسوق
            // final cartCount = ref.watch(cartNotifierProvider).when(...)
            const cartCount = 0; // مخفي حالياً حتى يتم ربط البيانات الفعلية

            if (cartCount == 0) {
              return const SizedBox.shrink();
            }

            return Text(
              cartCount > 9 ? '9+' : cartCount.toString(),
              style: const TextStyle(
                color: Colors.white,
                fontSize: 10,
                fontWeight: FontWeight.bold,
              ),
            );
          },
        ),
        backgroundColor: Colors.red,
        child: const Icon(Icons.shopping_cart),
      ),
      onPressed: () => UnifiedNavigationSystem.goToCart(context),
      tooltip: 'سلة التسوق',
    );
  }

  /// بناء أيقونة الإشعارات مع العداد
  Widget _buildNotificationIcon(BuildContext context, WidgetRef ref) {
    return IconButton(
      icon: Badge(
        label: Consumer(
          builder: (context, ref, child) {
            // ربط عدد الإشعارات غير المقروءة بالـ provider الفعلي
            final unreadCount = ref
                .watch(unreadNotificationCountProvider)
                .when(
                  data: (count) => count,
                  loading: () => 0,
                  error: (error, stackTrace) => 0,
                );

            if (unreadCount == 0) {
              return const SizedBox.shrink();
            }

            return Text(
              unreadCount > 9 ? '9+' : unreadCount.toString(),
              style: const TextStyle(
                color: Colors.white,
                fontSize: 10,
                fontWeight: FontWeight.bold,
              ),
            );
          },
        ),
        backgroundColor: Colors.red,
        child: const Icon(Icons.notifications),
      ),
      onPressed: onNotificationTap ?? () => _handleNotificationTap(context),
      tooltip: 'الإشعارات',
    );
  }

  /// التعامل مع ضغطة زر البحث
  void _handleSearchTap(BuildContext context) {
    UnifiedNavigationSystem.goToSearch(context);
  }

  /// التعامل مع ضغطة زر الإشعارات
  void _handleNotificationTap(BuildContext context) {
    UnifiedNavigationSystem.goToNotifications(context);
  }
}

/// نسخة مبسطة من AppBar للشاشات البسيطة
class SimpleAppBar extends ConsumerWidget implements PreferredSizeWidget {
  const SimpleAppBar({
    super.key,
    required this.title,
    this.centerTitle = true,
    this.elevation = 0,
    this.backgroundColor,
    this.actions,
  });

  final String title;
  final bool centerTitle;
  final double elevation;
  final Color? backgroundColor;
  final List<Widget>? actions;

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return AppBar(
      title: Text(
        title,
        style: Theme.of(
          context,
        ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
      ),
      centerTitle: centerTitle,
      elevation: elevation,
      backgroundColor: backgroundColor,
      actions: actions,
    );
  }
}

/// AppBar مخصص لشاشات البحث
class SearchAppBar extends ConsumerStatefulWidget
    implements PreferredSizeWidget {
  const SearchAppBar({
    super.key,
    this.onSearchChanged,
    this.onSearchSubmitted,
    this.initialQuery = '',
    this.hintText = 'ابحث عن قطع الغيار...',
    this.showFilters = true,
  });

  final ValueChanged<String>? onSearchChanged;
  final ValueChanged<String>? onSearchSubmitted;
  final String initialQuery;
  final String hintText;
  final bool showFilters;

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);

  @override
  ConsumerState<SearchAppBar> createState() => _SearchAppBarState();
}

class _SearchAppBarState extends ConsumerState<SearchAppBar> {
  late TextEditingController _searchController;
  late FocusNode _searchFocusNode;

  @override
  void initState() {
    super.initState();
    _searchController = TextEditingController(text: widget.initialQuery);
    _searchFocusNode = FocusNode();

    // التركيز على حقل البحث عند فتح الشاشة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _searchFocusNode.requestFocus();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AppBar(
      leading: IconButton(
        icon: const Icon(Icons.arrow_back),
        onPressed: () => Navigator.of(context).pop(),
      ),
      title: TextField(
        controller: _searchController,
        focusNode: _searchFocusNode,
        decoration: InputDecoration(
          hintText: widget.hintText,
          border: InputBorder.none,
          hintStyle: TextStyle(color: Colors.grey.shade600),
        ),
        style: const TextStyle(fontSize: 16),
        textInputAction: TextInputAction.search,
        onChanged: widget.onSearchChanged,
        onSubmitted: widget.onSearchSubmitted,
      ),
      actions: [
        if (_searchController.text.isNotEmpty)
          IconButton(
            icon: const Icon(Icons.clear),
            onPressed: () {
              _searchController.clear();
              widget.onSearchChanged?.call('');
            },
          ),
        if (widget.showFilters)
          IconButton(
            icon: const Icon(Icons.tune),
            onPressed: () => _showFilters(context),
            tooltip: 'تصفية النتائج',
          ),
      ],
    );
  }

  /// عرض نافذة التصفية
  void _showFilters(BuildContext context) {
    // سيتم تطوير هذه الوظيفة لاحقاً
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('سيتم إضافة خيارات التصفية قريباً'),
        duration: Duration(seconds: 2),
      ),
    );
  }
}
