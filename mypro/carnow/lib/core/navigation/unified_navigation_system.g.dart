// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'unified_navigation_system.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$currentRouteHash() => r'04ecda41513eac0bf72b494221b2f152e8ea86b4';

/// Provider مشتق يوفّر المسار الحالي فقط (قراءة فقط)
///
/// Copied from [currentRoute].
@ProviderFor(currentRoute)
final currentRouteProvider = AutoDisposeProvider<String>.internal(
  currentRoute,
  name: r'currentRouteProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$currentRouteHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CurrentRouteRef = AutoDisposeProviderRef<String>;
String _$navigationStateHash() => r'90d22489e8e800c14043c4780564d7892b78222e';

/// مزود Riverpod حديث لإدارة حالة المسار الحالي
///
/// Copied from [NavigationState].
@ProviderFor(NavigationState)
final navigationStateProvider =
    AutoDisposeNotifierProvider<NavigationState, String>.internal(
      NavigationState.new,
      name: r'navigationStateProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$navigationStateHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$NavigationState = AutoDisposeNotifier<String>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
