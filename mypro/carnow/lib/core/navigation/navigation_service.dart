import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// خدمة التنقل الموحدة لتطبيق CarNow
///
/// تقدم هذه الخدمة واجهة برمجية موحدة ونظيفة لإدارة التنقل
/// في جميع أنحاء التطبيق مع دعم كامل للحالات والأمان
class NavigationService {
  NavigationService._();
  static final NavigationService _instance = NavigationService._();
  static NavigationService get instance => _instance;

  late GoRouter _router;

  /// تهيئة خدمة التنقل
  void initialize(GoRouter router) {
    _router = router;
  }

  /// الحصول على الراوتر الحالي
  GoRouter get router => _router;

  /// معرفات المسارات
  static const String home = '/';
  static const String categories = '/categories';
  static const String garage = '/garage';
  static const String store = '/store';
  static const String account = '/account';
  static const String search = '/search';
  static const String cart = '/cart';
  static const String orders = '/orders';
  static const String support = '/support';
  static const String settings = '/settings';
  static const String notifications = '/notifications';
  static const String favorites = '/favorites';
  static const String profile = '/account/profile';

  // مسارات المصادقة
  static const String login = '/login';
  static const String register = '/register';
  static const String profileSetup = '/account/profile/complete';
  static const String passwordReset = '/reset-password';

  // مسارات البائع
  static const String sellerDashboard = '/seller';
  static const String sellerProducts = '/seller/products';
  static const String addProduct = '/seller/add-product';
  static const String sellerOrders = '/seller/orders';
  static const String sellerStats = '/seller/stats';
  static const String sellerApplication = '/seller/application';

  // مسارات المراسلة
  static const String messages = '/messages';
  static const String newConversation = '/messages/new';

  // مسارات المساعدة
  static const String helpCenter = '/help';
  static const String faq = '/help/faq';

  /// طرق التنقل الأساسية

  Future<void> navigateTo(String route, {Object? extra}) async {
    _router.go(route, extra: extra);
  }

  Future<T?> pushTo<T>(String route, {Object? extra}) async {
    return _router.push<T>(route, extra: extra);
  }

  void pop<T>([T? result]) {
    _router.pop(result);
  }

  void popUntil(String route) {
    while (_router.canPop() &&
        _router.routerDelegate.currentConfiguration.last.matchedLocation !=
            route) {
      _router.pop();
    }
  }

  bool canPop() => _router.canPop();

  String get currentRoute =>
      _router.routerDelegate.currentConfiguration.last.matchedLocation;

  /// طرق التنقل المخصصة

  Future<void> goHome() => navigateTo(home);

  Future<void> goToCategories([String? categoryId]) {
    return navigateTo(
      categoryId != null ? '$categories/$categoryId' : categories,
    );
  }

  Future<void> goToGarage() => navigateTo(garage);

  Future<void> goToStore() => navigateTo(store);

  Future<void> goToAccount() => navigateTo(account);

  Future<void> goToSearch([String? query]) {
    return navigateTo(query != null ? '$search?q=$query' : search);
  }

  Future<void> goToCart() => navigateTo(cart);

  Future<void> goToOrders() => navigateTo(orders);

  Future<void> goToSettings() => navigateTo(settings);

  Future<void> goToNotifications() => navigateTo(notifications);

  Future<void> goToFavorites() => navigateTo(favorites);

  Future<void> goToProfile() => pushTo(profile);

  Future<void> goToSupport() => pushTo(support);

  Future<T?> goToProduct<T>(String productId) =>
      pushTo<T>('/product/$productId');

  Future<void> goToAddVehicle() => pushTo('/garage/add-vehicle');

  Future<void> goToSellerDashboard() => navigateTo(sellerDashboard);

  Future<void> goToAddProduct() => pushTo(addProduct);

  Future<void> goToEditProduct(String productId) =>
      pushTo('/seller/products/$productId/edit');

  Future<void> goToMessages() => pushTo(messages);

  Future<void> goToChat(String conversationId) =>
      pushTo('/messages/$conversationId');

  Future<void> goToLogin([String? returnTo]) {
    final route = returnTo != null
        ? '$login?returnTo=${Uri.encodeComponent(returnTo)}'
        : login;
    return navigateTo(route);
  }

  Future<void> goToRegister([String? returnTo]) {
    final route = returnTo != null
        ? '$register?returnTo=${Uri.encodeComponent(returnTo)}'
        : register;
    return navigateTo(route);
  }

  Future<void> goToProfileSetup([String? returnTo]) {
    final route = returnTo != null
        ? '$profileSetup?returnTo=${Uri.encodeComponent(returnTo)}'
        : profileSetup;
    return navigateTo(route);
  }

  /// التحقق من حالة المسارات

  bool get isHome => currentRoute == home;

  bool get isSellerSection => currentRoute.startsWith('/seller');

  bool get isAuthSection =>
      [login, register, profileSetup, passwordReset].contains(currentRoute);

  /// المسارات المحمية التي تتطلب مصادقة
  static const List<String> protectedRoutes = [
    sellerDashboard,
    account,
    orders,
    messages,
    cart,
    favorites,
  ];

  bool isProtectedRoute(String route) {
    return protectedRoutes.any((protected) => route.startsWith(protected));
  }

  /// إعادة توجيه آمنة
  Future<void> safeNavigateTo(
    String route, {
    bool isAuthenticated = false,
    Object? extra,
  }) async {
    if (isProtectedRoute(route) && !isAuthenticated) {
      await goToLogin(route);
      return;
    }
    await navigateTo(route, extra: extra);
  }

  /// عرض حوار التأكيد
  Future<bool> showConfirmationDialog(
    BuildContext context, {
    required String title,
    required String message,
    String confirmText = 'موافق',
    String cancelText = 'إلغاء',
  }) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(cancelText),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: Text(confirmText),
          ),
        ],
      ),
    );
    return result ?? false;
  }

  /// التنقل مع تأكيد
  Future<void> navigateWithConfirmation(
    BuildContext context, {
    required String route,
    String title = 'تأكيد التنقل',
    String message = 'هل تريد الانتقال إلى هذه الصفحة؟',
  }) async {
    final confirmed = await showConfirmationDialog(
      context,
      title: title,
      message: message,
    );

    if (confirmed) {
      await navigateTo(route);
    }
  }

  /// إعادة تعيين المكدس والعودة للرئيسية
  Future<void> resetToHome() => navigateTo(home);

  /// استبدال المسار الحالي
  void replaceWith(String route) {
    _router.pushReplacement(route);
  }
}

/// مزود خدمة التنقل لـ Riverpod
final navigationServiceProvider = Provider<NavigationService>((ref) {
  return NavigationService.instance;
});

/// امتداد على BuildContext لسهولة الاستخدام
extension NavigationExtensions on BuildContext {
  NavigationService get nav => NavigationService.instance;

  Future<void> goHome() => nav.goHome();
  Future<void> goToCategories([String? categoryId]) =>
      nav.goToCategories(categoryId);
  Future<void> goToGarage() => nav.goToGarage();
  Future<void> goToStore() => nav.goToStore();
  Future<void> goToAccount() => nav.goToAccount();
  Future<void> goToSearch([String? query]) => nav.goToSearch(query);
  Future<void> goToCart() => nav.goToCart();
  Future<void> goToOrders() => nav.goToOrders();
  Future<void> goToSettings() => nav.goToSettings();
  Future<void> goToNotifications() => nav.goToNotifications();
  Future<void> goToFavorites() => nav.goToFavorites();
  Future<void> goToProfile() => nav.goToProfile();
  Future<void> goToSupport() => nav.goToSupport();
  Future<T?> goToProduct<T>(String productId) => nav.goToProduct<T>(productId);
  Future<void> goToAddVehicle() => nav.goToAddVehicle();
}
