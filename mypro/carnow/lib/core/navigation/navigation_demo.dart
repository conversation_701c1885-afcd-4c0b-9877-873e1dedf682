import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'navigation_exports.dart' hide UnifiedAppBar;
import '../widgets/unified_app_bar.dart';

/// صفحة توضيحية لاستخدام نظام التنقل الموحد
///
/// تُظهر هذه الصفحة كيفية استخدام:
/// - النظام الأساسي للتنقل
/// - شريط التنقل السفلي
/// - شريط التطبيق الموحد
/// - الانتقال بين الشاشات
class NavigationDemoScreen extends ConsumerWidget {
  const NavigationDemoScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: const UnifiedAppBar(title: 'نظام التنقل الموحد'),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSectionTitle('الوجهات الرئيسية'),
            _buildMainDestinations(context),
            const SizedBox(height: 24),

            _buildSectionTitle('وجهات البائع'),
            _buildSellerDestinations(context),
            const SizedBox(height: 24),

            _buildSectionTitle('وجهات المرآب'),
            _buildGarageDestinations(context),
            const SizedBox(height: 24),

            _buildSectionTitle('وجهات الدعم'),
            _buildSupportDestinations(context),
            const SizedBox(height: 24),

            _buildSectionTitle('استخدام NavigationBuilder'),
            _buildNavigationBuilderExample(context),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Text(
        title,
        style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
      ),
    );
  }

  Widget _buildMainDestinations(BuildContext context) {
    return Column(
      children: [
        _buildNavigationButton(
          context,
          title: 'الصفحة الرئيسية',
          subtitle: 'العودة للصفحة الرئيسية',
          icon: Icons.home,
          onTap: () => context.goHome(),
        ),
        _buildNavigationButton(
          context,
          title: 'البحث',
          subtitle: 'البحث عن قطع الغيار',
          icon: Icons.search,
          onTap: () => context.goToSearch(),
        ),
        _buildNavigationButton(
          context,
          title: 'البحث مع كلمة مفتاحية',
          subtitle: 'البحث عن "تويوتا كامري"',
          icon: Icons.search,
          onTap: () => context.goToSearch(query: 'تويوتا كامري'),
        ),
        _buildNavigationButton(
          context,
          title: 'تفاصيل منتج',
          subtitle: 'عرض تفاصيل منتج معين',
          icon: Icons.info,
          onTap: () =>
              UnifiedNavigationSystem.goToProduct(context, 'demo-product-123'),
        ),
        _buildNavigationButton(
          context,
          title: 'سلة التسوق',
          subtitle: 'عرض محتويات السلة',
          icon: Icons.shopping_cart,
          onTap: () => context.goToCart(),
        ),
        _buildNavigationButton(
          context,
          title: 'المفضلة',
          subtitle: 'عرض المنتجات المفضلة',
          icon: Icons.favorite,
          onTap: () => UnifiedNavigationSystem.goToFavorites(context),
        ),
      ],
    );
  }

  Widget _buildSellerDestinations(BuildContext context) {
    return Column(
      children: [
        _buildNavigationButton(
          context,
          title: 'لوحة تحكم البائع',
          subtitle: 'الانتقال للوحة التحكم',
          icon: Icons.dashboard,
          onTap: () => UnifiedNavigationSystem.goToSellerDashboard(context),
        ),
        _buildNavigationButton(
          context,
          title: 'إضافة منتج جديد',
          subtitle: 'إضافة منتج للبيع',
          icon: Icons.add_shopping_cart,
          onTap: () => UnifiedNavigationSystem.goToAddProduct(context),
        ),
        _buildNavigationButton(
          context,
          title: 'تحرير منتج',
          subtitle: 'تحرير منتج موجود',
          icon: Icons.edit,
          onTap: () =>
              UnifiedNavigationSystem.goToEditProduct(context, 'product-123'),
        ),
      ],
    );
  }

  Widget _buildGarageDestinations(BuildContext context) {
    return Column(
      children: [
        _buildNavigationButton(
          context,
          title: 'المرآب',
          subtitle: 'عرض سياراتي',
          icon: Icons.garage,
          onTap: () => context.goToGarage(),
        ),
        _buildNavigationButton(
          context,
          title: 'إضافة سيارة',
          subtitle: 'إضافة سيارة جديدة للمرآب',
          icon: Icons.add,
          onTap: () => UnifiedNavigationSystem.goToAddVehicle(context),
        ),
      ],
    );
  }

  Widget _buildSupportDestinations(BuildContext context) {
    return Column(
      children: [
        _buildNavigationButton(
          context,
          title: 'الرسائل',
          subtitle: 'عرض المحادثات',
          icon: Icons.message,
          onTap: () => UnifiedNavigationSystem.goToMessages(context),
        ),
        _buildNavigationButton(
          context,
          title: 'الدعم الفني',
          subtitle: 'التواصل مع الدعم',
          icon: Icons.support_agent,
          onTap: () => UnifiedNavigationSystem.goToSupportCenter(context),
        ),
      ],
    );
  }

  Widget _buildNavigationBuilderExample(BuildContext context) {
    return Column(
      children: [
        _buildNavigationButton(
          context,
          title: 'مثال NavigationBuilder',
          subtitle: 'استخدام Builder للتنقل المتقدم',
          icon: Icons.build,
          onTap: () {
            NavigationBuilder()
                .path(UnifiedNavigationSystem.search)
                .type(NavigationType.push)
                .query({'q': 'محرك', 'category': 'engine'})
                .execute(context);
          },
        ),
        _buildNavigationButton(
          context,
          title: 'تنقل مع استبدال',
          subtitle: 'استبدال الصفحة الحالية',
          icon: Icons.swap_horiz,
          onTap: () {
            const NavigationAction(
              path: UnifiedNavigationSystem.profile,
              type: NavigationType.replace,
            ).execute(context);
          },
        ),
      ],
    );
  }

  Widget _buildNavigationButton(
    BuildContext context, {
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Icon(icon),
        title: Text(title),
        subtitle: Text(subtitle),
        trailing: const Icon(Icons.arrow_forward_ios),
        onTap: onTap,
      ),
    );
  }
}

/// صفحة demo للتنقل السفلي الموحد
class BottomNavigationDemo extends ConsumerWidget {
  const BottomNavigationDemo({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return const UnifiedBottomNavigation(child: NavigationDemoScreen());
  }
}

/// صفحة demo لشريط البحث
class SearchBarDemo extends ConsumerWidget {
  const SearchBarDemo({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: SearchUnifiedAppBar(
        title: 'بحث',
        onSearchChanged: (query) {
          // Handle search query changes
          debugPrint('Search query changed: $query');
        },
      ),
      body: const Center(
        child: Text(
          'صفحة البحث التوضيحية\nاكتب في شريط البحث أعلاه',
          textAlign: TextAlign.center,
          style: TextStyle(fontSize: 18),
        ),
      ),
    );
  }
}

/// صفحة demo بسيطة
class SimplePageDemo extends StatelessWidget {
  const SimplePageDemo({super.key});

  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      appBar: SimpleUnifiedAppBar(title: 'صفحة بسيطة'),
      body: Center(
        child: Text(
          'هذه صفحة توضيحية بسيطة\nتستخدم SimpleUnifiedAppBar',
          textAlign: TextAlign.center,
        ),
      ),
    );
  }
}
