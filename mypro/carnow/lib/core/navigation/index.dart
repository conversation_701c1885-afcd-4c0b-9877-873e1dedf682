// نظام التنقل الموحد لتطبيق CarNow
//
// هذا الملف يوفر نقطة واحدة لاستيراد جميع مكونات نظام التنقل
// مما يسهل الصيانة والاستخدام عبر التطبيق

// الخدمة الأساسية للتنقل
export 'navigation_service.dart';

// المكونات المرئية للتنقل
export 'modern_bottom_navigation.dart';

// النظام القديم للتوافق (سيتم إزالته لاحقاً)
export 'unified_navigation_system.dart';

// ملفات الديمو والاختبار (للتطوير فقط)
export 'navigation_demo.dart';
export 'navigation_showcase.dart';

// امتدادات التنقل
import 'package:flutter/material.dart';
import 'navigation_service.dart';

/// امتدادات إضافية للتنقل السريع
extension QuickNavigationExtensions on BuildContext {
  /// التنقل السريع مع معالجة الأخطاء
  Future<void> safePush(String route, {Object? extra}) async {
    try {
      await NavigationService.instance.pushTo(route, extra: extra);
    } catch (e) {
      // يمكن إضافة معالجة الأخطاء هنا
      debugPrint('خطأ في التنقل: $e');
    }
  }

  /// التنقل مع إعادة التوجيه الآمن
  Future<void> safeNavigate(String route, {bool requiresAuth = false}) async {
    try {
      await NavigationService.instance.safeNavigateTo(
        route,
        isAuthenticated:
            !requiresAuth, // يمكن تحسين هذا مع حالة المصادقة الفعلية
      );
    } catch (e) {
      debugPrint('خطأ في التنقل الآمن: $e');
    }
  }

  /// التحقق من إمكانية التنقل للمسار
  bool canNavigateTo(String route) {
    try {
      // يمكن إضافة منطق التحقق هنا
      return route.isNotEmpty && route.startsWith('/');
    } catch (e) {
      return false;
    }
  }
}

/// نصائح للاستخدام:
///
/// 1. للتنقل الأساسي:
///    ```dart
///    context.goHome();
///    context.goToCategories();
///    ```
///
/// 2. للتنقل المتقدم:
///    ```dart
///    NavigationService.instance.navigateTo('/custom-route');
///    ```
///
/// 3. للتنقل الآمن:
///    ```dart
///    context.safeNavigate('/protected-route', requiresAuth: true);
///    ```
///
/// 4. للحصول على معلومات التنقل:
///    ```dart
///    final currentRoute = NavigationService.instance.currentRoute;
///    final canPop = NavigationService.instance.canPop();
///    ```
