import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// صفحة demo بسيطة لعرض البحث
class SearchBarDemo extends ConsumerWidget {
  const SearchBarDemo({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(title: const Text('البحث المتقدم')),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.search, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text('شريط البحث المتقدم'),
            SizedBox(height: 8),
            Text('مع اقتراحات ذكية وفلاتر متقدمة'),
          ],
        ),
      ),
    );
  }
}

/// صفحة demo بسيطة أخرى
class SimpleDemo extends ConsumerWidget {
  const SimpleDemo({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(title: const Text('أفضل الممارسات')),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.star, size: 64, color: Colors.amber),
            SizedBox(height: 16),
            Text('أفضل الممارسات'),
            SizedBox(height: 8),
            Text('نصائح وإرشادات لاستخدام النظام بكفاءة'),
          ],
        ),
      ),
    );
  }
}
