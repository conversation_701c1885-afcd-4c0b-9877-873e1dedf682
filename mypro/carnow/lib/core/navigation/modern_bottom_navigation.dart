import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import 'navigation_service.dart';

/// عنصر التنقل في الشريط السفلي
class NavigationItem {
  const NavigationItem({
    required this.icon,
    required this.activeIcon,
    required this.label,
    required this.route,
    this.badge,
  });

  final IconData icon;
  final IconData activeIcon;
  final String label;
  final String route;
  final String? badge;
}

/// مزود عناصر التنقل
final navigationItemsProvider = Provider<List<NavigationItem>>((ref) {
  return [
    const NavigationItem(
      icon: Icons.home_outlined,
      activeIcon: Icons.home,
      label: 'الرئيسية',
      route: NavigationService.home,
    ),
    const NavigationItem(
      icon: Icons.category_outlined,
      activeIcon: Icons.category,
      label: 'الفئات',
      route: NavigationService.categories,
    ),
    const NavigationItem(
      icon: Icons.store_outlined,
      activeIcon: Icons.store,
      label: 'المتجر',
      route: NavigationService.store,
    ),
    const NavigationItem(
      icon: Icons.person_outline,
      activeIcon: Icons.person,
      label: 'الحساب',
      route: NavigationService.account,
    ),
  ];
});

/// مزود الفهرس الحالي للتنقل
// final currentNavigationIndexProvider = StateProvider<int>((ref) => 0);

/// شريط التنقل السفلي الحديث
class ModernBottomNavigation extends ConsumerWidget {
  const ModernBottomNavigation({super.key, required this.navigationShell});

  final StatefulNavigationShell navigationShell;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final items = ref.watch(navigationItemsProvider);
    final theme = Theme.of(context);
    final currentIndex = navigationShell.currentIndex;

    return Container(
      decoration: BoxDecoration(
        color:
            theme.bottomNavigationBarTheme.backgroundColor ??
            theme.scaffoldBackgroundColor,
        boxShadow: [
          BoxShadow(
            color: theme.shadowColor.withAlpha((0.1 * 255).toInt()),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: items.asMap().entries.map((entry) {
              final index = entry.key;
              final item = entry.value;
              final isSelected = index == currentIndex;

              return _NavigationButton(
                item: item,
                isSelected: isSelected,
                onTap: () => _onItemTapped(index),
              );
            }).toList(),
          ),
        ),
      ),
    );
  }

  void _onItemTapped(int index) {
    navigationShell.goBranch(
      index,
      initialLocation: index == navigationShell.currentIndex,
    );
  }
}

/// زر التنقل المخصص
class _NavigationButton extends StatelessWidget {
  const _NavigationButton({
    required this.item,
    required this.isSelected,
    required this.onTap,
  });

  final NavigationItem item;
  final bool isSelected;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected
              ? colorScheme.primary.withAlpha((0.1 * 255).toInt())
              : Colors.transparent,
          borderRadius: BorderRadius.circular(20),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Stack(
              children: [
                AnimatedSwitcher(
                  duration: const Duration(milliseconds: 200),
                  child: Icon(
                    isSelected ? item.activeIcon : item.icon,
                    key: ValueKey(isSelected),
                    color: isSelected
                        ? colorScheme.primary
                        : colorScheme.onSurface.withAlpha((0.6 * 255).toInt()),
                    size: 24,
                  ),
                ),
                if (item.badge != null)
                  Positioned(
                    right: 0,
                    top: 0,
                    child: Container(
                      padding: const EdgeInsets.all(2),
                      decoration: BoxDecoration(
                        color: colorScheme.error,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      constraints: const BoxConstraints(
                        minWidth: 16,
                        minHeight: 16,
                      ),
                      child: Text(
                        item.badge!,
                        style: theme.textTheme.labelSmall?.copyWith(
                          color: colorScheme.onError,
                          fontSize: 10,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 4),
            AnimatedDefaultTextStyle(
              duration: const Duration(milliseconds: 200),
              style:
                  theme.textTheme.labelSmall?.copyWith(
                    color: isSelected
                        ? colorScheme.primary
                        : colorScheme.onSurface.withAlpha((0.6 * 255).toInt()),
                    fontWeight: isSelected
                        ? FontWeight.w600
                        : FontWeight.normal,
                  ) ??
                  const TextStyle(),
              child: Text(item.label),
            ),
          ],
        ),
      ),
    );
  }
}

/// شريط تنقل علوي موحد
class ModernAppBar extends ConsumerWidget implements PreferredSizeWidget {
  const ModernAppBar({
    super.key,
    this.title,
    this.showBackButton = true,
    this.showNotifications = true,
    this.actions,
    this.backgroundColor,
    this.elevation,
  });

  final String? title;
  final bool showBackButton;
  final bool showNotifications;
  final List<Widget>? actions;
  final Color? backgroundColor;
  final double? elevation;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final canPop = NavigationService.instance.canPop();

    return AppBar(
      title: title != null ? Text(title!) : null,
      backgroundColor: backgroundColor ?? theme.appBarTheme.backgroundColor,
      elevation: elevation ?? theme.appBarTheme.elevation,
      leading: showBackButton && canPop
          ? IconButton(
              icon: const Icon(Icons.arrow_back),
              onPressed: () => NavigationService.instance.pop(),
            )
          : null,
      actions: [
        if (showNotifications)
          IconButton(
            icon: const Icon(Icons.notifications_outlined),
            onPressed: () => context.goToNotifications(),
          ),
        ...?actions,
      ],
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

/// مخطط الشاشة الرئيسي مع التنقل
class ModernScaffoldWithNavigation extends ConsumerWidget {
  const ModernScaffoldWithNavigation({
    super.key,
    required this.navigationShell,
  });

  final StatefulNavigationShell navigationShell;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      body: navigationShell,
      bottomNavigationBar: ModernBottomNavigation(
        navigationShell: navigationShell,
      ),
    );
  }
}
