import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:riverpod/riverpod.dart';

part 'unified_navigation_system.g.dart';

/// نظام التنقل الموحد لتطبيق CarNow
///
/// يوفر هذا النظام:
/// - إدارة موحدة لجميع المسارات في التطبيق
/// - تنقل سهل ومنسق بين الشاشات
/// - دعم للروابط العميقة (Deep Links)
/// - إدارة حالة التنقل مع Riverpod
class UnifiedNavigationSystem {
  /// Routes Constants - ثوابت المسارات
  static const String home = '/';
  static const String categories = '/categories';
  static const String garage = '/garage';
  static const String store = '/store';
  static const String account = '/account';
  static const String search = '/search';
  static const String cart = '/cart';
  static const String orders = '/orders';
  static const String support = '/support';
  static const String settings = '/settings';
  static const String notifications = '/notifications';
  static const String profile = '/account/profile';

  // مسارات المصادقة
  static const String login = '/login';
  static const String register = '/register';
  static const String profileSetup = '/account/profile/complete';
  static const String passwordReset = '/reset-password';

  // Seller Routes
  static const String sellerDashboard = '/seller/dashboard';
  static const String sellerProducts = '/seller/products';
  static const String addProduct = '/seller/add-product';
  static const String editProduct = '/seller/products/:id/edit';
  static const String sellerOrders = '/seller/orders';
  static const String sellerStats = '/seller/stats';
  static const String sellerApplication = '/seller/application';

  // Chat & Messaging
  static const String messages = '/messages';
  static const String chatScreen = '/messages/:id';
  static const String newConversation = '/messages/new';

  // Support & Help
  static const String helpCenter = '/help';
  static const String faq = '/help/faq';

  // Admin Routes (for future use)
  static const String admin = '/admin';
  static const String adminDashboard = '/admin/dashboard';
  static const String adminUsers = '/admin/users';
  static const String adminProducts = '/admin/products';

  /// Navigation Helper Methods
  /// طرق مساعدة للتنقل

  /// التنقل إلى الصفحة الرئيسية
  static void goToHome(BuildContext context) {
    context.go(home);
  }

  /// التنقل إلى الصفحة الرئيسية - بديل
  static void goHome(BuildContext context) {
    context.go(home);
  }

  /// التنقل إلى الفئات
  static void goToCategories(BuildContext context, {String? categoryId}) {
    if (categoryId != null) {
      context.go('$categories/$categoryId');
    } else {
      context.go(categories);
    }
  }

  /// التنقل إلى الكراج
  static void goToGarage(BuildContext context) {
    context.go(garage);
  }

  /// التنقل إلى المتجر
  static void goToStore(BuildContext context) {
    context.go(store);
  }

  /// التنقل إلى الحساب
  static void goToAccount(BuildContext context) {
    context.go(account);
  }

  /// التنقل إلى البحث
  static void goToSearch(BuildContext context, {String? query}) {
    if (query != null) {
      context.go('$search?q=$query');
    } else {
      context.go(search);
    }
  }

  /// التنقل إلى السلة
  static void goToCart(BuildContext context) {
    context.go(cart);
  }

  /// التنقل إلى الطلبات
  static void goToOrders(BuildContext context) {
    context.go(orders);
  }

  /// التنقل إلى الإعدادات
  static void goToSettings(BuildContext context) {
    context.go(settings);
  }

  /// التنقل إلى الإشعارات
  static void goToNotifications(BuildContext context) {
    context.go(notifications);
  }

  /// التنقل إلى الملف الشخصي
  static void goToProfile(BuildContext context) {
    context.push(profile);
  }

  /// التنقل إلى إضافة مركبة
  static void goToAddVehicle(BuildContext context) {
    context.push('/garage/add-vehicle');
  }

  /// التنقل إلى منتج محدد
  static void goToProduct(BuildContext context, String productId) {
    context.push('/product/$productId');
  }

  /// التنقل إلى المفضلة
  static void goToFavorites(BuildContext context) {
    context.push('/favorites');
  }

  /// التنقل إلى لوحة تحكم البائع
  static void goToSellerDashboard(BuildContext context) {
    context.go(sellerDashboard);
  }

  /// التنقل إلى إضافة منتج (للبائعين)
  static void goToAddProduct(BuildContext context) {
    context.push(addProduct);
  }

  /// التنقل إلى تحرير منتج (للبائعين)
  static void goToEditProduct(BuildContext context, String productId) {
    context.push('/seller/products/$productId/edit');
  }

  /// التنقل إلى الرسائل
  static void goToMessages(BuildContext context) {
    context.push(messages);
  }

  /// التنقل إلى محادثة محددة
  static void goToChat(BuildContext context, String conversationId) {
    context.push('/messages/$conversationId');
  }

  /// التنقل إلى الدعم الفني
  static void goToSupportCenter(BuildContext context) {
    context.push(support);
  }

  /// التنقل إلى تسجيل الدخول
  static void goToLogin(BuildContext context, {String? returnTo}) {
    if (returnTo != null) {
      context.go('$login?returnTo=${Uri.encodeComponent(returnTo)}');
    } else {
      context.go(login);
    }
  }

  /// التنقل إلى التسجيل
  static void goToRegister(BuildContext context, {String? returnTo}) {
    if (returnTo != null) {
      context.go('$register?returnTo=${Uri.encodeComponent(returnTo)}');
    } else {
      context.go(register);
    }
  }

  /// التنقل إلى إعداد الملف الشخصي
  static void goToProfileSetup(BuildContext context, {String? returnTo}) {
    if (returnTo != null) {
      context.go('$profileSetup?returnTo=${Uri.encodeComponent(returnTo)}');
    } else {
      context.go(profileSetup);
    }
  }

  /// التنقل إلى إعادة تعيين كلمة المرور
  static void goToPasswordReset(BuildContext context, {String? returnTo}) {
    if (returnTo != null) {
      context.go('$passwordReset?returnTo=${Uri.encodeComponent(returnTo)}');
    } else {
      context.go(passwordReset);
    }
  }

  /// الرجوع إلى الصفحة السابقة
  static void goBack(BuildContext context) {
    if (context.canPop()) {
      context.pop();
    } else {
      context.go(home);
    }
  }

  /// التحقق من إمكانية الرجوع
  static bool canGoBack(BuildContext context) {
    return context.canPop();
  }

  /// الحصول على المسار الحالي
  static String getCurrentRoute(BuildContext context) {
    return GoRouterState.of(context).matchedLocation;
  }

  /// التحقق من كون المسار الحالي هو الصفحة الرئيسية
  static bool isHome(BuildContext context) {
    return getCurrentRoute(context) == home;
  }

  /// التحقق من كون المستخدم في قسم البائع
  static bool isSellerSection(BuildContext context) {
    return getCurrentRoute(context).startsWith('/seller');
  }

  /// إعادة تحديد مسار آمن بناءً على حالة المصادقة
  static String getSafeRoute(String route, {bool isAuthenticated = false}) {
    const protectedRoutes = [
      '/seller',
      '/account',
      '/orders',
      '/messages',
      '/cart',
    ];

    final isProtected = protectedRoutes.any(
      (protected) => route.startsWith(protected),
    );

    if (isProtected && !isAuthenticated) {
      return login;
    }

    return route;
  }

  /// إعادة توجيه آمنة بناءً على صلاحيات المستخدم
  static void safeNavigate(
    BuildContext context,
    String route, {
    bool isAuthenticated = false,
    Map<String, String>? queryParams,
  }) {
    final safeRoute = getSafeRoute(route, isAuthenticated: isAuthenticated);

    if (queryParams != null && queryParams.isNotEmpty) {
      final uri = Uri.parse(safeRoute);
      final newUri = uri.replace(
        queryParameters: {...uri.queryParameters, ...queryParams},
      );
      context.go(newUri.toString());
    } else {
      context.go(safeRoute);
    }
  }

  /// مسح تاريخ التنقل والعودة للصفحة الرئيسية
  static void resetToHome(BuildContext context) {
    context.go(home);
  }

  /// التنقل مع استبدال المسار الحالي
  static void replaceWith(BuildContext context, String route) {
    context.pushReplacement(route);
  }

  /// عرض نافذة حوار للتنقل
  static Future<bool?> showNavigationDialog(
    BuildContext context, {
    required String title,
    required String message,
    String confirmText = 'موافق',
    String cancelText = 'إلغاء',
  }) {
    return showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(cancelText),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: Text(confirmText),
          ),
        ],
      ),
    );
  }

  /// التنقل مع تأكيد
  static Future<void> navigateWithConfirmation(
    BuildContext context, {
    required String route,
    String title = 'تأكيد التنقل',
    String message = 'هل تريد الانتقال إلى هذه الصفحة؟',
  }) async {
    final confirmed = await showNavigationDialog(
      context,
      title: title,
      message: message,
    );

    if (confirmed == true && context.mounted) {
      context.go(route);
    }
  }
}

/// مزود Riverpod حديث لإدارة حالة المسار الحالي
@riverpod
class NavigationState extends _$NavigationState {
  @override
  String build() => UnifiedNavigationSystem.home;

  void setRoute(String route) => state = route;
}

/// Provider مشتق يوفّر المسار الحالي فقط (قراءة فقط)
@riverpod
String currentRoute(Ref ref) => ref.watch(navigationStateProvider);

/// Extension لتسهيل استخدام التنقل مع BuildContext
extension NavigationExtension on BuildContext {
  /// التنقل السريع إلى الصفحة الرئيسية
  void goHome() => UnifiedNavigationSystem.goToHome(this);

  /// التنقل السريع إلى الفئات
  void goToCategories({String? categoryId}) =>
      UnifiedNavigationSystem.goToCategories(this, categoryId: categoryId);

  /// التنقل السريع إلى الكراج
  void goToGarage() => UnifiedNavigationSystem.goToGarage(this);

  /// التنقل السريع إلى المتجر
  void goToStore() => UnifiedNavigationSystem.goToStore(this);

  /// التنقل السريع إلى الحساب
  void goToAccount() => UnifiedNavigationSystem.goToAccount(this);

  /// التنقل السريع إلى البحث
  void goToSearch({String? query}) =>
      UnifiedNavigationSystem.goToSearch(this, query: query);

  /// التنقل السريع إلى السلة
  void goToCart() => UnifiedNavigationSystem.goToCart(this);

  /// التنقل السريع إلى الطلبات
  void goToOrders() => UnifiedNavigationSystem.goToOrders(this);

  /// التنقل السريع إلى الإعدادات
  void goToSettings() => UnifiedNavigationSystem.goToSettings(this);

  /// الرجوع بأمان
  void goBackSafely() => UnifiedNavigationSystem.goBack(this);
}

/// Enum لأنواع التنقل المختلفة
enum NavigationType {
  push, // إضافة صفحة جديدة
  go, // الذهاب إلى صفحة (استبدال المكدس)
  replace, // استبدال الصفحة الحالية
  pop, // الرجوع للصفحة السابقة
}

/// Model للتنقل مع البيانات الإضافية
class NavigationAction {
  const NavigationAction({
    required this.path,
    required this.type,
    this.arguments,
    this.queryParameters,
  });

  final String path;
  final NavigationType type;
  final Map<String, dynamic>? arguments;
  final Map<String, String>? queryParameters;

  /// تنفيذ العملية على السياق المحدد
  void execute(BuildContext context) {
    String fullPath = path;

    // إضافة query parameters إذا وجدت
    if (queryParameters != null && queryParameters!.isNotEmpty) {
      final uri = Uri(path: path, queryParameters: queryParameters);
      fullPath = uri.toString();
    }

    switch (type) {
      case NavigationType.push:
        context.push(fullPath);
        break;
      case NavigationType.go:
        context.go(fullPath);
        break;
      case NavigationType.replace:
        context.pushReplacement(fullPath);
        break;
      case NavigationType.pop:
        if (context.canPop()) {
          context.pop();
        }
        break;
    }
  }
}

/// Builder للإنشاء المتقدم لأوامر التنقل
class NavigationBuilder {
  String? _path;
  NavigationType? _type;
  Map<String, dynamic>? _arguments;
  Map<String, String>? _queryParameters;

  NavigationBuilder path(String path) {
    _path = path;
    return this;
  }

  NavigationBuilder type(NavigationType type) {
    _type = type;
    return this;
  }

  NavigationBuilder arguments(Map<String, dynamic> arguments) {
    _arguments = arguments;
    return this;
  }

  NavigationBuilder query(Map<String, String> queryParameters) {
    _queryParameters = queryParameters;
    return this;
  }

  NavigationAction build() {
    assert(_path != null, 'Path is required');
    assert(_type != null, 'Navigation type is required');

    return NavigationAction(
      path: _path!,
      type: _type!,
      arguments: _arguments,
      queryParameters: _queryParameters,
    );
  }

  /// تنفيذ مباشر
  void execute(BuildContext context) {
    build().execute(context);
  }
}

/// بناء رابط مع معاملات الاستعلام
String buildUrlWithQuery(String path, Map<String, String> params) {
  if (params.isEmpty) return path;

  final uri = Uri.parse(path);
  final newUri = uri.replace(
    queryParameters: {...uri.queryParameters, ...params},
  );

  return newUri.toString();
}

/// استخراج معرف المنتج من المسار
String? getProductIdFromPath(String path) {
  final regex = RegExp('/product/([^/]+)');
  final match = regex.firstMatch(path);
  return match?.group(1);
}

/// استخراج معرف الفئة من المسار
String? getCategoryIdFromPath(String path) {
  final regex = RegExp('/category/([^/]+)');
  final match = regex.firstMatch(path);
  return match?.group(1);
}

/// التحقق من أن المسار يتطلب مصادقة
bool isProtectedRoute(String path) {
  const protectedRoutes = [
    '/account',
    '/orders',
    '/cart',
    '/seller',
    '/profile',
  ];

  return protectedRoutes.any((route) => path.startsWith(route));
}

/// التحقق من أن المسار هو مسار مصادقة
bool isAuthRoute(String path) {
  const authRoutes = [
    '/auth',
    '/login',
    '/register',
    '/auth/profile-setup',
    '/auth/reset-password',
  ];

  return authRoutes.any((route) => path.startsWith(route));
}
