# نظام التنقل الموحد لتطبيق CarNow

## نظرة عامة

نظام التنقل الموحد يوفر حلاً شاملاً ومنسقاً لإدارة التنقل في تطبيق CarNow. يشمل النظام مكونات متقدمة للتنقل بين الشاشات، وإدارة الحالة، ودعم الروابط العميقة.

## المكونات الرئيسية

### 1. UnifiedNavigationSystem

النظام الأساسي الذي يحتوي على:

- ثوابت المسارات لجميع الشاشات
- دوال مساعدة للتنقل
- دعم للمعاملات والاستعلامات

```dart
// مثال على الاستخدام
UnifiedNavigationSystem.goHome(context);
UnifiedNavigationSystem.goToProductDetails(context, productId);
UnifiedNavigationSystem.goToSearch(context, query: 'تويوتا');
```

### 2. UnifiedBottomNavigation

شريط التنقل السفلي الذكي مع:

- مؤشرات بصرية للحالة النشطة
- دعم للشارات (badges)
- تفاعل مع الضغط المزدوج

```dart
UnifiedBottomNavigation(
  child: YourMainContent(),
)
```

### 3. UnifiedAppBar

شريط التطبيق الموحد مع:

- تصميم متسق عبر التطبيق
- دعم للبحث والإشعارات
- إدارة ذكية للأزرار

```dart
UnifiedAppBar(
  title: 'عنوان الصفحة',
  showSearch: true,
  showNotifications: true,
  showCart: true,
)
```

### 4. SearchAppBar

شريط بحث متخصص مع:

- تركيز تلقائي على حقل البحث
- دعم للتصفية
- تفاعل فوري مع النص

```dart
SearchAppBar(
  hintText: 'ابحث عن قطع الغيار...',
  onSearchChanged: (query) => handleSearch(query),
  onSearchSubmitted: (query) => submitSearch(query),
)
```

## Extension Methods

تم إضافة extension methods لتسهيل الاستخدام:

```dart
// بدلاً من
UnifiedNavigationSystem.goHome(context);

// يمكن استخدام
context.goHome();
context.goToProduct(productId);
context.goToSearch(query: 'محرك');
context.goBackSafely();
```

## NavigationBuilder للتنقل المتقدم

للحالات المعقدة، يمكن استخدام NavigationBuilder:

```dart
NavigationBuilder()
  .path('/search')
  .type(NavigationType.push)
  .query({'q': 'محرك', 'category': 'engine'})
  .execute(context);
```

## أنواع التنقل

```dart
enum NavigationType {
  push,    // إضافة صفحة جديدة
  go,      // الذهاب إلى صفحة (استبدال المكدس)
  replace, // استبدال الصفحة الحالية
  pop,     // الرجوع للصفحة السابقة
}
```

## الثوابت المتاحة

### المسارات الرئيسية

- `UnifiedNavigationSystem.home` - الصفحة الرئيسية
- `UnifiedNavigationSystem.search` - البحث
- `UnifiedNavigationSystem.products` - المنتجات
- `UnifiedNavigationSystem.cart` - سلة التسوق
- `UnifiedNavigationSystem.favorites` - المفضلة

### مسارات البائع

- `UnifiedNavigationSystem.sellerDashboard` - لوحة تحكم البائع
- `UnifiedNavigationSystem.addProduct` - إضافة منتج
- `UnifiedNavigationSystem.editProduct` - تحرير منتج

### مسارات المرآب

- `UnifiedNavigationSystem.garage` - المرآب
- `UnifiedNavigationSystem.addVehicle` - إضافة سيارة
- `UnifiedNavigationSystem.vehicleDetails` - تفاصيل السيارة

### مسارات الدعم

- `UnifiedNavigationSystem.messages` - الرسائل
- `UnifiedNavigationSystem.support` - الدعم الفني
- `UnifiedNavigationSystem.helpCenter` - مركز المساعدة

## أمثلة عملية

### 1. التنقل البسيط

```dart
FilledButton(
  onPressed: () => context.goHome(),
  child: Text('الصفحة الرئيسية'),
)
```

### 2. التنقل مع معاملات

```dart
ListTile(
  title: Text(product.name),
  onTap: () => context.goToProduct(product.id),
)
```

### 3. البحث مع استعلام

```dart
SearchBar(
  onSubmitted: (query) => context.goToSearch(query: query),
)
```

### 4. التنقل المتقدم

```dart
void navigateToAdvancedSearch() {
  NavigationAction(
    path: UnifiedNavigationSystem.advancedSearch,
    type: NavigationType.push,
    queryParameters: {
      'category': selectedCategory,
      'priceRange': selectedPriceRange,
    },
  ).execute(context);
}
```

## إدارة الحالة

النظام يدعم Riverpod لإدارة حالة التنقل:

```dart
// Provider للمسار الحالي
final currentRoute = ref.watch(currentRouteProvider);

// Provider لحالة التنقل السفلي
final bottomNavIndex = ref.watch(bottomNavigationProvider);
```

## الاستيراد

```dart
// استيراد جميع مكونات التنقل
import 'core/navigation/navigation_exports.dart';

// أو استيراد مكونات محددة
import 'core/navigation/unified_navigation_system.dart';
import 'core/navigation/unified_bottom_navigation.dart';
import 'core/navigation/unified_app_bar.dart';
```

## صفحات التوضيح

تم إنشاء صفحات توضيحية في `navigation_demo.dart`:

- `NavigationDemoScreen` - صفحة شاملة تعرض جميع إمكانيات التنقل
- `BottomNavigationDemo` - مثال على التنقل السفلي
- `SearchBarDemo` - مثال على شريط البحث
- `SimpleDemo` - مثال بسيط

## أفضل الممارسات

### 1. استخدم Extension Methods

```dart
// ✅ جيد
context.goHome();

// ❌ تجنب
UnifiedNavigationSystem.goHome(context);
```

### 2. استخدم const للمسارات

```dart
// ✅ جيد
context.go(UnifiedNavigationSystem.home);

// ❌ تجنب
context.go('/');
```

### 3. تعامل مع الأخطاء

```dart
void navigateToProduct(String? productId) {
  if (productId == null) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('معرف المنتج غير صحيح')),
    );
    return;
  }
  context.goToProduct(productId);
}
```

### 4. استخدم التنقل الآمن

```dart
// ✅ جيد - يتحقق من إمكانية الرجوع
context.goBackSafely();

// ❌ قد يسبب مشاكل
Navigator.of(context).pop();
```

## التطوير المستقبلي

- [ ] دعم للتنقل بالإيماءات
- [ ] تخزين تاريخ التنقل
- [ ] تحليلات التنقل
- [ ] دعم للوضع المظلم المتقدم
- [ ] تخصيص أشرطة التنقل حسب المستخدم

## المساعدة والدعم

للحصول على المساعدة أو الإبلاغ عن مشاكل:

1. راجع ملفات التوضيح في `navigation_demo.dart`
2. تحقق من التوثيق في التعليقات
3. اطلع على أمثلة الاستخدام في الملفات الموجودة