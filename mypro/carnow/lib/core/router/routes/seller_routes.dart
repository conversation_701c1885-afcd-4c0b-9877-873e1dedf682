import 'package:go_router/go_router.dart';

import '../../../features/seller/screens/seller_dashboard_screen.dart';
import '../../../features/seller/screens/add_product_screen.dart';
import '../../../features/seller/screens/products/product_edit_screen.dart';
import '../../../features/seller/screens/order_management_screen.dart';
import '../../../features/seller/screens/sales_analytics_screen.dart';
import '../../../features/seller/screens/subscription_request_screen.dart';
import '../../../features/seller/screens/subscription_request_status_screen.dart' as status;

/// Seller routes configuration following Forever Plan Architecture
/// Removed enhanced patterns as they violate the architecture rules
List<RouteBase> sellerRoutes = [
  GoRoute(
    path: '/seller',
    builder: (context, state) => const SellerDashboardScreen(),
  ),
  GoRoute(
    path: '/seller/add-product',
    builder: (context, state) => const AddProductScreen(),
  ),
  GoRoute(
    path: '/seller/edit-product/:productId',
    builder: (context, state) {
      final productId = state.pathParameters['productId'] ?? '';
      return ProductEditScreen(productId: productId);
    },
  ),
  GoRoute(
    path: '/seller/orders',
    builder: (context, state) => const OrderManagementScreen(),
  ),
  GoRoute(
    path: '/seller/analytics',
    builder: (context, state) => const SalesAnalyticsScreen(),
  ),
  GoRoute(
    path: '/seller/products/edit/:productId',
    builder: (context, state) {
      final productId = state.pathParameters['productId'] ?? '';
      return ProductEditScreen(productId: productId);
    },
  ),
  GoRoute(
    path: '/seller/subscription-request',
    builder: (context, state) => const SubscriptionRequestScreen(),
  ),
  GoRoute(
    path: '/seller/subscription-status',
    builder: (context, state) => const status.SubscriptionRequestStatusScreen(),
  ),
];
