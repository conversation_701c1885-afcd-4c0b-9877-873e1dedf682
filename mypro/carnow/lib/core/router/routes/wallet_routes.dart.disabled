import 'package:go_router/go_router.dart';
import '../../../features/wallet/screens/wallet_screen.dart';
import '../../../features/wallet/screens/wallet_deposit_screen.dart';
import '../../../features/wallet/screens/wallet_withdraw_screen.dart';
import '../../../features/wallet/screens/wallet_transfer_screen.dart';
import '../../../features/wallet/screens/wallet_verification_screen.dart';
import '../../../features/wallet/screens/carnow_wallet_screen.dart';

/// مسارات المحفظة
final walletRoutes = [
  GoRoute(
    path: '/wallet',
    builder: (context, state) => const WalletScreen(),
  ),
  GoRoute(
    path: '/wallet/deposit',
    builder: (context, state) => const WalletDepositScreen(),
  ),
  GoRoute(
    path: '/wallet/withdraw',
    builder: (context, state) => const WalletWithdrawScreen(),
  ),
  GoRoute(
    path: '/wallet/transfer',
    builder: (context, state) => const WalletTransferScreen(),
  ),
  GoRoute(
    path: '/wallet/verification',
    builder: (context, state) => const WalletVerificationScreen(),
  ),
  // المحفظة المالية (Carnow Backend)
  GoRoute(
    path: '/wallet/financial',
    builder: (context, state) => const CarnowWalletScreen(),
  ),
];
