import 'package:go_router/go_router.dart';
import 'package:flutter/material.dart';

import '../../../features/account/screens/edit_profile_screen.dart';
import '../../../features/account/screens/profile_completion_screen.dart';
import '../../../features/account/screens/unified_subscription_screen.dart';
import '../../../features/orders/screens/my_orders_screen.dart';
import '../../../features/auth/screens/new_login_screen.dart';
import '../../../features/auth/screens/unified_auth_screen.dart';
import '../../../features/auth/screens/forgot_password_screen.dart';
import '../../../features/auth/screens/reset_password_screen.dart';
import '../../../features/auth/screens/email_verification_screen.dart';
import '../../../shared/screens/support_screen.dart';
import '../../../features/legal/screens/terms_conditions_screen.dart';
import '../../../features/legal/screens/privacy_policy_screen.dart';
import '../../../features/auth/screens/change_password_screen.dart';

final List<RouteBase> accountRoutes = [
  // Authentication routes
  GoRoute(
    path: '/login',
    name: 'login',
    builder: (context, state) {
      debugPrint('AccountRoutes: Building NewLoginScreen');
      return const NewLoginScreen();
    },
  ),
  GoRoute(
    path: '/register',
    name: 'register',
    builder: (context, state) {
      debugPrint('AccountRoutes: Building UnifiedAuthScreen');
      return const UnifiedAuthScreen();
    },
  ),
  GoRoute(
    path: '/forgot-password',
    builder: (context, state) => const ForgotPasswordScreen(),
  ),
  GoRoute(
    path: '/reset-password',
    builder: (context, state) => const ResetPasswordScreen(),
  ),
  GoRoute(
    path: '/email-verification',
    builder: (context, state) => EmailVerificationScreen(
      email: state.uri.queryParameters['email'] ?? '',
    ),
  ),

  // Account management routes
  GoRoute(
    path: '/account/profile/complete',
    builder: (context, state) => const ProfileCompletionScreen(),
  ),
  GoRoute(
    path: '/account/profile/edit',
    builder: (context, state) => const EditProfileScreen(),
  ),
  GoRoute(
    path: '/change-password',
    builder: (context, state) => const ChangePasswordScreen(),
  ),

  // Unified subscription route
  GoRoute(
    path: '/subscription/plans',
    builder: (context, state) => const UnifiedSubscriptionScreen(),
  ),

  // Order management routes
  GoRoute(
    path: '/orders',
    builder: (context, state) => const MyOrdersScreen(),
  ),

  // Support and legal routes
  GoRoute(
    path: '/support',
    builder: (context, state) => const SupportScreen(),
  ),
  GoRoute(
    path: '/terms-conditions',
    builder: (context, state) => const TermsConditionsScreen(),
  ),
  GoRoute(
    path: '/privacy-policy',
    builder: (context, state) => const PrivacyPolicyScreen(),
  ),
];

/// شاشة اختبار Supabase
///
/// تُستخدم للتحقق من الاتصال بقاعدة بيانات Supabase وعرض النتائج أثناء
/// التطوير.

class SupabaseTestScreen extends StatelessWidget {
  const SupabaseTestScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('اختبار Supabase'), centerTitle: true),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.bug_report, size: 64),
            SizedBox(height: 16),
            Text('اختبار Supabase'),
            SizedBox(height: 8),
            Text('صفحة اختبار الاتصال بقاعدة البيانات'),
          ],
        ),
      ),
    );
  }
}
