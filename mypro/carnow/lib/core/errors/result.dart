import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'app_error.dart';

/// نظام Result موحد لمعالجة النتائج والأخطاء
/// Unified Result system for handling success and error states
///
/// يستخدم هذا النظام لتوحيد طريقة التعامل مع العمليات التي قد تنجح أو تفشل
/// This system is used to unify the way we handle operations that may
/// succeed or fail
///
/// مثال على الاستخدام:
/// Usage example:
/// ```dart
/// Future<Result<User>> getUser(String id) async {
///   try {
///     final user = await userRepository.getById(id);
///     return Result.success(user);
///   } catch (e) {
///     return Result.failure(AppErrorFactory.fromError(e));
///   }
/// }
///
/// // في الـ UI
/// // In UI
/// final result = await getUser('123');
/// result.when(
///   success: (user) => showUserProfile(user),
///   failure: (error) => showErrorMessage(error.userMessage),
/// );
/// ```
sealed class Result<T> {
  const Result();

  /// إنشاء نتيجة ناجحة
  /// Create successful result
  const factory Result.success(T data) = Success<T>;

  /// إنشاء نتيجة فاشلة
  /// Create failure result
  const factory Result.failure(AppError error) = Failure<T>;

  /// تطبيق دالة بناءً على نوع النتيجة
  /// Apply function based on result type
  R when<R>({
    required R Function(T data) success,
    required R Function(AppError error) failure,
  }) {
    if (this is Success<T>) {
      return success((this as Success<T>).data);
    } else if (this is Failure<T>) {
      return failure((this as Failure<T>).error);
    }
    throw StateError('Unknown Result type');
  }

  /// تطبيق دالة بناءً على نوع النتيجة (nullable)
  /// Apply function based on result type (nullable)
  R? whenOrNull<R>({
    R Function(T data)? success,
    R Function(AppError error)? failure,
  }) {
    if (this is Success<T> && success != null) {
      return success((this as Success<T>).data);
    } else if (this is Failure<T> && failure != null) {
      return failure((this as Failure<T>).error);
    }
    return null;
  }
}

/// حالة النجاح
/// Success state
class Success<T> extends Result<T> {
  const Success(this.data);

  final T data;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Success<T> &&
          runtimeType == other.runtimeType &&
          data == other.data;

  @override
  int get hashCode => data.hashCode;

  @override
  String toString() => 'Success(data: $data)';
}

/// حالة الفشل
/// Failure state
class Failure<T> extends Result<T> {
  const Failure(this.error);

  final AppError error;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Failure<T> &&
          runtimeType == other.runtimeType &&
          error == other.error;

  @override
  int get hashCode => error.hashCode;

  @override
  String toString() => 'Failure(error: $error)';
}

/// امتدادات مفيدة لـ Result
/// Useful extensions for Result
extension ResultExtensions<T> on Result<T> {
  /// هل النتيجة ناجحة؟
  /// Is the result successful?
  bool get isSuccess => this is Success<T>;

  /// هل النتيجة فاشلة؟
  /// Is the result a failure?
  bool get isFailure => this is Failure<T>;

  /// الحصول على البيانات أو null
  /// Get data or null
  T? get dataOrNull => when(success: (data) => data, failure: (_) => null);

  /// الحصول على الخطأ أو null
  /// Get error or null
  AppError? get errorOrNull =>
      when(success: (_) => null, failure: (error) => error);

  /// الحصول على البيانات أو قيمة افتراضية
  /// Get data or default value
  T getOrElse(T defaultValue) =>
      when(success: (data) => data, failure: (_) => defaultValue);

  /// تحويل البيانات إذا كانت النتيجة ناجحة
  /// Transform data if result is successful
  Result<R> map<R>(R Function(T data) transform) => when(
    success: (data) => Result.success(transform(data)),
    failure: Result.failure,
  );

  /// تحويل الخطأ إذا كانت النتيجة فاشلة
  /// Transform error if result is a failure
  Result<T> mapError(AppError Function(AppError error) transform) => when(
    success: Result.success,
    failure: (error) => Result.failure(transform(error)),
  );

  /// تطبيق عملية أخرى إذا كانت النتيجة ناجحة
  /// Apply another operation if result is successful
  Result<R> flatMap<R>(Result<R> Function(T data) operation) =>
      when(success: operation, failure: Result.failure);

  /// تنفيذ عملية جانبية بناءً على النتيجة
  /// Execute side effect based on result
  Result<T> tap({
    void Function(T data)? onSuccess,
    void Function(AppError error)? onFailure,
  }) {
    when(
      success: (data) => onSuccess?.call(data),
      failure: (error) => onFailure?.call(error),
    );
    return this;
  }

  /// تحويل إلى `Future<T>` مع رمي الخطأ في حالة الفشل
  /// Convert to `Future<T>` throwing error on failure
  Future<T> toFuture() async => when(
    success: (data) => data,
    failure: (error) => throw Exception(error.toString()),
  );

  /// تحويل إلى AsyncValue للاستخدام مع Riverpod
  /// Convert to AsyncValue for use with Riverpod
  AsyncValue<T> toAsyncValue() => when(
    success: AsyncValue.data,
    failure: (error) => AsyncValue.error(error, StackTrace.current),
  );

  /// تحويل إلى Stream
  /// Convert to Stream
  Stream<T> toStream() => when(success: Stream.value, failure: Stream.error);

  /// تحقق من نوع الخطأ
  /// Check error type
  bool hasErrorType(AppErrorType type) =>
      when(success: (_) => false, failure: (error) => error.type == type);

  /// تحقق من إمكانية إعادة المحاولة
  /// Check if error can be retried
  bool get canRetry =>
      when(success: (_) => false, failure: (error) => error.canRetry);
}

/// امتدادات للعمل مع `Future<Result<T>>`
/// Extensions for working with `Future<Result<T>>`
extension FutureResultExtensions<T> on Future<Result<T>> {
  /// تحويل `Future<Result<T>>` إلى `Future<T>` مع رمي الخطأ
  /// Convert `Future<Result<T>>` to `Future<T>` throwing error
  Future<T> unwrap() async {
    final result = await this;
    return result.when(
      success: (data) => data,
      failure: (error) => throw Exception(error.toString()),
    );
  }

  /// تطبيق تحويل على البيانات
  /// Apply transformation to data
  Future<Result<R>> mapAsync<R>(Future<R> Function(T data) transform) async {
    final result = await this;
    return result.when(
      success: (data) async => Result.success(await transform(data)),
      failure: Result.failure,
    );
  }

  /// تطبيق عملية أخرى إذا كانت النتيجة ناجحة
  /// Apply another operation if result is successful
  Future<Result<R>> flatMapAsync<R>(
    Future<Result<R>> Function(T data) operation,
  ) async {
    final result = await this;
    return result.when(success: operation, failure: Result.failure);
  }
}

/// مساعدات لإنشاء Result
/// Helpers for creating Result instances
class ResultHelpers {
  /// إنشاء نتيجة ناجحة
  /// Create successful result
  static Result<T> success<T>(T data) => Result.success(data);

  /// إنشاء نتيجة فاشلة
  /// Create failure result
  static Result<T> failure<T>(AppError error) => Result.failure(error);

  /// إنشاء نتيجة فاشلة من خطأ عام
  /// Create failure result from general error
  static Result<T> failureFromError<T>(
    dynamic error, {
    StackTrace? stackTrace,
  }) =>
      Result.failure(AppErrorFactory.fromError(error, stackTrace: stackTrace));

  /// تنفيذ عملية مع التعامل مع الأخطاء تلقائياً
  /// Execute operation with automatic error handling
  static Future<Result<T>> tryAsync<T>(
    Future<T> Function() operation, {
    AppError Function(dynamic error, StackTrace stackTrace)? errorMapper,
  }) async {
    try {
      final data = await operation();
      return Result.success(data);
    } catch (error, stackTrace) {
      final appError =
          errorMapper?.call(error, stackTrace) ??
          AppErrorFactory.fromError(error, stackTrace: stackTrace);
      return Result.failure(appError);
    }
  }

  /// تنفيذ عملية متزامنة مع التعامل مع الأخطاء تلقائياً
  /// Execute synchronous operation with automatic error handling
  static Result<T> trySync<T>(
    T Function() operation, {
    AppError Function(dynamic error, StackTrace stackTrace)? errorMapper,
  }) {
    try {
      final data = operation();
      return Result.success(data);
    } catch (error, stackTrace) {
      final appError =
          errorMapper?.call(error, stackTrace) ??
          AppErrorFactory.fromError(error, stackTrace: stackTrace);
      return Result.failure(appError);
    }
  }

  /// دمج عدة نتائج في نتيجة واحدة
  /// Combine multiple results into one
  static Result<List<T>> combine<T>(List<Result<T>> results) {
    final successData = <T>[];

    for (final result in results) {
      final data = result.dataOrNull;
      if (data != null) {
        successData.add(data);
      } else {
        // إرجاع أول خطأ نواجهه
        // Return first error we encounter
        return Result.failure(result.errorOrNull!);
      }
    }

    return Result.success(successData);
  }

  /// تحويل قائمة من `Future<Result<T>>` إلى `Future<Result<List<T>>>`
  /// Convert list of `Future<Result<T>>` to `Future<Result<List<T>>>`
  static Future<Result<List<T>>> combineAsync<T>(
    List<Future<Result<T>>> futures,
  ) async {
    final results = await Future.wait(futures);
    return combine(results);
  }
}
