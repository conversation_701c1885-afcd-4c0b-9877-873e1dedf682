# دليل معالجة الأخطاء المركزي HTTP
# Centralized HTTP Error Handling Guide

## نظرة عامة | Overview

نظام معالجة الأخطاء المركزي مصمم خصيصاً لمعمارية Forever Plan:

```
Flutter (UI Only) → Go API → Supabase (Data Only)
```

## المميزات الرئيسية | Key Features

### ✅ معالجة شاملة للأخطاء
- جميع أنواع أخطاء HTTP (4xx, 5xx)
- أخطاء الشبكة والاتصال
- أخطاء انتهاء المهلة
- أخطاء المصادقة والتخويل

### ✅ رسائل خطأ باللغة العربية
- رسائل صديقة للمستخدم
- سياق واضح للأخطاء
- تفسير مناسب لكل نوع خطأ

### ✅ آلية استرداد ذكية
- إعادة محاولة تلقائية للأخطاء القابلة للاسترداد
- Exponential backoff
- تنظيف جلسة المصادقة عند الحاجة

### ✅ تكامل مع SimpleAuthSystem
- إبطال جلسة المصادقة عند أخطاء 401
- إعادة التوجيه التلقائي لصفحة تسجيل الدخول
- تحديث الرموز التلقائي

---

## الاستخدام الأساسي | Basic Usage

### 1. استخدام EnhancedSimpleApiClient

```dart
class MyProvider extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final apiClient = ref.watch(enhancedSimpleApiClientProvider);
    
    return FutureBuilder(
      future: _loadData(apiClient),
      builder: (context, snapshot) {
        // Handle loading, error, and success states
      },
    );
  }
  
  Future<List<Product>> _loadData(EnhancedSimpleApiClient apiClient) async {
    try {
      final response = await apiClient.getApi<List<dynamic>>(
        '/products',
        context: 'تحميل المنتجات',
        fromJson: (data) => data as List<dynamic>,
      );
      
      return response.data?.map((item) => Product.fromJson(item)).toList() ?? [];
    } catch (error) {
      // الخطأ تم معالجته تلقائياً بواسطة النظام المركزي
      rethrow;
    }
  }
}
```

### 2. استخدام مباشر للمعالج

```dart
class UserService {
  UserService(this._ref);
  final Ref _ref;
  
  Future<User?> getUserProfile() async {
    final errorHandler = _ref.read(centralizedHttpErrorHandlerProvider);
    
    try {
      final dio = _ref.read(simpleApiClientProvider);
      final response = await dio.get('/user/profile');
      return User.fromJson(response.data);
    } catch (error) {
      final appError = await errorHandler.handleHttpError(
        error,
        context: 'جلب ملف المستخدم',
        additionalData: {'userId': 'current'},
      );
      
      // يمكنك إما rethrow أو return null حسب التصميم
      throw appError;
    }
  }
}
```

---

## معالجة الأخطاء في الواجهة | UI Error Handling

### 1. عرض الأخطاء التلقائي

```dart
class ProductListScreen extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final errorHandler = ref.watch(centralizedHttpErrorHandlerProvider);
    
    return Scaffold(
      body: Consumer(
        builder: (context, ref, child) {
          return AsyncValueWidget(
            value: ref.watch(productsProvider),
            data: (products) => ProductList(products: products),
            error: (error, stackTrace) {
              // عرض الخطأ مع إمكانية إعادة المحاولة
              if (error is AppError) {
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  errorHandler.showError(
                    context,
                    error,
                    showRetryButton: errorHandler.isRetryableError(error),
                    onRetry: () => ref.invalidate(productsProvider),
                  );
                });
              }
              return ErrorWidget(error);
            },
          );
        },
      ),
    );
  }
}
```

### 2. عرض رسائل مخصصة

```dart
// رسالة خطأ سريعة
CentralizedHttpErrorHandler.showQuickError(
  context, 
  'حدث خطأ أثناء حفظ البيانات'
);

// رسالة نجاح
CentralizedHttpErrorHandler.showSuccess(
  context, 
  'تم حفظ البيانات بنجاح'
);
```

---

## التعامل مع الحالات الخاصة | Special Case Handling

### 1. أخطاء المصادقة (401)

```dart
// يتم تنظيف الجلسة تلقائياً
// النظام يقوم بـ:
// 1. إبطال SimpleAuthSystem
// 2. تنظيف الرموز المحفوظة
// 3. إعادة التوجيه لصفحة تسجيل الدخول

// في تطبيقك:
Consumer(
  builder: (context, ref, child) {
    ref.listen<AuthData>(simpleAuthSystemProvider, (previous, next) {
      if (next.status == AuthStatus.unauthenticated) {
        Navigator.of(context).pushReplacementNamed('/login');
      }
    });
    
    return YourMainApp();
  },
)
```

### 2. أخطاء الشبكة مع إعادة المحاولة

```dart
class WalletService {
  Future<WalletModel?> getUserWallet() async {
    final apiClient = ref.read(enhancedSimpleApiClientProvider);
    
    try {
      // النظام سيعيد المحاولة تلقائياً للأخطاء القابلة للاسترداد
      final response = await apiClient.getApi<Map<String, dynamic>>(
        '/wallets/user',
        context: 'جلب محفظة المستخدم',
        maxRetries: 3, // إعادة محاولة حتى 3 مرات
        initialDelay: Duration(seconds: 1),
        fromJson: (data) => data as Map<String, dynamic>,
      );
      
      return response.data != null ? WalletModel.fromJson(response.data!) : null;
    } catch (error) {
      // الخطأ تم معالجته ولن يتم إعادة المحاولة أكثر
      _logger.severe('Failed to get user wallet after all retries: $error');
      return null; // إرجاع null بدلاً من crash التطبيق
    }
  }
}
```

### 3. أخطاء التحقق (422)

```dart
class FormValidationHandler {
  static void handleValidationError(AppError error, BuildContext context) {
    if (error.type == AppErrorType.validation) {
      final validationErrors = error.data?['validationErrors'] as Map<String, dynamic>?;
      
      if (validationErrors != null) {
        // عرض أخطاء التحقق في النموذج
        validationErrors.forEach((field, message) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('$field: $message')),
          );
        });
      } else {
        // عرض رسالة عامة
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(error.message)),
        );
      }
    }
  }
}
```

---

## الاستخدام مع SafeApiCaller | Using SafeApiCaller

```dart
class ProductService {
  Future<List<Product>?> getProducts() async {
    final errorHandler = ref.read(centralizedHttpErrorHandlerProvider);
    
    return SafeApiCaller.call<List<Product>>(
      () async {
        final response = await apiClient.get<List<dynamic>>(
          '/products',
          context: 'جلب المنتجات',
        );
        return response?.map((item) => Product.fromJson(item)).toList() ?? [];
      },
      context: 'جلب قائمة المنتجات',
      fallback: [], // قائمة فارغة في حالة الخطأ
      errorHandler: errorHandler,
    );
  }
}
```

---

## نصائح أفضل الممارسات | Best Practices

### 1. استخدم Context واضح

```dart
// ❌ سيء
await apiClient.postApi('/auth/login', data: credentials);

// ✅ جيد  
await apiClient.postApi(
  '/auth/login', 
  data: credentials,
  context: 'تسجيل دخول المستخدم',
);
```

### 2. تعامل مع الأخطاء بذكاء

```dart
// ❌ سيء - crash التطبيق
final products = await getProducts(); // قد يرمي خطأ

// ✅ جيد - graceful handling
try {
  final products = await getProducts();
  updateUI(products);
} catch (error) {
  // عرض UI بديل أو قائمة فارغة
  showEmptyState();
}
```

### 3. استخدم Retry بحكمة

```dart
// للعمليات الحرجة - retry أكثر
await apiClient.postApi(
  '/payment/process',
  data: paymentData,
  context: 'معالجة دفع',
  maxRetries: 5,
  initialDelay: Duration(seconds: 2),
);

// للعمليات غير الحرجة - retry أقل
await apiClient.getApi(
  '/user/preferences',
  context: 'جلب تفضيلات المستخدم',
  maxRetries: 1,
);
```

### 4. مراقبة الأخطاء

```dart
class ErrorMonitor extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    ref.listen<AuthData>(simpleAuthSystemProvider, (previous, next) {
      if (next.status == AuthStatus.error) {
        // تسجيل الخطأ في Analytics
        FirebaseAnalytics.instance.logEvent(
          name: 'auth_error',
          parameters: {'error': next.error ?? 'unknown'},
        );
      }
    });
    
    return YourApp();
  }
}
```

---

## الخلاصة | Summary

نظام معالجة الأخطاء المركزي يوفر:

1. **معالجة شاملة** لجميع أنواع الأخطاء
2. **رسائل واضحة** باللغة العربية
3. **استرداد تلقائي** من الأخطاء القابلة للإصلاح
4. **تكامل سلس** مع معمارية Forever Plan
5. **سهولة استخدام** مع APIs واضحة

استخدم هذا النظام في جميع عمليات API لضمان تجربة مستخدم متسقة وموثوقة! 🚀 