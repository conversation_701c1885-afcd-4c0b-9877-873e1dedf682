// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'unified_error_handler.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$unifiedErrorHandlerHash() =>
    r'7bd3b5589bd0abfbf3868b71dfca917f7c6bb35f';

/// Provider للخدمة الموحدة لمعالجة الأخطاء
/// Provider for unified error handling service
///
/// Copied from [unifiedErrorHandler].
@ProviderFor(unifiedErrorHandler)
final unifiedErrorHandlerProvider =
    AutoDisposeProvider<UnifiedErrorHandler>.internal(
      unifiedErrorHandler,
      name: r'unifiedErrorHandlerProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$unifiedErrorHandlerHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef UnifiedErrorHandlerRef = AutoDisposeProviderRef<UnifiedErrorHandler>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
