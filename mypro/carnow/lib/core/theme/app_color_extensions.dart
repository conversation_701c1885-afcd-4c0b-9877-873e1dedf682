import 'package:flutter/material.dart';
import 'package:carnow/core/theme/app_colors.dart';

/// Convenient extensions for accessing the active [ColorScheme]
/// and adding project-specific semantic colors.
/// Use these instead of hard-coding colors or relying on legacy
/// `AppColors` constants.
extension ContextColors on BuildContext {
  /// Shorthand to the current [ThemeData.colorScheme].
  ColorScheme get colors => Theme.of(this).colorScheme;

  /// Shorthand to the current [ThemeData.textTheme].
  TextTheme get texts => Theme.of(this).textTheme;
}

/// Semantic colors that are not covered by the standard Material 3
/// palette can live here as extensions on [ColorScheme].
extension AppSemanticColors on ColorScheme {
  /// Color that represents an *active* auction.
  /// Chosen to be [primary] with a reduced opacity so that it adapts
  /// automatically to light/dark & dynamic palettes.
  Color get auctionActive => primary;

  /// Color that represents an *ending soon* auction.
  Color get auctionEnding => tertiary;

  /// Color that represents an *ended* auction.
  Color get auctionEnded => outline;

  /// Convenience getter for text that appears on primary surfaces.
  Color get onPrimaryText => onPrimary;
}

extension AppExtraColors on ColorScheme {
  Color get success => AppColors.success;
  Color get onSuccess => AppColors.white;
  Color get warning => AppColors.warning;
  Color get onWarning => AppColors.white;
  Color get metallic => AppColors.metallic;
  Color get premium => AppColors.premium;
  Color get performance => AppColors.performance;
  Color get efficiency => AppColors.efficiency;
  Color get successContainer => AppColors.successContainer;
  Color get warningContainer => AppColors.warningContainer;
  Color get errorContainer => AppColors.errorContainer;
  Color get onSuccessContainer => AppColors.textPrimary;
  Color get onWarningContainer => AppColors.textPrimary;
  Color get onErrorContainer => AppColors.textPrimary;
}
