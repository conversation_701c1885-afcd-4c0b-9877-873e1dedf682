import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import 'carnow_color_system.dart';
import '../providers/shared_preferences_provider.dart';

part 'carnow_theme_provider.freezed.dart';
part 'carnow_theme_provider.g.dart';

/// Theme preferences data model
@freezed
abstract class ThemePreferences with _$ThemePreferences {
  const factory ThemePreferences({
    @Default(ThemeMode.system) ThemeMode themeMode,
    @Default(true) bool useDynamicColor,
    @Default(false) bool useHighContrast,
    Color? customSeedColor,
    @Default(1.0) double textScaleFactor,
    @Default(false) bool useSystemNavigationBar,
  }) = _ThemePreferences;
}

/// Theme preferences notifier with persistence
@riverpod
class ThemePreferencesNotifier extends _$ThemePreferencesNotifier {
  static const String _themeKey = 'carnow_theme_preferences';
  
  @override
  ThemePreferences build() {
    return _loadThemePreferences();
  }

  ThemePreferences _loadThemePreferences() {
    final prefs = ref.read(sharedPreferencesProvider).value;
    if (prefs == null) return const ThemePreferences();

    final themeModeIndex = prefs.getInt('${_themeKey}_mode') ?? 0;
    final useDynamicColor = prefs.getBool('${_themeKey}_dynamic') ?? true;
    final useHighContrast = prefs.getBool('${_themeKey}_contrast') ?? false;
    final customSeedColor = prefs.getInt('${_themeKey}_seed');
    final textScaleFactor = prefs.getDouble('${_themeKey}_text_scale') ?? 1.0;
    final useSystemNavBar = prefs.getBool('${_themeKey}_system_nav') ?? false;

    return ThemePreferences(
      themeMode: ThemeMode.values[themeModeIndex.clamp(0, ThemeMode.values.length - 1)],
      useDynamicColor: useDynamicColor,
      useHighContrast: useHighContrast,
      customSeedColor: customSeedColor != null ? Color(customSeedColor) : null,
      textScaleFactor: textScaleFactor.clamp(0.8, 2.0),
      useSystemNavigationBar: useSystemNavBar,
    );
  }

  Future<void> updateThemeMode(ThemeMode mode) async {
    final prefs = ref.read(sharedPreferencesProvider).value;
    if (prefs == null) return;

    await prefs.setInt('${_themeKey}_mode', mode.index);
    state = state.copyWith(themeMode: mode);
    
    // Update system UI style
    _updateSystemUIStyle();
  }

  Future<void> updateDynamicColor(bool enabled) async {
    final prefs = ref.read(sharedPreferencesProvider).value;
    if (prefs == null) return;

    await prefs.setBool('${_themeKey}_dynamic', enabled);
    state = state.copyWith(useDynamicColor: enabled);
  }

  Future<void> updateHighContrast(bool enabled) async {
    final prefs = ref.read(sharedPreferencesProvider).value;
    if (prefs == null) return;

    await prefs.setBool('${_themeKey}_contrast', enabled);
    state = state.copyWith(useHighContrast: enabled);
  }

  Future<void> updateCustomSeedColor(Color? color) async {
    final prefs = ref.read(sharedPreferencesProvider).value;
    if (prefs == null) return;

    if (color != null) {
      await prefs.setInt('${_themeKey}_seed', color.toARGB32());
    } else {
      await prefs.remove('${_themeKey}_seed');
    }
    
    state = state.copyWith(customSeedColor: color);
  }

  Future<void> updateTextScaleFactor(double factor) async {
    final prefs = ref.read(sharedPreferencesProvider).value;
    if (prefs == null) return;

    final clampedFactor = factor.clamp(0.8, 2.0);
    await prefs.setDouble('${_themeKey}_text_scale', clampedFactor);
    state = state.copyWith(textScaleFactor: clampedFactor);
  }

  Future<void> updateSystemNavigationBar(bool enabled) async {
    final prefs = ref.read(sharedPreferencesProvider).value;
    if (prefs == null) return;

    await prefs.setBool('${_themeKey}_system_nav', enabled);
    state = state.copyWith(useSystemNavigationBar: enabled);
    
    _updateSystemUIStyle();
  }

  void _updateSystemUIStyle() {
    final brightness = _getCurrentBrightness();
    final overlayStyle = SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: brightness == Brightness.dark 
          ? Brightness.light 
          : Brightness.dark,
      systemNavigationBarColor: state.useSystemNavigationBar 
          ? Colors.transparent 
          : null,
      systemNavigationBarIconBrightness: brightness == Brightness.dark 
          ? Brightness.light 
          : Brightness.dark,
    );
    
    SystemChrome.setSystemUIOverlayStyle(overlayStyle);
  }

  Brightness _getCurrentBrightness() {
    switch (state.themeMode) {
      case ThemeMode.dark:
        return Brightness.dark;
      case ThemeMode.light:
        return Brightness.light;
      case ThemeMode.system:
        return WidgetsBinding.instance.platformDispatcher.platformBrightness;
    }
  }

  /// Reset all theme preferences to defaults
  Future<void> resetToDefaults() async {
    final prefs = ref.read(sharedPreferencesProvider).value;
    if (prefs == null) return;

    // Remove all theme-related keys
    final keys = [
      '${_themeKey}_mode',
      '${_themeKey}_dynamic',
      '${_themeKey}_contrast',
      '${_themeKey}_seed',
      '${_themeKey}_text_scale',
      '${_themeKey}_system_nav',
    ];

    for (final key in keys) {
      await prefs.remove(key);
    }

    state = const ThemePreferences();
    _updateSystemUIStyle();
  }
}

/// Main theme provider that generates Material 3 themes
@riverpod
class CarNowThemeNotifier extends _$CarNowThemeNotifier {
  @override
  Future<CarNowTheme> build() async {
    final preferences = ref.watch(themePreferencesNotifierProvider);
    return await _buildTheme(preferences);
  }

  Future<CarNowTheme> _buildTheme(ThemePreferences preferences) async {
    // Generate color schemes
    final lightColorScheme = await _generateColorScheme(
      brightness: Brightness.light,
      preferences: preferences,
    );
    
    final darkColorScheme = await _generateColorScheme(
      brightness: Brightness.dark,
      preferences: preferences,
    );

    // Build theme data
    final lightTheme = _buildThemeData(
      colorScheme: lightColorScheme,
      preferences: preferences,
    );
    
    final darkTheme = _buildThemeData(
      colorScheme: darkColorScheme,
      preferences: preferences,
    );

    return CarNowTheme(
      light: lightTheme,
      dark: darkTheme,
      mode: preferences.themeMode,
    );
  }

  Future<ColorScheme> _generateColorScheme({
    required Brightness brightness,
    required ThemePreferences preferences,
  }) async {
    final baseScheme = await CarNowColorSystem.generateColorScheme(
      brightness: brightness,
      useDynamicColor: preferences.useDynamicColor,
      customSeedColor: preferences.customSeedColor,
    );

    // Apply high contrast if enabled
    if (preferences.useHighContrast) {
      return _applyHighContrast(baseScheme);
    }

    return baseScheme;
  }

  ColorScheme _applyHighContrast(ColorScheme scheme) {
    if (scheme.brightness == Brightness.dark) {
      return scheme.copyWith(
        primary: Colors.white,
        onPrimary: Colors.black,
        secondary: Colors.white,
        onSecondary: Colors.black,
        surface: Colors.black,
        onSurface: Colors.white,
      );
    } else {
      return scheme.copyWith(
        primary: Colors.black,
        onPrimary: Colors.white,
        secondary: Colors.black,
        onSecondary: Colors.white,
        surface: Colors.white,
        onSurface: Colors.black,
      );
    }
  }

  ThemeData _buildThemeData({
    required ColorScheme colorScheme,
    required ThemePreferences preferences,
  }) {
    return ThemeData(
      useMaterial3: true,
      colorScheme: colorScheme,
      fontFamily: 'Cairo',
      textTheme: CarNowThemeBuilder.buildCarNowTextTheme()
          .apply(fontSizeFactor: preferences.textScaleFactor),
      appBarTheme: CarNowThemeBuilder.buildAppBarTheme(colorScheme),
      cardTheme: CarNowThemeBuilder.buildCardTheme(colorScheme),
      elevatedButtonTheme: CarNowThemeBuilder.buildElevatedButtonTheme(colorScheme),
      outlinedButtonTheme: CarNowThemeBuilder.buildOutlinedButtonTheme(colorScheme),
      filledButtonTheme: CarNowThemeBuilder.buildFilledButtonTheme(colorScheme),
      textButtonTheme: CarNowThemeBuilder.buildTextButtonTheme(colorScheme),
      inputDecorationTheme: CarNowThemeBuilder.buildInputDecorationTheme(colorScheme),
      bottomNavigationBarTheme: CarNowThemeBuilder.buildBottomNavTheme(colorScheme),
      navigationBarTheme: CarNowThemeBuilder.buildNavigationBarTheme(colorScheme),
      floatingActionButtonTheme: CarNowThemeBuilder.buildFABTheme(colorScheme),
      chipTheme: CarNowThemeBuilder.buildChipTheme(colorScheme),
      dividerTheme: CarNowThemeBuilder.buildDividerTheme(colorScheme),
      switchTheme: CarNowThemeBuilder.buildSwitchTheme(colorScheme),
      checkboxTheme: CarNowThemeBuilder.buildCheckboxTheme(colorScheme),
      radioTheme: CarNowThemeBuilder.buildRadioTheme(colorScheme),
      sliderTheme: CarNowThemeBuilder.buildSliderTheme(colorScheme),
      tabBarTheme: CarNowThemeBuilder.buildTabBarTheme(colorScheme),
      bottomSheetTheme: CarNowThemeBuilder.buildBottomSheetTheme(colorScheme),
      dialogTheme: CarNowThemeBuilder.buildDialogTheme(colorScheme),
      snackBarTheme: CarNowThemeBuilder.buildSnackBarTheme(colorScheme),
      listTileTheme: CarNowThemeBuilder.buildListTileTheme(colorScheme),
    );
  }
}

/// Theme data container
@freezed
abstract class CarNowTheme with _$CarNowTheme {
  const factory CarNowTheme({
    required ThemeData light,
    required ThemeData dark,
    required ThemeMode mode,
  }) = _CarNowTheme;
}

/// Complete Material 3 component theme builder
class CarNowThemeBuilder {
  static TextTheme buildCarNowTextTheme() {
    return const TextTheme(
      // Display styles (for large text)
      displayLarge: TextStyle(
        fontFamily: 'Cairo',
        fontSize: 57,
        fontWeight: FontWeight.w700,
        letterSpacing: -0.25,
        height: 1.12,
      ),
      displayMedium: TextStyle(
        fontFamily: 'Cairo',
        fontSize: 45,
        fontWeight: FontWeight.w600,
        letterSpacing: 0,
        height: 1.16,
      ),
      displaySmall: TextStyle(
        fontFamily: 'Cairo',
        fontSize: 36,
        fontWeight: FontWeight.w500,
        letterSpacing: 0,
        height: 1.22,
      ),
      
      // Headline styles (for sections)
      headlineLarge: TextStyle(
        fontFamily: 'Cairo',
        fontSize: 32,
        fontWeight: FontWeight.w600,
        letterSpacing: 0,
        height: 1.25,
      ),
      headlineMedium: TextStyle(
        fontFamily: 'Cairo',
        fontSize: 28,
        fontWeight: FontWeight.w500,
        letterSpacing: 0,
        height: 1.29,
      ),
      headlineSmall: TextStyle(
        fontFamily: 'Cairo',
        fontSize: 24,
        fontWeight: FontWeight.w500,
        letterSpacing: 0,
        height: 1.33,
      ),
      
      // Title styles (for cards, lists)
      titleLarge: TextStyle(
        fontFamily: 'Cairo',
        fontSize: 22,
        fontWeight: FontWeight.w600,
        letterSpacing: 0,
        height: 1.27,
      ),
      titleMedium: TextStyle(
        fontFamily: 'Cairo',
        fontSize: 16,
        fontWeight: FontWeight.w500,
        letterSpacing: 0.15,
        height: 1.50,
      ),
      titleSmall: TextStyle(
        fontFamily: 'Cairo',
        fontSize: 14,
        fontWeight: FontWeight.w500,
        letterSpacing: 0.1,
        height: 1.43,
      ),
      
      // Body styles (for content)
      bodyLarge: TextStyle(
        fontFamily: 'Cairo',
        fontSize: 16,
        fontWeight: FontWeight.w400,
        letterSpacing: 0.5,
        height: 1.50,
      ),
      bodyMedium: TextStyle(
        fontFamily: 'Cairo',
        fontSize: 14,
        fontWeight: FontWeight.w400,
        letterSpacing: 0.25,
        height: 1.43,
      ),
      bodySmall: TextStyle(
        fontFamily: 'Cairo',
        fontSize: 12,
        fontWeight: FontWeight.w400,
        letterSpacing: 0.4,
        height: 1.33,
      ),
      
      // Label styles (for buttons, tabs)
      labelLarge: TextStyle(
        fontFamily: 'Cairo',
        fontSize: 14,
        fontWeight: FontWeight.w500,
        letterSpacing: 0.1,
        height: 1.43,
      ),
      labelMedium: TextStyle(
        fontFamily: 'Cairo',
        fontSize: 12,
        fontWeight: FontWeight.w500,
        letterSpacing: 0.5,
        height: 1.33,
      ),
      labelSmall: TextStyle(
        fontFamily: 'Cairo',
        fontSize: 11,
        fontWeight: FontWeight.w500,
        letterSpacing: 0.5,
        height: 1.45,
      ),
    );
  }

  static AppBarTheme buildAppBarTheme(ColorScheme colorScheme) {
    return AppBarTheme(
      backgroundColor: colorScheme.surface,
      foregroundColor: colorScheme.onSurface,
      elevation: 0,
      scrolledUnderElevation: 3,
      centerTitle: true,
      titleTextStyle: TextStyle(
        fontFamily: 'Cairo',
        fontSize: 22,
        fontWeight: FontWeight.w600,
        color: colorScheme.onSurface,
      ),
      iconTheme: IconThemeData(color: colorScheme.onSurface),
      actionsIconTheme: IconThemeData(color: colorScheme.onSurface),
    );
  }

  static CardThemeData buildCardTheme(ColorScheme colorScheme) {
    return CardThemeData(
      color: colorScheme.surfaceContainerLow,
      shadowColor: colorScheme.shadow,
      elevation: 1,
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
    );
  }

  static ElevatedButtonThemeData buildElevatedButtonTheme(ColorScheme colorScheme) {
    return ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: colorScheme.primary,
        foregroundColor: colorScheme.onPrimary,
        disabledBackgroundColor: colorScheme.onSurface.withAlpha((255 * 0.12).round()),
        disabledForegroundColor: colorScheme.onSurface.withAlpha((255 * 0.38).round()),
        elevation: 1,
        shadowColor: colorScheme.shadow,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        minimumSize: const Size(64, 40),
        textStyle: const TextStyle(
          fontFamily: 'Cairo',
          fontSize: 14,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  static OutlinedButtonThemeData buildOutlinedButtonTheme(ColorScheme colorScheme) {
    return OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        foregroundColor: colorScheme.primary,
        disabledForegroundColor: colorScheme.onSurface.withAlpha((255 * 0.38).round()),
        side: BorderSide(color: colorScheme.outline),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        minimumSize: const Size(64, 40),
        textStyle: const TextStyle(
          fontFamily: 'Cairo',
          fontSize: 14,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  static FilledButtonThemeData buildFilledButtonTheme(ColorScheme colorScheme) {
    return FilledButtonThemeData(
      style: FilledButton.styleFrom(
        backgroundColor: colorScheme.primary,
        foregroundColor: colorScheme.onPrimary,
        disabledBackgroundColor: colorScheme.onSurface.withAlpha((255 * 0.12).round()),
        disabledForegroundColor: colorScheme.onSurface.withAlpha((255 * 0.38).round()),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        minimumSize: const Size(64, 40),
        textStyle: const TextStyle(
          fontFamily: 'Cairo',
          fontSize: 14,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  static TextButtonThemeData buildTextButtonTheme(ColorScheme colorScheme) {
    return TextButtonThemeData(
      style: TextButton.styleFrom(
        foregroundColor: colorScheme.primary,
        disabledForegroundColor: colorScheme.onSurface.withAlpha((255 * 0.38).round()),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        minimumSize: const Size(48, 40),
        textStyle: const TextStyle(
          fontFamily: 'Cairo',
          fontSize: 14,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  static InputDecorationTheme buildInputDecorationTheme(ColorScheme colorScheme) {
    return InputDecorationTheme(
      filled: true,
      fillColor: colorScheme.surfaceContainerHighest,
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(color: colorScheme.outline),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(color: colorScheme.outline),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(color: colorScheme.primary, width: 2),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(color: colorScheme.error),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(color: colorScheme.error, width: 2),
      ),
      labelStyle: TextStyle(
        fontFamily: 'Cairo',
        color: colorScheme.onSurfaceVariant,
      ),
      hintStyle: TextStyle(
        fontFamily: 'Cairo',
        color: colorScheme.onSurfaceVariant,
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
    );
  }

  static BottomNavigationBarThemeData buildBottomNavTheme(ColorScheme colorScheme) {
    return BottomNavigationBarThemeData(
      backgroundColor: colorScheme.surface,
      selectedItemColor: colorScheme.primary,
      unselectedItemColor: colorScheme.onSurfaceVariant,
      type: BottomNavigationBarType.fixed,
      elevation: 3,
      selectedLabelStyle: const TextStyle(
        fontFamily: 'Cairo',
        fontSize: 12,
        fontWeight: FontWeight.w500,
      ),
      unselectedLabelStyle: const TextStyle(
        fontFamily: 'Cairo',
        fontSize: 12,
        fontWeight: FontWeight.w400,
      ),
    );
  }

  static NavigationBarThemeData buildNavigationBarTheme(ColorScheme colorScheme) {
    return NavigationBarThemeData(
      backgroundColor: colorScheme.surface,
      indicatorColor: colorScheme.secondaryContainer,
      iconTheme: WidgetStateProperty.resolveWith((states) {
        if (states.contains(WidgetState.selected)) {
          return IconThemeData(color: colorScheme.onSecondaryContainer);
        }
        return IconThemeData(color: colorScheme.onSurfaceVariant);
      }),
      labelTextStyle: WidgetStateProperty.resolveWith((states) {
        if (states.contains(WidgetState.selected)) {
          return TextStyle(
            fontFamily: 'Cairo',
            fontSize: 12,
            fontWeight: FontWeight.w500,
            color: colorScheme.onSurface,
          );
        }
        return TextStyle(
          fontFamily: 'Cairo',
          fontSize: 12,
          fontWeight: FontWeight.w400,
          color: colorScheme.onSurfaceVariant,
        );
      }),
    );
  }

  static FloatingActionButtonThemeData buildFABTheme(ColorScheme colorScheme) {
    return FloatingActionButtonThemeData(
      backgroundColor: colorScheme.primaryContainer,
      foregroundColor: colorScheme.onPrimaryContainer,
      elevation: 6,
      focusElevation: 8,
      hoverElevation: 8,
      highlightElevation: 12,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
    );
  }

  static ChipThemeData buildChipTheme(ColorScheme colorScheme) {
    return ChipThemeData(
      backgroundColor: colorScheme.surfaceContainerLow,
      labelStyle: TextStyle(
        fontFamily: 'Cairo',
        color: colorScheme.onSurface,
      ),
      side: BorderSide(color: colorScheme.outline),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
    );
  }

  static DividerThemeData buildDividerTheme(ColorScheme colorScheme) {
    return DividerThemeData(
      color: colorScheme.outlineVariant,
      thickness: 1,
      space: 1,
    );
  }

  static SwitchThemeData buildSwitchTheme(ColorScheme colorScheme) {
    return SwitchThemeData(
      thumbColor: WidgetStateProperty.resolveWith((states) {
        if (states.contains(WidgetState.selected)) {
          return colorScheme.onPrimary;
        }
        return colorScheme.outline;
      }),
      trackColor: WidgetStateProperty.resolveWith((states) {
        if (states.contains(WidgetState.selected)) {
          return colorScheme.primary;
        }
        return colorScheme.surfaceContainerHighest;
      }),
    );
  }

  static CheckboxThemeData buildCheckboxTheme(ColorScheme colorScheme) {
    return CheckboxThemeData(
      fillColor: WidgetStateProperty.resolveWith((states) {
        if (states.contains(WidgetState.selected)) {
          return colorScheme.primary;
        }
        return Colors.transparent;
      }),
      checkColor: WidgetStateProperty.all(colorScheme.onPrimary),
      side: BorderSide(color: colorScheme.outline),
    );
  }

  static RadioThemeData buildRadioTheme(ColorScheme colorScheme) {
    return RadioThemeData(
      fillColor: WidgetStateProperty.resolveWith((states) {
        if (states.contains(WidgetState.selected)) {
          return colorScheme.primary;
        }
        return colorScheme.outline;
      }),
    );
  }

  static SliderThemeData buildSliderTheme(ColorScheme colorScheme) {
    return SliderThemeData(
      activeTrackColor: colorScheme.primary,
      inactiveTrackColor: colorScheme.surfaceContainerHighest,
      thumbColor: colorScheme.primary,
      overlayColor: colorScheme.primary.withAlpha((255 * 0.12).round()),
      valueIndicatorColor: colorScheme.primary,
      valueIndicatorTextStyle: TextStyle(
        fontFamily: 'Cairo',
        color: colorScheme.onPrimary,
      ),
    );
  }

  static TabBarThemeData buildTabBarTheme(ColorScheme colorScheme) {
    return TabBarThemeData(
      indicatorColor: colorScheme.primary,
      labelColor: colorScheme.primary,
      unselectedLabelColor: colorScheme.onSurfaceVariant,
      labelStyle: const TextStyle(
        fontFamily: 'Cairo',
        fontSize: 14,
        fontWeight: FontWeight.w500,
      ),
      unselectedLabelStyle: const TextStyle(
        fontFamily: 'Cairo',
        fontSize: 14,
        fontWeight: FontWeight.w400,
      ),
    );
  }

  static BottomSheetThemeData buildBottomSheetTheme(ColorScheme colorScheme) {
    return BottomSheetThemeData(
      backgroundColor: colorScheme.surface,
      modalBackgroundColor: colorScheme.surface,
      elevation: 8,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
    );
  }

  static DialogThemeData buildDialogTheme(ColorScheme colorScheme) {
    return DialogThemeData(
      backgroundColor: colorScheme.surface,
      elevation: 6,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      titleTextStyle: TextStyle(
        fontFamily: 'Cairo',
        fontSize: 24,
        fontWeight: FontWeight.w500,
        color: colorScheme.onSurface,
      ),
      contentTextStyle: TextStyle(
        fontFamily: 'Cairo',
        fontSize: 16,
        fontWeight: FontWeight.w400,
        color: colorScheme.onSurfaceVariant,
      ),
    );
  }

  static SnackBarThemeData buildSnackBarTheme(ColorScheme colorScheme) {
    return SnackBarThemeData(
      backgroundColor: colorScheme.inverseSurface,
      contentTextStyle: TextStyle(
        fontFamily: 'Cairo',
        color: colorScheme.onInverseSurface,
      ),
      actionTextColor: colorScheme.inversePrimary,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      behavior: SnackBarBehavior.floating,
    );
  }

  static ListTileThemeData buildListTileTheme(ColorScheme colorScheme) {
    return ListTileThemeData(
      iconColor: colorScheme.onSurfaceVariant,
      textColor: colorScheme.onSurface,
      titleTextStyle: TextStyle(
        fontFamily: 'Cairo',
        fontSize: 16,
        fontWeight: FontWeight.w500,
        color: colorScheme.onSurface,
      ),
      subtitleTextStyle: TextStyle(
        fontFamily: 'Cairo',
        fontSize: 14,
        fontWeight: FontWeight.w400,
        color: colorScheme.onSurfaceVariant,
      ),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
    );
  }
}