import 'package:flutter/material.dart';

/// Convenient accessors for [ColorScheme] that map CarNow brand semantics
/// and legacy color names to the Material 3 color model. This helps unify all
/// color usages behind the single source-of-truth `Theme.of(context).colorScheme`.
///
/// Example:
/// ```dart
/// Container(color: context.colors.primary);
/// Text('Total', style: TextStyle(color: context.colors.onSurface))
/// ```
extension ColorSchemeCarNow on ColorScheme {
  // Brand aliases
  Color get brandPrimary => primary;
  Color get brandSecondary => secondary;
  Color get brandTertiary => tertiary;

  // Status aliases
  Color get success => tertiary; // using green tone
  Color get warning => secondary; // using yellow tone
  Color get errorColor => error;

  // Containers (Material 3 convention)
  Color get successContainer => tertiaryContainer;
  Color get warningContainer => secondaryContainer;
  Color get errorContainer => errorContainer;
}

/// Handy extension on [BuildContext] to obtain the current [ColorScheme]
/// quickly using `context.colors`.
extension ColorSchemeContext on BuildContext {
  ColorScheme get colors => Theme.of(this).colorScheme;
} 