import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:dynamic_color/dynamic_color.dart';

/// CarNow Unified Color System with Material 3 Dynamic Color Support
/// 
/// This system provides a comprehensive color palette that:
/// - Adapts to user's system preferences (Dynamic Color)
/// - Maintains brand consistency across all platforms
/// - Ensures accessibility compliance (WCAG 2.1 AA)
/// - Supports automotive industry specific colors
class CarNowColorSystem {
  // Primary Brand Colors (CarNow Identity)
  static const Color primaryBrand = Color(0xFF1B5E20); // Forest Green
  static const Color secondaryBrand = Color(0xFF2196F3); // Sky Blue
  static const Color accentBrand = Color(0xFFFF9800); // Automotive Orange
  
  // Dynamic Color Seeds (Material 3)
  static const Color dynamicSeed = Color(0xFF1B5E20);
  
  // Semantic Colors (Business Logic)
  static const Color success = Color(0xFF4CAF50);
  static const Color warning = Color(0xFFFF9800);
  static const Color error = Color(0xFFE53935);
  static const Color info = Color(0xFF2196F3);
  
  // Automotive Specific Colors
  static const Color carInterior = Color(0xFF5D4037);
  static const Color carExterior = Color(0xFF37474F);
  static const Color carEngine = Color(0xFF424242);
  static const Color carParts = Color(0xFF6A1B9A);

  /// Generate Material 3 ColorScheme from system dynamic colors or brand colors
  static Future<ColorScheme> generateColorScheme({
    required Brightness brightness,
    bool useDynamicColor = true,
    Color? customSeedColor,
  }) async {
    Color seedColor = customSeedColor ?? dynamicSeed;

    if (useDynamicColor) {
      final dynamicColors = await DynamicColorPlugin.getCorePalette();
      if (dynamicColors != null) {
        return ColorScheme.fromSeed(
          seedColor: Color(dynamicColors.primary.get(40)),
          brightness: brightness,
        );
      }
    }

    return ColorScheme.fromSeed(
      seedColor: seedColor,
      brightness: brightness,
    );
  }

  /// Get adaptive color scheme for different screen sizes
  static ColorScheme getAdaptiveColorScheme(
    BuildContext context, {
    ColorScheme? baseScheme,
  }) {
    final brightness = Theme.of(context).brightness;
    final isTablet = MediaQuery.of(context).size.width > 600;
    final scheme = baseScheme ?? Theme.of(context).colorScheme;
    
    if (brightness == Brightness.dark) {
      return isTablet 
        ? _buildTabletDarkColorScheme(scheme) 
        : _buildMobileDarkColorScheme(scheme);
    } else {
      return isTablet 
        ? _buildTabletLightColorScheme(scheme) 
        : _buildMobileLightColorScheme(scheme);
    }
  }

  static ColorScheme _buildMobileLightColorScheme(ColorScheme base) {
    return base.copyWith(
      // Enhanced contrast for mobile screens
      primary: const Color(0xFF1B5E20),
      onPrimary: const Color(0xFFFFFFFF),
      secondary: const Color(0xFF2196F3),
      onSecondary: const Color(0xFFFFFFFF),
      tertiary: const Color(0xFFFF9800),
      onTertiary: const Color(0xFFFFFFFF),
    );
  }

  static ColorScheme _buildTabletLightColorScheme(ColorScheme base) {
    return base.copyWith(
      // Subtle colors for larger screens
      primary: const Color(0xFF2E7D32),
      onPrimary: const Color(0xFFFFFFFF),
      secondary: const Color(0xFF1976D2),
      onSecondary: const Color(0xFFFFFFFF),
      tertiary: const Color(0xFFF57C00),
      onTertiary: const Color(0xFFFFFFFF),
    );
  }

  static ColorScheme _buildMobileDarkColorScheme(ColorScheme base) {
    return base.copyWith(
      primary: const Color(0xFF4CAF50),
      onPrimary: const Color(0xFF000000),
      secondary: const Color(0xFF64B5F6),
      onSecondary: const Color(0xFF000000),
      tertiary: const Color(0xFFFFB74D),
      onTertiary: const Color(0xFF000000),
    );
  }

  static ColorScheme _buildTabletDarkColorScheme(ColorScheme base) {
    return base.copyWith(
      primary: const Color(0xFF66BB6A),
      onPrimary: const Color(0xFF000000),
      secondary: const Color(0xFF42A5F5),
      onSecondary: const Color(0xFF000000),
      tertiary: const Color(0xFFFF8A65),
      onTertiary: const Color(0xFF000000),
    );
  }
}

/// Expressive color collections for different contexts
class CarNowExpressiveColors {
  // Automotive Category Colors
  static final Map<String, Color> categories = {
    'engine': const Color(0xFF1B5E20),      // Forest Green
    'exterior': const Color(0xFF37474F),     // Blue Grey
    'interior': const Color(0xFF5D4037),     // Brown
    'electrical': const Color(0xFF2196F3),   // Blue
    'brakes': const Color(0xFFE53935),       // Red
    'transmission': const Color(0xFF6A1B9A), // Purple
    'suspension': const Color(0xFFFF9800),   // Orange
    'tires': const Color(0xFF424242),        // Grey
    'accessories': const Color(0xFF795548),  // Brown
    'tools': const Color(0xFF607D8B),        // Blue Grey
  };

  // Status Colors (Semantic)
  static final Map<String, Color> status = {
    'available': const Color(0xFF4CAF50),    // Green
    'pending': const Color(0xFFFF9800),      // Orange
    'sold': const Color(0xFF9E9E9E),         // Grey
    'reserved': const Color(0xFF2196F3),     // Blue
    'maintenance': const Color(0xFFE53935),  // Red
    'shipping': const Color(0xFF673AB7),     // Deep Purple
    'delivered': const Color(0xFF4CAF50),    // Green
    'cancelled': const Color(0xFF757575),    // Grey
  };

  // Price Range Colors (Visual Hierarchy)
  static final Map<String, Color> priceRanges = {
    'budget': const Color(0xFF4CAF50),       // Green
    'mid': const Color(0xFF2196F3),          // Blue
    'premium': const Color(0xFF6A1B9A),      // Purple
    'luxury': const Color(0xFFE91E63),       // Pink
  };

  // Condition Colors
  static final Map<String, Color> conditions = {
    'new': const Color(0xFF4CAF50),          // Green
    'excellent': const Color(0xFF8BC34A),    // Light Green
    'good': const Color(0xFF2196F3),         // Blue
    'fair': const Color(0xFFFF9800),         // Orange
    'poor': const Color(0xFFE53935),         // Red
    'unknown': const Color(0xFF9E9E9E),      // Grey
  };

  // Performance Rating Colors
  static final Map<String, Color> performance = {
    'sport': const Color(0xFFE53935),        // Red
    'luxury': const Color(0xFF6A1B9A),       // Purple
    'eco': const Color(0xFF4CAF50),          // Green
    'comfort': const Color(0xFF2196F3),      // Blue
    'utility': const Color(0xFF795548),      // Brown
    'compact': const Color(0xFF607D8B),      // Blue Grey
  };

  /// Get color by category with fallback
  static Color getCategoryColor(String category, {Color? fallback}) {
    return categories[category.toLowerCase()] ?? 
           fallback ?? 
           const Color(0xFF9E9E9E);
  }

  /// Get status color with fallback
  static Color getStatusColor(String status, {Color? fallback}) {
    return CarNowExpressiveColors.status[status.toLowerCase()] ?? 
           fallback ?? 
           const Color(0xFF9E9E9E);
  }

  /// Get condition color with semantic meaning
  static Color getConditionColor(String condition, {Color? fallback}) {
    return conditions[condition.toLowerCase()] ?? 
           fallback ?? 
           const Color(0xFF9E9E9E);
  }

  /// Get price range color
  static Color getPriceRangeColor(String range, {Color? fallback}) {
    return priceRanges[range.toLowerCase()] ?? 
           fallback ?? 
           const Color(0xFF9E9E9E);
  }
}

/// Automotive industry specific color extensions
class AutomotiveColorExtensions {
  // Car manufacturer inspired colors
  static final Map<String, Color> manufacturerColors = {
    'luxury_gold': const Color(0xFFD4AF37),
    'racing_red': const Color(0xFFDC143C),
    'electric_blue': const Color(0xFF0066CC),
    'hybrid_green': const Color(0xFF32CD32),
    'classic_black': const Color(0xFF1C1C1C),
    'pearl_white': const Color(0xFFF8F8FF),
    'chrome_silver': const Color(0xFFC0C0C0),
    'carbon_grey': const Color(0xFF36454F),
    'motorsport_orange': const Color(0xFFFF8C00),
    'premium_navy': const Color(0xFF191970),
  };

  // Performance category colors
  static final Map<String, Color> performanceColors = {
    'sport': const Color(0xFFE53935),
    'luxury': const Color(0xFF6A1B9A),
    'eco': const Color(0xFF4CAF50),
    'comfort': const Color(0xFF2196F3),
    'utility': const Color(0xFF795548),
    'racing': const Color(0xFFD32F2F),
    'electric': const Color(0xFF00E676),
    'hybrid': const Color(0xFF8BC34A),
  };

  // Price tier gradient colors
  static List<Color> getPriceTierGradient(String tier) {
    switch (tier.toLowerCase()) {
      case 'budget':
        return [const Color(0xFF4CAF50), const Color(0xFF8BC34A)];
      case 'mid':
        return [const Color(0xFF2196F3), const Color(0xFF03A9F4)];
      case 'premium':
        return [const Color(0xFF6A1B9A), const Color(0xFF9C27B0)];
      case 'luxury':
        return [const Color(0xFFD4AF37), const Color(0xFFFFD700)];
      default:
        return [const Color(0xFF9E9E9E), const Color(0xFFBDBDBD)];
    }
  }

  /// Get manufacturer inspired color
  static Color getManufacturerColor(String manufacturer, {Color? fallback}) {
    return manufacturerColors[manufacturer.toLowerCase()] ?? 
           fallback ?? 
           const Color(0xFF9E9E9E);
  }

  /// Get performance category color
  static Color getPerformanceColor(String performance, {Color? fallback}) {
    return performanceColors[performance.toLowerCase()] ?? 
           fallback ?? 
           const Color(0xFF9E9E9E);
  }
}

/// Accessibility-first color system
class AccessibilityColorSystem {
  static const double _minContrastRatio = 4.5; // WCAG AA standard
  static const double _enhancedContrastRatio = 7.0; // WCAG AAA standard

  /// Ensure color meets contrast requirements
  static Color ensureContrast(
    Color foreground, 
    Color background, {
    double minContrast = _minContrastRatio,
  }) {
    final currentContrast = calculateContrast(foreground, background);
    
    if (currentContrast >= minContrast) {
      return foreground;
    }
    
    // Adjust color to meet contrast requirements
    return _adjustColorForContrast(foreground, background, minContrast);
  }

  /// Calculate contrast ratio between two colors
  static double calculateContrast(Color color1, Color color2) {
    final luminance1 = color1.computeLuminance();
    final luminance2 = color2.computeLuminance();
    
    final lighter = math.max(luminance1, luminance2);
    final darker = math.min(luminance1, luminance2);
    
    return (lighter + 0.05) / (darker + 0.05);
  }

  static Color _adjustColorForContrast(
    Color original, 
    Color background, 
    double targetContrast,
  ) {
    final hsl = HSLColor.fromColor(original);
    
    // Adjust lightness to meet contrast requirements
    double adjustedLightness = hsl.lightness;
    final isBackgroundLight = background.computeLuminance() > 0.5;
    
    // Iteratively adjust lightness
    for (int i = 0; i < 20; i++) {
      final testColor = hsl.withLightness(adjustedLightness).toColor();
      final contrast = calculateContrast(testColor, background);
      
      if (contrast >= targetContrast) {
        return testColor;
      }
      
      if (isBackgroundLight) {
        // Make foreground darker
        adjustedLightness = math.max(0.0, adjustedLightness - 0.05);
      } else {
        // Make foreground lighter
        adjustedLightness = math.min(1.0, adjustedLightness + 0.05);
      }
    }
    
    return hsl.withLightness(adjustedLightness).toColor();
  }

  // Pre-validated accessible color pairs
  static final Map<String, Map<String, Color>> accessiblePairs = {
    'primary_on_surface': {
      'foreground': Color(0xFF1B5E20),
      'background': Color(0xFFFFFFFF),
    },
    'surface_on_primary': {
      'foreground': Color(0xFFFFFFFF),
      'background': Color(0xFF1B5E20),
    },
    'error_on_surface': {
      'foreground': Color(0xFFD32F2F),
      'background': Color(0xFFFFFFFF),
    },
    'success_on_surface': {
      'foreground': Color(0xFF2E7D32),
      'background': Color(0xFFFFFFFF),
    },
    'warning_on_surface': {
      'foreground': Color(0xFFE65100),
      'background': Color(0xFFFFFFFF),
    },
    'info_on_surface': {
      'foreground': Color(0xFF1565C0),
      'background': Color(0xFFFFFFFF),
    },
  };

  /// Get accessible color pair
  static Map<String, Color>? getAccessiblePair(String pairName) {
    return accessiblePairs[pairName];
  }

  /// Test if color combination is accessible
  static bool isAccessible(
    Color foreground, 
    Color background, {
    double minContrast = _minContrastRatio,
  }) {
    return calculateContrast(foreground, background) >= minContrast;
  }
} 