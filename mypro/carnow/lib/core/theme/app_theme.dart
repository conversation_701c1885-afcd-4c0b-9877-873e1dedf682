import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:carnow/core/theme/app_colors.dart';

/// Centralized theme and styling for the CarNow application.
/// This class provides a unified design system using Material 3 Expressive.
///
/// Features:
/// - Material 3 Expressive Design System
/// - Dynamic Color support (Material You)
/// - Complete Surface Container System
/// - Enhanced Typography with Arabic support
/// - Accessibility-first approach
class AppTheme {
  AppTheme._();

  // Color definitions are now centralized in `app_colors.dart`.
  // This file now consumes them to build the ThemeData.

  // --- Sizing & Spacing ---
  static const double spacingXXS = 2;
  static const double spacingXS = 4;
  static const double spacingS = 8;
  static const double spacingM = 16;
  static const double spacingL = 24;
  static const double spacingXL = 32;
  static const double spacingXXL = 48;

  // --- Border Radius ---
  static const double radiusS = 4;
  static const double radiusM = 8;
  static const double radiusL = 16;
  static const double radiusXL = 24;
  static const double radiusFull = 1000;

  // --- Elevation ---
  static const double elevationNone = 0;
  static const double elevationLow = 1;
  static const double elevationMedium = 4;
  static const double elevationHigh = 8;

  // --- Font Family ---
  static const String fontFamily = 'Cairo';

  // --- Additional Constants ---
  static const double radiusLarge = 16;
  static const double elevationLevel0 = 0;
  static const double elevationLevel1 = 1;
  static const double elevationLevel2 = 3;
  static const double elevationLevel3 = 6;
  static const double elevationLevel4 = 8;
  static const double elevationLevel5 = 12;

  // --- Text Theme (Material 3 - Enhanced for CarNow E-commerce) ---
  static const TextTheme _textTheme = TextTheme(
    // Display Styles - للعناوين الكبيرة جداً (شاشات البداية، العروض الخاصة)
    displayLarge: TextStyle(
      fontFamily: fontFamily,
      fontSize: 56,
      fontWeight: FontWeight.w800,
      letterSpacing: -0.25,
    ),
    displayMedium: TextStyle(
      fontFamily: fontFamily,
      fontSize: 44,
      fontWeight: FontWeight.w700,
      letterSpacing: 0,
    ),
    displaySmall: TextStyle(
      fontFamily: fontFamily,
      fontSize: 36,
      fontWeight: FontWeight.w600,
      letterSpacing: 0,
    ),

    // Headline Styles - للعناوين الرئيسية (أسماء الفئات، عناوين الصفحات)
    headlineLarge: TextStyle(
      fontFamily: fontFamily,
      fontSize: 32,
      fontWeight: FontWeight.w700,
      letterSpacing: 0,
    ),
    headlineMedium: TextStyle(
      fontFamily: fontFamily,
      fontSize: 28,
      fontWeight: FontWeight.w600,
      letterSpacing: 0,
    ),
    headlineSmall: TextStyle(
      fontFamily: fontFamily,
      fontSize: 24,
      fontWeight: FontWeight.w600,
      letterSpacing: 0,
    ),

    // Title Styles - لأسماء المنتجات والعناوين الفرعية
    titleLarge: TextStyle(
      fontFamily: fontFamily,
      fontSize: 22,
      fontWeight: FontWeight.w600,
      letterSpacing: 0,
      height: 1.3,
    ),
    titleMedium: TextStyle(
      fontFamily: fontFamily,
      fontSize: 18,
      fontWeight: FontWeight.w600,
      letterSpacing: 0.15,
      height: 1.3,
    ),
    titleSmall: TextStyle(
      fontFamily: fontFamily,
      fontSize: 16,
      fontWeight: FontWeight.w500,
      letterSpacing: 0.1,
      height: 1.3,
    ),

    // Body Styles - للنصوص العادية والأوصاف
    bodyLarge: TextStyle(
      fontFamily: fontFamily,
      fontSize: 16,
      fontWeight: FontWeight.w400,
      letterSpacing: 0.25,
      height: 1.5,
    ),
    bodyMedium: TextStyle(
      fontFamily: fontFamily,
      fontSize: 14,
      fontWeight: FontWeight.w400,
      letterSpacing: 0.25,
      height: 1.4,
    ),
    bodySmall: TextStyle(
      fontFamily: fontFamily,
      fontSize: 12,
      fontWeight: FontWeight.w400,
      letterSpacing: 0.4,
      height: 1.4,
    ),

    // Label Styles - للتسميات والأزرار
    labelLarge: TextStyle(
      fontFamily: fontFamily,
      fontSize: 14,
      fontWeight: FontWeight.w500,
      letterSpacing: 0.1,
      height: 1.4,
    ),
    labelMedium: TextStyle(
      fontFamily: fontFamily,
      fontSize: 12,
      fontWeight: FontWeight.w500,
      letterSpacing: 0.5,
      height: 1.3,
    ),
    labelSmall: TextStyle(
      fontFamily: fontFamily,
      fontSize: 11,
      fontWeight: FontWeight.w500,
      letterSpacing: 0.5,
      height: 1.3,
    ),
  );

  // --- Color Schemes ---
  static ColorScheme _colorScheme(bool isDark) {
    // Generate the full M3 tonal palette from our AppColors.primary seed.
    // This is the core of Material 3's dynamic color system.
    final scheme = ColorScheme.fromSeed(
      seedColor: AppColors.primary,
      brightness: isDark ? Brightness.dark : Brightness.light,
      // Override specific colors to match our precise branding from AppColors.
      secondary: AppColors.secondary,
      tertiary: AppColors.tertiary,
      error: AppColors.error,
    );

    // Return the generated scheme, allowing Material 3 to handle dark mode.
    // We can add specific overrides for the dark theme if needed.
    return scheme.copyWith(
      surface: isDark ? const Color(0xFF1E1E1E) : AppColors.surface,
      onSurface: isDark ? const Color(0xFFE6E1E5) : AppColors.textPrimary,
    );
  }

  // Material 3 Expressive Light Theme
  static ThemeData lightTheme = _createTheme(false);

  // Material 3 Expressive Dark Theme
  static ThemeData darkTheme = _createTheme(true);

  static ThemeData _createTheme(bool isDark) {
    final colorScheme = _colorScheme(isDark);

    return ThemeData(
      useMaterial3: true,
      colorScheme: colorScheme,
      brightness: isDark ? Brightness.dark : Brightness.light,

      // Enhanced Typography
      textTheme: _createEnhancedTextTheme(colorScheme),

      // Primary Swatch (for backwards compatibility)
      primarySwatch: Colors.blue,

      // Scaffold Configuration
      scaffoldBackgroundColor: colorScheme.surface,

      // App Bar Theme
      appBarTheme: _createExpressiveAppBarTheme(colorScheme, isDark),

      // Card Theme
      cardTheme: _createExpressiveCardTheme(colorScheme),

      // Enhanced Button Themes
      elevatedButtonTheme: _createExpressiveElevatedButtonTheme(colorScheme),
      filledButtonTheme: _createExpressiveFilledButtonTheme(colorScheme),
      outlinedButtonTheme: _createExpressiveOutlinedButtonTheme(colorScheme),
      textButtonTheme: _createExpressiveTextButtonTheme(colorScheme),

      // Enhanced Input Decoration
      inputDecorationTheme: _createExpressiveInputTheme(colorScheme, isDark),

      // Material 3 Chip Theme
      chipTheme: _createExpressiveChipTheme(colorScheme),

      // Enhanced Navigation Themes
      navigationBarTheme: _createExpressiveNavigationBarTheme(colorScheme),
      navigationRailTheme: _createExpressiveNavigationRailTheme(colorScheme),
      bottomNavigationBarTheme: _createExpressiveBottomNavTheme(colorScheme),

      // Enhanced Dialog Theme
      dialogTheme: _createExpressiveDialogTheme(colorScheme),

      // Enhanced Divider Theme
      dividerTheme: _createExpressiveDividerTheme(colorScheme),

      // Material 3 FAB Theme
      floatingActionButtonTheme: _createExpressiveFABTheme(colorScheme),

      // Enhanced Tab Bar Theme
      tabBarTheme: _createExpressiveTabBarTheme(colorScheme),

      // Material 3 Snackbar Theme
      snackBarTheme: _createExpressiveSnackBarTheme(colorScheme),

      // Enhanced List Tile Theme
      listTileTheme: _createExpressiveListTileTheme(colorScheme),

      // Icon Theme
      iconTheme: IconThemeData(color: colorScheme.onSurface, size: 24),
    );
  }

  // Enhanced Text Theme with better Arabic support
  static TextTheme _createEnhancedTextTheme(ColorScheme colorScheme) {
    return _textTheme
        .apply(
          bodyColor: colorScheme.onSurface,
          displayColor: colorScheme.onSurface,
          fontFamily: fontFamily,
        )
        .copyWith(
          // Enhanced display styles for headers
          displayLarge: _textTheme.displayLarge?.copyWith(
            color: colorScheme.onSurface,
            fontWeight: FontWeight.w800,
            letterSpacing: -0.5,
          ),
          displayMedium: _textTheme.displayMedium?.copyWith(
            color: colorScheme.onSurface,
            fontWeight: FontWeight.w700,
          ),
          displaySmall: _textTheme.displaySmall?.copyWith(
            color: colorScheme.onSurface,
            fontWeight: FontWeight.w600,
          ),

          // Enhanced headline styles
          headlineLarge: _textTheme.headlineLarge?.copyWith(
            color: colorScheme.onSurface,
            fontWeight: FontWeight.w700,
          ),
          headlineMedium: _textTheme.headlineMedium?.copyWith(
            color: colorScheme.onSurface,
            fontWeight: FontWeight.w600,
          ),
          headlineSmall: _textTheme.headlineSmall?.copyWith(
            color: colorScheme.onSurface,
            fontWeight: FontWeight.w600,
          ),

          // Enhanced title styles
          titleLarge: _textTheme.titleLarge?.copyWith(
            color: colorScheme.onSurface,
            fontWeight: FontWeight.w600,
            height: 1.3,
          ),
          titleMedium: _textTheme.titleMedium?.copyWith(
            color: colorScheme.onSurface,
            fontWeight: FontWeight.w500,
            height: 1.3,
          ),
          titleSmall: _textTheme.titleSmall?.copyWith(
            color: colorScheme.onSurface,
            fontWeight: FontWeight.w500,
            height: 1.3,
          ),

          // Enhanced body styles
          bodyLarge: _textTheme.bodyLarge?.copyWith(
            color: colorScheme.onSurface,
            height: 1.5,
          ),
          bodyMedium: _textTheme.bodyMedium?.copyWith(
            color: colorScheme.onSurface,
            height: 1.4,
          ),
          bodySmall: _textTheme.bodySmall?.copyWith(
            color: colorScheme.onSurfaceVariant,
            height: 1.4,
          ),

          // Enhanced label styles
          labelLarge: _textTheme.labelLarge?.copyWith(
            color: colorScheme.onSurface,
            fontWeight: FontWeight.w500,
          ),
          labelMedium: _textTheme.labelMedium?.copyWith(
            color: colorScheme.onSurface,
            fontWeight: FontWeight.w500,
          ),
          labelSmall: _textTheme.labelSmall?.copyWith(
            color: colorScheme.onSurfaceVariant,
            fontWeight: FontWeight.w500,
          ),
        );
  }

  // Material 3 Expressive App Bar Theme
  static AppBarTheme _createExpressiveAppBarTheme(
    ColorScheme colorScheme,
    bool isDark,
  ) {
    return AppBarTheme(
      systemOverlayStyle: SystemUiOverlayStyle(
        statusBarBrightness: isDark ? Brightness.dark : Brightness.light,
        statusBarIconBrightness: isDark ? Brightness.light : Brightness.dark,
        systemNavigationBarColor: colorScheme.surface,
        systemNavigationBarIconBrightness: isDark
            ? Brightness.light
            : Brightness.dark,
      ),
      elevation: elevationLevel0,
      scrolledUnderElevation: elevationLevel2,
      backgroundColor: colorScheme.surface,
      surfaceTintColor: colorScheme.surfaceTint,
      foregroundColor: colorScheme.onSurface,
      centerTitle: true,
      titleTextStyle: _textTheme.titleLarge?.copyWith(
        color: colorScheme.onSurface,
        fontWeight: FontWeight.w600,
      ),
      actionsIconTheme: IconThemeData(color: colorScheme.onSurface, size: 24),
      iconTheme: IconThemeData(color: colorScheme.onSurface, size: 24),
    );
  }

  // Material 3 Expressive Card Theme
  static CardThemeData _createExpressiveCardTheme(ColorScheme colorScheme) {
    return CardThemeData(
      elevation: elevationLevel1,
      color: colorScheme.surface,
      surfaceTintColor: colorScheme.surfaceTint,
      shadowColor: colorScheme.shadow,
      margin: const EdgeInsets.symmetric(
        vertical: spacingS,
        horizontal: spacingM,
      ),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(radiusLarge),
        side: BorderSide(color: colorScheme.outlineVariant, width: 0.5),
      ),
      clipBehavior: Clip.antiAlias,
    );
  }

  // Material 3 Navigation Bar Theme
  static NavigationBarThemeData _createExpressiveNavigationBarTheme(
    ColorScheme colorScheme,
  ) {
    return NavigationBarThemeData(
      backgroundColor: colorScheme.surface,
      surfaceTintColor: colorScheme.surfaceTint,
      indicatorColor: colorScheme.secondaryContainer,
      elevation: elevationLevel2,
      height: 80,
      labelTextStyle: WidgetStateTextStyle.resolveWith((states) {
        if (states.contains(WidgetState.selected)) {
          return _textTheme.labelMedium?.copyWith(
                color: colorScheme.onSurface,
                fontWeight: FontWeight.w600,
              ) ??
              const TextStyle();
        }
        return _textTheme.labelMedium?.copyWith(
              color: colorScheme.onSurfaceVariant,
              fontWeight: FontWeight.w500,
            ) ??
            const TextStyle();
      }),
      iconTheme: WidgetStateProperty.resolveWith((states) {
        if (states.contains(WidgetState.selected)) {
          return IconThemeData(
            color: colorScheme.onSecondaryContainer,
            size: 24,
          );
        }
        return IconThemeData(color: colorScheme.onSurfaceVariant, size: 24);
      }),
    );
  }

  // Material 3 Navigation Rail Theme
  static NavigationRailThemeData _createExpressiveNavigationRailTheme(
    ColorScheme colorScheme,
  ) {
    return NavigationRailThemeData(
      backgroundColor: colorScheme.surface,
      indicatorColor: colorScheme.secondaryContainer,
      selectedIconTheme: IconThemeData(
        color: colorScheme.onSecondaryContainer,
        size: 24,
      ),
      unselectedIconTheme: IconThemeData(
        color: colorScheme.onSurfaceVariant,
        size: 24,
      ),
      selectedLabelTextStyle: _textTheme.labelMedium?.copyWith(
        color: colorScheme.onSurface,
        fontWeight: FontWeight.w600,
      ),
      unselectedLabelTextStyle: _textTheme.labelMedium?.copyWith(
        color: colorScheme.onSurfaceVariant,
        fontWeight: FontWeight.w500,
      ),
    );
  }

  // Material 3 Expressive Bottom Navigation Theme
  static BottomNavigationBarThemeData _createExpressiveBottomNavTheme(
    ColorScheme colorScheme,
  ) {
    return BottomNavigationBarThemeData(
      backgroundColor: colorScheme.surface,
      selectedItemColor: colorScheme.primary,
      unselectedItemColor: colorScheme.onSurfaceVariant,
      selectedLabelStyle: _textTheme.labelMedium?.copyWith(
        color: colorScheme.primary,
        fontWeight: FontWeight.w600,
      ),
      unselectedLabelStyle: _textTheme.labelMedium?.copyWith(
        color: colorScheme.onSurfaceVariant,
        fontWeight: FontWeight.w500,
      ),
      type: BottomNavigationBarType.fixed,
      elevation: elevationLevel2,
    );
  }

  // Material 3 Expressive Elevated Button Theme
  static ElevatedButtonThemeData _createExpressiveElevatedButtonTheme(
    ColorScheme colorScheme,
  ) {
    return ElevatedButtonThemeData(
      style: FilledButton.styleFrom(
        backgroundColor: colorScheme.primary,
        foregroundColor: colorScheme.onPrimary,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(radiusFull),
        ),
        padding: const EdgeInsets.symmetric(
          horizontal: spacingL,
          vertical: spacingM,
        ),
        textStyle: _textTheme.labelLarge,
        elevation: elevationMedium,
        shadowColor: colorScheme.primary.withValues(alpha: 0.2),
      ),
    );
  }

  // Material 3 Expressive Filled Button Theme
  static FilledButtonThemeData _createExpressiveFilledButtonTheme(
    ColorScheme colorScheme,
  ) {
    return FilledButtonThemeData(
      style: FilledButton.styleFrom(
        backgroundColor: colorScheme.primary,
        foregroundColor: colorScheme.onPrimary,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(radiusFull),
        ),
        padding: const EdgeInsets.symmetric(
          horizontal: spacingL,
          vertical: spacingM,
        ),
        textStyle: _textTheme.labelLarge,
      ),
    );
  }

  // Material 3 Expressive Outlined Button Theme
  static OutlinedButtonThemeData _createExpressiveOutlinedButtonTheme(
    ColorScheme colorScheme,
  ) {
    return OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        foregroundColor: colorScheme.primary,
        side: BorderSide(color: colorScheme.outline, width: 1.5),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(radiusFull),
        ),
        padding: const EdgeInsets.symmetric(
          horizontal: spacingL,
          vertical: spacingM,
        ),
        textStyle: _textTheme.labelLarge,
      ),
    );
  }

  // Material 3 Expressive Text Button Theme
  static TextButtonThemeData _createExpressiveTextButtonTheme(
    ColorScheme colorScheme,
  ) {
    return TextButtonThemeData(
      style: TextButton.styleFrom(
        foregroundColor: colorScheme.primary,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(radiusM),
        ),
        padding: const EdgeInsets.symmetric(
          horizontal: spacingM,
          vertical: spacingS,
        ),
        textStyle: _textTheme.labelLarge?.copyWith(
          color: colorScheme.primary,
          // Removed inherit: false to fix TextStyle lerping issues
        ),
      ),
    );
  }

  // Material 3 Expressive Input Decoration Theme
  static InputDecorationTheme _createExpressiveInputTheme(
    ColorScheme colorScheme,
    bool isDark,
  ) {
    return InputDecorationTheme(
      filled: true,
      fillColor: isDark
          ? colorScheme.surfaceContainerHighest.withValues(alpha: 0.3)
          : colorScheme.surface,
      contentPadding: const EdgeInsets.symmetric(
        horizontal: spacingM,
        vertical: spacingM,
      ),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(radiusL),
        borderSide: BorderSide(color: colorScheme.outlineVariant),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(radiusL),
        borderSide: BorderSide(color: colorScheme.outlineVariant),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(radiusL),
        borderSide: BorderSide(color: colorScheme.primary, width: 2),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(radiusL),
        borderSide: BorderSide(color: colorScheme.error),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(radiusL),
        borderSide: BorderSide(color: colorScheme.error, width: 2),
      ),
      labelStyle: _textTheme.bodyLarge?.copyWith(
        color: colorScheme.onSurfaceVariant,
      ),
      floatingLabelStyle: _textTheme.bodyLarge?.copyWith(
        color: colorScheme.primary,
      ),
      errorStyle: _textTheme.bodySmall?.copyWith(color: colorScheme.error),
      helperStyle: _textTheme.bodySmall?.copyWith(
        color: colorScheme.onSurfaceVariant,
      ),
    );
  }

  // Material 3 Expressive Chip Theme
  static ChipThemeData _createExpressiveChipTheme(ColorScheme colorScheme) {
    return ChipThemeData(
      backgroundColor: colorScheme.secondaryContainer,
      labelStyle: _textTheme.labelMedium?.copyWith(
        color: colorScheme.onSecondaryContainer,
      ),
      side: BorderSide.none,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(radiusFull),
      ),
      padding: const EdgeInsets.symmetric(
        horizontal: spacingS,
        vertical: spacingXS,
      ),
    );
  }

  // Material 3 Expressive Dialog Theme
  static DialogThemeData _createExpressiveDialogTheme(ColorScheme colorScheme) {
    return DialogThemeData(
      backgroundColor: colorScheme.surface,
      elevation: elevationHigh,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(radiusXL),
      ),
      titleTextStyle: _textTheme.titleLarge?.copyWith(
        color: colorScheme.onSurface,
      ),
      contentTextStyle: _textTheme.bodyMedium?.copyWith(
        color: colorScheme.onSurfaceVariant,
      ),
    );
  }

  // Material 3 Expressive Divider Theme
  static DividerThemeData _createExpressiveDividerTheme(
    ColorScheme colorScheme,
  ) {
    return DividerThemeData(
      color: colorScheme.outlineVariant,
      thickness: 0.8,
      space: spacingM,
    );
  }

  // Material 3 Expressive FAB Theme
  static FloatingActionButtonThemeData _createExpressiveFABTheme(
    ColorScheme colorScheme,
  ) {
    return FloatingActionButtonThemeData(
      backgroundColor: colorScheme.primary,
      foregroundColor: colorScheme.onPrimary,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(radiusFull),
      ),
    );
  }

  // Material 3 Expressive Tab Bar Theme
  static TabBarThemeData _createExpressiveTabBarTheme(ColorScheme colorScheme) {
    return TabBarThemeData(
      indicator: UnderlineTabIndicator(
        borderSide: BorderSide(width: 3, color: colorScheme.primary),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(radiusS),
          topRight: Radius.circular(radiusS),
        ),
      ),
      labelColor: colorScheme.primary,
      unselectedLabelColor: colorScheme.onSurfaceVariant,
      labelStyle: _textTheme.labelLarge,
      unselectedLabelStyle: _textTheme.labelLarge,
      indicatorSize: TabBarIndicatorSize.label,
      overlayColor: WidgetStateProperty.all(
        colorScheme.primary.withValues(alpha: 0.1),
      ),
    );
  }

  // Material 3 Expressive Snackbar Theme
  static SnackBarThemeData _createExpressiveSnackBarTheme(
    ColorScheme colorScheme,
  ) {
    return SnackBarThemeData(
      backgroundColor: colorScheme.surface,
      actionTextColor: colorScheme.primary,
      disabledActionTextColor: colorScheme.onSurfaceVariant,
      elevation: elevationMedium,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(radiusL),
        side: BorderSide(color: colorScheme.outline),
      ),
    );
  }

  // Material 3 Expressive List Tile Theme
  static ListTileThemeData _createExpressiveListTileTheme(
    ColorScheme colorScheme,
  ) {
    return ListTileThemeData(
      textColor: colorScheme.onSurface,
      iconColor: colorScheme.onSurface,
      tileColor: colorScheme.surface,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(radiusS),
      ),
    );
  }

  // --- Color Shortcuts (legacy) ---
  // These static constants provide quick access to commonly used colors
  // and maintain backwards–compatibility with older widgets that referenced
  // `AppTheme.primary` and similar getters before the transition to a
  // `ColorScheme`-centric design. Prefer using Theme.of(context).colorScheme
  // for new code.
  static const Color primary = AppColors.primary;
  static const Color onPrimary = AppColors.textOnPrimary;

  static const Color onSurface = AppColors.textPrimary;
  static const Color onSurfaceVariant = AppColors.textSecondary;

  static const Color error = AppColors.error;
  static const Color errorContainer =
      AppColors.error; // Placeholder – update if custom shade is needed.

  static const Color success = AppColors.success;
}
