// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'carnow_theme_provider.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$ThemePreferences {

 ThemeMode get themeMode; bool get useDynamicColor; bool get useHighContrast; Color? get customSeedColor; double get textScaleFactor; bool get useSystemNavigationBar;
/// Create a copy of ThemePreferences
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ThemePreferencesCopyWith<ThemePreferences> get copyWith => _$ThemePreferencesCopyWithImpl<ThemePreferences>(this as ThemePreferences, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ThemePreferences&&(identical(other.themeMode, themeMode) || other.themeMode == themeMode)&&(identical(other.useDynamicColor, useDynamicColor) || other.useDynamicColor == useDynamicColor)&&(identical(other.useHighContrast, useHighContrast) || other.useHighContrast == useHighContrast)&&(identical(other.customSeedColor, customSeedColor) || other.customSeedColor == customSeedColor)&&(identical(other.textScaleFactor, textScaleFactor) || other.textScaleFactor == textScaleFactor)&&(identical(other.useSystemNavigationBar, useSystemNavigationBar) || other.useSystemNavigationBar == useSystemNavigationBar));
}


@override
int get hashCode => Object.hash(runtimeType,themeMode,useDynamicColor,useHighContrast,customSeedColor,textScaleFactor,useSystemNavigationBar);

@override
String toString() {
  return 'ThemePreferences(themeMode: $themeMode, useDynamicColor: $useDynamicColor, useHighContrast: $useHighContrast, customSeedColor: $customSeedColor, textScaleFactor: $textScaleFactor, useSystemNavigationBar: $useSystemNavigationBar)';
}


}

/// @nodoc
abstract mixin class $ThemePreferencesCopyWith<$Res>  {
  factory $ThemePreferencesCopyWith(ThemePreferences value, $Res Function(ThemePreferences) _then) = _$ThemePreferencesCopyWithImpl;
@useResult
$Res call({
 ThemeMode themeMode, bool useDynamicColor, bool useHighContrast, Color? customSeedColor, double textScaleFactor, bool useSystemNavigationBar
});




}
/// @nodoc
class _$ThemePreferencesCopyWithImpl<$Res>
    implements $ThemePreferencesCopyWith<$Res> {
  _$ThemePreferencesCopyWithImpl(this._self, this._then);

  final ThemePreferences _self;
  final $Res Function(ThemePreferences) _then;

/// Create a copy of ThemePreferences
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? themeMode = null,Object? useDynamicColor = null,Object? useHighContrast = null,Object? customSeedColor = freezed,Object? textScaleFactor = null,Object? useSystemNavigationBar = null,}) {
  return _then(_self.copyWith(
themeMode: null == themeMode ? _self.themeMode : themeMode // ignore: cast_nullable_to_non_nullable
as ThemeMode,useDynamicColor: null == useDynamicColor ? _self.useDynamicColor : useDynamicColor // ignore: cast_nullable_to_non_nullable
as bool,useHighContrast: null == useHighContrast ? _self.useHighContrast : useHighContrast // ignore: cast_nullable_to_non_nullable
as bool,customSeedColor: freezed == customSeedColor ? _self.customSeedColor : customSeedColor // ignore: cast_nullable_to_non_nullable
as Color?,textScaleFactor: null == textScaleFactor ? _self.textScaleFactor : textScaleFactor // ignore: cast_nullable_to_non_nullable
as double,useSystemNavigationBar: null == useSystemNavigationBar ? _self.useSystemNavigationBar : useSystemNavigationBar // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [ThemePreferences].
extension ThemePreferencesPatterns on ThemePreferences {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ThemePreferences value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ThemePreferences() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ThemePreferences value)  $default,){
final _that = this;
switch (_that) {
case _ThemePreferences():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ThemePreferences value)?  $default,){
final _that = this;
switch (_that) {
case _ThemePreferences() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( ThemeMode themeMode,  bool useDynamicColor,  bool useHighContrast,  Color? customSeedColor,  double textScaleFactor,  bool useSystemNavigationBar)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ThemePreferences() when $default != null:
return $default(_that.themeMode,_that.useDynamicColor,_that.useHighContrast,_that.customSeedColor,_that.textScaleFactor,_that.useSystemNavigationBar);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( ThemeMode themeMode,  bool useDynamicColor,  bool useHighContrast,  Color? customSeedColor,  double textScaleFactor,  bool useSystemNavigationBar)  $default,) {final _that = this;
switch (_that) {
case _ThemePreferences():
return $default(_that.themeMode,_that.useDynamicColor,_that.useHighContrast,_that.customSeedColor,_that.textScaleFactor,_that.useSystemNavigationBar);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( ThemeMode themeMode,  bool useDynamicColor,  bool useHighContrast,  Color? customSeedColor,  double textScaleFactor,  bool useSystemNavigationBar)?  $default,) {final _that = this;
switch (_that) {
case _ThemePreferences() when $default != null:
return $default(_that.themeMode,_that.useDynamicColor,_that.useHighContrast,_that.customSeedColor,_that.textScaleFactor,_that.useSystemNavigationBar);case _:
  return null;

}
}

}

/// @nodoc


class _ThemePreferences implements ThemePreferences {
  const _ThemePreferences({this.themeMode = ThemeMode.system, this.useDynamicColor = true, this.useHighContrast = false, this.customSeedColor, this.textScaleFactor = 1.0, this.useSystemNavigationBar = false});
  

@override@JsonKey() final  ThemeMode themeMode;
@override@JsonKey() final  bool useDynamicColor;
@override@JsonKey() final  bool useHighContrast;
@override final  Color? customSeedColor;
@override@JsonKey() final  double textScaleFactor;
@override@JsonKey() final  bool useSystemNavigationBar;

/// Create a copy of ThemePreferences
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ThemePreferencesCopyWith<_ThemePreferences> get copyWith => __$ThemePreferencesCopyWithImpl<_ThemePreferences>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ThemePreferences&&(identical(other.themeMode, themeMode) || other.themeMode == themeMode)&&(identical(other.useDynamicColor, useDynamicColor) || other.useDynamicColor == useDynamicColor)&&(identical(other.useHighContrast, useHighContrast) || other.useHighContrast == useHighContrast)&&(identical(other.customSeedColor, customSeedColor) || other.customSeedColor == customSeedColor)&&(identical(other.textScaleFactor, textScaleFactor) || other.textScaleFactor == textScaleFactor)&&(identical(other.useSystemNavigationBar, useSystemNavigationBar) || other.useSystemNavigationBar == useSystemNavigationBar));
}


@override
int get hashCode => Object.hash(runtimeType,themeMode,useDynamicColor,useHighContrast,customSeedColor,textScaleFactor,useSystemNavigationBar);

@override
String toString() {
  return 'ThemePreferences(themeMode: $themeMode, useDynamicColor: $useDynamicColor, useHighContrast: $useHighContrast, customSeedColor: $customSeedColor, textScaleFactor: $textScaleFactor, useSystemNavigationBar: $useSystemNavigationBar)';
}


}

/// @nodoc
abstract mixin class _$ThemePreferencesCopyWith<$Res> implements $ThemePreferencesCopyWith<$Res> {
  factory _$ThemePreferencesCopyWith(_ThemePreferences value, $Res Function(_ThemePreferences) _then) = __$ThemePreferencesCopyWithImpl;
@override @useResult
$Res call({
 ThemeMode themeMode, bool useDynamicColor, bool useHighContrast, Color? customSeedColor, double textScaleFactor, bool useSystemNavigationBar
});




}
/// @nodoc
class __$ThemePreferencesCopyWithImpl<$Res>
    implements _$ThemePreferencesCopyWith<$Res> {
  __$ThemePreferencesCopyWithImpl(this._self, this._then);

  final _ThemePreferences _self;
  final $Res Function(_ThemePreferences) _then;

/// Create a copy of ThemePreferences
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? themeMode = null,Object? useDynamicColor = null,Object? useHighContrast = null,Object? customSeedColor = freezed,Object? textScaleFactor = null,Object? useSystemNavigationBar = null,}) {
  return _then(_ThemePreferences(
themeMode: null == themeMode ? _self.themeMode : themeMode // ignore: cast_nullable_to_non_nullable
as ThemeMode,useDynamicColor: null == useDynamicColor ? _self.useDynamicColor : useDynamicColor // ignore: cast_nullable_to_non_nullable
as bool,useHighContrast: null == useHighContrast ? _self.useHighContrast : useHighContrast // ignore: cast_nullable_to_non_nullable
as bool,customSeedColor: freezed == customSeedColor ? _self.customSeedColor : customSeedColor // ignore: cast_nullable_to_non_nullable
as Color?,textScaleFactor: null == textScaleFactor ? _self.textScaleFactor : textScaleFactor // ignore: cast_nullable_to_non_nullable
as double,useSystemNavigationBar: null == useSystemNavigationBar ? _self.useSystemNavigationBar : useSystemNavigationBar // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}

/// @nodoc
mixin _$CarNowTheme {

 ThemeData get light; ThemeData get dark; ThemeMode get mode;
/// Create a copy of CarNowTheme
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CarNowThemeCopyWith<CarNowTheme> get copyWith => _$CarNowThemeCopyWithImpl<CarNowTheme>(this as CarNowTheme, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CarNowTheme&&(identical(other.light, light) || other.light == light)&&(identical(other.dark, dark) || other.dark == dark)&&(identical(other.mode, mode) || other.mode == mode));
}


@override
int get hashCode => Object.hash(runtimeType,light,dark,mode);

@override
String toString() {
  return 'CarNowTheme(light: $light, dark: $dark, mode: $mode)';
}


}

/// @nodoc
abstract mixin class $CarNowThemeCopyWith<$Res>  {
  factory $CarNowThemeCopyWith(CarNowTheme value, $Res Function(CarNowTheme) _then) = _$CarNowThemeCopyWithImpl;
@useResult
$Res call({
 ThemeData light, ThemeData dark, ThemeMode mode
});




}
/// @nodoc
class _$CarNowThemeCopyWithImpl<$Res>
    implements $CarNowThemeCopyWith<$Res> {
  _$CarNowThemeCopyWithImpl(this._self, this._then);

  final CarNowTheme _self;
  final $Res Function(CarNowTheme) _then;

/// Create a copy of CarNowTheme
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? light = null,Object? dark = null,Object? mode = null,}) {
  return _then(_self.copyWith(
light: null == light ? _self.light : light // ignore: cast_nullable_to_non_nullable
as ThemeData,dark: null == dark ? _self.dark : dark // ignore: cast_nullable_to_non_nullable
as ThemeData,mode: null == mode ? _self.mode : mode // ignore: cast_nullable_to_non_nullable
as ThemeMode,
  ));
}

}


/// Adds pattern-matching-related methods to [CarNowTheme].
extension CarNowThemePatterns on CarNowTheme {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _CarNowTheme value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _CarNowTheme() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _CarNowTheme value)  $default,){
final _that = this;
switch (_that) {
case _CarNowTheme():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _CarNowTheme value)?  $default,){
final _that = this;
switch (_that) {
case _CarNowTheme() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( ThemeData light,  ThemeData dark,  ThemeMode mode)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _CarNowTheme() when $default != null:
return $default(_that.light,_that.dark,_that.mode);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( ThemeData light,  ThemeData dark,  ThemeMode mode)  $default,) {final _that = this;
switch (_that) {
case _CarNowTheme():
return $default(_that.light,_that.dark,_that.mode);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( ThemeData light,  ThemeData dark,  ThemeMode mode)?  $default,) {final _that = this;
switch (_that) {
case _CarNowTheme() when $default != null:
return $default(_that.light,_that.dark,_that.mode);case _:
  return null;

}
}

}

/// @nodoc


class _CarNowTheme implements CarNowTheme {
  const _CarNowTheme({required this.light, required this.dark, required this.mode});
  

@override final  ThemeData light;
@override final  ThemeData dark;
@override final  ThemeMode mode;

/// Create a copy of CarNowTheme
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$CarNowThemeCopyWith<_CarNowTheme> get copyWith => __$CarNowThemeCopyWithImpl<_CarNowTheme>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _CarNowTheme&&(identical(other.light, light) || other.light == light)&&(identical(other.dark, dark) || other.dark == dark)&&(identical(other.mode, mode) || other.mode == mode));
}


@override
int get hashCode => Object.hash(runtimeType,light,dark,mode);

@override
String toString() {
  return 'CarNowTheme(light: $light, dark: $dark, mode: $mode)';
}


}

/// @nodoc
abstract mixin class _$CarNowThemeCopyWith<$Res> implements $CarNowThemeCopyWith<$Res> {
  factory _$CarNowThemeCopyWith(_CarNowTheme value, $Res Function(_CarNowTheme) _then) = __$CarNowThemeCopyWithImpl;
@override @useResult
$Res call({
 ThemeData light, ThemeData dark, ThemeMode mode
});




}
/// @nodoc
class __$CarNowThemeCopyWithImpl<$Res>
    implements _$CarNowThemeCopyWith<$Res> {
  __$CarNowThemeCopyWithImpl(this._self, this._then);

  final _CarNowTheme _self;
  final $Res Function(_CarNowTheme) _then;

/// Create a copy of CarNowTheme
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? light = null,Object? dark = null,Object? mode = null,}) {
  return _then(_CarNowTheme(
light: null == light ? _self.light : light // ignore: cast_nullable_to_non_nullable
as ThemeData,dark: null == dark ? _self.dark : dark // ignore: cast_nullable_to_non_nullable
as ThemeData,mode: null == mode ? _self.mode : mode // ignore: cast_nullable_to_non_nullable
as ThemeMode,
  ));
}


}

// dart format on
