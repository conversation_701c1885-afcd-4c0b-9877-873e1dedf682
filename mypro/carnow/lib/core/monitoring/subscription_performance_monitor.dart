import 'dart:async';
import 'dart:developer' as developer;

import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Performance monitoring service for subscription flow
/// Tracks key metrics and performance indicators
class SubscriptionPerformanceMonitor {
  static const String _logTag = 'SubscriptionPerformance';

  // Performance metrics
  final Map<String, DateTime> _operationStartTimes = {};
  final Map<String, Duration> _operationDurations = {};
  final Map<String, int> _operationCounts = {};
  final Map<String, List<String>> _errorLogs = {};

  // Singleton instance
  static final SubscriptionPerformanceMonitor _instance =
      SubscriptionPerformanceMonitor._internal();

  factory SubscriptionPerformanceMonitor() => _instance;

  SubscriptionPerformanceMonitor._internal();

  /// Start timing an operation
  void startOperation(String operationName) {
    _operationStartTimes[operationName] = DateTime.now();
    _operationCounts[operationName] =
        (_operationCounts[operationName] ?? 0) + 1;

    if (kDebugMode) {
      developer.log('Started operation: $operationName', name: _logTag);
    }
  }

  /// End timing an operation and record duration
  void endOperation(String operationName) {
    final startTime = _operationStartTimes[operationName];
    if (startTime != null) {
      final duration = DateTime.now().difference(startTime);
      _operationDurations[operationName] = duration;
      _operationStartTimes.remove(operationName);

      if (kDebugMode) {
        developer.log(
          'Completed operation: $operationName in ${duration.inMilliseconds}ms',
          name: _logTag,
        );
      }

      // Log slow operations
      if (duration.inMilliseconds > 2000) {
        logSlowOperation(operationName, duration);
      }
    }
  }

  /// Log a slow operation for analysis
  void logSlowOperation(String operationName, Duration duration) {
    final message =
        'Slow operation detected: $operationName took ${duration.inMilliseconds}ms';

    _errorLogs.putIfAbsent('slow_operations', () => []).add(message);

    if (kDebugMode) {
      developer.log(
        message,
        name: _logTag,
        level: 900, // Warning level
      );
    }
  }

  /// Log an error during subscription operations
  void logError(String operationName, String error, [StackTrace? stackTrace]) {
    final message = 'Error in $operationName: $error';

    _errorLogs.putIfAbsent(operationName, () => []).add(message);

    if (kDebugMode) {
      developer.log(
        message,
        name: _logTag,
        error: error,
        stackTrace: stackTrace,
        level: 1000, // Error level
      );
    }
  }

  /// Get performance metrics for an operation
  PerformanceMetrics? getMetrics(String operationName) {
    final duration = _operationDurations[operationName];
    final count = _operationCounts[operationName];
    final errors = _errorLogs[operationName];

    // Return metrics if we have any data for this operation
    if (duration != null || count != null || errors != null) {
      return PerformanceMetrics(
        operationName: operationName,
        lastDuration: duration,
        executionCount: count ?? 0,
        errors: errors ?? [],
      );
    }

    return null;
  }

  /// Get all performance metrics
  Map<String, PerformanceMetrics> getAllMetrics() {
    final Map<String, PerformanceMetrics> allMetrics = {};

    final allOperations = {
      ..._operationDurations.keys,
      ..._operationCounts.keys,
    };

    for (final operation in allOperations) {
      final metrics = getMetrics(operation);
      if (metrics != null) {
        allMetrics[operation] = metrics;
      }
    }

    return allMetrics;
  }

  /// Clear all metrics (useful for testing)
  void clearMetrics() {
    _operationStartTimes.clear();
    _operationDurations.clear();
    _operationCounts.clear();
    _errorLogs.clear();
  }

  /// Generate performance report
  String generateReport() {
    final buffer = StringBuffer();
    buffer.writeln('=== Subscription Performance Report ===');
    buffer.writeln('Generated at: ${DateTime.now()}');
    buffer.writeln();

    final metrics = getAllMetrics();

    if (metrics.isEmpty) {
      buffer.writeln('No performance data available.');
      return buffer.toString();
    }

    // Sort by execution count (most used operations first)
    final sortedMetrics = metrics.entries.toList()
      ..sort(
        (a, b) => b.value.executionCount.compareTo(a.value.executionCount),
      );

    for (final entry in sortedMetrics) {
      final metric = entry.value;
      buffer.writeln('Operation: ${metric.operationName}');
      buffer.writeln('  Executions: ${metric.executionCount}');

      if (metric.lastDuration != null) {
        buffer.writeln(
          '  Last Duration: ${metric.lastDuration!.inMilliseconds}ms',
        );
      }

      if (metric.errors.isNotEmpty) {
        buffer.writeln('  Errors (${metric.errors.length}):');
        for (final error in metric.errors.take(3)) {
          // Show only first 3 errors
          buffer.writeln('    - $error');
        }
        if (metric.errors.length > 3) {
          buffer.writeln('    ... and ${metric.errors.length - 3} more');
        }
      }

      buffer.writeln();
    }

    return buffer.toString();
  }
}

/// Performance metrics for a specific operation
class PerformanceMetrics {
  final String operationName;
  final Duration? lastDuration;
  final int executionCount;
  final List<String> errors;

  const PerformanceMetrics({
    required this.operationName,
    this.lastDuration,
    required this.executionCount,
    required this.errors,
  });

  /// Check if operation is performing well
  bool get isPerformingWell {
    // Consider operation healthy if:
    // 1. Last duration is under 2 seconds
    // 2. Error rate is less than 10%
    final hasGoodLatency =
        lastDuration == null || lastDuration!.inMilliseconds < 2000;
    final hasLowErrorRate =
        executionCount == 0 || (errors.length / executionCount) < 0.1;

    return hasGoodLatency && hasLowErrorRate;
  }

  /// Get error rate as percentage
  double get errorRate {
    if (executionCount == 0) return 0.0;
    return (errors.length / executionCount) * 100;
  }
}

/// Riverpod provider for performance monitor
final subscriptionPerformanceMonitorProvider =
    Provider<SubscriptionPerformanceMonitor>((ref) {
      return SubscriptionPerformanceMonitor();
    });

/// Performance metrics provider
final subscriptionPerformanceMetricsProvider =
    Provider<Map<String, PerformanceMetrics>>((ref) {
      final monitor = ref.watch(subscriptionPerformanceMonitorProvider);
      return monitor.getAllMetrics();
    });

/// Extension to easily track operations
extension PerformanceTracking on SubscriptionPerformanceMonitor {
  /// Execute an operation with automatic performance tracking
  Future<T> trackOperation<T>(
    String operationName,
    Future<T> Function() operation,
  ) async {
    startOperation(operationName);

    try {
      final result = await operation();
      endOperation(operationName);
      return result;
    } catch (error, stackTrace) {
      logError(operationName, error.toString(), stackTrace);
      endOperation(operationName);
      rethrow;
    }
  }

  /// Execute a synchronous operation with automatic performance tracking
  T trackSyncOperation<T>(String operationName, T Function() operation) {
    startOperation(operationName);

    try {
      final result = operation();
      endOperation(operationName);
      return result;
    } catch (error, stackTrace) {
      logError(operationName, error.toString(), stackTrace);
      endOperation(operationName);
      rethrow;
    }
  }
}
