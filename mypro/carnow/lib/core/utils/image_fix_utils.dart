import 'package:flutter/material.dart';
import '../widgets/network_image_cached.dart';
import 'package:logging/logging.dart';

/// Utility class to help fix common image URL issues
class ImageFixUtils {
  static final _logger = Logger('ImageFixUtils');

  /// Common problematic patterns and their fixes
  static const Map<String, String> _problematicPatterns = {
    'placehold.co': 'picsum.photos',
    'placeholder.com': 'picsum.photos',
    'via.placeholder.com': 'picsum.photos',
  };

  /// Fix a problematic image URL by replacing known bad patterns
  static String fixImageUrl(String originalUrl) {
    String fixedUrl = originalUrl;

    for (final entry in _problematicPatterns.entries) {
      if (originalUrl.contains(entry.key)) {
        // Extract dimensions if possible
        final dimensionMatch = RegExp(r'(\d+)x(\d+)').firstMatch(originalUrl);
        if (dimensionMatch != null) {
          final width = dimensionMatch.group(1)!;
          final height = dimensionMatch.group(2)!;
          fixedUrl = 'https://picsum.photos/$width/$height';
        } else {
          // Default fallback
          fixedUrl = 'https://picsum.photos/600/400';
        }

        _logger.info('Fixed problematic URL: $originalUrl -> $fixedUrl');
        break;
      }
    }

    return fixedUrl;
  }

  /// Check if an image URL is likely to cause issues
  static bool isProblematicUrl(String url) {
    return _problematicPatterns.keys.any((pattern) => url.contains(pattern));
  }

  /// Get a safe fallback image URL for automotive parts
  static String getAutomotiveFallbackUrl({
    int width = 600,
    int height = 400,
    String? category,
  }) {
    // Use seed based on category for consistent images
    if (category != null) {
      final seed = category.hashCode.abs();
      return 'https://picsum.photos/$width/$height?random=$seed';
    }
    return 'https://picsum.photos/$width/$height';
  }

  /// Batch fix URLs in a list
  static List<String> fixImageUrls(List<String> urls) {
    return urls.map(fixImageUrl).toList();
  }

  /// Create a widget that shows a working image or a nice fallback
  static Widget createRobustImage({
    required String? imageUrl,
    required BuildContext context,
    double? width,
    double? height,
    BoxFit fit = BoxFit.cover,
    String? category,
  }) {
    if (imageUrl == null || imageUrl.isEmpty) {
      return _createFallbackWidget(width, height, category);
    }

    final safeUrl = fixImageUrl(imageUrl);

    return NetworkImageCached(
      safeUrl,
      width: width,
      height: height,
      fit: fit,
      errorIcon: Icons.image_outlined,
    );
  }

  static Widget _createFallbackWidget(
    double? width,
    double? height,
    String? category,
  ) {
    IconData icon;
    String label;

    switch (category?.toLowerCase()) {
      case 'battery':
      case 'batteries':
        icon = Icons.battery_std;
        label = 'بطارية';
        break;
      case 'engine':
        icon = Icons.settings;
        label = 'محرك';
        break;
      case 'automotive':
      case 'car':
        icon = Icons.directions_car;
        label = 'سيارة';
        break;
      default:
        icon = Icons.image_not_supported;
        label = 'صورة';
    }

    return Container(
      width: width,
      height: height,
      color: Colors.grey.shade100,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 32, color: Colors.grey.shade600),
          const SizedBox(height: 4),
          Text(
            label,
            style: TextStyle(color: Colors.grey.shade600, fontSize: 12),
          ),
        ],
      ),
    );
  }
}
