import 'package:flutter/material.dart';

/// Extension methods on BuildContext for more efficient access to theme and sizing values.
/// Using these methods can reduce rebuilds and improve performance by caching values.
extension ContextualTheme on BuildContext {
  /// Access to ThemeData without needing Theme.of(context) each time
  ThemeData get theme => Theme.of(this);

  /// Direct access to color scheme without going through theme
  ColorScheme get colors => theme.colorScheme;

  /// Direct access to text theme without going through theme
  TextTheme get textTheme => theme.textTheme;

  /// Access screen size without creating a new MediaQuery
  Size get screenSize => MediaQuery.of(this).size;

  /// Check if device is in dark mode
  bool get isDarkMode => theme.brightness == Brightness.dark;

  /// Get the screen width
  double get screenWidth => screenSize.width;

  /// Get the screen height
  double get screenHeight => screenSize.height;

  /// Check if the screen width is less than 600 (considered mobile)
  bool get isMobile => screenWidth < 600;

  /// Get screen orientation
  Orientation get orientation => MediaQuery.of(this).orientation;

  /// Get paddings for the screen
  EdgeInsets get padding => MediaQuery.of(this).padding;

  /// Get the safe area insets (for notches, etc.)
  EdgeInsets get safeAreaInsets => MediaQuery.of(this).padding;
}
