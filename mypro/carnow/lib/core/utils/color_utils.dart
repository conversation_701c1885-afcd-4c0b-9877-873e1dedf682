import 'package:flutter/material.dart';
import '../../features/recommendations/models/recommendation_model.dart';

/// Helper class for common alpha values
class AlphaValues {
  static const double transparent = 0;
  static const double veryLight = 0.1;
  static const double light = 0.2;
  static const double medium = 0.5;
  static const double dark = 0.7;
  static const double veryDark = 0.9;
  static const double opaque = 1;
}

/// Unified utility class for all color operations with safety checks
/// دمج جميع أدوات الألوان في ملف واحد لتجنب التكرار
class ColorUtils {
  ColorUtils._();

  // === SAFE ALPHA OPERATIONS - عمليات الألفا الآمنة ===

  /// Safe withAlpha operation that prevents NaN/Infinity errors
  static Color safeWithAlpha(Color color, double alpha) {
    var effectiveAlpha = alpha;
    // Ensure alpha is within valid range [0.0, 1.0]
    if (effectiveAlpha.isNaN || effectiveAlpha.isInfinite) {
      effectiveAlpha = 1.0; // Default to fully opaque
    }

    effectiveAlpha = effectiveAlpha.clamp(0.0, 1.0);
    final alphaInt = (effectiveAlpha * 255).round().clamp(0, 255);

    return color.withAlpha(alphaInt);
  }

  /// Safe withAlpha operation that returns a fallback color if operation fails or the provided color is null.
  static Color safeWithAlphaOrFallback(
    Color? color,
    double alpha,
    Color? fallbackColor,
  ) {
    try {
      final targetColor = color ?? fallbackColor ?? Colors.transparent;
      return safeWithAlpha(targetColor, alpha);
    } catch (e) {
      // If everything fails, return a transparent color.
      return Colors.transparent;
    }
  }

  /// Safe withOpacity operation
  static Color safeWithOpacity(Color color, double opacity) {
    var effectiveOpacity = opacity;
    if (effectiveOpacity.isNaN || effectiveOpacity.isInfinite) {
      effectiveOpacity = 1.0;
    }

    effectiveOpacity = effectiveOpacity.clamp(0.0, 1.0);
    return safeWithAlpha(color, effectiveOpacity);
  }

  /// Get alpha value safely
  static int safeAlphaInt(double alpha) {
    if (alpha.isNaN || alpha.isInfinite) {
      return 255;
    }
    return (alpha.clamp(0.0, 1.0) * 255).round();
  }

  /// Convert double to int safely for alpha operations
  static int safeDoubleToInt(double value, {int fallback = 0}) {
    if (value.isNaN || value.isInfinite) {
      return fallback;
    }
    return value.round();
  }

  /// Common safe alpha applications
  static Color withLightAlpha(Color color) =>
      safeWithAlpha(color, AlphaValues.light);
  static Color withMediumAlpha(Color color) =>
      safeWithAlpha(color, AlphaValues.medium);
  static Color withDarkAlpha(Color color) =>
      safeWithAlpha(color, AlphaValues.dark);

  // === RECOMMENDATION COLORS - ألوان التوصيات ===

  /// Get color for recommendation type
  static Color getRecommendationTypeColor(BuildContext context, RecommendationType type) {
    final colorScheme = Theme.of(context).colorScheme;
    switch (type) {
      case RecommendationType.trending:
        return colorScheme.error;
      case RecommendationType.similarProducts:
        return Colors.blue.shade600;
      case RecommendationType.boughtTogether:
        return Colors.green.shade600;
      case RecommendationType.userBased:
        return colorScheme.tertiary;
      case RecommendationType.categoryBased:
        return colorScheme.secondary;
      case RecommendationType.personalized:
        return colorScheme.primary;
      case RecommendationType.viewedTogether:
        return Colors.purple.shade400;
    }
  }

  /// Get icon for recommendation type
  static IconData getRecommendationTypeIcon(RecommendationType type) {
    switch (type) {
      case RecommendationType.trending:
        return Icons.trending_up;
      case RecommendationType.similarProducts:
        return Icons.compare_arrows;
      case RecommendationType.boughtTogether:
        return Icons.shopping_cart_checkout;
      case RecommendationType.userBased:
        return Icons.group;
      case RecommendationType.categoryBased:
        return Icons.category_outlined;
      case RecommendationType.personalized:
        return Icons.person_search;
      case RecommendationType.viewedTogether:
        return Icons.visibility;
    }
  }

  /// Creates a lighter version of a color for icon backgrounds
  static Color getLightenedColor(Color color, {double opacity = 0.1}) {
    return color.withValues(alpha: opacity);
  }

  // === LEGACY COMPATIBILITY - التوافق العكسي ===
  
  /// @deprecated Use getRecommendationTypeColor instead
  @Deprecated('Use getRecommendationTypeColor instead')
  static Color getTypeColor(BuildContext context, RecommendationType type) =>
      getRecommendationTypeColor(context, type);

  /// @deprecated Use getRecommendationTypeIcon instead
  @Deprecated('Use getRecommendationTypeIcon instead')
  static IconData getTypeIcon(RecommendationType type) =>
      getRecommendationTypeIcon(type);
}

/// Extension on Color for safe alpha operations
extension SafeColorExtension on Color {
  /// Safely applies alpha with validation
  Color safeAlpha(double alpha) => ColorUtils.safeWithAlpha(this, alpha);

  /// Safely applies alpha with fallback
  Color safeAlphaOrFallback(double alpha, Color fallback) =>
      ColorUtils.safeWithAlphaOrFallback(this, alpha, fallback);
}
