/// دليل تحديث Providers من StateProvider إلى Notifier/AsyncNotifier
/// Guide for modernizing providers from StateProvider to Notifier/AsyncNotifier
library;

import 'package:flutter_riverpod/flutter_riverpod.dart';

// ========================================
// أمثلة على التحويل من StateProvider إلى Notifier
// Examples of converting from StateProvider to Notifier
// ========================================

// ❌ OLD: StateProvider (deprecated pattern)
// final currentPageProvider = StateProvider<int>((ref) => 1);

// ✅ NEW: Notifier (modern pattern)

// 1. Simple State Management
class CurrentPageNotifier extends Notifier<int> {
  @override
  int build() => 1;

  void setPage(int page) {
    state = page;
  }

  void nextPage() {
    state = state + 1;
  }

  void previousPage() {
    if (state > 1) {
      state = state - 1;
    }
  }
}

final currentPageProvider = NotifierProvider<CurrentPageNotifier, int>(
  CurrentPageNotifier.new,
);

// 2. Navigation Index Example
class NavigationIndexNotifier extends Notifier<int> {
  @override
  int build() => 0;

  void updateIndex(int index) {
    state = index;
  }

  void reset() {
    state = 0;
  }
}

final navigationIndexProvider = NotifierProvider<NavigationIndexNotifier, int>(
  NavigationIndexNotifier.new,
);

// 3. Authentication State Example
class AuthProtectionNotifier extends Notifier<bool> {
  @override
  bool build() => false;

  void enable() {
    state = true;
  }

  void disable() {
    state = false;
  }

  void toggle() {
    state = !state;
  }
}

final authProtectionProvider = NotifierProvider<AuthProtectionNotifier, bool>(
  AuthProtectionNotifier.new,
);

// 4. Complex State with Notifier
class LayoutStateNotifier extends Notifier<LayoutConfig> {
  @override
  LayoutConfig build() {
    return const LayoutConfig(
      isSidebarOpen: true,
      selectedTab: 0,
      isCompactMode: false,
    );
  }

  void toggleSidebar() {
    state = state.copyWith(isSidebarOpen: !state.isSidebarOpen);
  }

  void selectTab(int index) {
    state = state.copyWith(selectedTab: index);
  }

  void setCompactMode(bool isCompact) {
    state = state.copyWith(isCompactMode: isCompact);
  }
}

final layoutStateProvider = NotifierProvider<LayoutStateNotifier, LayoutConfig>(
  LayoutStateNotifier.new,
);

// 5. List Management Example
class BreadcrumbNavigationNotifier extends Notifier<List<BreadcrumbItem>> {
  @override
  List<BreadcrumbItem> build() => [];

  void push(BreadcrumbItem item) {
    state = [...state, item];
  }

  void pop() {
    if (state.isNotEmpty) {
      state = state.sublist(0, state.length - 1);
    }
  }

  void reset() {
    state = [];
  }

  void navigateTo(int index) {
    if (index >= 0 && index < state.length) {
      state = state.sublist(0, index + 1);
    }
  }
}

final breadcrumbNavigationProvider =
    NotifierProvider<BreadcrumbNavigationNotifier, List<BreadcrumbItem>>(
      BreadcrumbNavigationNotifier.new,
    );

// ========================================
// Helper Classes
// ========================================

class LayoutConfig {
  const LayoutConfig({
    required this.isSidebarOpen,
    required this.selectedTab,
    required this.isCompactMode,
  });

  final bool isSidebarOpen;
  final int selectedTab;
  final bool isCompactMode;

  LayoutConfig copyWith({
    bool? isSidebarOpen,
    int? selectedTab,
    bool? isCompactMode,
  }) {
    return LayoutConfig(
      isSidebarOpen: isSidebarOpen ?? this.isSidebarOpen,
      selectedTab: selectedTab ?? this.selectedTab,
      isCompactMode: isCompactMode ?? this.isCompactMode,
    );
  }
}

class BreadcrumbItem {
  const BreadcrumbItem({required this.title, required this.route, this.icon});

  final String title;
  final String route;
  final String? icon;
}

// ========================================
// دليل الاستخدام
// Usage Guide
// ========================================

/// How to use the new providers:
///
/// 1. Read state:
/// ```dart
/// final currentPage = ref.watch(currentPageProvider);
/// ```
///
/// 2. Update state:
/// ```dart
/// ref.read(currentPageProvider.notifier).setPage(2);
/// ref.read(currentPageProvider.notifier).nextPage();
/// ```
///
/// 3. In widgets:
/// ```dart
/// class MyWidget extends ConsumerWidget {
///   @override
///   Widget build(BuildContext context, WidgetRef ref) {
///     final page = ref.watch(currentPageProvider);
///
///     return ElevatedButton(
///       onPressed: () {
///         ref.read(currentPageProvider.notifier).nextPage();
///       },
///       child: Text('Next Page: $page'),
///     );
///   }
/// }
/// ```

// ========================================
// قائمة Providers التي تحتاج تحديث
// List of Providers that need updating
// ========================================

/// Providers to update:
///
/// 1. lib/features/products/providers/products_provider.dart:151
///    - Replace: final currentPageProvider = StateProvider<int>((ref) => 1);
///    - With: Use CurrentPageNotifier above
///
/// 2. lib/features/products/listings/widgets/product_list_view.dart:14
///    - Replace: final _currentPageProvider = StateProvider<int>((ref) => 1);
///    - With: Use CurrentPageNotifier above
///
/// 3. lib/core/widgets/smart_breadcrumb_navigation.dart:401
///    - Replace: final currentBreadcrumbProvider = StateProvider<List<BreadcrumbItem>>
///    - With: Use BreadcrumbNavigationNotifier above
///
/// 4. lib/core/widgets/simple_app_layout.dart:553
///    - Replace: final currentLayoutStateProvider = StateProvider<LayoutState>
///    - With: Use LayoutStateNotifier above
///
/// 5. lib/core/utils/auth_performance_fix.dart:106
///    - Replace: final authProtectionProvider = StateProvider<bool>((ref) => false);
///    - With: Use AuthProtectionNotifier above
///
/// 6. lib/core/navigation/unified_bottom_navigation.dart:255
///    - Replace: final bottomNavigationIndexProvider = StateProvider<int>((ref) => 0);
///    - With: Use NavigationIndexNotifier above
///
/// 7. lib/core/navigation/modern_bottom_navigation.dart:60
///    - Replace: final currentNavigationIndexProvider = StateProvider<int>((ref) => 0);
///    - With: Use NavigationIndexNotifier above
