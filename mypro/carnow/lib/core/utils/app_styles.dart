import 'package:flutter/material.dart';
import '../../core/theme/text_styles.dart';
import 'app_colors.dart';

/// App text styles and theme styles used throughout the application
/// Updated to use the CarNow unified typography system
class AppStyles {
  // ================================
  // Migrated to CarNowTextStyles - Use those instead
  // ================================

  /// @deprecated Use CarNowTextStyles.pageTitle instead
  static TextStyle get heading1 =>
      CarNowTextStyles.pageTitle.colored(AppColors.textPrimary);

  /// @deprecated Use CarNowTextStyles.sectionTitle instead
  static TextStyle get heading2 =>
      CarNowTextStyles.sectionTitle.colored(AppColors.textPrimary);

  /// @deprecated Use CarNowTextStyles.categoryTitle instead
  static TextStyle get heading3 =>
      CarNowTextStyles.categoryTitle.colored(AppColors.textPrimary);

  /// @deprecated Use CarNowTextStyles.productNameDetail instead
  static TextStyle get heading4 =>
      CarNowTextStyles.productNameDetail.colored(AppColors.textPrimary);

  /// @deprecated Use CarNowTextStyles.productName instead
  static TextStyle get heading5 =>
      CarNowTextStyles.productName.colored(AppColors.textPrimary);

  /// @deprecated Use CarNowTextStyles.productDescription instead
  static TextStyle get bodyLarge =>
      CarNowTextStyles.productDescription.colored(AppColors.textPrimary);

  /// @deprecated Use CarNowTextStyles.technicalInfo instead
  static TextStyle get bodyMedium =>
      CarNowTextStyles.technicalInfo.colored(AppColors.textSecondary);

  /// @deprecated Use CarNowTextStyles.hintText instead
  static TextStyle get bodySmall =>
      CarNowTextStyles.hintText.colored(AppColors.textHint);

  // ================================
  // Recommended CarNow Styles (New)
  // ================================

  /// Product name in cards
  static TextStyle get productName =>
      CarNowTextStyles.productName.colored(AppColors.textPrimary);

  /// Product name in detail view
  static TextStyle get productNameDetail =>
      CarNowTextStyles.productNameDetail.colored(AppColors.textPrimary);

  /// Product description text
  static TextStyle get productDescription =>
      CarNowTextStyles.productDescription.colored(AppColors.textPrimary);

  /// Brand name
  static TextStyle get brandName =>
      CarNowTextStyles.brandName.colored(AppColors.textPrimary);

  /// Main price (large and prominent)
  static TextStyle get priceMain =>
      CarNowTextStyles.priceMain.colored(AppColors.price);

  /// Price in product cards
  static TextStyle get priceCard =>
      CarNowTextStyles.priceCard.colored(AppColors.price);

  /// Original price (crossed out)
  static TextStyle get priceOriginal =>
      CarNowTextStyles.priceOriginal.colored(AppColors.textSecondary);

  /// Part number with monospace-like features
  static TextStyle get partNumber =>
      CarNowTextStyles.partNumber.colored(AppColors.textPrimary);

  /// OEM number
  static TextStyle get oemNumber =>
      CarNowTextStyles.oemNumber.colored(AppColors.textSecondary);

  /// Vehicle information (make, model, year)
  static TextStyle get vehicleInfo =>
      CarNowTextStyles.vehicleInfo.colored(AppColors.textPrimary);

  /// Technical specifications
  static TextStyle get specifications =>
      CarNowTextStyles.specifications.colored(AppColors.textSecondary);

  /// Stock status indicators
  static TextStyle get stockStatus => CarNowTextStyles.stockStatus;

  /// Rating and review text
  static TextStyle get rating =>
      CarNowTextStyles.rating.colored(AppColors.textSecondary);

  /// Order numbers and IDs
  static TextStyle get orderNumber =>
      CarNowTextStyles.orderNumber.colored(AppColors.textPrimary);

  // ================================
  // Backward compatibility aliases
  // ================================

  static TextStyle get headingLarge => heading1;
  static TextStyle get headingMedium => heading4;
  static TextStyle get headingSmall => heading5;

  static TextStyle get subtitle1 =>
      CarNowTextStyles.brandName.colored(AppColors.textPrimary);
  static TextStyle get subtitle2 =>
      CarNowTextStyles.technicalInfo.colored(AppColors.textSecondary);
  static TextStyle get bodyText1 =>
      CarNowTextStyles.productDescription.colored(AppColors.textPrimary);
  static TextStyle get bodyText2 =>
      CarNowTextStyles.technicalInfo.colored(AppColors.textSecondary);
  static TextStyle get caption =>
      CarNowTextStyles.hintText.colored(AppColors.textHint);

  // ================================
  // Button styles
  // ================================

  static ButtonStyle primaryButtonStyle = FilledButton.styleFrom(
    backgroundColor: AppColors.primary,
    foregroundColor: AppColors.textOnPrimary,
    padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
    textStyle: CarNowTextStyles.buttonPrimary,
  );

  static ButtonStyle secondaryButtonStyle = OutlinedButton.styleFrom(
    foregroundColor: AppColors.primary,
    side: const BorderSide(color: AppColors.primary),
    padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
    textStyle: CarNowTextStyles.buttonSecondary,
  );

  // ================================
  // Card styles
  // ================================

  static BoxDecoration cardDecoration = BoxDecoration(
    color: AppColors.card,
    borderRadius: BorderRadius.circular(8),
    boxShadow: [
      BoxShadow(
        color: Colors.black.withAlpha(25),
        blurRadius: 4,
        offset: const Offset(0, 2),
      ),
    ],
  );

  static BoxDecoration elevatedCardDecoration = BoxDecoration(
    color: AppColors.card,
    borderRadius: BorderRadius.circular(12),
    boxShadow: [
      BoxShadow(
        color: Colors.black.withAlpha(38),
        blurRadius: 8,
        offset: const Offset(0, 4),
      ),
    ],
  );

  // ================================
  // Input decoration
  // ================================

  static InputDecoration inputDecoration({
    String? labelText,
    String? hintText,
    Widget? prefixIcon,
    Widget? suffixIcon,
    EdgeInsetsGeometry? contentPadding,
  }) => InputDecoration(
    labelText: labelText,
    hintText: hintText,
    prefixIcon: prefixIcon,
    suffixIcon: suffixIcon,
    contentPadding: contentPadding,
  );

  // ================================
  // E-commerce specific styles
  // ================================

  /// @deprecated Use AppStyles.priceMain instead
  static TextStyle priceTextStyle = CarNowTextStyles.priceMain.colored(
    AppColors.price,
  );

  /// Auction timer style
  static TextStyle auctionTimerStyle = CarNowTextStyles.rating.colored(
    AppColors.auctionEnding,
  );

  /// Bid amount style
  static TextStyle bidAmountStyle = CarNowTextStyles.priceMain.colored(
    AppColors.primary,
  );

  // ================================
  // Spacing constants
  // ================================

  static const double spacingXSmall = 4;
  static const double spacingSmall = 8;
  static const double spacingMedium = 16;
  static const double spacingLarge = 24;
  static const double spacingXLarge = 32;

  // ================================
  // Border radius constants
  // ================================

  static const double borderRadiusSmall = 4;
  static const double borderRadiusMedium = 8;
  static const double borderRadiusLarge = 12;

  // ================================
  // Animation durations
  // ================================

  static const Duration animationDurationShort = Duration(milliseconds: 200);
  static const Duration animationDurationMedium = Duration(milliseconds: 300);
  static const Duration animationDurationLong = Duration(milliseconds: 500);
}

/// Extension for adding common text styles to TextStyle
extension TextStyleExtensions on TextStyle {
  TextStyle get bold => copyWith(fontWeight: FontWeight.bold);
  TextStyle get semiBold => copyWith(fontWeight: FontWeight.w600);
  TextStyle get medium => copyWith(fontWeight: FontWeight.w500);

  TextStyle withColor(Color color) => copyWith(color: color);
  TextStyle withSize(double fontSize) => copyWith(fontSize: fontSize);
}
