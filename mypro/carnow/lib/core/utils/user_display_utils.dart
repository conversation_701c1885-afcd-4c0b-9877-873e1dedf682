import 'package:flutter/material.dart';
import '../../features/account/models/user_model.dart';
import '../../l10n/app_localizations.dart';

/// Utility class for handling user display names and profile status
class UserDisplayUtils {
  /// Get a professional display name for a user
  static String getDisplayName(UserModel? user, BuildContext context) {
    if (user == null) {
      return AppLocalizations.of(context)!.guestUser;
    }

    // If user has a proper name, use it
    if (user.name != null && user.name!.trim().isNotEmpty) {
      final name = user.name!.trim();
      
      // Check if it's a default/generated name that needs improvement
      if (_isDefaultName(name, user.email)) {
        return _getImprovedDisplayName(user, context);
      }
      
      return name;
    }

    // Fallback to improved display name
    return _getImprovedDisplayName(user, context);
  }

  /// Get display name with profile status indicator
  static String getDisplayNameWithStatus(UserModel? user, BuildContext context) {
    if (user == null) {
      return AppLocalizations.of(context)!.guestUser;
    }

    final displayName = getDisplayName(user, context);
    
    // Add status indicator for incomplete profiles
    if (!_isProfileComplete(user)) {
      return '$displayName (${AppLocalizations.of(context)!.profileIncomplete})';
    }
    
    return displayName;
  }

  /// Check if the user's profile is complete
  static bool isProfileComplete(UserModel? user) {
    return _isProfileComplete(user);
  }

  /// Get the first letter for avatar display
  static String getAvatarLetter(UserModel? user, BuildContext context) {
    final displayName = getDisplayName(user, context);
    
    if (displayName.isNotEmpty) {
      return displayName[0].toUpperCase();
    }
    
    return '؟'; // Arabic question mark for unknown users
  }

  /// Get profile completion status message
  static String getProfileStatusMessage(UserModel? user, BuildContext context) {
    if (user == null) {
      return AppLocalizations.of(context)!.guestSubtitle;
    }

    if (!_isProfileComplete(user)) {
      return AppLocalizations.of(context)!.completeYourProfile;
    }

    return ''; // No message for complete profiles
  }

  /// Check if a name is a default/generated name
  static bool _isDefaultName(String name, String? email) {
    final lowerName = name.toLowerCase();
    
    // Check for exact default names only (not containing)
    if (lowerName == 'مستخدم' || 
        lowerName == 'user' || 
        lowerName == 'new user' ||
        lowerName == 'عضو جديد' ||
        lowerName == 'new member' ||
        lowerName == 'member' ||
        lowerName == 'عضو' ||
        lowerName == 'carnow user' ||
        lowerName == 'مستخدم carnow') {
      return true;
    }

    // Check if it's just the email prefix
    if (email != null && email.contains('@')) {
      final emailPrefix = email.split('@').first.toLowerCase();
      if (lowerName == emailPrefix) {
        return true;
      }
    }

    return false;
  }

  /// Get an improved display name for users with default names
  static String _getImprovedDisplayName(UserModel user, BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    
    // If user has email, create a more professional name
    if (user.email != null && user.email!.contains('@')) {
      final emailPrefix = user.email!.split('@').first;
      
      // Clean up the email prefix to make it more readable
      final cleanPrefix = _cleanEmailPrefix(emailPrefix);
      
      // If it's still not good enough, use new member
      if (cleanPrefix.length < 3 || _isNumericOrSymbolic(cleanPrefix)) {
        return l10n.newMember;
      }
      
      return cleanPrefix;
    }
    
    // Fallback to new member
    return l10n.newMember;
  }

  /// Clean up email prefix to make it more readable
  static String _cleanEmailPrefix(String prefix) {
    // Remove common numeric patterns and symbols
    String cleaned = prefix.replaceAll(RegExp(r'[0-9]+$'), ''); // Remove trailing numbers
    cleaned = cleaned.replaceAll(RegExp('[._-]'), ' '); // Replace symbols with spaces
    
    // Capitalize first letter of each word
    return cleaned.split(' ')
        .where((word) => word.isNotEmpty)
        .map((word) => word[0].toUpperCase() + word.substring(1).toLowerCase())
        .join(' ');
  }

  /// Check if a string is mostly numeric or symbolic
  static bool _isNumericOrSymbolic(String text) {
    final alphaCount = text.replaceAll(RegExp(r'[^a-zA-Z\u0600-\u06FF]'), '').length;
    return alphaCount < text.length * 0.5; // Less than 50% alphabetic
  }

  /// Check if the user's profile is complete
  static bool _isProfileComplete(UserModel? user) {
    if (user == null) return false;
    
    final hasEmail = user.email?.trim().isNotEmpty ?? false;
    final hasPhone = user.phone?.trim().isNotEmpty ?? false;
    final hasProperName = user.name?.trim().isNotEmpty ?? false;
    
    // Check if name is not a default name
    final hasRealName = hasProperName && !_isDefaultName(user.name!, user.email);
    
    return hasEmail && hasPhone && hasRealName;
  }
} 