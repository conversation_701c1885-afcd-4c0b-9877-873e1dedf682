import 'package:flutter/material.dart';
import 'package:carnow/l10n/app_localizations.dart';
import 'package:carnow/l10n/app_localizations_en.dart';

/// Helper utility for safe localization access
/// Provides fallbacks when AppLocalizations is not available
class LocalizationHelper {
  /// Safely get AppLocalizations with fallback to English
  static AppLocalizations? of(BuildContext context) {
    return AppLocalizations.of(context);
  }

  /// Get AppLocalizations with null check operator, but with better error handling
  static AppLocalizations ofOrThrow(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    if (l10n == null) {
      throw FlutterError(
        'AppLocalizations.of(context) returned null. '
        'Make sure you have properly configured localization in your MaterialApp. '
        'Check that AppLocalizations.delegate is included in localizationsDelegates '
        'and that the widget is within the MaterialApp widget tree.',
      );
    }
    return l10n;
  }

  /// Get AppLocalizations with fallback to default English instance
  static AppLocalizations ofWithFallback(BuildContext context) {
    return AppLocalizations.of(context) ?? AppLocalizationsEn();
  }

  /// Check if localization is available
  static bool isAvailable(BuildContext context) {
    return AppLocalizations.of(context) != null;
  }

  /// Get current locale from context
  static Locale? getCurrentLocale(BuildContext context) {
    return Localizations.localeOf(context);
  }

  /// Check if current locale is Arabic
  static bool isArabic(BuildContext context) {
    final locale = getCurrentLocale(context);
    return locale?.languageCode == 'ar';
  }

  /// Check if current locale is English
  static bool isEnglish(BuildContext context) {
    final locale = getCurrentLocale(context);
    return locale?.languageCode == 'en';
  }
}

/// Extension on BuildContext for easier localization access
extension LocalizationExtension on BuildContext {
  /// Safely get AppLocalizations
  AppLocalizations? get l10n => LocalizationHelper.of(this);

  /// Get AppLocalizations with fallback
  AppLocalizations get l10nSafe => LocalizationHelper.ofWithFallback(this);

  /// Get AppLocalizations with error handling
  AppLocalizations get l10nOrThrow => LocalizationHelper.ofOrThrow(this);

  /// Check if localization is available
  bool get hasLocalization => LocalizationHelper.isAvailable(this);

  /// Get current locale
  Locale? get currentLocale => LocalizationHelper.getCurrentLocale(this);

  /// Check if current locale is Arabic
  bool get isArabic => LocalizationHelper.isArabic(this);

  /// Check if current locale is English
  bool get isEnglish => LocalizationHelper.isEnglish(this);
} 