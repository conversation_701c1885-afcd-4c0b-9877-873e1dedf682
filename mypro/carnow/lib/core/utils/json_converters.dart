import 'package:freezed_annotation/freezed_annotation.dart';

/// Converts a value of any type from JSON to a double.
/// It handles int, double, and String representations of numbers.
class JsonDoubleConverter implements JsonConverter<double, dynamic> {
  const JsonDoubleConverter();

  @override
  double fromJson(dynamic json) {
    if (json is double) {
      return json;
    }
    if (json is int) {
      return json.toDouble();
    }
    if (json is String) {
      return double.tryParse(json) ?? 0;
    }
    return 0;
  }

  @override
  dynamic toJson(double object) => object;
}

/// Converts a value of any type from JSON to an int.
/// It handles int, double (by truncating), String representations of numbers, and UUIDs.
class JsonIntConverter implements JsonConverter<int, dynamic> {
  const JsonIntConverter();

  @override
  int fromJson(dynamic json) {
    if (json is int) {
      return json;
    }
    if (json is double) {
      return json.toInt();
    }
    if (json is String) {
      // Try to parse as int first
      final intValue = int.tryParse(json);
      if (intValue != null) {
        return intValue;
      }

      // If it's a UUID string, convert to hash-based integer
      if (json.length == 36 && json.contains('-')) {
        return json.hashCode.abs();
      }

      // For other string types, use hashCode
      return json.hashCode.abs();
    }
    return 0;
  }

  @override
  dynamic toJson(int object) => object;
}

/// Converts ID fields that can be either int or String to String.
/// This handles the mismatch between database integer IDs and model String IDs.
class JsonIdConverter implements JsonConverter<String, dynamic> {
  const JsonIdConverter();

  @override
  String fromJson(dynamic json) {
    if (json is String) {
      return json;
    }
    if (json is int) {
      return json.toString();
    }
    if (json is double) {
      return json.toInt().toString();
    }
    return json?.toString() ?? '';
  }

  @override
  dynamic toJson(String object) {
    // Try to convert back to int if it's a valid integer string
    final intValue = int.tryParse(object);
    return intValue ?? object;
  }
}
