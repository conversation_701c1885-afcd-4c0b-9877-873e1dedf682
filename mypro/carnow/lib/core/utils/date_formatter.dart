import 'package:intl/intl.dart';
import 'package:timeago/timeago.dart' as timeago;

/// Utility class for formatting dates in the CarNow app
class DateFormatter {
  // Private constructor to prevent instantiation
  DateFormatter._();

  /// Formats a DateTime as a readable date
  static String formatDate(DateTime date) =>
      DateFormat.yMMMd('ar').format(date);

  /// Formats a DateTime as a readable time
  static String formatTime(DateTime date) => DateFormat.jm('ar').format(date);

  /// Formats a DateTime as a readable date and time
  static String formatDateTime(DateTime date) =>
      DateFormat.yMMMd('ar').add_jm().format(date);

  /// Formats a DateTime as a "time ago" string (e.g. "منذ 5 دقائق")
  static String timeAgo(DateTime date) {
    // Set Arabic locale for timeago if not already set
    timeago.setLocaleMessages('ar', timeago.ArMessages());
    return timeago.format(date, locale: 'ar');
  }

  /// Formats a DateTime as a short date (DD/MM/YYYY)
  static String formatShortDate(DateTime date) =>
      DateFormat('dd/MM/yyyy').format(date);

  /// Formats a DateTime as a short time (HH:MM)
  static String formatShortTime(DateTime date) =>
      DateFormat('HH:mm').format(date);

  /// Formats a DateTime for display in bid cards and similar widgets
  static String formatBidTime(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 0) {
      return 'منذ ${difference.inDays} ${difference.inDays == 1 ? 'يوم' : 'أيام'}';
    } else if (difference.inHours > 0) {
      return 'منذ ${difference.inHours} ${difference.inHours == 1 ? 'ساعة' : 'ساعات'}';
    } else if (difference.inMinutes > 0) {
      return 'منذ ${difference.inMinutes} ${difference.inMinutes == 1 ? 'دقيقة' : 'دقائق'}';
    } else {
      return 'الآن';
    }
  }

  /// تنسيق التاريخ والوقت بالعربية (بديل)
  static String formatDateTimeArabic(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 7) {
      return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
    } else if (difference.inDays > 0) {
      return '${difference.inDays} ${difference.inDays == 1 ? 'يوم' : 'أيام'} مضت';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} ${difference.inHours == 1 ? 'ساعة' : 'ساعات'} مضت';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} ${difference.inMinutes == 1 ? 'دقيقة' : 'دقائق'} مضت';
    } else {
      return 'الآن';
    }
  }

  /// تنسيق التاريخ فقط بصيغة بديلة
  static String formatDateSimple(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
  }

  /// تنسيق الوقت فقط بصيغة بديلة
  static String formatTimeSimple(DateTime dateTime) {
    final hour = dateTime.hour.toString().padLeft(2, '0');
    final minute = dateTime.minute.toString().padLeft(2, '0');
    return '$hour:$minute';
  }
}
