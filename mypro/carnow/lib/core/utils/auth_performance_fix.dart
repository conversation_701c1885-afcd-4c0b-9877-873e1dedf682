import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/logger.dart';

import '../../../core/auth/unified_auth_provider.dart';

/// Critical fix for authentication logout issues caused by performance problems
/// ✅ Updated to use UnifiedAuthProvider (Forever Plan Architecture)
class AuthPerformanceFix {
  static final Logger _logger = Logger();
  static bool _isProtectionActive = false;
  static Timer? _protectionTimer;
  static int _consecutivePerformanceIssues = 0;

  /// Initialize the performance protection system
  static void initialize() {
    _logger.i('🛡️ Auth Performance Protection System Initialized');
  }

  /// Activate protection during heavy operations
  static void activateProtection({
    Duration duration = const Duration(seconds: 5),
  }) {
    if (_isProtectionActive) return;

    _isProtectionActive = true;
    _consecutivePerformanceIssues++;

    _logger.w('⚠️ Auth protection activated for ${duration.inSeconds}s');

    _protectionTimer?.cancel();
    _protectionTimer = Timer(duration, () {
      _isProtectionActive = false;
      _logger.i('✅ Auth protection deactivated');
    });
  }

  /// Deactivate protection
  static void deactivateProtection() {
    _protectionTimer?.cancel();
    _isProtectionActive = false;
    _logger.i('✅ Auth protection manually deactivated');
  }

  /// Check if auth operations should be blocked
  static bool shouldBlockAuthOperations() {
    return _isProtectionActive;
  }

  /// Safe session check that doesn't interfere with performance
  /// ✅ Uses UnifiedAuthProvider instead of direct Supabase
  static bool safeSessionCheck(WidgetRef ref) {
    if (shouldBlockAuthOperations()) {
      _logger.i('⏳ Auth operation blocked due to performance protection');
      return true; // Assume session is valid during protection
    }

    try {
      final isAuthenticated = ref.read(isAuthenticatedProvider);
      return isAuthenticated;
    } catch (e) {
      _logger.e('خطأ في فحص الجلسة الآمن: $e');
      return true; // Don't logout on error
    }
  }

  /// Emergency session recovery
  /// ✅ Uses SimpleAuthSystem instead of direct Supabase
  static Future<void> emergencySessionRecovery(WidgetRef ref) async {
    if (shouldBlockAuthOperations()) return;

    try {
      _logger.i('🆘 محاولة استرداد الجلسة الطارئة');

      // Try to get a valid access token (this will auto-refresh if needed)
      final validToken = ref.read(currentAccessTokenProvider);

      if (validToken != null) {
        _logger.i('✅ تم استرداد الجلسة بنجاح');
      } else {
        _logger.w('⚠️ فشل في استرداد الجلسة - قد يتطلب إعادة تسجيل دخول');
      }
    } catch (e) {
      _logger.e('فشل في استرداد الجلسة: $e');
    }
  }

  /// Fix for the "تسجيل الخروج بدون طلب تسجيل دخول" issue
  static String getLogoutCauseMessage() {
    if (_consecutivePerformanceIssues > 0) {
      return 'تم تسجيل الخروج بسبب مشاكل في الأداء. يرجى إعادة تسجيل الدخول.';
    }
    return 'تم تسجيل الخروج. يرجى إعادة تسجيل الدخول.';
  }

  /// Performance-aware provider wrapper
  static Widget wrapWithPerformanceProtection(Widget child) {
    return Consumer(
      builder: (context, ref, _) {
        return RepaintBoundary(child: child);
      },
    );
  }

  /// Dispose protection system
  static void dispose() {
    _protectionTimer?.cancel();
    _isProtectionActive = false;
    _consecutivePerformanceIssues = 0;
    _logger.i('🛡️ Auth Performance Protection System Disposed');
  }
}
