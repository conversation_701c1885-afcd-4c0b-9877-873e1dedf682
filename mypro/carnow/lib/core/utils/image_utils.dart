import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';

/// Utility class for optimized image handling
class ImageUtils {
  /// Calculate the optimal cached image size based on device pixels and image dimensions.
  /// This helps reduce memory usage for cached images.
  static int calculateMemCacheSize(
    BuildContext context, {
    double widthFactor = 1.0,
  }) {
    final devicePixelRatio = MediaQuery.of(context).devicePixelRatio;
    final screenWidth = MediaQuery.of(context).size.width;
    final width = (screenWidth * widthFactor * devicePixelRatio).round();

    // Keep the size reasonable, but at least screen width
    return width;
  }

  /// Get enhanced placeholder image URLs for automotive parts
  static String getSafePlaceholderUrl({
    int width = 600,
    int height = 400,
    String? category,
  }) {
    // Use specific automotive-themed images from reliable sources
    final Map<String, List<String>> automotiveImages = {
      'engine': [
        'https://images.unsplash.com/photo-1486262715619-67b85e0b08d3?w=$width&h=$height&fit=crop',
        'https://images.unsplash.com/photo-1572190194154-091b33b55ccf?w=$width&h=$height&fit=crop',
      ],
      'battery': [
        'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=$width&h=$height&fit=crop',
        'https://images.unsplash.com/photo-1581833971358-2c8b550f87b3?w=$width&h=$height&fit=crop',
      ],
      'brakes': [
        'https://images.unsplash.com/photo-1615906655593-ad0386982a0f?w=$width&h=$height&fit=crop',
        'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=$width&h=$height&fit=crop',
      ],
      'tires': [
        'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=$width&h=$height&fit=crop',
        'https://images.unsplash.com/photo-1544788-88c-photo-1558618666-fcd25c85cd64?w=$width&h=$height&fit=crop',
      ],
      'lights': [
        'https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=$width&h=$height&fit=crop',
        'https://images.unsplash.com/photo-1593941707882-a5bac6861d75?w=$width&h=$height&fit=crop',
      ],
      'electrical': [
        'https://images.unsplash.com/photo-1581833971358-2c8b550f87b3?w=$width&h=$height&fit=crop',
        'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=$width&h=$height&fit=crop',
      ],
    };

    // Get category-specific images
    if (category != null) {
      final categoryKey = category.toLowerCase();
      if (automotiveImages.containsKey(categoryKey)) {
        final images = automotiveImages[categoryKey]!;
        // Use hash of category to consistently pick the same image
        final index = categoryKey.hashCode.abs() % images.length;
        return images[index];
      }
    }

    // Fallback to automotive-themed generic images
    final List<String> genericAutomotiveImages = [
      'https://images.unsplash.com/photo-1486262715619-67b85e0b08d3?w=$width&h=$height&fit=crop',
      'https://images.unsplash.com/photo-1572190194154-091b33b55ccf?w=$width&h=$height&fit=crop',
      'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=$width&h=$height&fit=crop',
      'https://images.unsplash.com/photo-1615906655593-ad0386982a0f?w=$width&h=$height&fit=crop',
      'https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=$width&h=$height&fit=crop',
    ];

    // Use a random but consistent image based on width and height
    final index =
        (width + height).hashCode.abs() % genericAutomotiveImages.length;
    return genericAutomotiveImages[index];
  }

  /// Safely convert a nullable [double] dimension to an [int].
  /// Falls back to [defaultValue] when the value is `null`, `NaN`, or infinite.
  static int _sanitizeDimension(double? value, {required int defaultValue}) {
    if (value == null || value.isNaN || value.isInfinite) {
      return defaultValue;
    }
    return value.toInt();
  }

  /// Validate if an image URL is potentially valid
  static bool isValidImageUrl(String? url) {
    if (url == null || url.isEmpty) return false;

    try {
      final uri = Uri.parse(url);
      return uri.isAbsolute &&
          (uri.scheme == 'http' || uri.scheme == 'https') &&
          (url.contains('.jpg') ||
              url.contains('.jpeg') ||
              url.contains('.png') ||
              url.contains('.webp') ||
              url.contains('picsum.photos') ||
              url.contains('unsplash') ||
              url.contains('images.unsplash.com'));
    } catch (e) {
      return false;
    }
  }

  /// Get a CachedNetworkImage with optimized settings for performance
  static Widget optimizedNetworkImage({
    required String imageUrl,
    required BuildContext context,
    BoxFit fit = BoxFit.cover,
    double? width,
    double? height,
    double widthFactor = 1.0,
    Widget? placeholder,
    Widget? errorWidget,
    String? fallbackCategory,
  }) {
    final devicePixelRatio = MediaQuery.of(context).devicePixelRatio;
    final screenWidth = MediaQuery.of(context).size.width;
    final memCacheWidth = (screenWidth * widthFactor * devicePixelRatio)
        .round();

    // Validate the URL first
    final validUrl = isValidImageUrl(imageUrl) ? imageUrl : null;
    final fallbackUrl =
        validUrl ??
        getSafePlaceholderUrl(
          width: _sanitizeDimension(width, defaultValue: 600),
          height: _sanitizeDimension(height, defaultValue: 400),
          category: fallbackCategory,
        );

    return CachedNetworkImage(
      imageUrl: fallbackUrl,
      fit: fit,
      width: width,
      height: height,
      memCacheWidth: memCacheWidth,
      memCacheHeight: height != null && height.isFinite
          ? (height * devicePixelRatio).round()
          : null,
      placeholder: (context, url) =>
          placeholder ??
          Container(
            color: Colors.grey.shade200,
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(
                    width: 24,
                    height: 24,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'جاري التحميل...',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ),
          ),
      errorWidget: (context, url, error) =>
          errorWidget ??
          Container(
            color: Colors.grey.shade200,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  _getPartIcon(fallbackCategory ?? ''),
                  size: 32,
                  color: Colors.grey.shade600,
                ),
                const SizedBox(height: 8),
                Text(
                  _getPartDisplayName(fallbackCategory ?? ''),
                  style: TextStyle(color: Colors.grey.shade600, fontSize: 12),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
    );
  }

  /// Get an enhanced image widget for automotive parts
  static Widget automotivePartImage({
    required String? imageUrl,
    required BuildContext context,
    required String partCategory,
    double? width,
    double? height,
    BoxFit fit = BoxFit.cover,
  }) {
    return optimizedNetworkImage(
      imageUrl: imageUrl ?? '',
      context: context,
      width: width,
      height: height,
      fit: fit,
      fallbackCategory: partCategory,
      errorWidget: Container(
        color: Colors.grey.shade100,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              _getPartIcon(partCategory),
              size: 40,
              color: Colors.grey.shade600,
            ),
            const SizedBox(height: 8),
            Text(
              _getPartDisplayName(partCategory),
              style: TextStyle(color: Colors.grey.shade600, fontSize: 12),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  static IconData _getPartIcon(String category) {
    switch (category.toLowerCase()) {
      case 'battery':
      case 'batteries':
        return Icons.battery_charging_full;
      case 'engine':
        return Icons.settings;
      case 'transmission':
        return Icons.settings_applications;
      case 'brakes':
        return Icons.circle;
      case 'tires':
      case 'wheels':
        return Icons.tire_repair;
      case 'lights':
      case 'lighting':
        return Icons.lightbulb;
      case 'electrical':
        return Icons.electrical_services;
      default:
        return Icons.build_circle;
    }
  }

  static String _getPartDisplayName(String category) {
    switch (category.toLowerCase()) {
      case 'battery':
      case 'batteries':
        return 'بطارية';
      case 'engine':
        return 'محرك';
      case 'transmission':
        return 'ناقل حركة';
      case 'brakes':
        return 'فرامل';
      case 'tires':
      case 'wheels':
        return 'إطارات';
      case 'lights':
      case 'lighting':
        return 'إضاءة';
      case 'electrical':
        return 'كهربائيات';
      default:
        return 'قطعة غيار';
    }
  }
}
