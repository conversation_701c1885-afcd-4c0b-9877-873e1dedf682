import 'package:flutter_riverpod/flutter_riverpod.dart';

extension AsyncValueX<T> on AsyncValue<T> {
  /// Helper method to access the data value of an AsyncValue if available
  AsyncData<T> get asData => this as AsyncData<T>;

  /// Maps an AsyncValue to a new AsyncValue with a different type
  AsyncValue<R> whenData<R>(R Function(T value) cb) => when(
    data: (data) => AsyncData(cb(data)),
    error: AsyncError<R>.new,
    loading: AsyncLoading<R>.new,
  );

  /// Returns the current value or null if not available or loading
  T? get valueOrNull => when(
    data: (data) => data,
    error: (_, stack) => null,
    loading: () => null,
  );

  /// Returns true if this AsyncValue represents a loading state
  bool get isLoading => this is AsyncLoading;

  /// Returns true if this AsyncValue represents an error state
  bool get isError => this is AsyncError;

  /// Returns the error if this is an AsyncError, null otherwise
  Object? get errorOrNull =>
      maybeWhen(error: (error, _) => error, orElse: () => null);
}
