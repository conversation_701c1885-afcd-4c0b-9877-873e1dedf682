import 'package:logging/logging.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../networking/simple_api_client.dart';

/// Utility class for database maintenance operations
/// 
/// Following CarNow architecture: Flutter → Go API → Supabase
/// This class makes API calls to the Go backend for data integrity operations
class DatabaseMaintenanceUtils {
  static final _logger = Logger('DatabaseMaintenanceUtils');

  /// Get comprehensive health report for products
  static Future<Map<String, dynamic>> getProductsHealthReport({
    required Ref ref,
  }) async {
    try {
      _logger.info('Fetching products health report from backend...');
      
      final apiClient = ref.read(simpleApiClientProvider);
      final response = await apiClient.get(
        '/api/v1/admin/data-integrity/health-report',
      );

      final data = response.data as Map<String, dynamic>;
      _logger.info('Health report fetched successfully');
      return data;
    } catch (e) {
      _logger.severe('Error fetching health report: $e');
      // Forever Plan Architecture: NO MOCK DATA - throw proper error
      throw Exception('Failed to fetch health report from backend: $e');
    }
  }

  /// Fix a specific product with null seller_id
  static Future<bool> fixProductWithNullSellerId(
    String productId, {
    required Ref ref,
  }) async {
    try {
      _logger.info('Fixing product with ID: $productId');
      
      final apiClient = ref.read(simpleApiClientProvider);
      final response = await apiClient.post(
        '/api/v1/admin/data-integrity/fix-product',
        data: {'product_id': productId},
      );

      final data = response.data as Map<String, dynamic>;
      final success = data['success'] as bool? ?? false;
      _logger.info('Product fix result for $productId: $success');
      return success;
    } catch (e) {
      _logger.severe('Error fixing product $productId: $e');
      return false;
    }
  }

  /// Fix all orphaned products (products with null seller_id)
  static Future<Map<String, dynamic>> fixAllOrphanedProducts({
    required Ref ref,
  }) async {
    try {
      _logger.info('Starting fix for all orphaned products...');
      
      final apiClient = ref.read(simpleApiClientProvider);
      final response = await apiClient.post(
        '/api/v1/admin/data-integrity/fix-all-orphaned',
        data: {},
      );

      final data = response.data as Map<String, dynamic>;
      _logger.info('Orphaned products fix completed');
      return data;
    } catch (e) {
      _logger.severe('Error fixing orphaned products: $e');
      // Forever Plan Architecture: NO MOCK DATA - throw proper error
      throw Exception('Failed to fix orphaned products from backend: $e');
    }
  }

  /// Find all products with null seller_id
  static Future<List<String>> findProductsWithNullSellerId({
    required Ref ref,
  }) async {
    try {
      _logger.info('Finding products with null seller_id...');
      
      final apiClient = ref.read(simpleApiClientProvider);
      final response = await apiClient.get(
        '/api/v1/admin/data-integrity/orphaned-products',
      );

      final data = response.data as Map<String, dynamic>;
      final productIds = (data['product_ids'] as List?)
          ?.map((id) => id.toString())
          .toList() ?? [];
      
      _logger.info('Found ${productIds.length} orphaned products');
      return productIds;
    } catch (e) {
      _logger.severe('Error finding orphaned products: $e');
      return []; // Return empty list on error
    }
  }

  /// Print health report to console
  static void printHealthReport(Map<String, dynamic> report) {
    _logger.info('=== Data Integrity Health Report ===');
    _logger.info('Health Score: ${report['health_score_percentage']}%');
    _logger.info('Total Products: ${report['total_products']}');
    _logger.info('Products with null seller_id: ${report['products_with_null_seller_id']}');
    _logger.info('Active Products: ${report['active_products']}');
    _logger.info('Inactive Products: ${report['inactive_products']}');
    
    final issues = report['issues'] as List? ?? [];
    if (issues.isNotEmpty) {
      _logger.info('Issues Found:');
      for (final issue in issues) {
        _logger.info('  • $issue');
      }
    }
    
    final recommendations = report['recommendations'] as List? ?? [];
    if (recommendations.isNotEmpty) {
      _logger.info('Recommendations:');
      for (final rec in recommendations) {
        _logger.info('  • $rec');
      }
    }
    _logger.info('===================================');
  }

  // ❌ REMOVED: All mock data methods - violates ZERO MOCK DATA POLICY
  // ✅ REQUIRED: Use real database maintenance data from Supabase via Go backend
  // ✅ ARCHITECTURE: Flutter UI Only → Go API → Supabase Data
  //
  // Mock data methods have been removed to comply with Forever Plan Architecture.
  // All database maintenance data must come from real database via Go backend endpoints.
} 