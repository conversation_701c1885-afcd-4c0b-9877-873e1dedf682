import 'dart:async';
import 'package:flutter/material.dart';
import 'package:logging/logging.dart';

final _logger = Logger('PerformanceOptimizer');

/// مُحسِّن الأداء للتطبيق
class PerformanceOptimizer {
  static final Map<String, Timer> _debouncers = {};
  static final Map<String, dynamic> _requestCache = {};
  static final Map<String, DateTime> _cacheTimestamps = {};
  static const Duration _defaultCacheDuration = Duration(minutes: 2);

  /// تجميع الطلبات المتشابهة (Request Deduplication)
  static final Map<String, Completer<dynamic>> _pendingRequests = {};

  /// إلغاء التكرار للطلبات المتشابهة
  static Future<T> deduplicateRequest<T>(
    String key,
    Future<T> Function() request,
  ) async {
    // إذا كان هناك طلب معلق بنفس المفتاح، انتظر نتيجته
    if (_pendingRequests.containsKey(key)) {
      _logger.info('🔄 Request deduplication: waiting for existing request [$key]');
      return await _pendingRequests[key]!.future as T;
    }

    // إنشاء طلب جديد
    final completer = Completer<T>();
    _pendingRequests[key] = completer as Completer<dynamic>;

    try {
      _logger.info('🚀 New request started [$key]');
      final result = await request();
      completer.complete(result);
      return result;
    } catch (error) {
      completer.completeError(error);
      rethrow;
    } finally {
      _pendingRequests.remove(key);
    }
  }

  /// تأخير التنفيذ (Debouncing)
  static void debounce(
    String key,
    VoidCallback callback, {
    Duration delay = const Duration(milliseconds: 300),
  }) {
    _debouncers[key]?.cancel();
    _debouncers[key] = Timer(delay, () {
      callback();
      _debouncers.remove(key);
    });
  }

  /// تخزين مؤقت ذكي
  static void cacheData(
    String key,
    dynamic data, {
    Duration? ttl,
  }) {
    _requestCache[key] = data;
    _cacheTimestamps[key] = DateTime.now();
    _logger.info('💾 Data cached [$key]');
  }

  /// استرجاع البيانات من التخزين المؤقت
  static T? getCachedData<T>(
    String key, {
    Duration? ttl,
  }) {
    final timestamp = _cacheTimestamps[key];
    if (timestamp == null) return null;

    final maxAge = ttl ?? _defaultCacheDuration;
    if (DateTime.now().difference(timestamp) > maxAge) {
      _requestCache.remove(key);
      _cacheTimestamps.remove(key);
      _logger.info('⏰ Cache expired [$key]');
      return null;
    }

    _logger.info('🎯 Cache hit [$key]');
    return _requestCache[key] as T?;
  }

  /// تحميل البيانات مع تخزين مؤقت ذكي
  static Future<T> loadWithCache<T>(
    String key,
    Future<T> Function() loader, {
    Duration? ttl,
  }) async {
    // محاولة الحصول على البيانات من التخزين المؤقت
    final cached = getCachedData<T>(key, ttl: ttl);
    if (cached != null) {
      return cached;
    }

    // تحميل البيانات الجديدة مع منع التكرار
    return deduplicateRequest<T>(key, () async {
      final data = await loader();
      cacheData(key, data, ttl: ttl);
      return data;
    });
  }

  /// تحميل متوازي للبيانات المتعددة
  static Future<List<T>> loadConcurrently<T>(
    List<Future<T> Function()> loaders,
  ) async {
    _logger.info('🔀 Loading ${loaders.length} requests concurrently');
    final stopwatch = Stopwatch()..start();
    
    try {
      final results = await Future.wait(
        loaders.map((loader) => loader()),
      );
      
      stopwatch.stop();
      _logger.info('✅ Concurrent loading completed in ${stopwatch.elapsedMilliseconds}ms');
      
      return results;
    } catch (e) {
      stopwatch.stop();
      _logger.severe('❌ Concurrent loading failed after ${stopwatch.elapsedMilliseconds}ms: $e');
      rethrow;
    }
  }

  /// تحسين التمرير
  static Widget optimizeScrolling({
    required Widget child,
    bool cacheExtent = true,
    bool shrinkWrap = false,
  }) {
    return NotificationListener<ScrollNotification>(
      onNotification: (notification) {
        if (notification is ScrollStartNotification) {
          // إيقاف العمليات غير الضرورية أثناء التمرير
          _pauseNonCriticalOperations();
        } else if (notification is ScrollEndNotification) {
          // استئناف العمليات بعد انتهاء التمرير
          _resumeNonCriticalOperations();
        }
        return false;
      },
      child: child,
    );
  }

  /// إيقاف العمليات غير الحرجة
  static void _pauseNonCriticalOperations() {
    // يمكن إضافة منطق إيقاف العمليات هنا
    _logger.fine('⏸️ Pausing non-critical operations');
  }

  /// استئناف العمليات غير الحرجة
  static void _resumeNonCriticalOperations() {
    // يمكن إضافة منطق استئناف العمليات هنا
    _logger.fine('▶️ Resuming non-critical operations');
  }

  /// تنظيف التخزين المؤقت
  static void clearCache([String? pattern]) {
    if (pattern != null) {
      final keysToRemove = _requestCache.keys
          .where((key) => key.contains(pattern))
          .toList();
      
      for (final key in keysToRemove) {
        _requestCache.remove(key);
        _cacheTimestamps.remove(key);
      }
      
      _logger.info('🧹 Cache cleared for pattern: $pattern (${keysToRemove.length} items)');
    } else {
      _requestCache.clear();
      _cacheTimestamps.clear();
      _logger.info('🧹 All cache cleared');
    }
  }

  /// إحصائيات الأداء
  static Map<String, dynamic> getPerformanceStats() {
    return {
      'cache_size': _requestCache.length,
      'pending_requests': _pendingRequests.length,
      'active_debouncers': _debouncers.length,
      'cache_keys': _requestCache.keys.toList(),
    };
  }

  /// تنظيف الموارد
  static void dispose() {
    for (final debouncer in _debouncers.values) {
      debouncer.cancel();
    }
    _debouncers.clear();
    clearCache();
    _pendingRequests.clear();
    _logger.info('🗑️ PerformanceOptimizer disposed');
  }
}

/// مخزن للبيانات المؤقتة مع انتهاء صلاحية تلقائي
class SmartCache<T> {

  SmartCache({Duration ttl = const Duration(minutes: 5)}) : _ttl = ttl;
  final Duration _ttl;
  final Map<String, T> _cache = {};
  final Map<String, DateTime> _timestamps = {};
  final Map<String, Timer> _timers = {};

  /// تخزين البيانات
  void put(String key, T value) {
    _cache[key] = value;
    _timestamps[key] = DateTime.now();
    
    // إلغاء المؤقت السابق إن وجد
    _timers[key]?.cancel();
    
    // إنشاء مؤقت جديد للحذف التلقائي
    _timers[key] = Timer(_ttl, () {
      _cache.remove(key);
      _timestamps.remove(key);
      _timers.remove(key);
    });
  }

  /// استرجاع البيانات
  T? get(String key) {
    final timestamp = _timestamps[key];
    if (timestamp == null) return null;

    if (DateTime.now().difference(timestamp) > _ttl) {
      remove(key);
      return null;
    }

    return _cache[key];
  }

  /// حذف البيانات
  void remove(String key) {
    _cache.remove(key);
    _timestamps.remove(key);
    _timers[key]?.cancel();
    _timers.remove(key);
  }

  /// مسح جميع البيانات
  void clear() {
    _cache.clear();
    _timestamps.clear();
    for (final timer in _timers.values) {
      timer.cancel();
    }
    _timers.clear();
  }

  /// الحصول على حجم التخزين المؤقت
  int get size => _cache.length;

  /// التحقق من وجود المفتاح
  bool containsKey(String key) => _cache.containsKey(key);
}
