// ============================================================================
// CarNow Unified Authentication System - Account Lockout Service
// ============================================================================
// File: account_lockout_service.dart
// Description: Account lockout and suspicious activity detection service
// Forever Plan Architecture: Flutter (UI Only) → Go API → Supabase (Data Only)
// Created: 2024-01-20
// ============================================================================

import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../error/app_error.dart';
import '../error/app_error_factory.dart';
import '../networking/simple_api_client.dart';

part 'account_lockout_service.g.dart';

/// Account lockout status
enum LockoutStatus {
  active,      // Account is active and can be used
  locked,      // Account is temporarily locked
  suspended,   // Account is suspended by admin
  banned,      // Account is permanently banned
}

/// Lockout reason
enum LockoutReason {
  failedLogins,           // Too many failed login attempts
  suspiciousActivity,     // Suspicious activity detected
  bruteForceAttempt,      // Brute force attack detected
  multipleDevices,        // Login from too many devices
  geoAnomalies,          // Geographic anomalies
  adminAction,           // Manual admin action
  securityBreach,        // Security breach detected
  rateLimitExceeded,     // Rate limit exceeded
}

/// Failed login attempt record
class FailedAttempt {
  final String email;
  final String ipAddress;
  final String userAgent;
  final DateTime timestamp;
  final String? reason;
  final Map<String, dynamic>? metadata;

  const FailedAttempt({
    required this.email,
    required this.ipAddress,
    required this.userAgent,
    required this.timestamp,
    this.reason,
    this.metadata,
  });

  factory FailedAttempt.fromJson(Map<String, dynamic> json) {
    return FailedAttempt(
      email: json['email'],
      ipAddress: json['ip_address'],
      userAgent: json['user_agent'],
      timestamp: DateTime.parse(json['timestamp']),
      reason: json['reason'],
      metadata: json['metadata'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'email': email,
      'ip_address': ipAddress,
      'user_agent': userAgent,
      'timestamp': timestamp.toIso8601String(),
      'reason': reason,
      'metadata': metadata,
    };
  }
}

/// Account lockout information
class AccountLockout {
  final String email;
  final LockoutStatus status;
  final LockoutReason reason;
  final DateTime lockedAt;
  final DateTime? unlockAt;
  final int attemptCount;
  final String? adminNote;
  final List<FailedAttempt> recentAttempts;

  const AccountLockout({
    required this.email,
    required this.status,
    required this.reason,
    required this.lockedAt,
    this.unlockAt,
    required this.attemptCount,
    this.adminNote,
    required this.recentAttempts,
  });

  bool get isLocked => status == LockoutStatus.locked;
  bool get isSuspended => status == LockoutStatus.suspended;
  bool get isBanned => status == LockoutStatus.banned;
  bool get isActive => status == LockoutStatus.active;

  Duration? get remainingLockTime {
    if (unlockAt == null) return null;
    final now = DateTime.now();
    return unlockAt!.isAfter(now) ? unlockAt!.difference(now) : Duration.zero;
  }

  factory AccountLockout.fromJson(Map<String, dynamic> json) {
    return AccountLockout(
      email: json['email'],
      status: LockoutStatus.values.firstWhere(
        (s) => s.name == json['status'],
        orElse: () => LockoutStatus.active,
      ),
      reason: LockoutReason.values.firstWhere(
        (r) => r.name == json['reason'],
        orElse: () => LockoutReason.failedLogins,
      ),
      lockedAt: DateTime.parse(json['locked_at']),
      unlockAt: json['unlock_at'] != null ? DateTime.parse(json['unlock_at']) : null,
      attemptCount: json['attempt_count'] ?? 0,
      adminNote: json['admin_note'],
      recentAttempts: (json['recent_attempts'] as List? ?? [])
          .map((a) => FailedAttempt.fromJson(a))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'email': email,
      'status': status.name,
      'reason': reason.name,
      'locked_at': lockedAt.toIso8601String(),
      'unlock_at': unlockAt?.toIso8601String(),
      'attempt_count': attemptCount,
      'admin_note': adminNote,
      'recent_attempts': recentAttempts.map((a) => a.toJson()).toList(),
    };
  }
}

/// Lockout configuration
class LockoutConfig {
  final int maxFailedAttempts;
  final Duration lockoutDuration;
  final Duration attemptWindow;
  final bool enableProgressiveLockout;
  final List<Duration> progressiveDurations;
  final int maxAttemptsPerIP;
  final Duration ipLockoutDuration;
  final bool enableSuspiciousActivityDetection;
  final int maxDevicesPerUser;
  final double geoAnomalyThreshold;
  final bool enableBruteForceDetection;
  final int bruteForceThreshold;
  final Duration bruteForceWindow;

  const LockoutConfig({
    this.maxFailedAttempts = 5,
    this.lockoutDuration = const Duration(minutes: 15),
    this.attemptWindow = const Duration(minutes: 30),
    this.enableProgressiveLockout = true,
    this.progressiveDurations = const [
      Duration(minutes: 5),   // 1st lockout
      Duration(minutes: 15),  // 2nd lockout
      Duration(hours: 1),     // 3rd lockout
      Duration(hours: 4),     // 4th lockout
      Duration(hours: 24),    // 5th+ lockout
    ],
    this.maxAttemptsPerIP = 20,
    this.ipLockoutDuration = const Duration(hours: 1),
    this.enableSuspiciousActivityDetection = true,
    this.maxDevicesPerUser = 10,
    this.geoAnomalyThreshold = 0.8,
    this.enableBruteForceDetection = true,
    this.bruteForceThreshold = 50,
    this.bruteForceWindow = const Duration(minutes: 10),
  });

  /// Default configuration for CarNow
  static const carNow = LockoutConfig(
    maxFailedAttempts: 3,
    lockoutDuration: Duration(minutes: 10),
    attemptWindow: Duration(minutes: 15),
    enableProgressiveLockout: true,
    maxAttemptsPerIP: 15,
    ipLockoutDuration: Duration(minutes: 30),
    enableSuspiciousActivityDetection: true,
    maxDevicesPerUser: 5,
    enableBruteForceDetection: true,
    bruteForceThreshold: 30,
    bruteForceWindow: Duration(minutes: 5),
  );

  /// Strict configuration for high-security environments
  static const strict = LockoutConfig(
    maxFailedAttempts: 2,
    lockoutDuration: Duration(minutes: 30),
    attemptWindow: Duration(minutes: 10),
    enableProgressiveLockout: true,
    progressiveDurations: [
      Duration(minutes: 30),
      Duration(hours: 2),
      Duration(hours: 8),
      Duration(hours: 24),
      Duration(days: 7),
    ],
    maxAttemptsPerIP: 10,
    ipLockoutDuration: Duration(hours: 2),
    enableSuspiciousActivityDetection: true,
    maxDevicesPerUser: 3,
    geoAnomalyThreshold: 0.9,
    enableBruteForceDetection: true,
    bruteForceThreshold: 20,
    bruteForceWindow: Duration(minutes: 3),
  );
}

/// Account lockout service with suspicious activity detection
class AccountLockoutService {
  final SimpleApiClient _apiClient;
  late SharedPreferences _prefs;
  
  // In-memory cache for recent attempts
  final Map<String, List<FailedAttempt>> _recentAttempts = {};
  final Map<String, List<FailedAttempt>> _ipAttempts = {};
  final Map<String, AccountLockout> _lockoutCache = {};
  
  // Stream controller for lockout events
  final StreamController<AccountLockout> _lockoutController = 
      StreamController<AccountLockout>.broadcast();

  AccountLockoutService(this._apiClient);

  /// Initialize the service
  Future<void> initialize() async {
    _prefs = await SharedPreferences.getInstance();
    await _loadCachedData();
    _startCleanupTimer();
  }

  /// Stream of lockout events
  Stream<AccountLockout> get lockoutStream => _lockoutController.stream;

  /// Record a failed login attempt
  Future<AppResult<void>> recordFailedAttempt({
    required String email,
    required String ipAddress,
    required String userAgent,
    String? reason,
    Map<String, dynamic>? metadata,
    LockoutConfig config = LockoutConfig.carNow,
  }) async {
    try {
      final attempt = FailedAttempt(
        email: email,
        ipAddress: ipAddress,
        userAgent: userAgent,
        timestamp: DateTime.now(),
        reason: reason,
        metadata: metadata,
      );

      // Add to local cache
      _recentAttempts.putIfAbsent(email, () => []).add(attempt);
      _ipAttempts.putIfAbsent(ipAddress, () => []).add(attempt);

      // Clean old attempts
      _cleanOldAttempts(config);

      // Check if account should be locked
      final shouldLock = await _shouldLockAccount(email, config);
      if (shouldLock.shouldLock) {
        await _lockAccount(
          email: email,
          reason: shouldLock.reason,
          config: config,
          metadata: metadata,
        );
      }

      // Check for IP-based lockout
      await _checkIPLockout(ipAddress, config);

      // Check for suspicious activity
      if (config.enableSuspiciousActivityDetection) {
        await _checkSuspiciousActivity(email, attempt, config);
      }

      // Check for brute force attack
      if (config.enableBruteForceDetection) {
        await _checkBruteForceAttack(ipAddress, config);
      }

      // Send to server for logging
      await _logFailedAttempt(attempt);

      return const AppResult.success(null);
    } catch (e, stackTrace) {
      final error = AppErrorFactory.fromException(
        e is Exception ? e : Exception(e.toString()),
        stackTrace: stackTrace,
      );
      return AppResult.failure(error);
    }
  }

  /// Check if account is currently locked
  Future<AppResult<AccountLockout?>> checkAccountLockout(String email) async {
    try {
      // Check local cache first
      final cachedLockout = _lockoutCache[email];
      if (cachedLockout != null) {
        // Check if lockout has expired
        if (cachedLockout.unlockAt != null && 
            DateTime.now().isAfter(cachedLockout.unlockAt!)) {
          await _unlockAccount(email);
          return const AppResult.success(null);
        }
        return AppResult.success(cachedLockout);
      }

      // Check with server
      final response = await _apiClient.get('/auth/lockout/$email');
      if (!response.isSuccess) {
        // If not found, account is not locked
        return const AppResult.success(null);
      }

      final lockout = AccountLockout.fromJson(response.data);
      _lockoutCache[email] = lockout;

      return AppResult.success(lockout);
    } catch (e, stackTrace) {
      final error = AppErrorFactory.fromException(
        e is Exception ? e : Exception(e.toString()),
        stackTrace: stackTrace,
      );
      return AppResult.failure(error);
    }
  }

  /// Unlock an account manually (admin function)
  Future<AppResult<void>> unlockAccount(
    String email, {
    String? adminNote,
  }) async {
    try {
      final response = await _apiClient.post('/auth/lockout/$email/unlock', data: {
        'admin_note': adminNote,
        'unlocked_at': DateTime.now().toIso8601String(),
      });

      if (response.isSuccess) {
        await _unlockAccount(email);
        return const AppResult.success(null);
      } else {
        return AppResult.failure(AppError(
          type: AppErrorType.unauthorized,
          code: 'UNLOCK_FAILED',
          message: response.error ?? 'Failed to unlock account',
          messageAr: 'فشل في إلغاء قفل الحساب',
        ));
      }
    } catch (e, stackTrace) {
      final error = AppErrorFactory.fromException(
        e is Exception ? e : Exception(e.toString()),
        stackTrace: stackTrace,
      );
      return AppResult.failure(error);
    }
  }

  /// Get lockout statistics
  Future<AppResult<Map<String, dynamic>>> getLockoutStatistics({
    Duration? period,
  }) async {
    try {
      final periodHours = period?.inHours ?? 24;
      final response = await _apiClient.get(
        '/auth/lockout/statistics',
        queryParameters: {'period_hours': periodHours},
      );

      if (response.isSuccess) {
        return AppResult.success(response.data);
      } else {
        return AppResult.failure(AppError(
          type: AppErrorType.internalServerError,
          code: 'STATISTICS_FAILED',
          message: response.error ?? 'Failed to get lockout statistics',
          messageAr: 'فشل في الحصول على إحصائيات القفل',
        ));
      }
    } catch (e, stackTrace) {
      final error = AppErrorFactory.fromException(
        e is Exception ? e : Exception(e.toString()),
        stackTrace: stackTrace,
      );
      return AppResult.failure(error);
    }
  }

  /// Check if account should be locked
  Future<_LockoutDecision> _shouldLockAccount(
    String email, 
    LockoutConfig config,
  ) async {
    final attempts = _recentAttempts[email] ?? [];
    final recentAttempts = attempts.where((a) => 
        DateTime.now().difference(a.timestamp) <= config.attemptWindow).toList();

    if (recentAttempts.length >= config.maxFailedAttempts) {
      return _LockoutDecision(
        shouldLock: true,
        reason: LockoutReason.failedLogins,
        attemptCount: recentAttempts.length,
      );
    }

    return _LockoutDecision(shouldLock: false);
  }

  /// Lock an account
  Future<void> _lockAccount({
    required String email,
    required LockoutReason reason,
    required LockoutConfig config,
    Map<String, dynamic>? metadata,
  }) async {
    // Calculate lockout duration
    Duration lockoutDuration;
    if (config.enableProgressiveLockout) {
      final previousLockouts = await _getPreviousLockoutCount(email);
      final durationIndex = min(previousLockouts, config.progressiveDurations.length - 1);
      lockoutDuration = config.progressiveDurations[durationIndex];
    } else {
      lockoutDuration = config.lockoutDuration;
    }

    final unlockAt = DateTime.now().add(lockoutDuration);
    final attempts = _recentAttempts[email] ?? [];

    final lockout = AccountLockout(
      email: email,
      status: LockoutStatus.locked,
      reason: reason,
      lockedAt: DateTime.now(),
      unlockAt: unlockAt,
      attemptCount: attempts.length,
      recentAttempts: attempts,
    );

    // Cache locally
    _lockoutCache[email] = lockout;

    // Send to server
    await _apiClient.post('/auth/lockout', data: {
      'email': email,
      'status': lockout.status.name,
      'reason': lockout.reason.name,
      'locked_at': lockout.lockedAt.toIso8601String(),
      'unlock_at': lockout.unlockAt?.toIso8601String(),
      'attempt_count': lockout.attemptCount,
      'metadata': metadata,
    });

    // Notify listeners
    _lockoutController.add(lockout);

    // Clear failed attempts for this email
    _recentAttempts.remove(email);
  }

  /// Unlock an account
  Future<void> _unlockAccount(String email) async {
    _lockoutCache.remove(email);
    _recentAttempts.remove(email);
    
    // Save to persistent storage
    await _saveCachedData();
  }

  /// Check for IP-based lockout
  Future<void> _checkIPLockout(String ipAddress, LockoutConfig config) async {
    final attempts = _ipAttempts[ipAddress] ?? [];
    final recentAttempts = attempts.where((a) => 
        DateTime.now().difference(a.timestamp) <= config.attemptWindow).toList();

    if (recentAttempts.length >= config.maxAttemptsPerIP) {
      // Implement IP lockout logic here
      print('IP $ipAddress should be locked due to ${recentAttempts.length} attempts');
    }
  }

  /// Check for suspicious activity
  Future<void> _checkSuspiciousActivity(
    String email,
    FailedAttempt attempt,
    LockoutConfig config,
  ) async {
    final attempts = _recentAttempts[email] ?? [];
    
    // Check for multiple devices
    final uniqueUserAgents = attempts.map((a) => a.userAgent).toSet();
    if (uniqueUserAgents.length > config.maxDevicesPerUser) {
      await _lockAccount(
        email: email,
        reason: LockoutReason.multipleDevices,
        config: config,
        metadata: {'unique_devices': uniqueUserAgents.length},
      );
      return;
    }

    // Check for geographic anomalies
    final uniqueIPs = attempts.map((a) => a.ipAddress).toSet();
    if (uniqueIPs.length > 5) { // Threshold for different locations
      await _lockAccount(
        email: email,
        reason: LockoutReason.geoAnomalies,
        config: config,
        metadata: {'unique_ips': uniqueIPs.length},
      );
      return;
    }

    // Check for rapid attempts from different sources
    final last5Minutes = attempts.where((a) => 
        DateTime.now().difference(a.timestamp) <= const Duration(minutes: 5)).toList();
    
    if (last5Minutes.length > 10) {
      await _lockAccount(
        email: email,
        reason: LockoutReason.suspiciousActivity,
        config: config,
        metadata: {'rapid_attempts': last5Minutes.length},
      );
    }
  }

  /// Check for brute force attack
  Future<void> _checkBruteForceAttack(String ipAddress, LockoutConfig config) async {
    final attempts = _ipAttempts[ipAddress] ?? [];
    final recentAttempts = attempts.where((a) => 
        DateTime.now().difference(a.timestamp) <= config.bruteForceWindow).toList();

    if (recentAttempts.length >= config.bruteForceThreshold) {
      // Log brute force attack
      await _apiClient.post('/auth/security/brute-force', data: {
        'ip_address': ipAddress,
        'attempt_count': recentAttempts.length,
        'window_minutes': config.bruteForceWindow.inMinutes,
        'timestamp': DateTime.now().toIso8601String(),
      });
    }
  }

  /// Get previous lockout count for progressive lockout
  Future<int> _getPreviousLockoutCount(String email) async {
    final key = 'lockout_count_$email';
    return _prefs.getInt(key) ?? 0;
  }

  /// Clean old attempts from memory
  void _cleanOldAttempts(LockoutConfig config) {
    final cutoff = DateTime.now().subtract(config.attemptWindow);
    
    _recentAttempts.forEach((email, attempts) {
      attempts.removeWhere((a) => a.timestamp.isBefore(cutoff));
    });
    
    _ipAttempts.forEach((ip, attempts) {
      attempts.removeWhere((a) => a.timestamp.isBefore(cutoff));
    });
    
    // Remove empty entries
    _recentAttempts.removeWhere((_, attempts) => attempts.isEmpty);
    _ipAttempts.removeWhere((_, attempts) => attempts.isEmpty);
  }

  /// Log failed attempt to server
  Future<void> _logFailedAttempt(FailedAttempt attempt) async {
    try {
      final response = await _apiClient.post('/auth/audit/failed-attempt', data: attempt.toJson());
      if (!response.isSuccess) {
        print('Failed to log attempt: ${response.error}');
      }
    } catch (e) {
      // Logging failed, but don't break the main flow
      print('Failed to log attempt: $e');
    }
  }

  /// Load cached data from storage
  Future<void> _loadCachedData() async {
    try {
      final lockoutData = _prefs.getString('lockout_cache');
      if (lockoutData != null) {
        final Map<String, dynamic> data = jsonDecode(lockoutData);
        data.forEach((email, lockoutJson) {
          _lockoutCache[email] = AccountLockout.fromJson(lockoutJson);
        });
      }
    } catch (e) {
      print('Failed to load cached lockout data: $e');
    }
  }

  /// Save cached data to storage
  Future<void> _saveCachedData() async {
    try {
      final data = <String, dynamic>{};
      _lockoutCache.forEach((email, lockout) {
        data[email] = lockout.toJson();
      });
      await _prefs.setString('lockout_cache', jsonEncode(data));
    } catch (e) {
      print('Failed to save cached lockout data: $e');
    }
  }

  /// Start cleanup timer
  void _startCleanupTimer() {
    Timer.periodic(const Duration(minutes: 5), (_) {
      _cleanOldAttempts(LockoutConfig.carNow);
    });
  }

  /// Dispose resources
  void dispose() {
    _lockoutController.close();
  }
}

/// Internal class for lockout decision
class _LockoutDecision {
  final bool shouldLock;
  final LockoutReason reason;
  final int attemptCount;

  const _LockoutDecision({
    required this.shouldLock,
    this.reason = LockoutReason.failedLogins,
    this.attemptCount = 0,
  });
}

/// Riverpod provider for AccountLockoutService
@riverpod
AccountLockoutService accountLockoutService(Ref ref) {
  final apiClient = ref.read(simpleApiClientProvider);
  final service = AccountLockoutService(apiClient);
  
  // Initialize the service
  service.initialize();
  
  // Dispose when provider is disposed
  ref.onDispose(() => service.dispose());
  
  return service;
}
