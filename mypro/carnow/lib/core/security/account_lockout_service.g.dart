// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'account_lockout_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$accountLockoutServiceHash() =>
    r'f8b62b3aa137e7f9bdfe30b20753ff1bd77a3ac7';

/// Riverpod provider for AccountLockoutService
///
/// Copied from [accountLockoutService].
@ProviderFor(accountLockoutService)
final accountLockoutServiceProvider =
    AutoDisposeProvider<AccountLockoutService>.internal(
      accountLockoutService,
      name: r'accountLockoutServiceProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$accountLockoutServiceHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef AccountLockoutServiceRef =
    AutoDisposeProviderRef<AccountLockoutService>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
