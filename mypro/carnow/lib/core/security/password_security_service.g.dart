// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'password_security_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$passwordSecurityServiceHash() =>
    r'e6edf73bbfc9e33b1a78e3f7df871576cbd9758a';

/// Riverpod provider for PasswordSecurityService
///
/// Copied from [passwordSecurityService].
@ProviderFor(passwordSecurityService)
final passwordSecurityServiceProvider =
    AutoDisposeProvider<PasswordSecurityService>.internal(
      passwordSecurityService,
      name: r'passwordSecurityServiceProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$passwordSecurityServiceHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef PasswordSecurityServiceRef =
    AutoDisposeProviderRef<PasswordSecurityService>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
