# Local Development Configuration Override

app:
  environment: "development"
  debug: true

# Database configuration for GORM (Supabase PostgreSQL)
database:
  host: "aws-0-eu-central-1.pooler.supabase.com"
  port: 6543
  username: "postgres.lpxtghyvxuenyyisrrro"
  password: "9uS2LhnExynEJNWR"
  database: "postgres"
  ssl_mode: "require"
  max_open_conns: 5
  max_idle_conns: 2
  conn_max_lifetime: "30m"
  conn_max_idle_time: "10m"

# Supabase configuration for local development
supabase:
  url: "https://lpxtghyvxuenyyisrrro.supabase.co"
  anon_key: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImxweHRnaHl2eHVlbnl5aXNycnJvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI2MjIxNzgsImV4cCI6MjA2ODE5ODE3OH0.7GQrSfJwLQskYOcu9nusXF1amMnezpw3s96sk-zHQ64"
  service_role_key: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImxweHRnaHl2eHVlbnl5aXNycnJvIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MjYyMjE3OCwiZXhwIjoyMDY4MTk4MTc4fQ.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8"  # Replace with actual service role key
  jwt_secret: "lpy6EINYAXN45OdylhQ3ydgYu+Z99B2Z3R/iN7VZiyAIzm09pAevxPL8uN6HIaBLHtbje0xhoVqpBs4dOMdt/A=="  # Generated secure JWT secret

# Google OAuth configuration for development
google:
  client_id: "630980378491-vbd1bsp32o4g0664pmt1mhbj9raccnem.apps.googleusercontent.com"
  client_secret: "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"  # Development client secret
  android_client_id: "630980378491-vbd1bsp32o4g0664pmt1mhbj9raccnem.apps.googleusercontent.com"
  web_redirect_url: "https://lpxtghyvxuenyyisrrro.supabase.co/auth/v1/callback"
  mobile_redirect_url: "https://lpxtghyvxuenyyisrrro.supabase.co/auth/v1/callback"

# Temporary development keys (replace in production)
jwt:
  secret: "lpy6EINYAXN45OdylhQ3ydgYu+Z99B2Z3R/iN7VZiyAIzm09pAevxPL8uN6HIaBLHtbje0xhoVqpBs4dOMdt/A=="
  issuer: "carnow-backend"
  audience: "carnow-app"

security:
  encryption_key: "0WZJmpvU1LP5S6fzBRrCBkx/CxBESsj9yv30AUERC5HSyJJyougxkmUp8OLDDTdup5AgkmCUoQvLHJldsdi3aQ=="
  rate_limit_requests: 100
  rate_limit_window: "1m"
  rate_limit:
    enabled: true
    requests_limit: 100
    window_size: 60
    login_attempts: 5
    register_attempts: 3
    window_minutes: 15
  cors:
    enabled: true
    allowed_origins:
      - "*"

# Redis configuration for local development (graceful fallback)
redis:
  enabled: false  # Disabled for development - graceful fallback mode
  addrs:
    - "localhost:6379"
  password: ""
  db: 0
  pool_size: 5
  min_idle_conns: 2
  max_retries: 3
  dial_timeout: "5s"
  read_timeout: "3s"
  write_timeout: "3s"
  idle_timeout: "5m"
  default_ttl: "30m"  # Shorter TTL for development
  key_prefix: "carnow:dev:"



logging:
  level: "debug"
  format: "text"