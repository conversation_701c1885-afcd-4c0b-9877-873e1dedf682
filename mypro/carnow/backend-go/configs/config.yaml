# CarNow Backend Configuration (Forever Plan - Production Ready)

app:
  name: "CarNow Backend"
  version: "1.0.0"
  environment: "production"
  debug: false
  timezone: "Africa/Tripoli"

server:
  host: "0.0.0.0"
  port: 8080
  read_timeout: "30s"
  write_timeout: "30s"
  idle_timeout: "60s"
  max_header_bytes: 1048576
  graceful_timeout: "15s"

database:
  host: "aws-0-eu-central-1.pooler.supabase.com"
  port: 6543
  username: "postgres.lpxtghyvxuenyyisrrro"
  database: "postgres"
  ssl_mode: "require"
  max_open_conns: 10
  max_idle_conns: 5
  conn_max_lifetime: "1h"
  conn_max_idle_time: "30m"

# Redis Cache Configuration (Task 2.1: Redis Cache Infrastructure)
# Disabled for development - Redis is optional (Forever Plan)
redis:
  enabled: false
  addrs:
    - "localhost:6379"
  password: ""
  db: 0
  pool_size: 10
  min_idle_conns: 5
  max_retries: 3
  dial_timeout: "5s"
  read_timeout: "3s"
  write_timeout: "3s"
  idle_timeout: "5m"
  default_ttl: "1h"
  key_prefix: "carnow:"

supabase:
  url: "https://lpxtghyvxuenyyisrrro.supabase.co"
  anon_key: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImxweHRnaHl2eHVlbnl5aXNycnJvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI2MjIxNzgsImV4cCI6MjA2ODE5ODE3OH0.7GQrSfJwLQskYOcu9nusXF1amMnezpw3s96sk-zHQ64"
  service_role_key: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImxweHRnaHl2eHVlbnl5aXNycnJvIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MjYyMjE3OCwiZXhwIjoyMDY4MTk4MTc4fQ.A7CQuCdCJbImUKTuon-voUTchVky4Quo4OQtq94ElaM"
  jwt_secret: "cmk11gkJgFX6BKiIOioVpWfHY4ZZ7RRR6xTWyy7/wOlK6ZoRyFYlS+2hyQIS+OBt3ef5D1z9HVFYWoDJwwD4eQ=="
  project_ref: "lpxtghyvxuenyyisrrro"

google:
  client_id: "630980378491-vbd1bsp32o4g0664pmt1mhbj9raccnem.apps.googleusercontent.com"
  client_secret: "" # Will be loaded from environment variable CARNOW_GOOGLE_CLIENT_SECRET
  android_client_id: "630980378491-vbd1bsp32o4g0664pmt1mhbj9raccnem.apps.googleusercontent.com"
  web_redirect_url: "https://lpxtghyvxuenyyisrrro.supabase.co/auth/v1/callback"
  mobile_redirect_url: "https://lpxtghyvxuenyyisrrro.supabase.co/auth/v1/callback"

jwt:
  secret: ""
  expires_in: "15m"
  refresh_expires_in: "168h"
  issuer: "carnow-backend"
  audience: "carnow-app"
  algorithm: "RS256"

security:
  encryption_key: ""
  rate_limit_requests: 100
  rate_limit_window: "1m"
  cors_allowed_origins:
    - "*"
  cors_allowed_methods:
    - "GET"
    - "POST"
    - "PUT"
    - "DELETE"
    - "OPTIONS"
  cors_allowed_headers:
    - "Origin"
    - "Content-Type"
    - "Accept"
    - "Authorization"
  cors_allow_credentials: true
  # Rate limiting configuration
  rate_limit:
    enabled: true
    requests_limit: 100
    window_size: 60
    login_attempts: 5
    register_attempts: 3
    window_minutes: 15
  # CORS configuration
  cors:
    enabled: true
    allowed_origins:
      - "*"

logging:
  level: "info"
  format: "json"
  output: "stdout"
  max_size: 100
  max_backups: 3
  max_age: 28
  compress: true

features:
  enable_swagger: true
  enable_metrics: true
  enable_health_checks: true
  enable_graceful_shutdown: true
  # Forever Plan: These must remain false
  enable_complex_auth: false
  enable_dual_database: false
  enable_sync_services: false
  enable_enhanced_features: false