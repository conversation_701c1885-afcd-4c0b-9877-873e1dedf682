version: '3.8'

services:
  redis:
    image: redis:7-alpine
    container_name: carnow-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - carnow-network

  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: carnow-redis-commander
    environment:
      - REDIS_HOSTS=local:redis:6379
    ports:
      - "8081:8081"
    depends_on:
      - redis
    networks:
      - carnow-network

volumes:
  redis_data:
    driver: local

networks:
  carnow-network:
    driver: bridge