//go:build generate_jwt
// +build generate_jwt

package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"os"
	"time"
)

type SignInRequest struct {
	Email    string `json:"email"`
	Password string `json:"password"`
}

type AuthResponse struct {
	User         map[string]interface{} `json:"user"`
	Session      Session                `json:"session"`
	AccessToken  string                 `json:"access_token"`
	RefreshToken string                 `json:"refresh_token"`
}

type Session struct {
	AccessToken  string                 `json:"access_token"`
	RefreshToken string                 `json:"refresh_token"`
	ExpiresIn    int                    `json:"expires_in"`
	TokenType    string                 `json:"token_type"`
	User         map[string]interface{} `json:"user"`
}

func main() {
	// Supabase project URL and key for Carnow - UPDATED TO CORRECT KEY
	supabaseURL := "https://lpxtghyvxuenyyisrrro.supabase.co"
	supabaseKey := "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImxweHRnaHl2eHVlbnl5aXNycnJvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI2MjIxNzgsImV4cCI6MjA2ODE5ODE3OH0.7GQrSfJwLQskYOcu9nusXF1amMnezpw3s96sk-zHQ64"

	if len(os.Args) < 3 {
		fmt.Println("🔑 JWT Token Generator for Carnow Project")
		fmt.Println("=========================================")
		fmt.Println("Usage: go run tools/generate_test_jwt.go <email> <password>")
		fmt.Println("Example: go run tools/generate_test_jwt.go <EMAIL> mypassword")
		os.Exit(1)
	}

	email := os.Args[1]
	password := os.Args[2]

	fmt.Println("🔑 JWT Token Generator for Carnow Project")
	fmt.Println("=========================================")
	fmt.Printf("📧 Attempting to sign in: %s\n", email)

	// Create sign-in request
	signInReq := SignInRequest{
		Email:    email,
		Password: password,
	}

	jsonData, err := json.Marshal(signInReq)
	if err != nil {
		fmt.Printf("❌ Error marshaling request: %v\n", err)
		os.Exit(1)
	}

	// Make sign-in request to Supabase
	url := fmt.Sprintf("%s/auth/v1/token?grant_type=password", supabaseURL)

	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		fmt.Printf("❌ Error creating request: %v\n", err)
		os.Exit(1)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("apikey", supabaseKey)
	req.Header.Set("Authorization", "Bearer "+supabaseKey)

	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		fmt.Printf("❌ Error making request: %v\n", err)
		os.Exit(1)
	}
	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("❌ Error reading response: %v\n", err)
		os.Exit(1)
	}

	if resp.StatusCode != 200 {
		fmt.Printf("❌ Sign-in failed (status %d): %s\n", resp.StatusCode, string(body))
		os.Exit(1)
	}

	var authResp AuthResponse
	if err := json.Unmarshal(body, &authResp); err != nil {
		fmt.Printf("❌ Error parsing response: %v\n", err)
		os.Exit(1)
	}

	// Extract access token
	accessToken := authResp.Session.AccessToken
	if accessToken == "" {
		fmt.Printf("❌ No access token in response\n")
		os.Exit(1)
	}

	fmt.Println("✅ Sign-in successful!")
	fmt.Printf("📧 Email: %s\n", email)
	fmt.Printf("👤 User ID: %v\n", authResp.Session.User["id"])
	fmt.Printf("🔑 Access Token (length: %d):\n", len(accessToken))
	fmt.Printf("%s\n", accessToken)
	fmt.Println("\n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
	fmt.Println("💡 You can now test this token with:")
	fmt.Printf("go run test_jwt_debug.go \"%s\"\n", accessToken)
	fmt.Println("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")

	// Also test the token immediately
	fmt.Println("\n🧪 Testing the generated token...")
	fmt.Println("==================================")

	// Create a simple test
	testURL := "https://backend-go-8klm.onrender.com/api/v1/users/email/" + email
	testReq, err := http.NewRequest("GET", testURL, nil)
	if err != nil {
		fmt.Printf("❌ Error creating test request: %v\n", err)
		return
	}

	testReq.Header.Set("Authorization", "Bearer "+accessToken)
	testResp, err := client.Do(testReq)
	if err != nil {
		fmt.Printf("❌ Error making test request: %v\n", err)
		return
	}
	defer testResp.Body.Close()

	testBody, _ := ioutil.ReadAll(testResp.Body)

	fmt.Printf("📡 Test GET request to: %s\n", testURL)
	fmt.Printf("🔢 Response status: %d\n", testResp.StatusCode)
	fmt.Printf("📄 Response body: %s\n", string(testBody))

	if testResp.StatusCode == 200 {
		fmt.Println("✅ Token works correctly!")
	} else {
		fmt.Println("❌ Token validation failed!")
	}
}
