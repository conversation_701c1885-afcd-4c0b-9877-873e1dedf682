package domain

import (
	"time"
)

// SubscriptionPlan represents a subscription plan for sellers.
// @Description SubscriptionPlan represents a subscription plan for sellers, including pricing, features, and other details.
// @gorm:table subscription_plans
type SubscriptionPlan struct {
	ID                   uint      `json:"id" gorm:"primaryKey"`
	Name                 string    `json:"name" gorm:"not null"`
	NameAr               string    `json:"name_ar" gorm:"column:name_ar"`
	NameEn               string    `json:"name_en" gorm:"column:name_en"`
	Description          string    `json:"description"`
	DescriptionAr        string    `json:"description_ar" gorm:"column:description_ar"`
	DescriptionEn        string    `json:"description_en" gorm:"column:description_en"`
	PriceMonthly         float64   `json:"price_monthly" gorm:"column:price_monthly;not null"`
	PriceYearly          float64   `json:"price_yearly" gorm:"column:price_yearly;not null"`
	MaxListings          int       `json:"max_listings" gorm:"column:max_listings;not null"`
	Popular              bool      `json:"popular" gorm:"column:popular;default:false"`
	Currency             string    `json:"currency" gorm:"default:'LYD'"`
	Features             []string  `json:"features" gorm:"type:text[]"`
	IsActive             bool      `json:"is_active" gorm:"column:is_active;default:true"`
	Tier                 string    `json:"tier" gorm:"default:'basic'"`
	AdditionalListingFee float64   `json:"additional_listing_fee" gorm:"column:additional_listing_fee;default:0"`
	TrialDays            int       `json:"trial_days" gorm:"column:trial_days;default:0"`
	PrioritySupport      bool      `json:"priority_support" gorm:"column:priority_support;default:false"`
	DedicatedSupport     bool      `json:"dedicated_support" gorm:"column:dedicated_support;default:false"`
	CreatedAt            time.Time `json:"created_at" gorm:"column:created_at;autoCreateTime"`
	UpdatedAt            time.Time `json:"updated_at" gorm:"column:updated_at;autoUpdateTime"`
}
