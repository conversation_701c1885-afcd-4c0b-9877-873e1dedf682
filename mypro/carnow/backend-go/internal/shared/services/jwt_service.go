package services

import (
	"crypto/rand"
	"crypto/rsa"
	"crypto/x509"
	"encoding/base64"
	"encoding/pem"
	"fmt"
	"log"
	"strings"
	"time"

	"carnow-backend/internal/config"

	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
)

// JWTService handles secure JWT operations with production-ready features
// Task 1.2 Requirement: Implement secure key generation and storage
type JWTService struct {
	config          *config.Config
	privateKey      *rsa.PrivateKey
	publicKey       *rsa.PublicKey
	keyID           string
	revokedTokens   map[string]time.Time // In-memory token blacklist (use Redis in production)
	keyRotationTime time.Time
}

// JWTClaims represents the claims in our JWT tokens
type JWTClaims struct {
	UserID    string `json:"user_id"`
	Email     string `json:"email"`
	Role      string `json:"role"`
	TokenType string `json:"token_type"` // "access" or "refresh"
	jwt.RegisteredClaims
}

// TokenPair represents access and refresh tokens
type TokenPair struct {
	AccessToken  string    `json:"access_token"`
	RefreshToken string    `json:"refresh_token"`
	ExpiresAt    time.Time `json:"expires_at"`
	TokenType    string    `json:"token_type"`
}

// NewJWTService creates a new JWT service with secure configuration
func NewJWTService(cfg *config.Config) (*JWTService, error) {
	service := &JWTService{
		config: cfg,
	}

	// Initialize RSA keys for secure JWT signing
	if err := service.initializeKeys(); err != nil {
		return nil, fmt.Errorf("failed to initialize JWT keys: %w", err)
	}

	return service, nil
}

// initializeKeys sets up RSA keys for JWT signing with enhanced security
// Task 1.2 Requirement: Implement secure key generation and storage
func (j *JWTService) initializeKeys() error {
	// Check if we have a JWT secret configured
	if j.config.JWT.Secret == "" {
		return fmt.Errorf("JWT secret not configured")
	}

	// Initialize revoked tokens map
	j.revokedTokens = make(map[string]time.Time)

	// Generate unique key ID for this instance
	j.keyID = fmt.Sprintf("carnow-jwt-key-%d", time.Now().Unix())

	// Try to load existing keys from environment or generate new ones
	if err := j.loadOrGenerateKeys(); err != nil {
		return fmt.Errorf("failed to initialize JWT keys: %w", err)
	}

	// Set key rotation time (rotate keys every 30 days in production)
	j.keyRotationTime = time.Now().Add(30 * 24 * time.Hour)

	return nil
}

// GenerateTokenPair creates a new access and refresh token pair
func (j *JWTService) GenerateTokenPair(userID, email, role string) (*TokenPair, error) {
	now := time.Now()

	// Generate access token (short-lived: 15 minutes)
	accessClaims := JWTClaims{
		UserID:    userID,
		Email:     email,
		Role:      role,
		TokenType: "access",
		RegisteredClaims: jwt.RegisteredClaims{
			ID:        uuid.New().String(),
			Subject:   userID,
			Audience:  []string{j.config.JWT.Audience},
			Issuer:    j.config.JWT.Issuer,
			IssuedAt:  jwt.NewNumericDate(now),
			NotBefore: jwt.NewNumericDate(now),
			ExpiresAt: jwt.NewNumericDate(now.Add(15 * time.Minute)), // Short-lived
		},
	}

	accessToken, err := j.signToken(accessClaims)
	if err != nil {
		return nil, fmt.Errorf("failed to sign access token: %w", err)
	}

	// Generate refresh token (long-lived: 7 days)
	refreshClaims := JWTClaims{
		UserID:    userID,
		Email:     email,
		Role:      role,
		TokenType: "refresh",
		RegisteredClaims: jwt.RegisteredClaims{
			ID:        uuid.New().String(),
			Subject:   userID,
			Audience:  []string{j.config.JWT.Audience},
			Issuer:    j.config.JWT.Issuer,
			IssuedAt:  jwt.NewNumericDate(now),
			NotBefore: jwt.NewNumericDate(now),
			ExpiresAt: jwt.NewNumericDate(now.Add(7 * 24 * time.Hour)), // Long-lived
		},
	}

	refreshToken, err := j.signToken(refreshClaims)
	if err != nil {
		return nil, fmt.Errorf("failed to sign refresh token: %w", err)
	}

	return &TokenPair{
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		ExpiresAt:    accessClaims.ExpiresAt.Time,
		TokenType:    "Bearer",
	}, nil
}

// ValidateToken validates and parses a JWT token
func (j *JWTService) ValidateToken(tokenString string) (*JWTClaims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &JWTClaims{}, func(token *jwt.Token) (interface{}, error) {
		// Verify signing method
		if _, ok := token.Method.(*jwt.SigningMethodRSA); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return j.publicKey, nil
	})

	if err != nil {
		return nil, fmt.Errorf("failed to parse token: %w", err)
	}

	claims, ok := token.Claims.(*JWTClaims)
	if !ok || !token.Valid {
		return nil, fmt.Errorf("invalid token claims")
	}

	// Additional validation
	if err := j.validateClaims(claims); err != nil {
		return nil, fmt.Errorf("invalid claims: %w", err)
	}

	return claims, nil
}

// validateTokenFormat validates the basic format of a JWT token
func (j *JWTService) validateTokenFormat(token string) error {
	if token == "" {
		return fmt.Errorf("token is empty")
	}

	// Basic JWT format validation (3 parts separated by dots)
	parts := strings.Split(token, ".")
	if len(parts) != 3 {
		return fmt.Errorf("invalid JWT format: expected 3 parts, got %d", len(parts))
	}

	// Validate each part is base64 encoded
	for i, part := range parts {
		if len(part) == 0 {
			return fmt.Errorf("JWT part %d is empty", i+1)
		}
		// Try to decode to validate base64 format
		if _, err := base64.RawURLEncoding.DecodeString(part); err != nil {
			return fmt.Errorf("JWT part %d is not valid base64: %w", i+1, err)
		}
	}

	return nil
}

// RefreshToken refreshes an access token using a valid refresh token
func (j *JWTService) RefreshToken(refreshToken string) (*TokenPair, error) {
	// Validate refresh token format and structure
	if err := j.validateTokenFormat(refreshToken); err != nil {
		return nil, fmt.Errorf("invalid refresh token format: %w", err)
	}

	// Check if refresh token is revoked
	if j.IsTokenRevoked(refreshToken) {
		return nil, fmt.Errorf("refresh token has been revoked")
	}

	// Parse and validate refresh token
	token, err := jwt.ParseWithClaims(refreshToken, &JWTClaims{}, func(token *jwt.Token) (interface{}, error) {
		// Validate signing method
		if _, ok := token.Method.(*jwt.SigningMethodRSA); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return &j.privateKey.PublicKey, nil
	})

	if err != nil {
		return nil, fmt.Errorf("invalid refresh token: %w", err)
	}

	// Extract and validate claims
	claims, ok := token.Claims.(*JWTClaims)
	if !ok || !token.Valid {
		return nil, fmt.Errorf("invalid refresh token claims")
	}

	// Validate token type
	if claims.TokenType != "refresh" {
		return nil, fmt.Errorf("token is not a refresh token")
	}

	// Validate token timing
	now := time.Now()
	if claims.ExpiresAt.Before(now) {
		return nil, fmt.Errorf("refresh token has expired")
	}

	if claims.IssuedAt.After(now) {
		return nil, fmt.Errorf("refresh token issued in the future")
	}

	// Validate issuer and audience
	if claims.Issuer != j.config.JWT.Issuer {
		return nil, fmt.Errorf("invalid token issuer")
	}

	if len(claims.Audience) == 0 || claims.Audience[0] != j.config.JWT.Audience {
		return nil, fmt.Errorf("invalid token audience")
	}

	// Check if user ID is valid (basic validation)
	if claims.UserID == "" {
		return nil, fmt.Errorf("invalid user ID in refresh token")
	}

	// Revoke the old refresh token to prevent reuse
	j.RevokeToken(refreshToken)

	// Generate new token pair
	newTokenPair, err := j.GenerateTokenPair(claims.UserID, claims.Email, claims.Role)
	if err != nil {
		return nil, fmt.Errorf("failed to generate new token pair: %w", err)
	}

	log.Printf("Token refreshed successfully for user: %s", claims.UserID)
	return newTokenPair, nil
}

// RevokeToken adds a token to the revocation list
// Task 1.2 Requirement: Add token blacklisting capability for logout functionality
func (j *JWTService) RevokeToken(tokenID string) error {
	if tokenID == "" {
		return fmt.Errorf("token ID cannot be empty")
	}
	
	// Add to in-memory blacklist with expiration time
	// In production, this should use Redis for distributed systems
	j.revokedTokens[tokenID] = time.Now().Add(7 * 24 * time.Hour) // Keep for 7 days
	
	// Clean up expired tokens to prevent memory leaks
	go j.cleanupExpiredTokens()
	
	log.Printf("🚫 JWT: Token revoked: %s", tokenID[:8]+"...")
	return nil
}

// IsTokenRevoked checks if a token has been revoked
// Task 1.2 Requirement: Add token blacklisting capability for logout functionality
func (j *JWTService) IsTokenRevoked(tokenID string) bool {
	if tokenID == "" {
		return false
	}
	
	expiration, exists := j.revokedTokens[tokenID]
	if !exists {
		return false
	}
	
	// Check if the revocation has expired
	if time.Now().After(expiration) {
		delete(j.revokedTokens, tokenID)
		return false
	}
	
	return true
}

// cleanupExpiredTokens removes expired tokens from the blacklist
func (j *JWTService) cleanupExpiredTokens() {
	now := time.Now()
	for tokenID, expiration := range j.revokedTokens {
		if now.After(expiration) {
			delete(j.revokedTokens, tokenID)
		}
	}
}

// loadOrGenerateKeys loads existing keys or generates new RSA keys
// Task 1.2 Requirement: Implement secure key generation and storage
func (j *JWTService) loadOrGenerateKeys() error {
	// TODO: In production, load keys from secure storage (AWS KMS, HashiCorp Vault, etc.)
	// For now, generate new keys each time (acceptable for development)
	
	// Generate RSA keys for enhanced security (4096-bit for production)
	keySize := 2048
	if j.config.App.Environment == "production" {
		keySize = 4096 // Higher security for production
	}
	
	privateKey, err := rsa.GenerateKey(rand.Reader, keySize)
	if err != nil {
		return fmt.Errorf("failed to generate RSA private key: %w", err)
	}
	
	j.privateKey = privateKey
	j.publicKey = &privateKey.PublicKey
	
	log.Printf("✅ JWT: Generated %d-bit RSA keys for %s environment", keySize, j.config.App.Environment)
	return nil
}

// signToken signs a JWT token using RSA private key
// Task 1.2 Requirement: Enhanced token signing with key rotation support
func (j *JWTService) signToken(claims JWTClaims) (string, error) {
	token := jwt.NewWithClaims(jwt.SigningMethodRS256, claims)

	// Add key ID to header for key rotation support
	token.Header["kid"] = j.keyID

	return token.SignedString(j.privateKey)
}

// validateClaims performs additional validation on JWT claims
func (j *JWTService) validateClaims(claims *JWTClaims) error {
	now := time.Now()

	// Check expiration
	if claims.ExpiresAt != nil && claims.ExpiresAt.Before(now) {
		return fmt.Errorf("token has expired")
	}

	// Check not before
	if claims.NotBefore != nil && claims.NotBefore.After(now) {
		return fmt.Errorf("token not valid yet")
	}

	// Check issuer
	if claims.Issuer != j.config.JWT.Issuer {
		return fmt.Errorf("invalid issuer")
	}

	// Check audience
	if len(claims.Audience) == 0 || claims.Audience[0] != j.config.JWT.Audience {
		return fmt.Errorf("invalid audience")
	}

	// Check required fields
	if claims.UserID == "" || claims.Email == "" {
		return fmt.Errorf("missing required claims")
	}

	// Validate UUID format
	if _, err := uuid.Parse(claims.UserID); err != nil {
		return fmt.Errorf("invalid user ID format")
	}

	return nil
}

// GetPublicKeyPEM returns the public key in PEM format for external validation
func (j *JWTService) GetPublicKeyPEM() (string, error) {
	pubKeyBytes, err := x509.MarshalPKIXPublicKey(j.publicKey)
	if err != nil {
		return "", fmt.Errorf("failed to marshal public key: %w", err)
	}

	pubKeyPEM := pem.EncodeToMemory(&pem.Block{
		Type:  "PUBLIC KEY",
		Bytes: pubKeyBytes,
	})

	return string(pubKeyPEM), nil
}

// GenerateSecureSecret generates a cryptographically secure secret
func GenerateSecureSecret() (string, error) {
	bytes := make([]byte, 64) // 512 bits
	if _, err := rand.Read(bytes); err != nil {
		return "", fmt.Errorf("failed to generate secure random bytes: %w", err)
	}
	return base64.StdEncoding.EncodeToString(bytes), nil
}
