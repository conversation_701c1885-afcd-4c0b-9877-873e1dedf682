package services

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestGoogleOAuthService_ProductionValidation tests the enhanced production validation features
// Task 1.1 Requirement: Write unit tests for token verification with real Google API responses
func TestGoogleOAuthService_ProductionValidation(t *testing.T) {
	testCases := []struct {
		name        string
		clientID    string
		clientSecret string
		expectError bool
		errorMsg    string
	}{
		{
			name:         "Valid Google Client ID",
			clientID:     "************-vbd1bsp32o4g0664pmt1mhbj9raccnem.apps.googleusercontent.com",
			clientSecret: "test-secret",
			expectError:  false,
		},
		{
			name:         "Empty Client ID",
			clientID:     "",
			clientSecret: "test-secret",
			expectError:  true,
			errorMsg:     "Google OAuth client ID is required for production",
		},
		{
			name:         "Invalid Client ID Format",
			clientID:     "invalid-client-id",
			clientSecret: "test-secret",
			expectError:  true,
			errorMsg:     "invalid Google OAuth client ID format",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			service, err := NewGoogleOAuthService(tc.clientID, tc.clientSecret)

			if tc.expectError {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tc.errorMsg)
				assert.Nil(t, service)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, service)
				assert.Equal(t, tc.clientID, service.clientID)
				assert.NotNil(t, service.allowedIssuers)
				assert.Contains(t, service.allowedIssuers, "https://accounts.google.com")
			}
		})
	}
}

// TestGoogleOAuthService_TokenInputValidation tests enhanced token input validation
func TestGoogleOAuthService_TokenInputValidation(t *testing.T) {
	service, err := NewGoogleOAuthService("************-vbd1bsp32o4g0664pmt1mhbj9raccnem.apps.googleusercontent.com", "test-secret")
	require.NoError(t, err)

	testCases := []struct {
		name        string
		token       string
		expectError bool
		errorMsg    string
	}{
		{
			name:        "Empty Token",
			token:       "",
			expectError: true,
			errorMsg:    "ID token cannot be empty",
		},
		{
			name:        "Too Short Token",
			token:       "short",
			expectError: true,
			errorMsg:    "token appears to be too short",
		},
		{
			name:        "Valid JWT Format",
			token:       "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************.signature",
			expectError: false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			err := service.validateTokenInput(tc.token)
			if tc.expectError {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tc.errorMsg)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// TestGoogleOAuthService_SecurityValidations tests security validation methods
func TestGoogleOAuthService_SecurityValidations(t *testing.T) {
	service, err := NewGoogleOAuthService("************-vbd1bsp32o4g0664pmt1mhbj9raccnem.apps.googleusercontent.com", "test-secret")
	require.NoError(t, err)

	// Test issuer validation
	t.Run("ValidateIssuer", func(t *testing.T) {
		validClaims := map[string]interface{}{
			"iss": "https://accounts.google.com",
		}
		err := service.validateIssuer(validClaims)
		assert.NoError(t, err)

		invalidClaims := map[string]interface{}{
			"iss": "https://malicious.com",
		}
		err = service.validateIssuer(invalidClaims)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "invalid token issuer")
	})

	// Test audience validation
	t.Run("ValidateAudience", func(t *testing.T) {
		validClaims := map[string]interface{}{
			"aud": service.clientID,
		}
		err := service.validateAudience(validClaims)
		assert.NoError(t, err)

		invalidClaims := map[string]interface{}{
			"aud": "wrong-client-id",
		}
		err = service.validateAudience(invalidClaims)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "invalid token audience")
	})

	// Test token timing validation
	t.Run("ValidateTokenTiming", func(t *testing.T) {
		now := time.Now().Unix()
		validClaims := map[string]interface{}{
			"exp": float64(now + 3600), // expires in 1 hour
			"iat": float64(now - 60),   // issued 1 minute ago
		}
		err := service.validateTokenTiming(validClaims)
		assert.NoError(t, err)

		expiredClaims := map[string]interface{}{
			"exp": float64(now - 3600), // expired 1 hour ago
			"iat": float64(now - 7200), // issued 2 hours ago
		}
		err = service.validateTokenTiming(expiredClaims)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "token has expired")
	})
}
