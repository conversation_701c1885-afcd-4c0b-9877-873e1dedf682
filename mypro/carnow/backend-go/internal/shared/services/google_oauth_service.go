package services

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"regexp"
	"strings"
	"time"

	"google.golang.org/api/idtoken"
	"google.golang.org/api/oauth2/v2"
	"google.golang.org/api/option"
)

// GoogleOAuthService handles Google OAuth token verification with production-ready security
// Task 1.1 Requirement: Remove all mock token processing and implement real Google ID token verification
type GoogleOAuthService struct {
	clientID       string
	clientSecret   string
	validator      *idtoken.Validator
	client         *http.Client
	allowedIssuers []string
	tokenRegex     *regexp.Regexp
}

// GoogleUserInfo represents verified Google user information
type GoogleUserInfo struct {
	ID            string `json:"id"`
	Email         string `json:"email"`
	VerifiedEmail bool   `json:"verified_email"`
	Name          string `json:"name"`
	GivenName     string `json:"given_name"`
	FamilyName    string `json:"family_name"`
	Picture       string `json:"picture"`
	Locale        string `json:"locale"`
}

// NewGoogleOAuthService creates a new production-ready Google OAuth service
// Task 1.1 Requirement: Implement real Google ID token verification with proper audience and issuer validation
func NewGoogleOAuthService(clientID, clientSecret string) (*GoogleOAuthService, error) {
	if clientID == "" {
		return nil, fmt.Errorf("Google OAuth client ID is required for production")
	}

	// Validate client ID format (Google client IDs have specific format)
	if !strings.HasSuffix(clientID, ".apps.googleusercontent.com") {
		return nil, fmt.Errorf("invalid Google OAuth client ID format: must end with .apps.googleusercontent.com")
	}

	// Create HTTP client with production-ready timeouts and security settings
	client := &http.Client{
		Timeout: 15 * time.Second,
		Transport: &http.Transport{
			MaxIdleConns:        10,
			IdleConnTimeout:     30 * time.Second,
			DisableCompression:  false,
			TLSHandshakeTimeout: 10 * time.Second,
			MaxIdleConnsPerHost: 5,
		},
	}

	// Initialize ID token validator for production use
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	validator, err := idtoken.NewValidator(ctx, option.WithHTTPClient(client))
	if err != nil {
		log.Printf("⚠️ GoogleOAuth: Failed to create validator, will use fallback methods: %v", err)
		// Don't fail service creation, just log warning and continue without validator
	}

	// Compile regex for JWT token format validation
	tokenRegex := regexp.MustCompile(`^[A-Za-z0-9_-]+\.[A-Za-z0-9_-]+\.[A-Za-z0-9_-]+$`)

	service := &GoogleOAuthService{
		clientID:       clientID,
		clientSecret:   clientSecret,
		validator:      validator,
		client:         client,
		allowedIssuers: []string{"https://accounts.google.com", "accounts.google.com"},
		tokenRegex:     tokenRegex,
	}

	log.Printf("✅ GoogleOAuth: Production service initialized with client ID: %s", maskClientID(clientID))
	log.Printf("🔒 GoogleOAuth: Security features enabled - audience validation, issuer validation, format validation")
	return service, nil
}

// maskClientID masks the client ID for secure logging
func maskClientID(clientID string) string {
	if len(clientID) <= 20 {
		return "***"
	}
	return clientID[:10] + "***" + clientID[len(clientID)-10:]
}

// VerifyIDToken verifies a Google ID token with production-ready security and validation
// Task 1.1 Requirement: Add proper audience and issuer validation for security
func (g *GoogleOAuthService) VerifyIDToken(ctx context.Context, idToken string) (*GoogleUserInfo, error) {
	log.Printf("🔍 GoogleOAuth: Verifying ID token with Google servers (production mode)")

	// Enhanced input validation
	if err := g.validateTokenInput(idToken); err != nil {
		return nil, fmt.Errorf("token validation failed: %w", err)
	}

	// Use pre-initialized validator if available, otherwise create new one
	var validator *idtoken.Validator
	var err error

	if g.validator != nil {
		validator = g.validator
	} else {
		log.Printf("⚠️ GoogleOAuth: Creating new validator (fallback)")
		validator, err = idtoken.NewValidator(ctx, option.WithHTTPClient(g.client))
		if err != nil {
			return nil, fmt.Errorf("failed to create ID token validator: %w", err)
		}
	}

	// Verify the ID token with strict audience validation
	payload, err := validator.Validate(ctx, idToken, g.clientID)
	if err != nil {
		log.Printf("❌ GoogleOAuth: Token validation failed: %v", err)
		return nil, fmt.Errorf("invalid ID token: %w", err)
	}

	// Enhanced issuer validation with comprehensive security checks
	if err := g.validateIssuer(payload.Claims); err != nil {
		return nil, fmt.Errorf("issuer validation failed: %w", err)
	}

	// Enhanced audience validation with comprehensive security checks
	if err := g.validateAudience(payload.Claims); err != nil {
		return nil, fmt.Errorf("audience validation failed: %w", err)
	}

	// Validate token timing (issued at, expires at, not before)
	if err := g.validateTokenTiming(payload.Claims); err != nil {
		return nil, fmt.Errorf("token timing validation failed: %w", err)
	}

	// Extract and validate user information
	email, ok := payload.Claims["email"].(string)
	if !ok || email == "" {
		return nil, fmt.Errorf("email not found in token payload")
	}

	emailVerified, ok := payload.Claims["email_verified"].(bool)
	if !ok || !emailVerified {
		return nil, fmt.Errorf("email not verified by Google")
	}

	// Get user ID from 'sub' claim (subject) - required
	userID, ok := payload.Claims["sub"].(string)
	if !ok || userID == "" {
		return nil, fmt.Errorf("user ID not found in token payload")
	}

	// Extract optional user information with safe type assertions
	name, _ := payload.Claims["name"].(string)
	if name == "" {
		name = email // Fallback to email if name not available
	}

	givenName, _ := payload.Claims["given_name"].(string)
	familyName, _ := payload.Claims["family_name"].(string)
	picture, _ := payload.Claims["picture"].(string)
	locale, _ := payload.Claims["locale"].(string)

	// Validate token expiration (additional check)
	if exp, ok := payload.Claims["exp"].(float64); ok {
		expTime := time.Unix(int64(exp), 0)
		if time.Now().After(expTime) {
			return nil, fmt.Errorf("token has expired at %v", expTime)
		}
	}

	log.Printf("✅ GoogleOAuth: Token verified successfully for user: %s (ID: %s)", email, userID)

	// Return verified user information
	return &GoogleUserInfo{
		ID:            userID,
		Email:         email,
		VerifiedEmail: emailVerified,
		Name:          name,
		GivenName:     givenName,
		FamilyName:    familyName,
		Picture:       picture,
		Locale:        locale,
	}, nil
}

// VerifyIDTokenWithOAuth2 uses OAuth2 API as alternative verification method with enhanced security
func (g *GoogleOAuthService) VerifyIDTokenWithOAuth2(ctx context.Context, idToken string) (*GoogleUserInfo, error) {
	log.Printf("🔍 GoogleOAuth: Using OAuth2 API as alternative verification method")

	// Input validation
	if idToken == "" {
		return nil, fmt.Errorf("ID token cannot be empty")
	}

	// Create OAuth2 service with timeout context
	oauth2Service, err := oauth2.NewService(ctx, option.WithHTTPClient(g.client))
	if err != nil {
		return nil, fmt.Errorf("failed to create OAuth2 service: %w", err)
	}

	// Verify the ID token using OAuth2 tokeninfo endpoint
	tokenInfo, err := oauth2Service.Tokeninfo().IdToken(idToken).Context(ctx).Do()
	if err != nil {
		log.Printf("❌ GoogleOAuth: OAuth2 token verification failed: %v", err)
		return nil, fmt.Errorf("OAuth2 token verification failed: %w", err)
	}

	// Comprehensive validation checks
	if tokenInfo == nil {
		return nil, fmt.Errorf("received nil token info from Google")
	}

	// Verify the audience (client ID) - critical security check
	if tokenInfo.Audience != g.clientID {
		log.Printf("❌ GoogleOAuth: Invalid audience. Expected: %s, Got: %s", g.clientID, tokenInfo.Audience)
		return nil, fmt.Errorf("invalid token audience: expected %s, got %s", g.clientID, tokenInfo.Audience)
	}

	// Check if token is expired
	if tokenInfo.ExpiresIn <= 0 {
		log.Printf("❌ GoogleOAuth: Token has expired (expires_in: %d)", tokenInfo.ExpiresIn)
		return nil, fmt.Errorf("token has expired")
	}

	// Verify email is present and verified
	if tokenInfo.Email == "" {
		return nil, fmt.Errorf("email not found in token")
	}

	if !tokenInfo.VerifiedEmail {
		return nil, fmt.Errorf("email not verified by Google")
	}

	// Verify user ID is present
	if tokenInfo.UserId == "" {
		return nil, fmt.Errorf("user ID not found in token")
	}

	log.Printf("✅ GoogleOAuth: OAuth2 verification successful for user: %s (ID: %s)", tokenInfo.Email, tokenInfo.UserId)

	// Return verified user information (OAuth2 tokeninfo has limited fields)
	return &GoogleUserInfo{
		ID:            tokenInfo.UserId,
		Email:         tokenInfo.Email,
		VerifiedEmail: tokenInfo.VerifiedEmail,
		Name:          tokenInfo.Email, // Use email as name (tokeninfo doesn't provide name)
		GivenName:     "",              // Not available in tokeninfo
		FamilyName:    "",              // Not available in tokeninfo
		Picture:       "",              // Not available in tokeninfo
		Locale:        "",              // Not available in tokeninfo
	}, nil
}

// VerifyIDTokenWithFallback tries the primary method first, then falls back to OAuth2 API
// This method provides production-ready token verification with multiple validation layers
func (g *GoogleOAuthService) VerifyIDTokenWithFallback(ctx context.Context, idToken string) (*GoogleUserInfo, error) {
	log.Printf("🔍 GoogleOAuth: Starting production token verification with fallback")

	// Input validation
	if idToken == "" {
		return nil, fmt.Errorf("ID token cannot be empty")
	}

	// Try the primary method first (most secure - uses Google's official validator)
	userInfo, err := g.VerifyIDToken(ctx, idToken)
	if err == nil {
		log.Printf("✅ GoogleOAuth: Primary verification successful")
		return userInfo, nil
	}

	log.Printf("⚠️ GoogleOAuth: Primary verification failed, trying OAuth2 fallback: %v", err)

	// Fallback to OAuth2 API (secondary verification method)
	userInfo, fallbackErr := g.VerifyIDTokenWithOAuth2(ctx, idToken)
	if fallbackErr == nil {
		log.Printf("✅ GoogleOAuth: OAuth2 fallback verification successful")
		return userInfo, nil
	}

	log.Printf("❌ GoogleOAuth: All verification methods failed. Primary: %v, Fallback: %v", err, fallbackErr)
	return nil, fmt.Errorf("token verification failed: primary error: %w, fallback error: %v", err, fallbackErr)
}

// VerifyAccessToken verifies an access token using Google's userinfo endpoint with enhanced security
func (g *GoogleOAuthService) VerifyAccessToken(ctx context.Context, accessToken string) (*GoogleUserInfo, error) {
	log.Printf("🔍 GoogleOAuth: Verifying access token with userinfo endpoint")

	// Input validation
	if accessToken == "" {
		return nil, fmt.Errorf("access token cannot be empty")
	}

	// Make request to Google's userinfo endpoint
	url := "https://www.googleapis.com/oauth2/v2/userinfo"

	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Add authorization header
	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", accessToken))
	req.Header.Set("Accept", "application/json")

	// Execute request with timeout
	resp, err := g.client.Do(req)
	if err != nil {
		log.Printf("❌ GoogleOAuth: Failed to make userinfo request: %v", err)
		return nil, fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()

	// Handle different HTTP status codes
	switch resp.StatusCode {
	case http.StatusOK:
		// Success - continue processing
	case http.StatusUnauthorized:
		return nil, fmt.Errorf("access token is invalid or expired")
	case http.StatusForbidden:
		return nil, fmt.Errorf("access token does not have required permissions")
	case http.StatusTooManyRequests:
		return nil, fmt.Errorf("rate limit exceeded, please try again later")
	default:
		return nil, fmt.Errorf("Google API returned unexpected status: %d", resp.StatusCode)
	}

	var userInfo GoogleUserInfo
	if err := json.NewDecoder(resp.Body).Decode(&userInfo); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	// Validate required fields
	if userInfo.Email == "" {
		return nil, fmt.Errorf("email not found in user info")
	}

	if userInfo.ID == "" {
		return nil, fmt.Errorf("user ID not found in user info")
	}

	if !userInfo.VerifiedEmail {
		return nil, fmt.Errorf("email not verified by Google")
	}

	log.Printf("✅ GoogleOAuth: Access token verification successful for user: %s (ID: %s)", userInfo.Email, userInfo.ID)

	return &userInfo, nil
}

// ValidateTokenFormat performs basic format validation on tokens before API calls
func (g *GoogleOAuthService) ValidateTokenFormat(token string) error {
	if token == "" {
		return fmt.Errorf("token cannot be empty")
	}

	if len(token) < 50 {
		return fmt.Errorf("token appears to be too short")
	}

	if len(token) > 4096 {
		return fmt.Errorf("token appears to be too long")
	}

	// Basic JWT format check for ID tokens (should have 3 parts separated by dots)
	parts := strings.Split(token, ".")
	if len(parts) == 3 {
		// Looks like a JWT token - this is expected for ID tokens
		return nil
	}

	// For access tokens, we don't enforce JWT format as Google may use different formats
	return nil
}

// GetClientID returns the configured client ID (for debugging/logging purposes)
func (g *GoogleOAuthService) GetClientID() string {
	return maskClientID(g.clientID)
}

// =============================================================================
// Enhanced Security Validation Methods (Task 1.1 Requirements)
// =============================================================================

// validateTokenInput performs comprehensive input validation on tokens
// Task 1.1 Requirement: Create comprehensive error handling for OAuth failures
func (g *GoogleOAuthService) validateTokenInput(token string) error {
	if token == "" {
		return fmt.Errorf("ID token cannot be empty")
	}

	// Check token length (JWT tokens should be reasonable length)
	if len(token) < 50 {
		return fmt.Errorf("token appears to be too short (minimum 50 characters)")
	}

	if len(token) > 4096 {
		return fmt.Errorf("token appears to be too long (maximum 4096 characters)")
	}

	// Validate JWT format using regex
	if !g.tokenRegex.MatchString(token) {
		return fmt.Errorf("token does not match expected JWT format (header.payload.signature)")
	}

	// Check for suspicious characters or patterns
	if strings.Contains(token, " ") {
		return fmt.Errorf("token contains invalid whitespace characters")
	}

	if strings.Contains(token, "\n") || strings.Contains(token, "\r") {
		return fmt.Errorf("token contains invalid newline characters")
	}

	log.Printf("✅ GoogleOAuth: Token input validation passed")
	return nil
}

// validateIssuer validates the token issuer with enhanced security checks
// Task 1.1 Requirement: Add proper audience and issuer validation for security
func (g *GoogleOAuthService) validateIssuer(claims map[string]interface{}) error {
	issuer, ok := claims["iss"].(string)
	if !ok {
		return fmt.Errorf("issuer claim not found or not a string")
	}

	if issuer == "" {
		return fmt.Errorf("issuer claim is empty")
	}

	// Check against allowed issuers
	validIssuer := false
	for _, allowedIssuer := range g.allowedIssuers {
		if issuer == allowedIssuer {
			validIssuer = true
			break
		}
	}

	if !validIssuer {
		return fmt.Errorf("invalid token issuer: %s (expected one of: %v)", issuer, g.allowedIssuers)
	}

	log.Printf("✅ GoogleOAuth: Issuer validation passed: %s", issuer)
	return nil
}

// validateAudience validates the token audience with enhanced security checks
// Task 1.1 Requirement: Add proper audience and issuer validation for security
func (g *GoogleOAuthService) validateAudience(claims map[string]interface{}) error {
	audience, ok := claims["aud"].(string)
	if !ok {
		return fmt.Errorf("audience claim not found or not a string")
	}

	if audience == "" {
		return fmt.Errorf("audience claim is empty")
	}

	// Validate audience matches our client ID exactly
	if audience != g.clientID {
		return fmt.Errorf("invalid token audience: expected %s, got %s", g.clientID, audience)
	}

	// Additional security: Check for audience manipulation attempts
	if strings.Contains(audience, " ") || strings.Contains(audience, "\t") {
		return fmt.Errorf("audience contains invalid whitespace characters")
	}

	log.Printf("✅ GoogleOAuth: Audience validation passed: %s", maskClientID(audience))
	return nil
}

// validateTokenTiming validates token timing claims (iat, exp, nbf)
// Task 1.1 Requirement: Create comprehensive error handling for OAuth failures
func (g *GoogleOAuthService) validateTokenTiming(claims map[string]interface{}) error {
	now := time.Now().Unix()

	// Validate expiration time (exp)
	if exp, ok := claims["exp"].(float64); ok {
		if int64(exp) <= now {
			return fmt.Errorf("token has expired (exp: %d, now: %d)", int64(exp), now)
		}
		log.Printf("✅ GoogleOAuth: Token expiration validation passed (expires in %d seconds)", int64(exp)-now)
	} else {
		return fmt.Errorf("expiration claim (exp) not found or invalid")
	}

	// Validate issued at time (iat)
	if iat, ok := claims["iat"].(float64); ok {
		// Token should not be issued more than 1 hour in the future (clock skew tolerance)
		if int64(iat) > now+3600 {
			return fmt.Errorf("token issued too far in the future (iat: %d, now: %d)", int64(iat), now)
		}
		// Token should not be older than 24 hours for security
		if int64(iat) < now-86400 {
			return fmt.Errorf("token is too old (iat: %d, now: %d)", int64(iat), now)
		}
		log.Printf("✅ GoogleOAuth: Token issued at validation passed (age: %d seconds)", now-int64(iat))
	} else {
		return fmt.Errorf("issued at claim (iat) not found or invalid")
	}

	// Validate not before time (nbf) if present
	if nbf, ok := claims["nbf"].(float64); ok {
		if int64(nbf) > now {
			return fmt.Errorf("token not valid yet (nbf: %d, now: %d)", int64(nbf), now)
		}
		log.Printf("✅ GoogleOAuth: Token not-before validation passed")
	}

	return nil
}
