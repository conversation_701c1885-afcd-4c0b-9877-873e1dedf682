package services

import (
	"context"
	"errors"
	"fmt"
	"math/rand"
	"time"

	"carnow-backend/internal/config"

	"go.uber.org/zap"
)

// RetryService خدمة إعادة المحاولة مع Exponential Backoff (Forever Plan - مبسط)
type RetryService struct {
	logger        *zap.Logger
	timeoutConfig config.TimeoutConfig
	rand          *rand.Rand
}

// NewRetryService إنشاء خدمة إعادة المحاولة
func NewRetryService(logger *zap.Logger, timeoutConfig config.TimeoutConfig) *RetryService {
	return &RetryService{
		logger:        logger,
		timeoutConfig: timeoutConfig,
		rand:          rand.New(rand.NewSource(time.Now().UnixNano())),
	}
}

// Operation دالة العملية التي سيتم إعادة المحاولة بها
type Operation func(ctx context.Context) error

// OperationWithResult دالة العملية مع إرجاع النتيجة
type OperationWithResult func(ctx context.Context) (interface{}, error)

// ExecuteWithRetry تنفيذ العملية مع إعادة المحاولة
func (rs *RetryService) ExecuteWithRetry(
	ctx context.Context,
	operation Operation,
	operationName string,
) error {
	retryConfig := rs.timeoutConfig.GetRetryConfig()
	timeout := rs.timeoutConfig.GetTimeoutForOperation(operationName)

	// إنشاء context مع timeout
	ctx, cancel := context.WithTimeout(ctx, timeout)
	defer cancel()

	var lastErr error
	delay := retryConfig.BaseDelay

	for attempt := 1; attempt <= retryConfig.MaxAttempts; attempt++ {
		rs.logger.Info("Executing operation",
			zap.String("operation", operationName),
			zap.Int("attempt", attempt),
			zap.Int("max_attempts", retryConfig.MaxAttempts),
		)

		// تنفيذ العملية
		err := operation(ctx)
		if err == nil {
			rs.logger.Info("Operation succeeded",
				zap.String("operation", operationName),
				zap.Int("attempt", attempt),
			)
			return nil
		}

		lastErr = err
		rs.logger.Warn("Operation failed",
			zap.String("operation", operationName),
			zap.Int("attempt", attempt),
			zap.Error(err),
		)

		// التحقق من إمكانية إعادة المحاولة
		if !rs.isRetryableError(err) || attempt >= retryConfig.MaxAttempts {
			rs.logger.Error("Operation failed permanently",
				zap.String("operation", operationName),
				zap.Int("attempts", attempt),
				zap.Error(err),
			)
			return fmt.Errorf("operation %s failed after %d attempts: %w", operationName, attempt, err)
		}

		// حساب التأخير التالي
		if attempt < retryConfig.MaxAttempts {
			nextDelay := rs.calculateNextDelay(delay, retryConfig)
			rs.logger.Info("Retrying operation",
				zap.String("operation", operationName),
				zap.Duration("delay", nextDelay),
			)

			// انتظار مع إمكانية الإلغاء
			select {
			case <-time.After(nextDelay):
				// استمرار
			case <-ctx.Done():
				return fmt.Errorf("operation %s cancelled: %w", operationName, ctx.Err())
			}

			delay = nextDelay
		}
	}

	return fmt.Errorf("operation %s failed after %d attempts: %w", operationName, retryConfig.MaxAttempts, lastErr)
}

// ExecuteWithRetryAndResult تنفيذ العملية مع إعادة المحاولة وإرجاع النتيجة
func (rs *RetryService) ExecuteWithRetryAndResult(
	ctx context.Context,
	operation OperationWithResult,
	operationName string,
) (interface{}, error) {
	retryConfig := rs.timeoutConfig.GetRetryConfig()
	timeout := rs.timeoutConfig.GetTimeoutForOperation(operationName)

	// إنشاء context مع timeout
	ctx, cancel := context.WithTimeout(ctx, timeout)
	defer cancel()

	var lastErr error
	delay := retryConfig.BaseDelay

	for attempt := 1; attempt <= retryConfig.MaxAttempts; attempt++ {
		rs.logger.Info("Executing operation with result",
			zap.String("operation", operationName),
			zap.Int("attempt", attempt),
			zap.Int("max_attempts", retryConfig.MaxAttempts),
		)

		// تنفيذ العملية
		result, err := operation(ctx)
		if err == nil {
			rs.logger.Info("Operation with result succeeded",
				zap.String("operation", operationName),
				zap.Int("attempt", attempt),
			)
			return result, nil
		}

		lastErr = err
		rs.logger.Warn("Operation with result failed",
			zap.String("operation", operationName),
			zap.Int("attempt", attempt),
			zap.Error(err),
		)

		// التحقق من إمكانية إعادة المحاولة
		if !rs.isRetryableError(err) || attempt >= retryConfig.MaxAttempts {
			rs.logger.Error("Operation with result failed permanently",
				zap.String("operation", operationName),
				zap.Int("attempts", attempt),
				zap.Error(err),
			)
			return nil, fmt.Errorf("operation %s failed after %d attempts: %w", operationName, attempt, err)
		}

		// حساب التأخير التالي
		if attempt < retryConfig.MaxAttempts {
			nextDelay := rs.calculateNextDelay(delay, retryConfig)
			rs.logger.Info("Retrying operation with result",
				zap.String("operation", operationName),
				zap.Duration("delay", nextDelay),
			)

			// انتظار مع إمكانية الإلغاء
			select {
			case <-time.After(nextDelay):
				// استمرار
			case <-ctx.Done():
				return nil, fmt.Errorf("operation %s cancelled: %w", operationName, ctx.Err())
			}

			delay = nextDelay
		}
	}

	return nil, fmt.Errorf("operation %s failed after %d attempts: %w", operationName, retryConfig.MaxAttempts, lastErr)
}

// ExecuteWithTimeout تنفيذ العملية مع timeout فقط (بدون إعادة محاولة)
func (rs *RetryService) ExecuteWithTimeout(
	ctx context.Context,
	operation Operation,
	operationName string,
) error {
	timeout := rs.timeoutConfig.GetTimeoutForOperation(operationName)

	// إنشاء context مع timeout
	ctx, cancel := context.WithTimeout(ctx, timeout)
	defer cancel()

	rs.logger.Info("Executing operation with timeout",
		zap.String("operation", operationName),
		zap.Duration("timeout", timeout),
	)

	err := operation(ctx)
	if err != nil {
		rs.logger.Error("Operation with timeout failed",
			zap.String("operation", operationName),
			zap.Error(err),
		)
		return fmt.Errorf("operation %s failed: %w", operationName, err)
	}

	rs.logger.Info("Operation with timeout succeeded",
		zap.String("operation", operationName),
	)
	return nil
}

// ExecuteWithFallback تنفيذ العملية مع fallback للتدهور التدريجي
func (rs *RetryService) ExecuteWithFallback(
	ctx context.Context,
	primaryOperation Operation,
	fallbackOperation Operation,
	operationName string,
) error {
	rs.logger.Info("Attempting primary operation",
		zap.String("operation", operationName),
	)

	// محاولة العملية الأساسية
	err := rs.ExecuteWithRetry(ctx, primaryOperation, operationName+"-primary")
	if err == nil {
		rs.logger.Info("Primary operation succeeded",
			zap.String("operation", operationName),
		)
		return nil
	}

	rs.logger.Warn("Primary operation failed, trying fallback",
		zap.String("operation", operationName),
		zap.Error(err),
	)

	// محاولة العملية البديلة
	fallbackErr := rs.ExecuteWithRetry(ctx, fallbackOperation, operationName+"-fallback")
	if fallbackErr == nil {
		rs.logger.Info("Fallback operation succeeded",
			zap.String("operation", operationName),
		)
		return nil
	}

	rs.logger.Error("Both primary and fallback operations failed",
		zap.String("operation", operationName),
		zap.Error(err),
		zap.NamedError("fallback_error", fallbackErr),
	)

	// إرجاع الخطأ الأساسي للتشخيص الأفضل
	return fmt.Errorf("operation %s failed: primary=%w, fallback=%v", operationName, err, fallbackErr)
}

// calculateNextDelay حساب التأخير التالي مع Exponential Backoff و Jitter
func (rs *RetryService) calculateNextDelay(currentDelay time.Duration, retryConfig config.RetryConfig) time.Duration {
	// Exponential backoff
	nextDelay := time.Duration(float64(currentDelay) * retryConfig.Multiplier)

	// تطبيق الحد الأقصى للتأخير
	if nextDelay > retryConfig.MaxDelay {
		nextDelay = retryConfig.MaxDelay
	}

	// إضافة Jitter لتجنب Thundering Herd
	if retryConfig.JitterEnabled {
		jitter := rs.rand.Float64() * retryConfig.JitterFactor
		jitterMs := int64(float64(nextDelay.Nanoseconds()) * jitter)
		nextDelay += time.Duration(jitterMs)
	}

	return nextDelay
}

// isRetryableError التحقق من إمكانية إعادة المحاولة للخطأ
func (rs *RetryService) isRetryableError(err error) bool {
	if err == nil {
		return false
	}

	// أخطاء Context غير قابلة لإعادة المحاولة
	if errors.Is(err, context.Canceled) || errors.Is(err, context.DeadlineExceeded) {
		return false
	}

	// أخطاء الشبكة والخادم قابلة لإعادة المحاولة
	errStr := err.Error()
	retryableMessages := []string{
		"connection refused",
		"connection reset",
		"timeout",
		"temporary failure",
		"server error",
		"service unavailable",
		"too many requests",
		"rate limit",
	}

	for _, msg := range retryableMessages {
		if contains(errStr, msg) {
			return true
		}
	}

	// افتراضياً معظم الأخطاء قابلة لإعادة المحاولة
	return true
}

// contains التحقق من وجود نص فرعي (مساعد)
func contains(s, substr string) bool {
	return len(s) >= len(substr) && (s == substr || len(substr) == 0 ||
		(len(s) > len(substr) && (s[:len(substr)] == substr || s[len(s)-len(substr):] == substr ||
			indexOf(s, substr) >= 0)))
}

// indexOf البحث عن موقع النص الفرعي (مساعد)
func indexOf(s, substr string) int {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return i
		}
	}
	return -1
}

// RetryMetrics مقاييس إعادة المحاولة
type RetryMetrics struct {
	OperationName     string        `json:"operation_name"`
	TotalAttempts     int           `json:"total_attempts"`
	SuccessfulRuns    int           `json:"successful_runs"`
	FailedRuns        int           `json:"failed_runs"`
	AverageDelay      time.Duration `json:"average_delay"`
	LastExecutionTime time.Time     `json:"last_execution_time"`
}

// GetMetrics الحصول على مقاييس الأداء (للمراقبة)
func (rs *RetryService) GetMetrics() map[string]RetryMetrics {
	// TODO: تنفيذ تجميع المقاييس
	// هذا سيتطلب إضافة متغيرات لتتبع الإحصائيات
	return make(map[string]RetryMetrics)
}
