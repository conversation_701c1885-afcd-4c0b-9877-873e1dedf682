package services

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"go.uber.org/zap"
)

// DeadLetterQueueService خدمة Dead Letter Queue للعمليات الفاشلة (Forever Plan - مبسط)
type DeadLetterQueueService struct {
	logger         *zap.Logger
	mutex          sync.RWMutex
	failedOps      map[string]*FailedOperation
	maxQueueSize   int
	retryInterval  time.Duration
	processingChan chan *FailedOperation
	ctx            context.Context
	cancel         context.CancelFunc
}

// FailedOperation عملية فاشلة في Dead Letter Queue
type FailedOperation struct {
	ID            string                 `json:"id"`
	OperationName string                 `json:"operation_name"`
	ErrorMessage  string                 `json:"error_message"`
	AttemptCount  int                    `json:"attempt_count"`
	Timestamp     time.Time              `json:"timestamp"`
	Status        FailedOperationStatus  `json:"status"`
	Metadata      map[string]interface{} `json:"metadata,omitempty"`
	RetryAfter    time.Time              `json:"retry_after,omitempty"`
}

// FailedOperationStatus حالة العملية الفاشلة
type FailedOperationStatus string

const (
	StatusPending   FailedOperationStatus = "pending"
	StatusProcessed FailedOperationStatus = "processed"
	StatusRetrying  FailedOperationStatus = "retrying"
	StatusFailed    FailedOperationStatus = "failed"
)

// QueueStatistics إحصائيات Dead Letter Queue
type QueueStatistics struct {
	TotalOperations     int `json:"total_operations"`
	PendingOperations   int `json:"pending_operations"`
	ProcessedOperations int `json:"processed_operations"`
	FailedOperations    int `json:"failed_operations"`
	RetryingOperations  int `json:"retrying_operations"`
}

// NewDeadLetterQueueService إنشاء خدمة Dead Letter Queue
func NewDeadLetterQueueService(logger *zap.Logger, maxQueueSize int, retryInterval time.Duration) *DeadLetterQueueService {
	ctx, cancel := context.WithCancel(context.Background())

	dlq := &DeadLetterQueueService{
		logger:         logger,
		failedOps:      make(map[string]*FailedOperation),
		maxQueueSize:   maxQueueSize,
		retryInterval:  retryInterval,
		processingChan: make(chan *FailedOperation, 100),
		ctx:            ctx,
		cancel:         cancel,
	}

	// بدء معالج العمليات
	go dlq.processOperations()

	return dlq
}

// AddFailedOperation إضافة عملية فاشلة إلى Dead Letter Queue
func (dlq *DeadLetterQueueService) AddFailedOperation(
	operationName string,
	errorMessage string,
	attemptCount int,
	metadata map[string]interface{},
) error {
	dlq.mutex.Lock()
	defer dlq.mutex.Unlock()

	// إنشاء معرف فريد
	id := fmt.Sprintf("%s_%d", operationName, time.Now().UnixNano())

	// إنشاء العملية الفاشلة
	failedOp := &FailedOperation{
		ID:            id,
		OperationName: operationName,
		ErrorMessage:  errorMessage,
		AttemptCount:  attemptCount,
		Timestamp:     time.Now(),
		Status:        StatusPending,
		Metadata:      metadata,
		RetryAfter:    time.Now().Add(dlq.retryInterval),
	}

	// إضافة إلى القائمة
	dlq.failedOps[id] = failedOp

	// تنظيف القائمة إذا تجاوزت الحد الأقصى
	if len(dlq.failedOps) > dlq.maxQueueSize {
		dlq.cleanupOldOperations()
	}

	dlq.logger.Warn("Added failed operation to dead letter queue",
		zap.String("id", id),
		zap.String("operation", operationName),
		zap.String("error", errorMessage),
		zap.Int("attempt_count", attemptCount),
	)

	// إرسال للمعالجة
	select {
	case dlq.processingChan <- failedOp:
	default:
		dlq.logger.Warn("Processing channel full, skipping processing",
			zap.String("operation_id", id),
		)
	}

	return nil
}

// GetFailedOperations الحصول على جميع العمليات الفاشلة
func (dlq *DeadLetterQueueService) GetFailedOperations() []*FailedOperation {
	dlq.mutex.RLock()
	defer dlq.mutex.RUnlock()

	operations := make([]*FailedOperation, 0, len(dlq.failedOps))
	for _, op := range dlq.failedOps {
		operations = append(operations, op)
	}

	return operations
}

// GetPendingOperations الحصول على العمليات المعلقة
func (dlq *DeadLetterQueueService) GetPendingOperations() []*FailedOperation {
	dlq.mutex.RLock()
	defer dlq.mutex.RUnlock()

	var pending []*FailedOperation
	for _, op := range dlq.failedOps {
		if op.Status == StatusPending {
			pending = append(pending, op)
		}
	}

	return pending
}

// MarkAsProcessed تعيين العملية كمعالجة
func (dlq *DeadLetterQueueService) MarkAsProcessed(operationID string) error {
	dlq.mutex.Lock()
	defer dlq.mutex.Unlock()

	op, exists := dlq.failedOps[operationID]
	if !exists {
		return fmt.Errorf("operation %s not found", operationID)
	}

	op.Status = StatusProcessed
	dlq.logger.Info("Marked operation as processed",
		zap.String("operation_id", operationID),
		zap.String("operation_name", op.OperationName),
	)

	return nil
}

// RemoveOperation إزالة عملية من Dead Letter Queue
func (dlq *DeadLetterQueueService) RemoveOperation(operationID string) error {
	dlq.mutex.Lock()
	defer dlq.mutex.Unlock()

	op, exists := dlq.failedOps[operationID]
	if !exists {
		return fmt.Errorf("operation %s not found", operationID)
	}

	delete(dlq.failedOps, operationID)
	dlq.logger.Info("Removed operation from dead letter queue",
		zap.String("operation_id", operationID),
		zap.String("operation_name", op.OperationName),
	)

	return nil
}

// GetStatistics الحصول على إحصائيات Dead Letter Queue
func (dlq *DeadLetterQueueService) GetStatistics() QueueStatistics {
	dlq.mutex.RLock()
	defer dlq.mutex.RUnlock()

	stats := QueueStatistics{
		TotalOperations: len(dlq.failedOps),
	}

	for _, op := range dlq.failedOps {
		switch op.Status {
		case StatusPending:
			stats.PendingOperations++
		case StatusProcessed:
			stats.ProcessedOperations++
		case StatusRetrying:
			stats.RetryingOperations++
		case StatusFailed:
			stats.FailedOperations++
		}
	}

	return stats
}

// ClearQueue تنظيف Dead Letter Queue
func (dlq *DeadLetterQueueService) ClearQueue() {
	dlq.mutex.Lock()
	defer dlq.mutex.Unlock()

	dlq.failedOps = make(map[string]*FailedOperation)
	dlq.logger.Info("Cleared dead letter queue")
}

// ExportOperations تصدير العمليات الفاشلة (للتحليل)
func (dlq *DeadLetterQueueService) ExportOperations() ([]byte, error) {
	operations := dlq.GetFailedOperations()

	data, err := json.MarshalIndent(operations, "", "  ")
	if err != nil {
		dlq.logger.Error("Failed to export operations", zap.Error(err))
		return nil, fmt.Errorf("failed to export operations: %w", err)
	}

	dlq.logger.Info("Exported dead letter queue operations",
		zap.Int("operation_count", len(operations)),
	)

	return data, nil
}

// processOperations معالجة العمليات الفاشلة في الخلفية
func (dlq *DeadLetterQueueService) processOperations() {
	dlq.logger.Info("Started dead letter queue processor")

	ticker := time.NewTicker(dlq.retryInterval)
	defer ticker.Stop()

	for {
		select {
		case <-dlq.ctx.Done():
			dlq.logger.Info("Stopping dead letter queue processor")
			return

		case operation := <-dlq.processingChan:
			dlq.processOperation(operation)

		case <-ticker.C:
			dlq.processRetryableOperations()
		}
	}
}

// processOperation معالجة عملية واحدة
func (dlq *DeadLetterQueueService) processOperation(operation *FailedOperation) {
	dlq.logger.Info("Processing failed operation",
		zap.String("operation_id", operation.ID),
		zap.String("operation_name", operation.OperationName),
		zap.String("error", operation.ErrorMessage),
		zap.Int("attempt_count", operation.AttemptCount),
	)

	// هنا يمكن إضافة منطق معالجة إضافي مثل:
	// - إرسال تنبيهات
	// - إعادة محاولة العملية
	// - تحليل السبب
	// - حفظ في قاعدة البيانات

	// للآن نكتفي بتسجيل العملية
	dlq.MarkAsProcessed(operation.ID)
}

// processRetryableOperations معالجة العمليات القابلة لإعادة المحاولة
func (dlq *DeadLetterQueueService) processRetryableOperations() {
	dlq.mutex.RLock()
	retryableOps := make([]*FailedOperation, 0)
	now := time.Now()

	for _, op := range dlq.failedOps {
		if op.Status == StatusPending && now.After(op.RetryAfter) {
			retryableOps = append(retryableOps, op)
		}
	}
	dlq.mutex.RUnlock()

	if len(retryableOps) > 0 {
		dlq.logger.Info("Processing retryable operations",
			zap.Int("count", len(retryableOps)),
		)

		for _, op := range retryableOps {
			dlq.processOperation(op)
		}
	}
}

// cleanupOldOperations تنظيف العمليات القديمة
func (dlq *DeadLetterQueueService) cleanupOldOperations() {
	// حذف العمليات المعالجة والقديمة
	cutoff := time.Now().Add(-24 * time.Hour) // حذف العمليات الأقدم من 24 ساعة
	deletedCount := 0

	for id, op := range dlq.failedOps {
		if op.Status == StatusProcessed && op.Timestamp.Before(cutoff) {
			delete(dlq.failedOps, id)
			deletedCount++
		}
	}

	if deletedCount > 0 {
		dlq.logger.Info("Cleaned up old operations",
			zap.Int("deleted_count", deletedCount),
		)
	}
}

// Shutdown إغلاق خدمة Dead Letter Queue
func (dlq *DeadLetterQueueService) Shutdown() {
	dlq.logger.Info("Shutting down dead letter queue service")
	dlq.cancel()
	close(dlq.processingChan)
}
