package services

import (
	"context"
	"crypto/rand"
	"encoding/base64"
	"fmt"
	"net/http"
	"strings"
	"sync"
	"time"

	"go.uber.org/zap"
	"google.golang.org/api/idtoken"
	"google.golang.org/api/oauth2/v2"
	"google.golang.org/api/option"
)

// EnhancedGoogleOAuthService provides production-grade Google OAuth security
type EnhancedGoogleOAuthService struct {
	clientID     string
	clientSecret string
	redirectURL  string
	client       *http.Client
	logger       *zap.Logger

	// Security features
	stateManager   *StateManager
	tokenValidator *TokenValidator
	auditLogger    *AuditLogger
	metrics        *OAuthMetrics
	rateLimiter    *OAuthRateLimiter

	// Configuration
	config *OAuthSecurityConfig
	mutex  sync.RWMutex
}

// OAuthSecurityConfig contains security configuration
type OAuthSecurityConfig struct {
	EnableAuditLogging   bool          `yaml:"enable_audit_logging"`
	EnableRateLimiting   bool          `yaml:"enable_rate_limiting"`
	TokenCacheTimeout    time.Duration `yaml:"token_cache_timeout"`
	MaxTokenAge          time.Duration `yaml:"max_token_age"`
	RequireHTTPS         bool          `yaml:"require_https"`
	CSRFProtection       bool          `yaml:"csrf_protection"`
	StateExpiration      time.Duration `yaml:"state_expiration"`
	MaxAttemptsPerIP     int           `yaml:"max_attempts_per_ip"`
	BlockDuration        time.Duration `yaml:"block_duration"`
	EnableTokenBlacklist bool          `yaml:"enable_token_blacklist"`
}

// DefaultOAuthSecurityConfig returns production-ready security configuration
func DefaultOAuthSecurityConfig() *OAuthSecurityConfig {
	return &OAuthSecurityConfig{
		EnableAuditLogging:   true,
		EnableRateLimiting:   true,
		TokenCacheTimeout:    5 * time.Minute,
		MaxTokenAge:          time.Hour,
		RequireHTTPS:         true,
		CSRFProtection:       true,
		StateExpiration:      10 * time.Minute,
		MaxAttemptsPerIP:     5,
		BlockDuration:        time.Hour,
		EnableTokenBlacklist: true,
	}
}

// StateManager handles OAuth state parameter security
type StateManager struct {
	states map[string]*StateInfo
	mutex  sync.RWMutex
	ttl    time.Duration
}

// StateInfo contains state information
type StateInfo struct {
	Value     string
	CreatedAt time.Time
	IP        string
	UserAgent string
}

// TokenValidator provides advanced token validation
type TokenValidator struct {
	cache     map[string]*ValidationResult
	mutex     sync.RWMutex
	blacklist map[string]time.Time
}

// ValidationResult contains token validation results
type ValidationResult struct {
	Valid       bool
	UserInfo    *GoogleUserInfo
	ValidatedAt time.Time
	ExpiresAt   time.Time
}

// AuditLogger handles security audit logging
type AuditLogger struct {
	logger *zap.Logger
}

// OAuthMetrics tracks OAuth security metrics
type OAuthMetrics struct {
	TotalAttempts      int64
	SuccessfulAuths    int64
	FailedAuths        int64
	SuspiciousActivity int64
	BlockedIPs         map[string]time.Time
	mutex              sync.RWMutex
}

// OAuthRateLimiter handles rate limiting per IP
type OAuthRateLimiter struct {
	attempts map[string]*AttemptInfo
	mutex    sync.RWMutex
	config   *OAuthSecurityConfig
}

// AttemptInfo tracks authentication attempts per IP
type AttemptInfo struct {
	Count        int
	FirstAttempt time.Time
	LastAttempt  time.Time
	Blocked      bool
	BlockedUntil time.Time
}

// NewEnhancedGoogleOAuthService creates a production-grade OAuth service
func NewEnhancedGoogleOAuthService(clientID, clientSecret, redirectURL string, logger *zap.Logger) *EnhancedGoogleOAuthService {
	config := DefaultOAuthSecurityConfig()

	service := &EnhancedGoogleOAuthService{
		clientID:     clientID,
		clientSecret: clientSecret,
		redirectURL:  redirectURL,
		client: &http.Client{
			Timeout: 10 * time.Second,
		},
		logger: logger,
		config: config,

		// Initialize security components
		stateManager: &StateManager{
			states: make(map[string]*StateInfo),
			ttl:    config.StateExpiration,
		},
		tokenValidator: &TokenValidator{
			cache:     make(map[string]*ValidationResult),
			blacklist: make(map[string]time.Time),
		},
		auditLogger: &AuditLogger{logger: logger},
		metrics: &OAuthMetrics{
			BlockedIPs: make(map[string]time.Time),
		},
		rateLimiter: &OAuthRateLimiter{
			attempts: make(map[string]*AttemptInfo),
			config:   config,
		},
	}

	// Start cleanup routines
	go service.stateManager.cleanup()
	go service.tokenValidator.cleanup()
	go service.rateLimiter.cleanup()

	return service
}

// GenerateAuthURL generates a secure OAuth authorization URL
func (s *EnhancedGoogleOAuthService) GenerateAuthURL(ctx context.Context, clientIP, userAgent string) (string, string, error) {
	s.auditLogger.logAuthAttempt(clientIP, userAgent, "generate_auth_url")

	// Check rate limiting
	if s.config.EnableRateLimiting && s.rateLimiter.isBlocked(clientIP) {
		s.auditLogger.logSecurityEvent(clientIP, "rate_limit_exceeded", "OAuth URL generation blocked")
		return "", "", fmt.Errorf("rate limit exceeded")
	}

	// Generate secure state parameter
	state, err := s.stateManager.generateState(clientIP, userAgent)
	if err != nil {
		return "", "", fmt.Errorf("failed to generate state: %w", err)
	}

	// Build OAuth URL with security parameters
	baseURL := "https://accounts.google.com/o/oauth2/auth"
	params := []string{
		"client_id=" + s.clientID,
		"redirect_uri=" + s.redirectURL,
		"scope=openid profile email",
		"response_type=code",
		"state=" + state,
		"access_type=offline",
		"prompt=consent",
		"include_granted_scopes=true",
	}

	authURL := baseURL + "?" + strings.Join(params, "&")

	return authURL, state, nil
}

// VerifyIDTokenSecure performs enhanced token verification with security checks
func (s *EnhancedGoogleOAuthService) VerifyIDTokenSecure(ctx context.Context, idToken, clientIP, userAgent string) (*GoogleUserInfo, error) {
	startTime := time.Now()
	s.metrics.TotalAttempts++

	// Security pre-checks
	if err := s.performSecurityChecks(idToken, clientIP, userAgent); err != nil {
		s.metrics.SuspiciousActivity++
		s.auditLogger.logSecurityEvent(clientIP, "security_check_failed", err.Error())
		return nil, fmt.Errorf("security check failed: %w", err)
	}

	// Check token blacklist
	if s.config.EnableTokenBlacklist && s.tokenValidator.isBlacklisted(idToken) {
		s.auditLogger.logSecurityEvent(clientIP, "blacklisted_token", "Token found in blacklist")
		return nil, fmt.Errorf("token has been revoked")
	}

	// Check cache first
	if result := s.tokenValidator.getCachedResult(idToken); result != nil && result.Valid {
		s.auditLogger.logAuthSuccess(clientIP, userAgent, result.UserInfo.Email, "cached")
		return result.UserInfo, nil
	}

	// Primary validation using Google's official validator
	userInfo, err := s.verifyWithGoogleValidator(ctx, idToken)
	if err != nil {
		// Try fallback method
		s.logger.Warn("Primary validation failed, trying fallback", zap.Error(err))
		userInfo, err = s.verifyWithOAuth2Fallback(ctx, idToken)
		if err != nil {
			s.metrics.FailedAuths++
			s.rateLimiter.recordFailedAttempt(clientIP)
			s.auditLogger.logAuthFailure(clientIP, userAgent, "token_validation_failed", err)
			return nil, fmt.Errorf("token validation failed: %w", err)
		}
	}

	// Additional security validations
	if err := s.validateUserInfo(userInfo); err != nil {
		s.auditLogger.logSecurityEvent(clientIP, "user_validation_failed", err.Error())
		return nil, fmt.Errorf("user validation failed: %w", err)
	}

	// Cache the result
	s.tokenValidator.cacheResult(idToken, userInfo)

	// Log successful authentication
	s.metrics.SuccessfulAuths++
	s.auditLogger.logAuthSuccess(clientIP, userAgent, userInfo.Email, "verified")

	duration := time.Since(startTime)
	s.logger.Info("OAuth verification completed",
		zap.String("email", userInfo.Email),
		zap.Duration("duration", duration),
		zap.String("client_ip", clientIP),
	)

	return userInfo, nil
}

// performSecurityChecks performs pre-validation security checks
func (s *EnhancedGoogleOAuthService) performSecurityChecks(idToken, clientIP, userAgent string) error {
	// Rate limiting check
	if s.config.EnableRateLimiting && s.rateLimiter.isBlocked(clientIP) {
		return fmt.Errorf("IP %s is rate limited", clientIP)
	}

	// Token format validation
	if len(idToken) < 10 || len(idToken) > 2048 {
		return fmt.Errorf("invalid token format")
	}

	// Check for suspicious patterns
	if strings.Contains(idToken, "<script>") || strings.Contains(idToken, "javascript:") {
		return fmt.Errorf("suspicious token content detected")
	}

	// User agent validation
	if userAgent == "" || len(userAgent) > 1000 {
		return fmt.Errorf("invalid user agent")
	}

	return nil
}

// verifyWithGoogleValidator uses Google's official ID token validator
func (s *EnhancedGoogleOAuthService) verifyWithGoogleValidator(ctx context.Context, idToken string) (*GoogleUserInfo, error) {
	validator, err := idtoken.NewValidator(ctx, option.WithHTTPClient(s.client))
	if err != nil {
		return nil, fmt.Errorf("failed to create ID token validator: %w", err)
	}

	payload, err := validator.Validate(ctx, idToken, s.clientID)
	if err != nil {
		return nil, fmt.Errorf("invalid ID token: %w", err)
	}

	return s.extractUserInfoFromPayload(payload)
}

// verifyWithOAuth2Fallback uses OAuth2 API as fallback
func (s *EnhancedGoogleOAuthService) verifyWithOAuth2Fallback(ctx context.Context, idToken string) (*GoogleUserInfo, error) {
	oauth2Service, err := oauth2.NewService(ctx, option.WithHTTPClient(s.client))
	if err != nil {
		return nil, fmt.Errorf("failed to create OAuth2 service: %w", err)
	}

	tokenInfo, err := oauth2Service.Tokeninfo().IdToken(idToken).Do()
	if err != nil {
		return nil, fmt.Errorf("OAuth2 validation failed: %w", err)
	}

	if tokenInfo.Audience != s.clientID {
		return nil, fmt.Errorf("invalid audience")
	}

	if tokenInfo.ExpiresIn <= 0 {
		return nil, fmt.Errorf("token expired")
	}

	if !tokenInfo.VerifiedEmail {
		return nil, fmt.Errorf("email not verified")
	}

	return &GoogleUserInfo{
		ID:            tokenInfo.UserId,
		Email:         tokenInfo.Email,
		VerifiedEmail: tokenInfo.VerifiedEmail,
		Name:          tokenInfo.Email,
	}, nil
}

// extractUserInfoFromPayload extracts user info from JWT payload
func (s *EnhancedGoogleOAuthService) extractUserInfoFromPayload(payload *idtoken.Payload) (*GoogleUserInfo, error) {
	email, ok := payload.Claims["email"].(string)
	if !ok || email == "" {
		return nil, fmt.Errorf("email not found in token")
	}

	emailVerified, ok := payload.Claims["email_verified"].(bool)
	if !ok || !emailVerified {
		return nil, fmt.Errorf("email not verified")
	}

	name, _ := payload.Claims["name"].(string)
	if name == "" {
		name = email
	}

	givenName, _ := payload.Claims["given_name"].(string)
	familyName, _ := payload.Claims["family_name"].(string)
	picture, _ := payload.Claims["picture"].(string)
	locale, _ := payload.Claims["locale"].(string)
	userID, _ := payload.Claims["sub"].(string)

	return &GoogleUserInfo{
		ID:            userID,
		Email:         email,
		VerifiedEmail: emailVerified,
		Name:          name,
		GivenName:     givenName,
		FamilyName:    familyName,
		Picture:       picture,
		Locale:        locale,
	}, nil
}

// validateUserInfo performs additional validation on user information
func (s *EnhancedGoogleOAuthService) validateUserInfo(userInfo *GoogleUserInfo) error {
	if userInfo.Email == "" {
		return fmt.Errorf("email is required")
	}

	if !userInfo.VerifiedEmail {
		return fmt.Errorf("email must be verified")
	}

	// Additional domain validation if needed
	// if !strings.HasSuffix(userInfo.Email, "@alloweddomain.com") {
	//     return fmt.Errorf("email domain not allowed")
	// }

	return nil
}

// State Manager methods
func (sm *StateManager) generateState(clientIP, userAgent string) (string, error) {
	// Generate cryptographically secure random state
	bytes := make([]byte, 32)
	if _, err := rand.Read(bytes); err != nil {
		return "", fmt.Errorf("failed to generate random state: %w", err)
	}

	state := base64.URLEncoding.EncodeToString(bytes)

	sm.mutex.Lock()
	defer sm.mutex.Unlock()

	sm.states[state] = &StateInfo{
		Value:     state,
		CreatedAt: time.Now(),
		IP:        clientIP,
		UserAgent: userAgent,
	}

	return state, nil
}

func (sm *StateManager) validateState(state, clientIP, userAgent string) error {
	sm.mutex.RLock()
	defer sm.mutex.RUnlock()

	stateInfo, exists := sm.states[state]
	if !exists {
		return fmt.Errorf("invalid state parameter")
	}

	if time.Since(stateInfo.CreatedAt) > sm.ttl {
		return fmt.Errorf("state parameter expired")
	}

	if stateInfo.IP != clientIP {
		return fmt.Errorf("state IP mismatch")
	}

	// Remove used state
	delete(sm.states, state)

	return nil
}

func (sm *StateManager) cleanup() {
	ticker := time.NewTicker(time.Minute)
	defer ticker.Stop()

	for range ticker.C {
		sm.mutex.Lock()
		now := time.Now()
		for state, info := range sm.states {
			if now.Sub(info.CreatedAt) > sm.ttl {
				delete(sm.states, state)
			}
		}
		sm.mutex.Unlock()
	}
}

// Token Validator methods
func (tv *TokenValidator) getCachedResult(token string) *ValidationResult {
	tv.mutex.RLock()
	defer tv.mutex.RUnlock()

	result, exists := tv.cache[token]
	if !exists {
		return nil
	}

	if time.Now().After(result.ExpiresAt) {
		delete(tv.cache, token)
		return nil
	}

	return result
}

func (tv *TokenValidator) cacheResult(token string, userInfo *GoogleUserInfo) {
	tv.mutex.Lock()
	defer tv.mutex.Unlock()

	tv.cache[token] = &ValidationResult{
		Valid:       true,
		UserInfo:    userInfo,
		ValidatedAt: time.Now(),
		ExpiresAt:   time.Now().Add(5 * time.Minute),
	}
}

func (tv *TokenValidator) isBlacklisted(token string) bool {
	tv.mutex.RLock()
	defer tv.mutex.RUnlock()

	expiresAt, exists := tv.blacklist[token]
	if !exists {
		return false
	}

	if time.Now().After(expiresAt) {
		delete(tv.blacklist, token)
		return false
	}

	return true
}

func (tv *TokenValidator) cleanup() {
	ticker := time.NewTicker(5 * time.Minute)
	defer ticker.Stop()

	for range ticker.C {
		tv.mutex.Lock()
		now := time.Now()

		// Clean expired cache entries
		for token, result := range tv.cache {
			if now.After(result.ExpiresAt) {
				delete(tv.cache, token)
			}
		}

		// Clean expired blacklist entries
		for token, expiresAt := range tv.blacklist {
			if now.After(expiresAt) {
				delete(tv.blacklist, token)
			}
		}

		tv.mutex.Unlock()
	}
}

// Rate Limiter methods
func (rl *OAuthRateLimiter) isBlocked(clientIP string) bool {
	rl.mutex.RLock()
	defer rl.mutex.RUnlock()

	attempt, exists := rl.attempts[clientIP]
	if !exists {
		return false
	}

	if attempt.Blocked && time.Now().Before(attempt.BlockedUntil) {
		return true
	}

	return false
}

func (rl *OAuthRateLimiter) recordFailedAttempt(clientIP string) {
	rl.mutex.Lock()
	defer rl.mutex.Unlock()

	now := time.Now()
	attempt, exists := rl.attempts[clientIP]

	if !exists {
		rl.attempts[clientIP] = &AttemptInfo{
			Count:        1,
			FirstAttempt: now,
			LastAttempt:  now,
		}
		return
	}

	// Reset counter if enough time has passed
	if now.Sub(attempt.FirstAttempt) > time.Hour {
		attempt.Count = 1
		attempt.FirstAttempt = now
	} else {
		attempt.Count++
	}

	attempt.LastAttempt = now

	// Block if too many attempts
	if attempt.Count >= rl.config.MaxAttemptsPerIP {
		attempt.Blocked = true
		attempt.BlockedUntil = now.Add(rl.config.BlockDuration)
	}
}

func (rl *OAuthRateLimiter) cleanup() {
	ticker := time.NewTicker(time.Hour)
	defer ticker.Stop()

	for range ticker.C {
		rl.mutex.Lock()
		now := time.Now()

		for ip, attempt := range rl.attempts {
			// Remove old attempts
			if now.Sub(attempt.LastAttempt) > 24*time.Hour {
				delete(rl.attempts, ip)
				continue
			}

			// Unblock expired blocks
			if attempt.Blocked && now.After(attempt.BlockedUntil) {
				attempt.Blocked = false
				attempt.Count = 0
			}
		}

		rl.mutex.Unlock()
	}
}

// Audit Logger methods
func (al *AuditLogger) logAuthAttempt(clientIP, userAgent, action string) {
	al.logger.Info("OAuth authentication attempt",
		zap.String("action", action),
		zap.String("client_ip", clientIP),
		zap.String("user_agent", userAgent),
		zap.Time("timestamp", time.Now()),
	)
}

func (al *AuditLogger) logAuthSuccess(clientIP, userAgent, email, method string) {
	al.logger.Info("OAuth authentication successful",
		zap.String("email", email),
		zap.String("method", method),
		zap.String("client_ip", clientIP),
		zap.String("user_agent", userAgent),
		zap.Time("timestamp", time.Now()),
	)
}

func (al *AuditLogger) logAuthFailure(clientIP, userAgent, reason string, err error) {
	al.logger.Warn("OAuth authentication failed",
		zap.String("reason", reason),
		zap.Error(err),
		zap.String("client_ip", clientIP),
		zap.String("user_agent", userAgent),
		zap.Time("timestamp", time.Now()),
	)
}

func (al *AuditLogger) logSecurityEvent(clientIP, eventType, description string) {
	al.logger.Error("OAuth security event",
		zap.String("event_type", eventType),
		zap.String("description", description),
		zap.String("client_ip", clientIP),
		zap.Time("timestamp", time.Now()),
	)
}

// GetMetrics returns current OAuth metrics
func (s *EnhancedGoogleOAuthService) GetMetrics() *OAuthMetrics {
	s.metrics.mutex.RLock()
	defer s.metrics.mutex.RUnlock()

	// Return a copy to prevent race conditions
	return &OAuthMetrics{
		TotalAttempts:      s.metrics.TotalAttempts,
		SuccessfulAuths:    s.metrics.SuccessfulAuths,
		FailedAuths:        s.metrics.FailedAuths,
		SuspiciousActivity: s.metrics.SuspiciousActivity,
		BlockedIPs:         make(map[string]time.Time),
	}
}
