package resilience

import (
	"context"
	"sync"
	"time"

	"go.uber.org/zap"
)

// ResilienceService provides comprehensive resilience patterns
type ResilienceService struct {
	circuitBreakerManager *CircuitBreakerManager
	retryManager          *RetryManager
	retryStatsCollector   *RetryStatsCollector
	logger                *zap.Logger
	config                *ResilienceConfig
	mu                    sync.RWMutex
}

// ResilienceConfig contains configuration for resilience service
type ResilienceConfig struct {
	// Circuit breaker settings
	DefaultCircuitBreakerConfig *CircuitBreakerConfig `json:"default_circuit_breaker_config"`

	// Retry settings
	DefaultRetryConfig *RetryConfig `json:"default_retry_config"`

	// Timeout settings
	DefaultTimeout time.Duration `json:"default_timeout"`

	// Monitoring settings
	EnableMetrics bool   `json:"enable_metrics"`
	MetricsPrefix string `json:"metrics_prefix"`
}

// DefaultResilienceConfig returns default configuration
func DefaultResilienceConfig() *ResilienceConfig {
	return &ResilienceConfig{
		DefaultCircuitBreakerConfig: DefaultCircuitBreakerConfig("default"),
		DefaultRetryConfig:          DefaultRetryConfig(),
		DefaultTimeout:              30 * time.Second,
		EnableMetrics:               true,
		MetricsPrefix:               "carnow_resilience",
	}
}

// NewResilienceService creates a new resilience service
func NewResilienceService(config *ResilienceConfig, logger *zap.Logger) *ResilienceService {
	if config == nil {
		config = DefaultResilienceConfig()
	}

	if logger == nil {
		logger, _ = zap.NewProduction()
	}

	return &ResilienceService{
		circuitBreakerManager: NewCircuitBreakerManager(logger),
		retryManager:          NewRetryManager(config.DefaultRetryConfig, logger),
		retryStatsCollector:   NewRetryStatsCollector(logger),
		logger:                logger,
		config:                config,
	}
}

// ExecuteWithResilience executes an operation with full resilience patterns
func (rs *ResilienceService) ExecuteWithResilience(
	ctx context.Context,
	operationName string,
	operation func() error,
	options ...ResilienceOption,
) error {
	// Apply options
	opts := &ResilienceOptions{
		EnableCircuitBreaker: true,
		EnableRetry:          true,
		EnableTimeout:        true,
		CircuitBreakerName:   operationName,
		Timeout:              rs.config.DefaultTimeout,
	}

	for _, option := range options {
		option(opts)
	}

	// Create context with timeout if enabled
	if opts.EnableTimeout && opts.Timeout > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithTimeout(ctx, opts.Timeout)
		defer cancel()
	}

	// Get or create circuit breaker if enabled
	var circuitBreaker *CircuitBreaker
	if opts.EnableCircuitBreaker {
		cbConfig := opts.CircuitBreakerConfig
		if cbConfig == nil {
			cbConfig = rs.config.DefaultCircuitBreakerConfig
		}
		circuitBreaker = rs.circuitBreakerManager.GetOrCreate(opts.CircuitBreakerName, cbConfig)
	}

	// Execute with retry and circuit breaker
	startTime := time.Now()
	var attempts int
	var lastErr error

	executeOperation := func() error {
		attempts++

		if opts.EnableCircuitBreaker {
			return circuitBreaker.Execute(ctx, operation)
		}
		return operation()
	}

	if opts.EnableRetry {
		retryConfig := opts.RetryConfig
		if retryConfig == nil {
			retryConfig = rs.config.DefaultRetryConfig
		}

		// Add custom retry callback to track attempts
		originalOnRetry := retryConfig.OnRetry
		retryConfig.OnRetry = func(attempt int, err error, delay time.Duration) {
			rs.logger.Debug("Retry attempt",
				zap.String("operation", operationName),
				zap.Int("attempt", attempt),
				zap.Error(err),
				zap.Duration("delay", delay),
			)
			if originalOnRetry != nil {
				originalOnRetry(attempt, err, delay)
			}
		}

		lastErr = rs.retryManager.ExecuteWithConfig(ctx, executeOperation, retryConfig)
	} else {
		lastErr = executeOperation()
	}

	// Record statistics
	duration := time.Since(startTime)
	success := lastErr == nil

	if rs.config.EnableMetrics {
		rs.retryStatsCollector.RecordOperation(attempts, success, duration)
	}

	// Log operation result
	if success {
		if attempts > 1 {
			rs.logger.Info("Resilient operation succeeded after retries",
				zap.String("operation", operationName),
				zap.Int("attempts", attempts),
				zap.Duration("total_duration", duration),
			)
		}
	} else {
		rs.logger.Error("Resilient operation failed",
			zap.String("operation", operationName),
			zap.Int("attempts", attempts),
			zap.Duration("total_duration", duration),
			zap.Error(lastErr),
		)
	}

	return lastErr
}

// ExecuteWithFallback executes an operation with fallback
func (rs *ResilienceService) ExecuteWithFallback(
	ctx context.Context,
	operationName string,
	primaryOperation func() error,
	fallbackOperation func() error,
	options ...ResilienceOption,
) error {
	err := rs.ExecuteWithResilience(ctx, operationName, primaryOperation, options...)

	if err != nil {
		rs.logger.Info("Primary operation failed, executing fallback",
			zap.String("operation", operationName),
			zap.Error(err),
		)

		// Execute fallback with basic resilience (no circuit breaker to avoid cascading failures)
		fallbackOptions := []ResilienceOption{
			WithCircuitBreaker(false),
			WithRetry(true),
			WithTimeout(rs.config.DefaultTimeout / 2), // Shorter timeout for fallback
		}

		return rs.ExecuteWithResilience(ctx, operationName+"_fallback", fallbackOperation, fallbackOptions...)
	}

	return nil
}

// GetCircuitBreakerStats returns circuit breaker statistics
func (rs *ResilienceService) GetCircuitBreakerStats() map[string]CircuitBreakerStats {
	return rs.circuitBreakerManager.GetStats()
}

// GetRetryStats returns retry statistics
func (rs *ResilienceService) GetRetryStats() *RetryStats {
	return rs.retryStatsCollector.GetStats()
}

// GetHealthStatus returns overall health status
func (rs *ResilienceService) GetHealthStatus() *HealthStatus {
	cbStats := rs.GetCircuitBreakerStats()
	retryStats := rs.GetRetryStats()

	// Calculate health score based on circuit breaker states and retry success rate
	var totalCircuitBreakers int
	var openCircuitBreakers int

	for _, stats := range cbStats {
		totalCircuitBreakers++
		if stats.State == "OPEN" {
			openCircuitBreakers++
		}
	}

	var retrySuccessRate float64
	if retryStats.TotalOperations > 0 {
		retrySuccessRate = float64(retryStats.SuccessfulRetries) / float64(retryStats.TotalOperations)
	}

	// Calculate overall health score (0-100)
	healthScore := 100.0

	if totalCircuitBreakers > 0 {
		circuitBreakerPenalty := float64(openCircuitBreakers) / float64(totalCircuitBreakers) * 50
		healthScore -= circuitBreakerPenalty
	}

	if retryStats.TotalOperations > 0 {
		retryPenalty := (1.0 - retrySuccessRate) * 30
		healthScore -= retryPenalty
	}

	status := "healthy"
	if healthScore < 50 {
		status = "unhealthy"
	} else if healthScore < 80 {
		status = "degraded"
	}

	return &HealthStatus{
		Status:               status,
		HealthScore:          healthScore,
		TotalCircuitBreakers: totalCircuitBreakers,
		OpenCircuitBreakers:  openCircuitBreakers,
		RetrySuccessRate:     retrySuccessRate,
		LastCheck:            time.Now(),
		CircuitBreakerStats:  cbStats,
		RetryStats:           retryStats,
	}
}

// ResilienceOptions contains options for resilience execution
type ResilienceOptions struct {
	EnableCircuitBreaker bool
	EnableRetry          bool
	EnableTimeout        bool
	CircuitBreakerName   string
	CircuitBreakerConfig *CircuitBreakerConfig
	RetryConfig          *RetryConfig
	Timeout              time.Duration
}

// ResilienceOption is a function that configures resilience options
type ResilienceOption func(*ResilienceOptions)

// WithCircuitBreaker enables/disables circuit breaker
func WithCircuitBreaker(enabled bool) ResilienceOption {
	return func(opts *ResilienceOptions) {
		opts.EnableCircuitBreaker = enabled
	}
}

// WithCircuitBreakerConfig sets circuit breaker configuration
func WithCircuitBreakerConfig(config *CircuitBreakerConfig) ResilienceOption {
	return func(opts *ResilienceOptions) {
		opts.CircuitBreakerConfig = config
		opts.EnableCircuitBreaker = true
	}
}

// WithCircuitBreakerName sets circuit breaker name
func WithCircuitBreakerName(name string) ResilienceOption {
	return func(opts *ResilienceOptions) {
		opts.CircuitBreakerName = name
	}
}

// WithRetry enables/disables retry
func WithRetry(enabled bool) ResilienceOption {
	return func(opts *ResilienceOptions) {
		opts.EnableRetry = enabled
	}
}

// WithRetryConfig sets retry configuration
func WithRetryConfig(config *RetryConfig) ResilienceOption {
	return func(opts *ResilienceOptions) {
		opts.RetryConfig = config
		opts.EnableRetry = true
	}
}

// WithTimeout sets timeout
func WithTimeout(timeout time.Duration) ResilienceOption {
	return func(opts *ResilienceOptions) {
		opts.Timeout = timeout
		opts.EnableTimeout = timeout > 0
	}
}

// HealthStatus represents the health status of the resilience service
type HealthStatus struct {
	Status               string                         `json:"status"`
	HealthScore          float64                        `json:"health_score"`
	TotalCircuitBreakers int                            `json:"total_circuit_breakers"`
	OpenCircuitBreakers  int                            `json:"open_circuit_breakers"`
	RetrySuccessRate     float64                        `json:"retry_success_rate"`
	LastCheck            time.Time                      `json:"last_check"`
	CircuitBreakerStats  map[string]CircuitBreakerStats `json:"circuit_breaker_stats"`
	RetryStats           *RetryStats                    `json:"retry_stats"`
}

// Predefined resilience configurations

// DatabaseResilienceOptions returns options for database operations
func DatabaseResilienceOptions() []ResilienceOption {
	return []ResilienceOption{
		WithCircuitBreakerConfig(&CircuitBreakerConfig{
			Name:        "database",
			MaxFailures: 3,
			Timeout:     30 * time.Second,
			MaxRequests: 1,
		}),
		WithRetryConfig(DatabaseRetryConfig()),
		WithTimeout(10 * time.Second),
	}
}

// ExternalAPIResilienceOptions returns options for external API calls
func ExternalAPIResilienceOptions() []ResilienceOption {
	return []ResilienceOption{
		WithCircuitBreakerConfig(&CircuitBreakerConfig{
			Name:        "external_api",
			MaxFailures: 5,
			Timeout:     60 * time.Second,
			MaxRequests: 2,
		}),
		WithRetryConfig(NetworkRetryConfig()),
		WithTimeout(30 * time.Second),
	}
}

// CriticalOperationResilienceOptions returns options for critical operations
func CriticalOperationResilienceOptions() []ResilienceOption {
	return []ResilienceOption{
		WithCircuitBreakerConfig(&CircuitBreakerConfig{
			Name:        "critical",
			MaxFailures: 2,
			Timeout:     120 * time.Second,
			MaxRequests: 1,
		}),
		WithRetryConfig(SlowRetryConfig()),
		WithTimeout(60 * time.Second),
	}
}

// FastOperationResilienceOptions returns options for fast operations
func FastOperationResilienceOptions() []ResilienceOption {
	return []ResilienceOption{
		WithCircuitBreakerConfig(&CircuitBreakerConfig{
			Name:        "fast",
			MaxFailures: 10,
			Timeout:     10 * time.Second,
			MaxRequests: 5,
		}),
		WithRetryConfig(QuickRetryConfig()),
		WithTimeout(5 * time.Second),
	}
}
