package monitoring

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"sync"
	"time"

	"go.uber.org/zap"
)

// AlertingSystem provides comprehensive alerting for authentication failures and security events
type AlertingSystem struct {
	logger       *zap.Logger
	config       *AlertingConfig
	authMonitor  *AuthMonitor
	errorMonitor *ErrorMonitor
	alertQueue   chan Alert
	stopChan     chan struct{}
	wg           sync.WaitGroup
	mu           sync.RWMutex
}

// AlertingConfig contains configuration for the alerting system
type AlertingConfig struct {
	// Alert channels
	EnableSlackAlerts    bool   `json:"enable_slack_alerts"`
	EnableEmailAlerts    bool   `json:"enable_email_alerts"`
	EnableWebhookAlerts  bool   `json:"enable_webhook_alerts"`
	SlackWebhookURL      string `json:"slack_webhook_url"`
	EmailSMTPServer      string `json:"email_smtp_server"`
	EmailSMTPPort        int    `json:"email_smtp_port"`
	EmailUsername        string `json:"email_username"`
	EmailPassword        string `json:"email_password"`
	EmailRecipients      []string `json:"email_recipients"`
	WebhookURL           string `json:"webhook_url"`

	// Alert thresholds
	HighFailureRateThreshold     float64       `json:"high_failure_rate_threshold"`
	SuspiciousActivityThreshold  int           `json:"suspicious_activity_threshold"`
	SlowResponseThreshold        time.Duration `json:"slow_response_threshold"`
	ErrorRateThreshold           float64       `json:"error_rate_threshold"`
	
	// Alert timing
	AlertCooldownPeriod          time.Duration `json:"alert_cooldown_period"`
	BatchAlertInterval           time.Duration `json:"batch_alert_interval"`
	MaxAlertsPerInterval         int           `json:"max_alerts_per_interval"`
	
	// Alert severity levels
	EnableInfoAlerts             bool `json:"enable_info_alerts"`
	EnableWarningAlerts          bool `json:"enable_warning_alerts"`
	EnableErrorAlerts            bool `json:"enable_error_alerts"`
	EnableCriticalAlerts         bool `json:"enable_critical_alerts"`
}

// DefaultAlertingConfig returns default configuration
func DefaultAlertingConfig() *AlertingConfig {
	return &AlertingConfig{
		EnableSlackAlerts:            true,
		EnableEmailAlerts:            false,
		EnableWebhookAlerts:          false,
		HighFailureRateThreshold:     0.15, // 15% failure rate
		SuspiciousActivityThreshold:  5,    // 5 failed attempts
		SlowResponseThreshold:        3 * time.Second,
		ErrorRateThreshold:           0.05, // 5% error rate
		AlertCooldownPeriod:          15 * time.Minute,
		BatchAlertInterval:           5 * time.Minute,
		MaxAlertsPerInterval:         10,
		EnableInfoAlerts:             false,
		EnableWarningAlerts:          true,
		EnableErrorAlerts:            true,
		EnableCriticalAlerts:         true,
	}
}

// Alert represents an alert to be sent
type Alert struct {
	ID          string                 `json:"id"`
	Timestamp   time.Time              `json:"timestamp"`
	Severity    AlertSeverity          `json:"severity"`
	Type        AlertType              `json:"type"`
	Title       string                 `json:"title"`
	Message     string                 `json:"message"`
	Source      string                 `json:"source"`
	Context     map[string]interface{} `json:"context"`
	Metrics     map[string]interface{} `json:"metrics"`
	Resolved    bool                   `json:"resolved"`
	ResolvedAt  *time.Time             `json:"resolved_at,omitempty"`
}

// AlertSeverity represents the severity level of an alert
type AlertSeverity string

const (
	AlertSeverityInfo     AlertSeverity = "info"
	AlertSeverityWarning  AlertSeverity = "warning"
	AlertSeverityError    AlertSeverity = "error"
	AlertSeverityCritical AlertSeverity = "critical"
)

// AlertType represents the type of alert
type AlertType string

const (
	AlertTypeAuthFailure        AlertType = "auth_failure"
	AlertTypeSuspiciousActivity AlertType = "suspicious_activity"
	AlertTypeHighErrorRate      AlertType = "high_error_rate"
	AlertTypeSlowResponse       AlertType = "slow_response"
	AlertTypeSystemHealth       AlertType = "system_health"
	AlertTypeSecurityBreach     AlertType = "security_breach"
)

// SlackMessage represents a Slack webhook message
type SlackMessage struct {
	Text        string            `json:"text"`
	Username    string            `json:"username,omitempty"`
	IconEmoji   string            `json:"icon_emoji,omitempty"`
	Channel     string            `json:"channel,omitempty"`
	Attachments []SlackAttachment `json:"attachments,omitempty"`
}

// SlackAttachment represents a Slack message attachment
type SlackAttachment struct {
	Color     string       `json:"color"`
	Title     string       `json:"title"`
	Text      string       `json:"text"`
	Fields    []SlackField `json:"fields,omitempty"`
	Timestamp int64        `json:"ts,omitempty"`
}

// SlackField represents a field in a Slack attachment
type SlackField struct {
	Title string `json:"title"`
	Value string `json:"value"`
	Short bool   `json:"short"`
}

// NewAlertingSystem creates a new alerting system
func NewAlertingSystem(config *AlertingConfig, logger *zap.Logger, authMonitor *AuthMonitor, errorMonitor *ErrorMonitor) *AlertingSystem {
	if config == nil {
		config = DefaultAlertingConfig()
	}

	if logger == nil {
		logger, _ = zap.NewProduction()
	}

	return &AlertingSystem{
		logger:       logger,
		config:       config,
		authMonitor:  authMonitor,
		errorMonitor: errorMonitor,
		alertQueue:   make(chan Alert, 1000), // Buffer for 1000 alerts
		stopChan:     make(chan struct{}),
	}
}

// Start starts the alerting system
func (as *AlertingSystem) Start(ctx context.Context) error {
	as.logger.Info("🚀 Starting alerting system")

	// Start alert processor
	as.wg.Add(1)
	go as.processAlerts(ctx)

	// Start periodic health checks
	as.wg.Add(1)
	go as.periodicHealthCheck(ctx)

	// Start metrics monitoring
	as.wg.Add(1)
	go as.monitorMetrics(ctx)

	return nil
}

// Stop stops the alerting system
func (as *AlertingSystem) Stop() {
	as.logger.Info("🛑 Stopping alerting system")
	close(as.stopChan)
	as.wg.Wait()
	close(as.alertQueue)
}

// SendAlert sends an alert through the alerting system
func (as *AlertingSystem) SendAlert(alert Alert) {
	select {
	case as.alertQueue <- alert:
		as.logger.Debug("Alert queued", zap.String("alert_id", alert.ID))
	default:
		as.logger.Warn("Alert queue full, dropping alert", zap.String("alert_id", alert.ID))
	}
}

// processAlerts processes alerts from the queue
func (as *AlertingSystem) processAlerts(ctx context.Context) {
	defer as.wg.Done()

	ticker := time.NewTicker(as.config.BatchAlertInterval)
	defer ticker.Stop()

	alertBatch := make([]Alert, 0, as.config.MaxAlertsPerInterval)

	for {
		select {
		case <-ctx.Done():
			return
		case <-as.stopChan:
			return
		case alert := <-as.alertQueue:
			alertBatch = append(alertBatch, alert)
			
			// Process immediately for critical alerts
			if alert.Severity == AlertSeverityCritical {
				as.processAlert(alert)
			}
			
			// Process batch if it reaches max size
			if len(alertBatch) >= as.config.MaxAlertsPerInterval {
				as.processBatch(alertBatch)
				alertBatch = alertBatch[:0] // Reset slice
			}
		case <-ticker.C:
			// Process batch on timer
			if len(alertBatch) > 0 {
				as.processBatch(alertBatch)
				alertBatch = alertBatch[:0] // Reset slice
			}
		}
	}
}

// processBatch processes a batch of alerts
func (as *AlertingSystem) processBatch(alerts []Alert) {
	for _, alert := range alerts {
		if alert.Severity != AlertSeverityCritical { // Critical alerts already processed
			as.processAlert(alert)
		}
	}
}

// processAlert processes a single alert
func (as *AlertingSystem) processAlert(alert Alert) {
	// Check if alert should be sent based on severity
	if !as.shouldSendAlert(alert) {
		return
	}

	as.logger.Info("🚨 Processing alert",
		zap.String("alert_id", alert.ID),
		zap.String("severity", string(alert.Severity)),
		zap.String("type", string(alert.Type)),
		zap.String("title", alert.Title),
	)

	// Send to configured channels
	if as.config.EnableSlackAlerts && as.config.SlackWebhookURL != "" {
		go as.sendSlackAlert(alert)
	}

	if as.config.EnableEmailAlerts {
		go as.sendEmailAlert(alert)
	}

	if as.config.EnableWebhookAlerts && as.config.WebhookURL != "" {
		go as.sendWebhookAlert(alert)
	}
}

// shouldSendAlert determines if an alert should be sent based on configuration
func (as *AlertingSystem) shouldSendAlert(alert Alert) bool {
	switch alert.Severity {
	case AlertSeverityInfo:
		return as.config.EnableInfoAlerts
	case AlertSeverityWarning:
		return as.config.EnableWarningAlerts
	case AlertSeverityError:
		return as.config.EnableErrorAlerts
	case AlertSeverityCritical:
		return as.config.EnableCriticalAlerts
	default:
		return false
	}
}

// sendSlackAlert sends an alert to Slack
func (as *AlertingSystem) sendSlackAlert(alert Alert) {
	color := as.getSlackColor(alert.Severity)
	emoji := as.getAlertEmoji(alert.Severity)

	message := SlackMessage{
		Username:  "CarNow Auth Monitor",
		IconEmoji: ":warning:",
		Attachments: []SlackAttachment{
			{
				Color:     color,
				Title:     fmt.Sprintf("%s %s", emoji, alert.Title),
				Text:      alert.Message,
				Timestamp: alert.Timestamp.Unix(),
				Fields: []SlackField{
					{
						Title: "Severity",
						Value: string(alert.Severity),
						Short: true,
					},
					{
						Title: "Type",
						Value: string(alert.Type),
						Short: true,
					},
					{
						Title: "Source",
						Value: alert.Source,
						Short: true,
					},
				},
			},
		},
	}

	// Add context fields
	for key, value := range alert.Context {
		message.Attachments[0].Fields = append(message.Attachments[0].Fields, SlackField{
			Title: key,
			Value: fmt.Sprintf("%v", value),
			Short: true,
		})
	}

	// Send to Slack
	if err := as.sendToSlack(message); err != nil {
		as.logger.Error("Failed to send Slack alert",
			zap.String("alert_id", alert.ID),
			zap.Error(err),
		)
	}
}

// sendToSlack sends a message to Slack webhook
func (as *AlertingSystem) sendToSlack(message SlackMessage) error {
	jsonData, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("failed to marshal Slack message: %w", err)
	}

	resp, err := http.Post(as.config.SlackWebhookURL, "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("failed to send to Slack: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("Slack webhook returned status %d", resp.StatusCode)
	}

	return nil
}

// sendEmailAlert sends an alert via email (placeholder implementation)
func (as *AlertingSystem) sendEmailAlert(alert Alert) {
	// TODO: Implement email sending using SMTP
	as.logger.Info("📧 Would send email alert",
		zap.String("alert_id", alert.ID),
		zap.String("title", alert.Title),
		zap.Strings("recipients", as.config.EmailRecipients),
	)
}

// sendWebhookAlert sends an alert to a webhook endpoint
func (as *AlertingSystem) sendWebhookAlert(alert Alert) {
	jsonData, err := json.Marshal(alert)
	if err != nil {
		as.logger.Error("Failed to marshal webhook alert",
			zap.String("alert_id", alert.ID),
			zap.Error(err),
		)
		return
	}

	resp, err := http.Post(as.config.WebhookURL, "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		as.logger.Error("Failed to send webhook alert",
			zap.String("alert_id", alert.ID),
			zap.Error(err),
		)
		return
	}
	defer resp.Body.Close()

	if resp.StatusCode >= 200 && resp.StatusCode < 300 {
		as.logger.Info("✅ Webhook alert sent successfully",
			zap.String("alert_id", alert.ID),
		)
	} else {
		as.logger.Error("Webhook returned error status",
			zap.String("alert_id", alert.ID),
			zap.Int("status_code", resp.StatusCode),
		)
	}
}

// periodicHealthCheck performs periodic health checks and sends alerts
func (as *AlertingSystem) periodicHealthCheck(ctx context.Context) {
	defer as.wg.Done()

	ticker := time.NewTicker(5 * time.Minute) // Check every 5 minutes
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-as.stopChan:
			return
		case <-ticker.C:
			as.checkSystemHealth()
		}
	}
}

// checkSystemHealth checks overall system health and sends alerts if needed
func (as *AlertingSystem) checkSystemHealth() {
	if as.authMonitor == nil {
		return
	}

	healthStatus := as.authMonitor.GetHealthStatus()
	status, ok := healthStatus["status"].(string)
	if !ok {
		return
	}

	if status == "unhealthy" {
		alert := Alert{
			ID:        generateAlertID(),
			Timestamp: time.Now(),
			Severity:  AlertSeverityCritical,
			Type:      AlertTypeSystemHealth,
			Title:     "Authentication System Unhealthy",
			Message:   "The authentication system is reporting unhealthy status",
			Source:    "health_check",
			Context: map[string]interface{}{
				"health_status": healthStatus,
			},
		}
		as.SendAlert(alert)
	} else if status == "degraded" {
		alert := Alert{
			ID:        generateAlertID(),
			Timestamp: time.Now(),
			Severity:  AlertSeverityWarning,
			Type:      AlertTypeSystemHealth,
			Title:     "Authentication System Degraded",
			Message:   "The authentication system is experiencing degraded performance",
			Source:    "health_check",
			Context: map[string]interface{}{
				"health_status": healthStatus,
			},
		}
		as.SendAlert(alert)
	}
}

// monitorMetrics monitors authentication metrics and sends alerts
func (as *AlertingSystem) monitorMetrics(ctx context.Context) {
	defer as.wg.Done()

	ticker := time.NewTicker(2 * time.Minute) // Check every 2 minutes
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-as.stopChan:
			return
		case <-ticker.C:
			as.checkMetricsThresholds()
		}
	}
}

// checkMetricsThresholds checks metrics against thresholds and sends alerts
func (as *AlertingSystem) checkMetricsThresholds() {
	if as.authMonitor == nil {
		return
	}

	metrics := as.authMonitor.GetMetrics()

	// Check failure rate
	if metrics.TotalRequests > 10 {
		failureRate := float64(metrics.FailedRequests) / float64(metrics.TotalRequests)
		if failureRate > as.config.HighFailureRateThreshold {
			alert := Alert{
				ID:        generateAlertID(),
				Timestamp: time.Now(),
				Severity:  AlertSeverityError,
				Type:      AlertTypeAuthFailure,
				Title:     "High Authentication Failure Rate",
				Message:   fmt.Sprintf("Authentication failure rate is %.2f%%, exceeding threshold of %.2f%%", failureRate*100, as.config.HighFailureRateThreshold*100),
				Source:    "metrics_monitor",
				Context: map[string]interface{}{
					"failure_rate":       failureRate,
					"threshold":          as.config.HighFailureRateThreshold,
					"total_requests":     metrics.TotalRequests,
					"failed_requests":    metrics.FailedRequests,
					"successful_requests": metrics.SuccessfulRequests,
				},
			}
			as.SendAlert(alert)
		}
	}

	// Check suspicious activities
	if metrics.SuspiciousActivities > int64(as.config.SuspiciousActivityThreshold) {
		alert := Alert{
			ID:        generateAlertID(),
			Timestamp: time.Now(),
			Severity:  AlertSeverityWarning,
			Type:      AlertTypeSuspiciousActivity,
			Title:     "Suspicious Activity Detected",
			Message:   fmt.Sprintf("Detected %d suspicious activities, exceeding threshold of %d", metrics.SuspiciousActivities, as.config.SuspiciousActivityThreshold),
			Source:    "metrics_monitor",
			Context: map[string]interface{}{
				"suspicious_activities": metrics.SuspiciousActivities,
				"threshold":             as.config.SuspiciousActivityThreshold,
				"blocked_ips":           len(metrics.BlockedIPs),
			},
		}
		as.SendAlert(alert)
	}

	// Check slow response times
	if metrics.AverageResponseTime > as.config.SlowResponseThreshold {
		alert := Alert{
			ID:        generateAlertID(),
			Timestamp: time.Now(),
			Severity:  AlertSeverityWarning,
			Type:      AlertTypeSlowResponse,
			Title:     "Slow Authentication Response Times",
			Message:   fmt.Sprintf("Average response time is %v, exceeding threshold of %v", metrics.AverageResponseTime, as.config.SlowResponseThreshold),
			Source:    "metrics_monitor",
			Context: map[string]interface{}{
				"average_response_time": metrics.AverageResponseTime.Milliseconds(),
				"threshold":             as.config.SlowResponseThreshold.Milliseconds(),
				"slow_requests":         metrics.SlowRequests,
			},
		}
		as.SendAlert(alert)
	}
}

// Helper functions

func (as *AlertingSystem) getSlackColor(severity AlertSeverity) string {
	switch severity {
	case AlertSeverityInfo:
		return "#36a64f" // Green
	case AlertSeverityWarning:
		return "#ff9500" // Orange
	case AlertSeverityError:
		return "#ff0000" // Red
	case AlertSeverityCritical:
		return "#8b0000" // Dark Red
	default:
		return "#808080" // Gray
	}
}

func (as *AlertingSystem) getAlertEmoji(severity AlertSeverity) string {
	switch severity {
	case AlertSeverityInfo:
		return "ℹ️"
	case AlertSeverityWarning:
		return "⚠️"
	case AlertSeverityError:
		return "❌"
	case AlertSeverityCritical:
		return "🚨"
	default:
		return "📢"
	}
}

func generateAlertID() string {
	return fmt.Sprintf("alert_%d_%d", time.Now().Unix(), time.Now().Nanosecond())
}
