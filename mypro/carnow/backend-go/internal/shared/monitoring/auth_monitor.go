package monitoring

import (
	"context"
	"fmt"
	"sync"
	"time"

	"go.uber.org/zap"
)

// AuthMonitor provides specialized monitoring for authentication operations
type AuthMonitor struct {
	logger       *zap.Logger
	config       *AuthMonitorConfig
	metrics      *AuthMetrics
	errorMonitor *ErrorMonitor
	mu           sync.RWMutex
}

// AuthMonitorConfig contains configuration for authentication monitoring
type AuthMonitorConfig struct {
	// Performance thresholds
	SlowRequestThreshold    time.Duration `json:"slow_request_threshold"`
	FailureRateThreshold    float64       `json:"failure_rate_threshold"`
	SuspiciousActivityLimit int           `json:"suspicious_activity_limit"`
	
	// Monitoring settings
	EnablePerformanceTracking bool `json:"enable_performance_tracking"`
	EnableSecurityMonitoring  bool `json:"enable_security_monitoring"`
	EnableUserBehaviorTracking bool `json:"enable_user_behavior_tracking"`
	
	// Alerting
	AlertOnHighFailureRate    bool          `json:"alert_on_high_failure_rate"`
	AlertOnSuspiciousActivity bool          `json:"alert_on_suspicious_activity"`
	AlertCooldownPeriod       time.Duration `json:"alert_cooldown_period"`
}

// DefaultAuthMonitorConfig returns default configuration
func DefaultAuthMonitorConfig() *AuthMonitorConfig {
	return &AuthMonitorConfig{
		SlowRequestThreshold:       2 * time.Second,
		FailureRateThreshold:       0.1, // 10% failure rate threshold
		SuspiciousActivityLimit:    5,   // 5 failed attempts in short time
		EnablePerformanceTracking:  true,
		EnableSecurityMonitoring:   true,
		EnableUserBehaviorTracking: true,
		AlertOnHighFailureRate:     true,
		AlertOnSuspiciousActivity:  true,
		AlertCooldownPeriod:        10 * time.Minute,
	}
}

// AuthMetrics tracks authentication-specific metrics
type AuthMetrics struct {
	// Request metrics
	TotalRequests        int64            `json:"total_requests"`
	SuccessfulRequests   int64            `json:"successful_requests"`
	FailedRequests       int64            `json:"failed_requests"`
	RequestsByEndpoint   map[string]int64 `json:"requests_by_endpoint"`
	
	// Performance metrics
	AverageResponseTime  time.Duration    `json:"average_response_time"`
	SlowRequests         int64            `json:"slow_requests"`
	ResponseTimes        []time.Duration  `json:"response_times"`
	
	// Security metrics
	FailedLoginAttempts  int64            `json:"failed_login_attempts"`
	SuspiciousActivities int64            `json:"suspicious_activities"`
	BlockedIPs           map[string]int64 `json:"blocked_ips"`
	
	// User behavior metrics
	UniqueUsers          int64            `json:"unique_users"`
	UsersByProvider      map[string]int64 `json:"users_by_provider"`
	SessionDurations     []time.Duration  `json:"session_durations"`
	
	// Time-based metrics
	LastRequestTime      time.Time        `json:"last_request_time"`
	LastFailureTime      time.Time        `json:"last_failure_time"`
	
	mu sync.RWMutex
}

// AuthEvent represents an authentication event
type AuthEvent struct {
	ID            string                 `json:"id"`
	Timestamp     time.Time              `json:"timestamp"`
	EventType     AuthEventType          `json:"event_type"`
	Endpoint      string                 `json:"endpoint"`
	UserID        string                 `json:"user_id,omitempty"`
	Email         string                 `json:"email,omitempty"`
	Provider      string                 `json:"provider,omitempty"`
	IPAddress     string                 `json:"ip_address,omitempty"`
	UserAgent     string                 `json:"user_agent,omitempty"`
	Success       bool                   `json:"success"`
	ErrorMessage  string                 `json:"error_message,omitempty"`
	ResponseTime  time.Duration          `json:"response_time"`
	RequestID     string                 `json:"request_id,omitempty"`
	Context       map[string]interface{} `json:"context,omitempty"`
}

// AuthEventType represents different types of authentication events
type AuthEventType string

const (
	AuthEventLogin          AuthEventType = "login"
	AuthEventRegister       AuthEventType = "register"
	AuthEventLogout         AuthEventType = "logout"
	AuthEventTokenRefresh   AuthEventType = "token_refresh"
	AuthEventPasswordReset  AuthEventType = "password_reset"
	AuthEventEmailVerify    AuthEventType = "email_verify"
	AuthEventGoogleOAuth    AuthEventType = "google_oauth"
	AuthEventSuspicious     AuthEventType = "suspicious_activity"
)

// NewAuthMonitor creates a new authentication monitor
func NewAuthMonitor(config *AuthMonitorConfig, logger *zap.Logger, errorMonitor *ErrorMonitor) *AuthMonitor {
	if config == nil {
		config = DefaultAuthMonitorConfig()
	}

	if logger == nil {
		logger, _ = zap.NewProduction()
	}

	return &AuthMonitor{
		logger:       logger,
		config:       config,
		errorMonitor: errorMonitor,
		metrics: &AuthMetrics{
			RequestsByEndpoint: make(map[string]int64),
			BlockedIPs:         make(map[string]int64),
			UsersByProvider:    make(map[string]int64),
			ResponseTimes:      make([]time.Duration, 0, 1000),
			SessionDurations:   make([]time.Duration, 0, 1000),
		},
	}
}

// RecordAuthEvent records an authentication event
func (am *AuthMonitor) RecordAuthEvent(ctx context.Context, event AuthEvent) {
	am.mu.Lock()
	defer am.mu.Unlock()

	// Update basic metrics
	am.metrics.TotalRequests++
	am.metrics.RequestsByEndpoint[event.Endpoint]++
	am.metrics.LastRequestTime = event.Timestamp

	if event.Success {
		am.metrics.SuccessfulRequests++
		if event.Provider != "" {
			am.metrics.UsersByProvider[event.Provider]++
		}
		if event.UserID != "" {
			am.metrics.UniqueUsers++
		}
	} else {
		am.metrics.FailedRequests++
		am.metrics.LastFailureTime = event.Timestamp
		
		if event.EventType == AuthEventLogin {
			am.metrics.FailedLoginAttempts++
		}
	}

	// Track performance metrics
	if am.config.EnablePerformanceTracking {
		am.updatePerformanceMetrics(event)
	}

	// Track security metrics
	if am.config.EnableSecurityMonitoring {
		am.updateSecurityMetrics(event)
	}

	// Log the event
	am.logAuthEvent(event)

	// Check for alerts
	am.checkAlerts(event)
}

// updatePerformanceMetrics updates performance-related metrics
func (am *AuthMonitor) updatePerformanceMetrics(event AuthEvent) {
	// Track response times
	am.metrics.ResponseTimes = append(am.metrics.ResponseTimes, event.ResponseTime)
	
	// Keep only last 1000 response times
	if len(am.metrics.ResponseTimes) > 1000 {
		am.metrics.ResponseTimes = am.metrics.ResponseTimes[1:]
	}

	// Calculate average response time
	var total time.Duration
	for _, rt := range am.metrics.ResponseTimes {
		total += rt
	}
	am.metrics.AverageResponseTime = total / time.Duration(len(am.metrics.ResponseTimes))

	// Track slow requests
	if event.ResponseTime > am.config.SlowRequestThreshold {
		am.metrics.SlowRequests++
	}
}

// updateSecurityMetrics updates security-related metrics
func (am *AuthMonitor) updateSecurityMetrics(event AuthEvent) {
	// Track suspicious activities
	if !event.Success && event.EventType == AuthEventLogin {
		if event.IPAddress != "" {
			am.metrics.BlockedIPs[event.IPAddress]++
			
			// Check for suspicious activity
			if am.metrics.BlockedIPs[event.IPAddress] >= int64(am.config.SuspiciousActivityLimit) {
				am.metrics.SuspiciousActivities++
				
				// Create suspicious activity event
				suspiciousEvent := AuthEvent{
					ID:        generateEventID(),
					Timestamp: time.Now(),
					EventType: AuthEventSuspicious,
					IPAddress: event.IPAddress,
					Context: map[string]interface{}{
						"failed_attempts": am.metrics.BlockedIPs[event.IPAddress],
						"original_event":  event,
					},
				}
				
				// Record as error
				if am.errorMonitor != nil {
					am.errorMonitor.RecordError(
						context.Background(),
						fmt.Errorf("suspicious activity detected from IP %s", event.IPAddress),
						ErrorLevelWarning,
						"auth/suspicious",
					)
				}
				
				am.logger.Warn("🚨 Suspicious Activity Detected",
					zap.String("ip_address", event.IPAddress),
					zap.Int64("failed_attempts", am.metrics.BlockedIPs[event.IPAddress]),
					zap.String("event_id", suspiciousEvent.ID),
				)
			}
		}
	}
}

// logAuthEvent logs the authentication event
func (am *AuthMonitor) logAuthEvent(event AuthEvent) {
	fields := []zap.Field{
		zap.String("event_id", event.ID),
		zap.String("event_type", string(event.EventType)),
		zap.String("endpoint", event.Endpoint),
		zap.Bool("success", event.Success),
		zap.Duration("response_time", event.ResponseTime),
		zap.Time("timestamp", event.Timestamp),
	}

	if event.UserID != "" {
		fields = append(fields, zap.String("user_id", event.UserID))
	}
	if event.Email != "" {
		fields = append(fields, zap.String("email", event.Email))
	}
	if event.Provider != "" {
		fields = append(fields, zap.String("provider", event.Provider))
	}
	if event.IPAddress != "" {
		fields = append(fields, zap.String("ip_address", event.IPAddress))
	}
	if event.RequestID != "" {
		fields = append(fields, zap.String("request_id", event.RequestID))
	}
	if event.ErrorMessage != "" {
		fields = append(fields, zap.String("error", event.ErrorMessage))
	}

	if event.Success {
		am.logger.Info("✅ Auth Success", fields...)
	} else {
		am.logger.Warn("❌ Auth Failure", fields...)
	}
}

// checkAlerts checks if any alerts should be triggered
func (am *AuthMonitor) checkAlerts(event AuthEvent) {
	// Check failure rate
	if am.config.AlertOnHighFailureRate {
		failureRate := float64(am.metrics.FailedRequests) / float64(am.metrics.TotalRequests)
		if failureRate > am.config.FailureRateThreshold && am.metrics.TotalRequests > 10 {
			am.logger.Error("🚨 High Authentication Failure Rate Alert",
				zap.Float64("failure_rate", failureRate),
				zap.Float64("threshold", am.config.FailureRateThreshold),
				zap.Int64("total_requests", am.metrics.TotalRequests),
				zap.Int64("failed_requests", am.metrics.FailedRequests),
			)
		}
	}

	// Check suspicious activity
	if am.config.AlertOnSuspiciousActivity && event.EventType == AuthEventSuspicious {
		am.logger.Error("🚨 Suspicious Activity Alert",
			zap.String("event_id", event.ID),
			zap.String("ip_address", event.IPAddress),
			zap.Any("context", event.Context),
		)
	}
}

// GetMetrics returns current authentication metrics
func (am *AuthMonitor) GetMetrics() *AuthMetrics {
	am.metrics.mu.RLock()
	defer am.metrics.mu.RUnlock()

	// Create a copy to avoid race conditions
	metrics := &AuthMetrics{
		TotalRequests:        am.metrics.TotalRequests,
		SuccessfulRequests:   am.metrics.SuccessfulRequests,
		FailedRequests:       am.metrics.FailedRequests,
		RequestsByEndpoint:   make(map[string]int64),
		AverageResponseTime:  am.metrics.AverageResponseTime,
		SlowRequests:         am.metrics.SlowRequests,
		ResponseTimes:        make([]time.Duration, len(am.metrics.ResponseTimes)),
		FailedLoginAttempts:  am.metrics.FailedLoginAttempts,
		SuspiciousActivities: am.metrics.SuspiciousActivities,
		BlockedIPs:           make(map[string]int64),
		UniqueUsers:          am.metrics.UniqueUsers,
		UsersByProvider:      make(map[string]int64),
		SessionDurations:     make([]time.Duration, len(am.metrics.SessionDurations)),
		LastRequestTime:      am.metrics.LastRequestTime,
		LastFailureTime:      am.metrics.LastFailureTime,
	}

	// Copy maps
	for k, v := range am.metrics.RequestsByEndpoint {
		metrics.RequestsByEndpoint[k] = v
	}
	for k, v := range am.metrics.BlockedIPs {
		metrics.BlockedIPs[k] = v
	}
	for k, v := range am.metrics.UsersByProvider {
		metrics.UsersByProvider[k] = v
	}

	// Copy slices
	copy(metrics.ResponseTimes, am.metrics.ResponseTimes)
	copy(metrics.SessionDurations, am.metrics.SessionDurations)

	return metrics
}

// GetHealthStatus returns the health status of authentication services
func (am *AuthMonitor) GetHealthStatus() map[string]interface{} {
	metrics := am.GetMetrics()
	
	status := map[string]interface{}{
		"status": "healthy",
		"timestamp": time.Now(),
		"metrics": map[string]interface{}{
			"total_requests":         metrics.TotalRequests,
			"success_rate":          float64(metrics.SuccessfulRequests) / float64(metrics.TotalRequests),
			"average_response_time": metrics.AverageResponseTime.Milliseconds(),
			"failed_login_attempts": metrics.FailedLoginAttempts,
			"suspicious_activities": metrics.SuspiciousActivities,
			"unique_users":          metrics.UniqueUsers,
		},
	}

	// Determine health status
	if metrics.TotalRequests > 0 {
		successRate := float64(metrics.SuccessfulRequests) / float64(metrics.TotalRequests)
		if successRate < 0.9 {
			status["status"] = "degraded"
		}
		if successRate < 0.5 {
			status["status"] = "unhealthy"
		}
	}

	if metrics.AverageResponseTime > am.config.SlowRequestThreshold {
		status["status"] = "degraded"
	}

	return status
}

// Helper functions

func generateEventID() string {
	return fmt.Sprintf("auth_%d_%d", time.Now().Unix(), time.Now().Nanosecond())
}
