package monitoring

import (
	"context"
	"fmt"
	"runtime"
	"sync"
	"time"

	"go.uber.org/zap"
)

// ErrorMonitor provides centralized error monitoring and reporting
type ErrorMonitor struct {
	logger       *zap.Logger
	config       *ErrorMonitorConfig
	metrics      *ErrorMetrics
	alertManager *AlertManager
	mu           sync.RWMutex
}

// ErrorMonitorConfig contains configuration for error monitoring
type ErrorMonitorConfig struct {
	// Error thresholds
	ErrorRateThreshold  float64       `json:"error_rate_threshold"`
	CriticalErrorWindow time.Duration `json:"critical_error_window"`
	AlertCooldownPeriod time.Duration `json:"alert_cooldown_period"`

	// Monitoring settings
	EnableStackTrace     bool `json:"enable_stack_trace"`
	EnableContextCapture bool `json:"enable_context_capture"`
	MaxErrorsPerMinute   int  `json:"max_errors_per_minute"`

	// Integration settings
	SentryDSN         string `json:"sentry_dsn,omitempty"`
	SlackWebhookURL   string `json:"slack_webhook_url,omitempty"`
	EnableEmailAlerts bool   `json:"enable_email_alerts"`
}

// DefaultErrorMonitorConfig returns default configuration
func DefaultErrorMonitorConfig() *ErrorMonitorConfig {
	return &ErrorMonitorConfig{
		ErrorRateThreshold:   0.05, // 5% error rate threshold
		CriticalErrorWindow:  5 * time.Minute,
		AlertCooldownPeriod:  15 * time.Minute,
		EnableStackTrace:     true,
		EnableContextCapture: true,
		MaxErrorsPerMinute:   100,
		EnableEmailAlerts:    false,
	}
}

// ErrorMetrics tracks error statistics
type ErrorMetrics struct {
	TotalErrors      int64            `json:"total_errors"`
	ErrorsByType     map[string]int64 `json:"errors_by_type"`
	ErrorsByEndpoint map[string]int64 `json:"errors_by_endpoint"`
	CriticalErrors   int64            `json:"critical_errors"`
	LastErrorTime    time.Time        `json:"last_error_time"`
	ErrorRate        float64          `json:"error_rate"`
	RecentErrors     []ErrorEvent     `json:"recent_errors"`
	AlertsSent       int64            `json:"alerts_sent"`
	LastAlertTime    time.Time        `json:"last_alert_time"`
	mu               sync.RWMutex
}

// ErrorEvent represents a single error occurrence
type ErrorEvent struct {
	ID         string                 `json:"id"`
	Timestamp  time.Time              `json:"timestamp"`
	Level      ErrorLevel             `json:"level"`
	Type       string                 `json:"type"`
	Message    string                 `json:"message"`
	Endpoint   string                 `json:"endpoint,omitempty"`
	UserID     string                 `json:"user_id,omitempty"`
	RequestID  string                 `json:"request_id,omitempty"`
	StackTrace string                 `json:"stack_trace,omitempty"`
	Context    map[string]interface{} `json:"context,omitempty"`
	Count      int                    `json:"count"`
}

// ErrorLevel represents the severity of an error
type ErrorLevel string

const (
	ErrorLevelInfo     ErrorLevel = "info"
	ErrorLevelWarning  ErrorLevel = "warning"
	ErrorLevelError    ErrorLevel = "error"
	ErrorLevelCritical ErrorLevel = "critical"
)

// AlertManager handles error alerting
type AlertManager struct {
	config     *ErrorMonitorConfig
	logger     *zap.Logger
	lastAlerts map[string]time.Time
	mu         sync.RWMutex
}

// NewErrorMonitor creates a new error monitor
func NewErrorMonitor(config *ErrorMonitorConfig, logger *zap.Logger) *ErrorMonitor {
	if config == nil {
		config = DefaultErrorMonitorConfig()
	}

	if logger == nil {
		logger, _ = zap.NewProduction()
	}

	return &ErrorMonitor{
		logger: logger,
		config: config,
		metrics: &ErrorMetrics{
			ErrorsByType:     make(map[string]int64),
			ErrorsByEndpoint: make(map[string]int64),
			RecentErrors:     make([]ErrorEvent, 0, 100),
		},
		alertManager: &AlertManager{
			config:     config,
			logger:     logger,
			lastAlerts: make(map[string]time.Time),
		},
	}
}

// RecordError records an error event
func (em *ErrorMonitor) RecordError(ctx context.Context, err error, level ErrorLevel, endpoint string) {
	em.mu.Lock()
	defer em.mu.Unlock()

	// Create error event
	event := ErrorEvent{
		ID:        generateErrorID(),
		Timestamp: time.Now(),
		Level:     level,
		Type:      getErrorType(err),
		Message:   err.Error(),
		Endpoint:  endpoint,
		Count:     1,
	}

	// Add context information if enabled
	if em.config.EnableContextCapture {
		event.Context = extractContextInfo(ctx)
		if userID := getUserIDFromContext(ctx); userID != "" {
			event.UserID = userID
		}
		if requestID := getRequestIDFromContext(ctx); requestID != "" {
			event.RequestID = requestID
		}
	}

	// Add stack trace if enabled
	if em.config.EnableStackTrace {
		event.StackTrace = getStackTrace()
	}

	// Update metrics
	em.updateMetrics(event)

	// Log the error
	em.logError(event)

	// Check if we need to send alerts
	if em.shouldSendAlert(event) {
		go em.sendAlert(event)
	}
}

// updateMetrics updates error metrics
func (em *ErrorMonitor) updateMetrics(event ErrorEvent) {
	em.metrics.mu.Lock()
	defer em.metrics.mu.Unlock()

	em.metrics.TotalErrors++
	em.metrics.ErrorsByType[event.Type]++
	if event.Endpoint != "" {
		em.metrics.ErrorsByEndpoint[event.Endpoint]++
	}

	if event.Level == ErrorLevelCritical {
		em.metrics.CriticalErrors++
	}

	em.metrics.LastErrorTime = event.Timestamp

	// Add to recent errors (keep last 100)
	em.metrics.RecentErrors = append(em.metrics.RecentErrors, event)
	if len(em.metrics.RecentErrors) > 100 {
		em.metrics.RecentErrors = em.metrics.RecentErrors[1:]
	}

	// Calculate error rate (errors per minute in last 5 minutes)
	em.calculateErrorRate()
}

// calculateErrorRate calculates the current error rate
func (em *ErrorMonitor) calculateErrorRate() {
	now := time.Now()
	window := 5 * time.Minute
	cutoff := now.Add(-window)

	var recentErrorCount int64
	for _, event := range em.metrics.RecentErrors {
		if event.Timestamp.After(cutoff) {
			recentErrorCount++
		}
	}

	// Errors per minute
	em.metrics.ErrorRate = float64(recentErrorCount) / window.Minutes()
}

// shouldSendAlert determines if an alert should be sent
func (em *ErrorMonitor) shouldSendAlert(event ErrorEvent) bool {
	// Always alert on critical errors
	if event.Level == ErrorLevelCritical {
		return em.alertManager.canSendAlert("critical")
	}

	// Alert if error rate exceeds threshold
	if em.metrics.ErrorRate > em.config.ErrorRateThreshold {
		return em.alertManager.canSendAlert("error_rate")
	}

	return false
}

// canSendAlert checks if we can send an alert (respects cooldown)
func (am *AlertManager) canSendAlert(alertType string) bool {
	am.mu.Lock()
	defer am.mu.Unlock()

	lastAlert, exists := am.lastAlerts[alertType]
	if !exists {
		am.lastAlerts[alertType] = time.Now()
		return true
	}

	if time.Since(lastAlert) > am.config.AlertCooldownPeriod {
		am.lastAlerts[alertType] = time.Now()
		return true
	}

	return false
}

// sendAlert sends an alert for the error
func (em *ErrorMonitor) sendAlert(event ErrorEvent) {
	em.metrics.mu.Lock()
	em.metrics.AlertsSent++
	em.metrics.LastAlertTime = time.Now()
	em.metrics.mu.Unlock()

	// Log alert
	em.logger.Error("🚨 Error Alert Triggered",
		zap.String("error_id", event.ID),
		zap.String("level", string(event.Level)),
		zap.String("type", event.Type),
		zap.String("message", event.Message),
		zap.String("endpoint", event.Endpoint),
		zap.Float64("error_rate", em.metrics.ErrorRate),
	)

	// Send to external services (Slack, email, etc.)
	if em.config.SlackWebhookURL != "" {
		go em.sendSlackAlert(event)
	}

	// TODO: Implement Sentry integration
	// TODO: Implement email alerts
}

// sendSlackAlert sends alert to Slack
func (em *ErrorMonitor) sendSlackAlert(event ErrorEvent) {
	// Implementation would send to Slack webhook
	// For now, just log
	em.logger.Info("📱 Would send Slack alert",
		zap.String("error_id", event.ID),
		zap.String("message", event.Message),
	)
}

// logError logs the error event
func (em *ErrorMonitor) logError(event ErrorEvent) {
	fields := []zap.Field{
		zap.String("error_id", event.ID),
		zap.String("type", event.Type),
		zap.String("endpoint", event.Endpoint),
		zap.Time("timestamp", event.Timestamp),
	}

	if event.UserID != "" {
		fields = append(fields, zap.String("user_id", event.UserID))
	}

	if event.RequestID != "" {
		fields = append(fields, zap.String("request_id", event.RequestID))
	}

	switch event.Level {
	case ErrorLevelCritical:
		em.logger.Error("🔴 CRITICAL ERROR", fields...)
	case ErrorLevelError:
		em.logger.Error("❌ ERROR", fields...)
	case ErrorLevelWarning:
		em.logger.Warn("⚠️ WARNING", fields...)
	default:
		em.logger.Info("ℹ️ INFO", fields...)
	}
}

// GetMetrics returns current error metrics
func (em *ErrorMonitor) GetMetrics() *ErrorMetrics {
	em.metrics.mu.RLock()
	defer em.metrics.mu.RUnlock()

	// Create a copy to avoid race conditions
	metrics := &ErrorMetrics{
		TotalErrors:      em.metrics.TotalErrors,
		ErrorsByType:     make(map[string]int64),
		ErrorsByEndpoint: make(map[string]int64),
		CriticalErrors:   em.metrics.CriticalErrors,
		LastErrorTime:    em.metrics.LastErrorTime,
		ErrorRate:        em.metrics.ErrorRate,
		RecentErrors:     make([]ErrorEvent, len(em.metrics.RecentErrors)),
		AlertsSent:       em.metrics.AlertsSent,
		LastAlertTime:    em.metrics.LastAlertTime,
	}

	// Copy maps
	for k, v := range em.metrics.ErrorsByType {
		metrics.ErrorsByType[k] = v
	}
	for k, v := range em.metrics.ErrorsByEndpoint {
		metrics.ErrorsByEndpoint[k] = v
	}

	// Copy recent errors
	copy(metrics.RecentErrors, em.metrics.RecentErrors)

	return metrics
}

// Helper functions

func generateErrorID() string {
	return fmt.Sprintf("err_%d_%d", time.Now().Unix(), time.Now().Nanosecond())
}

func getErrorType(err error) string {
	return fmt.Sprintf("%T", err)
}

func getStackTrace() string {
	buf := make([]byte, 4096)
	n := runtime.Stack(buf, false)
	return string(buf[:n])
}

func extractContextInfo(ctx context.Context) map[string]interface{} {
	info := make(map[string]interface{})

	// Extract common context values
	if deadline, ok := ctx.Deadline(); ok {
		info["deadline"] = deadline
	}

	// Add more context extraction as needed
	return info
}

func getUserIDFromContext(ctx context.Context) string {
	if userID := ctx.Value("user_id"); userID != nil {
		if id, ok := userID.(string); ok {
			return id
		}
	}
	return ""
}

func getRequestIDFromContext(ctx context.Context) string {
	if requestID := ctx.Value("request_id"); requestID != nil {
		if id, ok := requestID.(string); ok {
			return id
		}
	}
	return ""
}
