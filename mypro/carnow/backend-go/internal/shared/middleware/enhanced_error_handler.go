package middleware

import (
	"fmt"
	"net/http"
	"runtime"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

// Enhanced Error Handler for Phase 3: Error Handling and Resilience
//
// Features:
// - Structured error responses with proper HTTP status codes
// - Comprehensive error logging and monitoring
// - Error categorization and severity levels
// - Request context tracking
// - Recovery from panics with detailed logging
// - Performance impact monitoring

// ErrorSeverity represents the severity level of an error
type ErrorSeverity string

const (
	SeverityLow      ErrorSeverity = "low"
	SeverityMedium   ErrorSeverity = "medium"
	SeverityHigh     ErrorSeverity = "high"
	SeverityCritical ErrorSeverity = "critical"
)

// ErrorCategory represents the category of an error
type ErrorCategory string

const (
	CategoryValidation    ErrorCategory = "validation"
	CategoryAuth          ErrorCategory = "authentication"
	CategoryAuthorization ErrorCategory = "authorization"
	CategoryBusiness      ErrorCategory = "business"
	CategoryDatabase      ErrorCategory = "database"
	CategoryNetwork       ErrorCategory = "network"
	CategorySystem        ErrorCategory = "system"
	CategoryUnknown       ErrorCategory = "unknown"
)

// EnhancedError represents a structured error with metadata
type EnhancedError struct {
	Code       string                 `json:"code"`
	Message    string                 `json:"message"`
	Details    string                 `json:"details,omitempty"`
	Category   ErrorCategory          `json:"category"`
	Severity   ErrorSeverity          `json:"severity"`
	HTTPStatus int                    `json:"-"`
	Timestamp  time.Time              `json:"timestamp"`
	RequestID  string                 `json:"request_id,omitempty"`
	UserID     string                 `json:"user_id,omitempty"`
	Context    map[string]interface{} `json:"context,omitempty"`
	StackTrace string                 `json:"stack_trace,omitempty"`
}

// ErrorResponse represents the standardized API error response
type ErrorResponse struct {
	Success   bool           `json:"success"`
	Error     *EnhancedError `json:"error"`
	Timestamp time.Time      `json:"timestamp"`
	RequestID string         `json:"request_id,omitempty"`
	Path      string         `json:"path,omitempty"`
	Method    string         `json:"method,omitempty"`
}

// EnhancedErrorHandler provides comprehensive error handling capabilities
type EnhancedErrorHandler struct {
	logger  *zap.Logger
	config  *ErrorHandlerConfig
	metrics *ErrorMetrics
}

// ErrorHandlerConfig contains configuration for the error handler
type ErrorHandlerConfig struct {
	IncludeStackTrace bool
	LogLevel          zapcore.Level
	MaxStackDepth     int
	EnableMetrics     bool
	SanitizeErrors    bool
}

// ErrorMetrics tracks error statistics
type ErrorMetrics struct {
	TotalErrors      int64
	ErrorsByCategory map[ErrorCategory]int64
	ErrorsBySeverity map[ErrorSeverity]int64
	ErrorsByStatus   map[int]int64
	PanicRecoveries  int64
}

// NewEnhancedErrorHandler creates a new enhanced error handler
func NewEnhancedErrorHandler(logger *zap.Logger, config *ErrorHandlerConfig) *EnhancedErrorHandler {
	if config == nil {
		config = &ErrorHandlerConfig{
			IncludeStackTrace: false,
			LogLevel:          zap.WarnLevel,
			MaxStackDepth:     10,
			EnableMetrics:     true,
			SanitizeErrors:    true,
		}
	}

	return &EnhancedErrorHandler{
		logger: logger,
		config: config,
		metrics: &ErrorMetrics{
			ErrorsByCategory: make(map[ErrorCategory]int64),
			ErrorsBySeverity: make(map[ErrorSeverity]int64),
			ErrorsByStatus:   make(map[int]int64),
		},
	}
}

// ErrorHandlerMiddleware returns a Gin middleware for enhanced error handling
func (h *EnhancedErrorHandler) ErrorHandlerMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Add request ID to context
		requestID := generateRequestID()
		c.Set("request_id", requestID)
		c.Header("X-Request-ID", requestID)

		// Recover from panics
		defer func() {
			if err := recover(); err != nil {
				h.handlePanic(c, err, requestID)
			}
		}()

		// Execute the request
		c.Next()

		// Handle any errors that occurred during request processing
		if len(c.Errors) > 0 {
			h.handleErrors(c, requestID)
		}
	}
}

// handlePanic handles panic recovery with detailed logging
func (h *EnhancedErrorHandler) handlePanic(c *gin.Context, recovered interface{}, requestID string) {
	// Increment panic counter
	h.metrics.PanicRecoveries++

	// Get stack trace
	stack := make([]byte, 4096)
	length := runtime.Stack(stack, false)
	stackTrace := string(stack[:length])

	// Create enhanced error for panic
	enhancedErr := &EnhancedError{
		Code:       "PANIC_RECOVERED",
		Message:    "Internal server error - panic recovered",
		Details:    fmt.Sprintf("Panic: %v", recovered),
		Category:   CategorySystem,
		Severity:   SeverityCritical,
		HTTPStatus: http.StatusInternalServerError,
		Timestamp:  time.Now(),
		RequestID:  requestID,
		StackTrace: stackTrace,
		Context: map[string]interface{}{
			"panic_value": recovered,
			"path":        c.Request.URL.Path,
			"method":      c.Request.Method,
			"user_agent":  c.GetHeader("User-Agent"),
			"client_ip":   c.ClientIP(),
		},
	}

	// Get user ID if available
	if userID, exists := c.Get("user_id"); exists {
		if uid, ok := userID.(string); ok {
			enhancedErr.UserID = uid
		}
	}

	// Log the panic
	h.logError(enhancedErr)

	// Update metrics
	h.updateMetrics(enhancedErr)

	// Send error response
	h.sendErrorResponse(c, enhancedErr)
}

// handleErrors processes errors from the Gin context
func (h *EnhancedErrorHandler) handleErrors(c *gin.Context, requestID string) {
	// Get the last error (most recent)
	lastError := c.Errors.Last()
	if lastError == nil {
		return
	}

	// Convert to enhanced error
	enhancedErr := h.convertToEnhancedError(lastError.Err, c, requestID)

	// Log the error
	h.logError(enhancedErr)

	// Update metrics
	h.updateMetrics(enhancedErr)

	// Send error response if not already sent
	if !c.Writer.Written() {
		h.sendErrorResponse(c, enhancedErr)
	}
}

// convertToEnhancedError converts a standard error to an enhanced error
func (h *EnhancedErrorHandler) convertToEnhancedError(err error, c *gin.Context, requestID string) *EnhancedError {
	enhanced := &EnhancedError{
		Timestamp: time.Now(),
		RequestID: requestID,
		Context: map[string]interface{}{
			"path":       c.Request.URL.Path,
			"method":     c.Request.Method,
			"user_agent": c.GetHeader("User-Agent"),
			"client_ip":  c.ClientIP(),
		},
	}

	// Get user ID if available
	if userID, exists := c.Get("user_id"); exists {
		if uid, ok := userID.(string); ok {
			enhanced.UserID = uid
		}
	}

	// Determine error details based on error type and message
	h.categorizeError(err, enhanced)

	// Add stack trace if enabled
	if h.config.IncludeStackTrace && enhanced.Severity == SeverityCritical {
		enhanced.StackTrace = h.getStackTrace()
	}

	return enhanced
}

// categorizeError determines the category, severity, and HTTP status of an error
func (h *EnhancedErrorHandler) categorizeError(err error, enhanced *EnhancedError) {
	errorMsg := strings.ToLower(err.Error())

	// Default values
	enhanced.Code = "INTERNAL_ERROR"
	enhanced.Message = "An internal error occurred"
	enhanced.Category = CategorySystem
	enhanced.Severity = SeverityHigh
	enhanced.HTTPStatus = http.StatusInternalServerError

	// Categorize based on error message patterns
	switch {
	// Validation errors
	case strings.Contains(errorMsg, "validation") || strings.Contains(errorMsg, "invalid") ||
		strings.Contains(errorMsg, "required") || strings.Contains(errorMsg, "format"):
		enhanced.Code = "VALIDATION_ERROR"
		enhanced.Message = "Validation failed"
		enhanced.Details = err.Error()
		enhanced.Category = CategoryValidation
		enhanced.Severity = SeverityMedium
		enhanced.HTTPStatus = http.StatusBadRequest

	// Authentication errors
	case strings.Contains(errorMsg, "unauthorized") || strings.Contains(errorMsg, "authentication") ||
		strings.Contains(errorMsg, "token") || strings.Contains(errorMsg, "login"):
		enhanced.Code = "AUTHENTICATION_ERROR"
		enhanced.Message = "Authentication failed"
		enhanced.Details = h.sanitizeErrorMessage(err.Error())
		enhanced.Category = CategoryAuth
		enhanced.Severity = SeverityHigh
		enhanced.HTTPStatus = http.StatusUnauthorized

	// Authorization errors
	case strings.Contains(errorMsg, "forbidden") || strings.Contains(errorMsg, "permission") ||
		strings.Contains(errorMsg, "access denied"):
		enhanced.Code = "AUTHORIZATION_ERROR"
		enhanced.Message = "Access denied"
		enhanced.Details = h.sanitizeErrorMessage(err.Error())
		enhanced.Category = CategoryAuthorization
		enhanced.Severity = SeverityMedium
		enhanced.HTTPStatus = http.StatusForbidden

	// Database errors
	case strings.Contains(errorMsg, "database") || strings.Contains(errorMsg, "sql") ||
		strings.Contains(errorMsg, "connection") || strings.Contains(errorMsg, "timeout"):
		enhanced.Code = "DATABASE_ERROR"
		enhanced.Message = "Database operation failed"
		enhanced.Details = h.sanitizeErrorMessage(err.Error())
		enhanced.Category = CategoryDatabase
		enhanced.Severity = SeverityHigh
		enhanced.HTTPStatus = http.StatusInternalServerError

	// Network errors
	case strings.Contains(errorMsg, "network") || strings.Contains(errorMsg, "timeout") ||
		strings.Contains(errorMsg, "connection refused"):
		enhanced.Code = "NETWORK_ERROR"
		enhanced.Message = "Network operation failed"
		enhanced.Details = h.sanitizeErrorMessage(err.Error())
		enhanced.Category = CategoryNetwork
		enhanced.Severity = SeverityHigh
		enhanced.HTTPStatus = http.StatusBadGateway

	// Business logic errors
	case strings.Contains(errorMsg, "business") || strings.Contains(errorMsg, "rule") ||
		strings.Contains(errorMsg, "constraint"):
		enhanced.Code = "BUSINESS_ERROR"
		enhanced.Message = "Business rule violation"
		enhanced.Details = err.Error()
		enhanced.Category = CategoryBusiness
		enhanced.Severity = SeverityMedium
		enhanced.HTTPStatus = http.StatusUnprocessableEntity

	// Not found errors
	case strings.Contains(errorMsg, "not found") || strings.Contains(errorMsg, "does not exist"):
		enhanced.Code = "NOT_FOUND"
		enhanced.Message = "Resource not found"
		enhanced.Details = err.Error()
		enhanced.Category = CategoryBusiness
		enhanced.Severity = SeverityLow
		enhanced.HTTPStatus = http.StatusNotFound

	default:
		// Keep default values for unknown errors
		enhanced.Details = h.sanitizeErrorMessage(err.Error())
	}
}

// sanitizeErrorMessage removes sensitive information from error messages
func (h *EnhancedErrorHandler) sanitizeErrorMessage(message string) string {
	if !h.config.SanitizeErrors {
		return message
	}

	// Remove potential sensitive information
	sanitized := message
	sensitivePatterns := []string{
		"password=",
		"token=",
		"secret=",
		"key=",
		"api_key=",
	}

	for _, pattern := range sensitivePatterns {
		if strings.Contains(strings.ToLower(sanitized), pattern) {
			sanitized = "Sensitive information hidden"
			break
		}
	}

	return sanitized
}

// getStackTrace returns the current stack trace
func (h *EnhancedErrorHandler) getStackTrace() string {
	stack := make([]byte, 4096)
	length := runtime.Stack(stack, false)
	return string(stack[:length])
}

// logError logs the enhanced error with appropriate level
func (h *EnhancedErrorHandler) logError(err *EnhancedError) {
	fields := []zap.Field{
		zap.String("code", err.Code),
		zap.String("category", string(err.Category)),
		zap.String("severity", string(err.Severity)),
		zap.String("request_id", err.RequestID),
		zap.String("user_id", err.UserID),
		zap.Int("http_status", err.HTTPStatus),
		zap.Any("context", err.Context),
	}

	if err.StackTrace != "" {
		fields = append(fields, zap.String("stack_trace", err.StackTrace))
	}

	// Log with appropriate level based on severity
	switch err.Severity {
	case SeverityLow:
		h.logger.Info(err.Message, fields...)
	case SeverityMedium:
		h.logger.Warn(err.Message, fields...)
	case SeverityHigh:
		h.logger.Error(err.Message, fields...)
	case SeverityCritical:
		h.logger.Error(err.Message, fields...)
		// Could trigger alerts here
	}
}

// updateMetrics updates error metrics
func (h *EnhancedErrorHandler) updateMetrics(err *EnhancedError) {
	if !h.config.EnableMetrics {
		return
	}

	h.metrics.TotalErrors++
	h.metrics.ErrorsByCategory[err.Category]++
	h.metrics.ErrorsBySeverity[err.Severity]++
	h.metrics.ErrorsByStatus[err.HTTPStatus]++
}

// sendErrorResponse sends a structured error response
func (h *EnhancedErrorHandler) sendErrorResponse(c *gin.Context, err *EnhancedError) {
	response := &ErrorResponse{
		Success:   false,
		Error:     err,
		Timestamp: time.Now(),
		RequestID: err.RequestID,
		Path:      c.Request.URL.Path,
		Method:    c.Request.Method,
	}

	c.JSON(err.HTTPStatus, response)
}

// GetMetrics returns current error metrics
func (h *EnhancedErrorHandler) GetMetrics() *ErrorMetrics {
	return h.metrics
}

// CreateBusinessError creates a business logic error
func CreateBusinessError(code, message, details string) error {
	return &BusinessError{
		Code:    code,
		Message: message,
		Details: details,
	}
}

// CreateValidationError creates a validation error
func CreateValidationError(field, message string) error {
	return &ValidationError{
		Field:   field,
		Message: message,
	}
}

// BusinessError represents a business logic error
type BusinessError struct {
	Code    string
	Message string
	Details string
}

func (e *BusinessError) Error() string {
	return fmt.Sprintf("Business error [%s]: %s - %s", e.Code, e.Message, e.Details)
}

// ValidationError represents a validation error
type ValidationError struct {
	Field   string
	Message string
}

func (e *ValidationError) Error() string {
	return fmt.Sprintf("Validation error: %s - %s", e.Field, e.Message)
}

// generateRequestID generates a unique request ID
func generateRequestID() string {
	return fmt.Sprintf("%d-%d", time.Now().UnixNano(), time.Now().Unix())
}
