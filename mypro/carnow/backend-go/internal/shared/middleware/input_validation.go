package middleware

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// InputValidationConfig holds configuration for input validation
type InputValidationConfig struct {
	MaxRequestSize               int64         // Maximum request body size in bytes
	MaxStringLength              int           // Maximum length for string fields
	MaxArrayLength               int           // Maximum length for arrays
	RequestTimeout               time.Duration // Request processing timeout
	EnableXSSProtection          bool          // Enable XSS protection
	EnableSQLInjectionProtection bool          // Enable SQL injection protection
	Logger                       *zap.Logger   // Logger instance
}

// DefaultInputValidationConfig returns default configuration
func DefaultInputValidationConfig() *InputValidationConfig {
	logger, _ := zap.NewProduction()
	return &InputValidationConfig{
		MaxRequestSize:               1024 * 1024, // 1MB
		MaxStringLength:              1000,
		MaxArrayLength:               100,
		RequestTimeout:               30 * time.Second,
		EnableXSSProtection:          true,
		EnableSQLInjectionProtection: true,
		Logger:                       logger,
	}
}

// InputValidationMiddleware creates a middleware for comprehensive input validation
func InputValidationMiddleware(config *InputValidationConfig) gin.HandlerFunc {
	if config == nil {
		config = DefaultInputValidationConfig()
	}

	return func(c *gin.Context) {
		// Set request timeout
		if config.RequestTimeout > 0 {
			c.Request = c.Request.WithContext(c.Request.Context())
		}

		// Validate request size
		if err := validateRequestSize(c, config); err != nil {
			config.Logger.Warn("Request size validation failed",
				zap.String("client_ip", c.ClientIP()),
				zap.String("user_agent", c.GetHeader("User-Agent")),
				zap.Error(err),
			)
			c.JSON(http.StatusRequestEntityTooLarge, gin.H{
				"success": false,
				"error":   "Request too large",
				"code":    "REQUEST_TOO_LARGE",
				"message": fmt.Sprintf("Request size exceeds maximum allowed size of %d bytes", config.MaxRequestSize),
			})
			c.Abort()
			return
		}

		// Validate content type for POST/PUT/PATCH requests
		if err := validateContentType(c); err != nil {
			config.Logger.Warn("Content type validation failed",
				zap.String("content_type", c.GetHeader("Content-Type")),
				zap.String("method", c.Request.Method),
				zap.Error(err),
			)
			c.JSON(http.StatusUnsupportedMediaType, gin.H{
				"success": false,
				"error":   "Unsupported content type",
				"code":    "UNSUPPORTED_CONTENT_TYPE",
				"message": "Content-Type must be application/json for this endpoint",
			})
			c.Abort()
			return
		}

		// Validate and sanitize request body
		if err := validateAndSanitizeBody(c, config); err != nil {
			config.Logger.Warn("Request body validation failed",
				zap.String("client_ip", c.ClientIP()),
				zap.String("path", c.Request.URL.Path),
				zap.Error(err),
			)
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   "Invalid request data",
				"code":    "INVALID_REQUEST_DATA",
				"message": err.Error(),
			})
			c.Abort()
			return
		}

		// Validate query parameters
		if err := validateQueryParams(c, config); err != nil {
			config.Logger.Warn("Query parameter validation failed",
				zap.String("query", c.Request.URL.RawQuery),
				zap.Error(err),
			)
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   "Invalid query parameters",
				"code":    "INVALID_QUERY_PARAMS",
				"message": err.Error(),
			})
			c.Abort()
			return
		}

		// Validate headers
		if err := validateHeaders(c, config); err != nil {
			config.Logger.Warn("Header validation failed",
				zap.Error(err),
			)
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   "Invalid headers",
				"code":    "INVALID_HEADERS",
				"message": err.Error(),
			})
			c.Abort()
			return
		}

		// Log successful validation
		config.Logger.Info("Request validation passed",
			zap.String("method", c.Request.Method),
			zap.String("path", c.Request.URL.Path),
			zap.String("client_ip", c.ClientIP()),
		)

		c.Next()
	}
}

// validateRequestSize checks if request size is within limits
func validateRequestSize(c *gin.Context, config *InputValidationConfig) error {
	if c.Request.ContentLength > config.MaxRequestSize {
		return fmt.Errorf("request size %d exceeds maximum %d", c.Request.ContentLength, config.MaxRequestSize)
	}
	return nil
}

// validateContentType validates the content type for requests with body
func validateContentType(c *gin.Context) error {
	method := c.Request.Method
	if method == "POST" || method == "PUT" || method == "PATCH" {
		contentType := c.GetHeader("Content-Type")
		if contentType == "" {
			return fmt.Errorf("missing Content-Type header")
		}

		// Allow application/json and multipart/form-data
		if !strings.HasPrefix(contentType, "application/json") &&
			!strings.HasPrefix(contentType, "multipart/form-data") {
			return fmt.Errorf("unsupported content type: %s", contentType)
		}
	}
	return nil
}

// validateAndSanitizeBody validates and sanitizes the request body
func validateAndSanitizeBody(c *gin.Context, config *InputValidationConfig) error {
	if c.Request.Body == nil {
		return nil
	}

	// Read body
	bodyBytes, err := io.ReadAll(c.Request.Body)
	if err != nil {
		return fmt.Errorf("failed to read request body: %w", err)
	}

	// Restore body for further processing
	c.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))

	// Skip validation for empty body
	if len(bodyBytes) == 0 {
		return nil
	}

	// Parse JSON if content type is application/json
	contentType := c.GetHeader("Content-Type")
	if strings.HasPrefix(contentType, "application/json") {
		var jsonData interface{}
		if err := json.Unmarshal(bodyBytes, &jsonData); err != nil {
			return fmt.Errorf("invalid JSON format: %w", err)
		}

		// Validate and sanitize JSON data
		if err := validateJSONData(jsonData, config, 0); err != nil {
			return fmt.Errorf("JSON validation failed: %w", err)
		}

		// Check for potential security threats
		bodyString := string(bodyBytes)
		if err := checkSecurityThreats(bodyString, config); err != nil {
			return fmt.Errorf("security validation failed: %w", err)
		}
	}

	return nil
}

// validateJSONData recursively validates JSON data structure
func validateJSONData(data interface{}, config *InputValidationConfig, depth int) error {
	// Prevent deep nesting attacks
	if depth > 10 {
		return fmt.Errorf("JSON nesting too deep (max 10 levels)")
	}

	switch v := data.(type) {
	case string:
		if len(v) > config.MaxStringLength {
			return fmt.Errorf("string length %d exceeds maximum %d", len(v), config.MaxStringLength)
		}
		if err := validateStringContent(v, config); err != nil {
			return err
		}
	case []interface{}:
		if len(v) > config.MaxArrayLength {
			return fmt.Errorf("array length %d exceeds maximum %d", len(v), config.MaxArrayLength)
		}
		for _, item := range v {
			if err := validateJSONData(item, config, depth+1); err != nil {
				return err
			}
		}
	case map[string]interface{}:
		if len(v) > config.MaxArrayLength {
			return fmt.Errorf("object has too many keys: %d (max %d)", len(v), config.MaxArrayLength)
		}
		for key, value := range v {
			if len(key) > config.MaxStringLength {
				return fmt.Errorf("object key too long: %d (max %d)", len(key), config.MaxStringLength)
			}
			if err := validateStringContent(key, config); err != nil {
				return fmt.Errorf("invalid object key: %w", err)
			}
			if err := validateJSONData(value, config, depth+1); err != nil {
				return err
			}
		}
	}

	return nil
}

// validateStringContent validates string content for security threats
func validateStringContent(s string, config *InputValidationConfig) error {
	// Check for null bytes
	if strings.Contains(s, "\x00") {
		return fmt.Errorf("null bytes not allowed in strings")
	}

	// Check for control characters (except common ones like \n, \r, \t)
	for _, r := range s {
		if r < 32 && r != 9 && r != 10 && r != 13 {
			return fmt.Errorf("control characters not allowed in strings")
		}
	}

	// XSS protection
	if config.EnableXSSProtection {
		if err := checkXSSPatterns(s); err != nil {
			return err
		}
	}

	// SQL injection protection
	if config.EnableSQLInjectionProtection {
		if err := checkSQLInjectionPatterns(s); err != nil {
			return err
		}
	}

	return nil
}

// checkSecurityThreats checks for various security threats in the request body
func checkSecurityThreats(body string, config *InputValidationConfig) error {
	// Convert to lowercase for case-insensitive matching
	lowerBody := strings.ToLower(body)

	// Check for common attack patterns
	dangerousPatterns := []string{
		"<script",
		"javascript:",
		"vbscript:",
		"onload=",
		"onerror=",
		"onclick=",
		"eval(",
		"expression(",
		"url(",
		"import(",
	}

	for _, pattern := range dangerousPatterns {
		if strings.Contains(lowerBody, pattern) {
			return fmt.Errorf("potentially dangerous content detected: %s", pattern)
		}
	}

	return nil
}

// checkXSSPatterns checks for XSS attack patterns
func checkXSSPatterns(s string) error {
	// Common XSS patterns
	xssPatterns := []string{
		`<script[^>]*>.*?</script>`,
		`javascript:`,
		`vbscript:`,
		`on\w+\s*=`,
		`<iframe[^>]*>`,
		`<object[^>]*>`,
		`<embed[^>]*>`,
		`<link[^>]*>`,
		`<meta[^>]*>`,
		`<style[^>]*>.*?</style>`,
	}

	for _, pattern := range xssPatterns {
		matched, err := regexp.MatchString(`(?i)`+pattern, s)
		if err != nil {
			continue // Skip invalid regex
		}
		if matched {
			return fmt.Errorf("potential XSS attack detected")
		}
	}

	return nil
}

// checkSQLInjectionPatterns checks for SQL injection attack patterns
func checkSQLInjectionPatterns(s string) error {
	// Common SQL injection patterns
	sqlPatterns := []string{
		`(?i)\b(union|select|insert|update|delete|drop|create|alter|exec|execute)\b.*\b(from|where|into|values|table|database|schema)\b`,
		`(?i)(\bor\b|\band\b)\s+\w+\s*=\s*\w+`,
		`(?i)\b(or|and)\s+['"]?\d+['"]?\s*=\s*['"]?\d+['"]?`,
		`(?i)\b(or|and)\s+['"]?\w+['"]?\s*=\s*['"]?\w+['"]?`,
		`(?i)['"];?\s*(drop|delete|insert|update|create|alter|exec|execute)`,
		`(?i)\bxp_cmdshell\b`,
		`(?i)\bsp_executesql\b`,
		`(?i)--\s*$`,
		`(?i)/\*.*\*/`,
		`(?i)\bcast\s*\(`,
		`(?i)\bconvert\s*\(`,
		`(?i)\bchar\s*\(`,
		`(?i)\bhex\s*\(`,
		`(?i)\bunhex\s*\(`,
	}

	for _, pattern := range sqlPatterns {
		matched, err := regexp.MatchString(pattern, s)
		if err != nil {
			continue // Skip invalid regex
		}
		if matched {
			return fmt.Errorf("potential SQL injection attack detected")
		}
	}

	return nil
}

// validateQueryParams validates query parameters
func validateQueryParams(c *gin.Context, config *InputValidationConfig) error {
	for key, values := range c.Request.URL.Query() {
		// Validate key
		if len(key) > config.MaxStringLength {
			return fmt.Errorf("query parameter key too long: %s", key)
		}
		if err := validateStringContent(key, config); err != nil {
			return fmt.Errorf("invalid query parameter key %s: %w", key, err)
		}

		// Validate values
		if len(values) > config.MaxArrayLength {
			return fmt.Errorf("too many values for query parameter %s", key)
		}
		for _, value := range values {
			if len(value) > config.MaxStringLength {
				return fmt.Errorf("query parameter value too long for key %s", key)
			}
			if err := validateStringContent(value, config); err != nil {
				return fmt.Errorf("invalid query parameter value for key %s: %w", key, err)
			}
		}
	}

	return nil
}

// validateHeaders validates HTTP headers
func validateHeaders(c *gin.Context, config *InputValidationConfig) error {
	// List of headers to validate
	headersToValidate := []string{
		"User-Agent",
		"Referer",
		"X-Forwarded-For",
		"X-Real-IP",
		"X-Requested-With",
	}

	for _, headerName := range headersToValidate {
		headerValue := c.GetHeader(headerName)
		if headerValue == "" {
			continue
		}

		if len(headerValue) > config.MaxStringLength {
			return fmt.Errorf("header %s too long", headerName)
		}

		// Basic validation for common headers
		switch headerName {
		case "User-Agent":
			if err := validateUserAgent(headerValue); err != nil {
				return fmt.Errorf("invalid User-Agent: %w", err)
			}
		case "Referer":
			if err := validateReferer(headerValue); err != nil {
				return fmt.Errorf("invalid Referer: %w", err)
			}
		default:
			if err := validateStringContent(headerValue, config); err != nil {
				return fmt.Errorf("invalid header %s: %w", headerName, err)
			}
		}
	}

	return nil
}

// validateUserAgent validates User-Agent header
func validateUserAgent(userAgent string) error {
	// Check for suspicious patterns in User-Agent
	suspiciousPatterns := []string{
		"<script",
		"javascript:",
		"eval(",
		"expression(",
		"\x00",
	}

	lowerUA := strings.ToLower(userAgent)
	for _, pattern := range suspiciousPatterns {
		if strings.Contains(lowerUA, pattern) {
			return fmt.Errorf("suspicious pattern in User-Agent")
		}
	}

	return nil
}

// validateReferer validates Referer header
func validateReferer(referer string) error {
	// Basic URL validation for referer
	if !strings.HasPrefix(referer, "http://") && !strings.HasPrefix(referer, "https://") {
		return fmt.Errorf("invalid referer URL format")
	}

	// Check for suspicious patterns
	if err := checkXSSPatterns(referer); err != nil {
		return fmt.Errorf("suspicious pattern in referer: %w", err)
	}

	return nil
}

// SanitizeString sanitizes a string by removing/escaping dangerous characters
func SanitizeString(s string) string {
	// Remove null bytes
	s = strings.ReplaceAll(s, "\x00", "")

	// Remove control characters except common ones
	var result strings.Builder
	for _, r := range s {
		if r >= 32 || r == 9 || r == 10 || r == 13 {
			result.WriteRune(r)
		}
	}

	return result.String()
}

// ValidateEmail validates email format
func ValidateEmail(email string) error {
	if len(email) == 0 {
		return fmt.Errorf("email is required")
	}

	if len(email) > 254 {
		return fmt.Errorf("email too long")
	}

	// Basic email regex
	emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
	if !emailRegex.MatchString(email) {
		return fmt.Errorf("invalid email format")
	}

	return nil
}

// ValidatePassword validates password strength
func ValidatePassword(password string) error {
	if len(password) < 8 {
		return fmt.Errorf("password must be at least 8 characters long")
	}

	if len(password) > 128 {
		return fmt.Errorf("password too long")
	}

	// Check for at least one uppercase, lowercase, digit, and special character
	hasUpper := regexp.MustCompile(`[A-Z]`).MatchString(password)
	hasLower := regexp.MustCompile(`[a-z]`).MatchString(password)
	hasDigit := regexp.MustCompile(`\d`).MatchString(password)
	hasSpecial := regexp.MustCompile(`[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]`).MatchString(password)

	if !hasUpper || !hasLower || !hasDigit || !hasSpecial {
		return fmt.Errorf("password must contain at least one uppercase letter, lowercase letter, digit, and special character")
	}

	return nil
}

// ValidateID validates UUID format
func ValidateID(id string) error {
	if len(id) == 0 {
		return fmt.Errorf("ID is required")
	}

	// UUID regex
	uuidRegex := regexp.MustCompile(`^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$`)
	if !uuidRegex.MatchString(strings.ToLower(id)) {
		return fmt.Errorf("invalid ID format")
	}

	return nil
}

// ValidateNumeric validates numeric input
func ValidateNumeric(value string, min, max float64) error {
	if value == "" {
		return fmt.Errorf("numeric value is required")
	}

	num, err := strconv.ParseFloat(value, 64)
	if err != nil {
		return fmt.Errorf("invalid numeric format")
	}

	if num < min || num > max {
		return fmt.Errorf("numeric value must be between %f and %f", min, max)
	}

	return nil
}
