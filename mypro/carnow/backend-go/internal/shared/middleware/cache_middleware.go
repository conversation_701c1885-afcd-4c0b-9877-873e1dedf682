package middleware

import (
	"bytes"
	"context"
	"crypto/md5"
	"encoding/hex"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"carnow-backend/internal/infrastructure/cache"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// CacheMiddleware provides HTTP response caching
type CacheMiddleware struct {
	cacheProvider *cache.CacheProvider
	logger        *zap.Logger
	config        CacheMiddlewareConfig
}

// CacheMiddlewareConfig configures the cache middleware
type CacheMiddlewareConfig struct {
	DefaultTTL        time.Duration
	MaxResponseSize   int
	CacheableStatuses []int
	CacheableMethods  []string
	SkipHeaders       []string
	VaryHeaders       []string
	EnableCompression bool
	EnableETag        bool
	IgnoreQueryParams []string
	CacheKeyPrefix    string
}

// CachedResponse represents a cached HTTP response
type CachedResponse struct {
	StatusCode int               `json:"status_code"`
	Headers    map[string]string `json:"headers"`
	Body       []byte            `json:"body"`
	ETag       string            `json:"etag,omitempty"`
	CachedAt   time.Time         `json:"cached_at"`
	TTL        time.Duration     `json:"ttl"`
}

// responseWriter wraps gin.ResponseWriter to capture response data
type responseWriter struct {
	gin.ResponseWriter
	body   *bytes.Buffer
	status int
}

// NewCacheMiddleware creates a new cache middleware
func NewCacheMiddleware(cacheProvider *cache.CacheProvider, logger *zap.Logger) *CacheMiddleware {
	return &CacheMiddleware{
		cacheProvider: cacheProvider,
		logger:        logger,
		config: CacheMiddlewareConfig{
			DefaultTTL:        5 * time.Minute,
			MaxResponseSize:   1024 * 1024, // 1MB
			CacheableStatuses: []int{200, 301, 302, 404},
			CacheableMethods:  []string{"GET", "HEAD"},
			VaryHeaders:       []string{"Accept", "Accept-Encoding", "Authorization"},
			EnableCompression: true,
			EnableETag:        true,
			CacheKeyPrefix:    "http:",
		},
	}
}

// WithConfig sets custom configuration
func (cm *CacheMiddleware) WithConfig(config CacheMiddlewareConfig) *CacheMiddleware {
	cm.config = config
	return cm
}

// CacheResponse returns a middleware that caches HTTP responses
func (cm *CacheMiddleware) CacheResponse(ttl time.Duration) gin.HandlerFunc {
	if ttl <= 0 {
		ttl = cm.config.DefaultTTL
	}

	return func(c *gin.Context) {
		// Skip caching if cache is disabled
		if !cm.cacheProvider.IsEnabled() {
			c.Next()
			return
		}

		// Check if request is cacheable
		if !cm.isCacheableRequest(c.Request) {
			c.Next()
			return
		}

		// Generate cache key
		cacheKey := cm.generateCacheKey(c.Request)

		// Try to get cached response
		var cachedResp CachedResponse
		if err := cm.cacheProvider.Get(c.Request.Context(), cacheKey, &cachedResp); err == nil {
			// Cache hit - return cached response
			cm.serveCachedResponse(c, &cachedResp)
			return
		}

		// Cache miss - capture response
		writer := &responseWriter{
			ResponseWriter: c.Writer,
			body:           bytes.NewBuffer([]byte{}),
			status:         200,
		}
		c.Writer = writer

		// Process request
		c.Next()

		// Cache the response if it's cacheable
		if cm.isCacheableResponse(writer.status, writer.body.Len()) {
			cm.cacheResponse(c.Request.Context(), cacheKey, writer, ttl)
		}
	}
}

// CacheStatic returns a middleware for caching static content with long TTL
func (cm *CacheMiddleware) CacheStatic(ttl time.Duration) gin.HandlerFunc {
	if ttl <= 0 {
		ttl = 24 * time.Hour // Default 24 hours for static content
	}

	return cm.CacheResponse(ttl)
}

// CacheAPI returns a middleware for caching API responses with shorter TTL
func (cm *CacheMiddleware) CacheAPI(ttl time.Duration) gin.HandlerFunc {
	if ttl <= 0 {
		ttl = 5 * time.Minute // Default 5 minutes for API responses
	}

	return cm.CacheResponse(ttl)
}

// NoCache returns a middleware that prevents caching
func (cm *CacheMiddleware) NoCache() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("Cache-Control", "no-cache, no-store, must-revalidate")
		c.Header("Pragma", "no-cache")
		c.Header("Expires", "0")
		c.Next()
	}
}

// ConditionalCache returns a middleware that caches based on a condition
func (cm *CacheMiddleware) ConditionalCache(condition func(*gin.Context) bool, ttl time.Duration) gin.HandlerFunc {
	cacheMiddleware := cm.CacheResponse(ttl)
	noCacheMiddleware := cm.NoCache()

	return func(c *gin.Context) {
		if condition(c) {
			cacheMiddleware(c)
		} else {
			noCacheMiddleware(c)
		}
	}
}

// Helper methods

func (cm *CacheMiddleware) isCacheableRequest(r *http.Request) bool {
	// Check method
	for _, method := range cm.config.CacheableMethods {
		if r.Method == method {
			goto methodOk
		}
	}
	return false

methodOk:
	// Skip if has authorization header (unless explicitly allowed)
	if r.Header.Get("Authorization") != "" {
		// Check if authorization is in vary headers (meaning we cache per user)
		for _, header := range cm.config.VaryHeaders {
			if strings.ToLower(header) == "authorization" {
				goto authOk
			}
		}
		return false
	}

authOk:
	// Skip if has cache-control: no-cache
	if cacheControl := r.Header.Get("Cache-Control"); cacheControl != "" {
		if strings.Contains(strings.ToLower(cacheControl), "no-cache") {
			return false
		}
	}

	return true
}

func (cm *CacheMiddleware) isCacheableResponse(statusCode, bodySize int) bool {
	// Check status code
	for _, status := range cm.config.CacheableStatuses {
		if statusCode == status {
			goto statusOk
		}
	}
	return false

statusOk:
	// Check response size
	if bodySize > cm.config.MaxResponseSize {
		return false
	}

	return true
}

func (cm *CacheMiddleware) generateCacheKey(r *http.Request) string {
	// Start with method and path
	key := fmt.Sprintf("%s:%s", r.Method, r.URL.Path)

	// Add query parameters (excluding ignored ones)
	if r.URL.RawQuery != "" {
		query := r.URL.Query()
		var queryParts []string

		for param, values := range query {
			// Skip ignored parameters
			skip := false
			for _, ignored := range cm.config.IgnoreQueryParams {
				if param == ignored {
					skip = true
					break
				}
			}
			if skip {
				continue
			}

			for _, value := range values {
				queryParts = append(queryParts, fmt.Sprintf("%s=%s", param, value))
			}
		}

		if len(queryParts) > 0 {
			key += "?" + strings.Join(queryParts, "&")
		}
	}

	// Add varying headers
	var varyParts []string
	for _, header := range cm.config.VaryHeaders {
		if value := r.Header.Get(header); value != "" {
			varyParts = append(varyParts, fmt.Sprintf("%s:%s", header, value))
		}
	}

	if len(varyParts) > 0 {
		key += "|" + strings.Join(varyParts, "|")
	}

	// Add prefix
	if cm.config.CacheKeyPrefix != "" {
		key = cm.config.CacheKeyPrefix + key
	}

	// Hash long keys
	if len(key) > 250 {
		hash := md5.Sum([]byte(key))
		key = cm.config.CacheKeyPrefix + hex.EncodeToString(hash[:])
	}

	return key
}

func (cm *CacheMiddleware) serveCachedResponse(c *gin.Context, cachedResp *CachedResponse) {
	// Set headers
	for key, value := range cachedResp.Headers {
		c.Header(key, value)
	}

	// Set cache headers
	c.Header("X-Cache", "HIT")
	c.Header("X-Cache-Date", cachedResp.CachedAt.Format(time.RFC3339))

	// Set ETag if available
	if cachedResp.ETag != "" {
		c.Header("ETag", cachedResp.ETag)

		// Check If-None-Match
		if inm := c.GetHeader("If-None-Match"); inm != "" {
			if inm == cachedResp.ETag {
				c.Status(http.StatusNotModified)
				return
			}
		}
	}

	// Set content length
	c.Header("Content-Length", strconv.Itoa(len(cachedResp.Body)))

	// Write response
	c.Data(cachedResp.StatusCode, c.GetHeader("Content-Type"), cachedResp.Body)

	cm.logger.Debug("Served cached response",
		zap.String("method", c.Request.Method),
		zap.String("path", c.Request.URL.Path),
		zap.Int("status", cachedResp.StatusCode),
		zap.Int("size", len(cachedResp.Body)),
	)
}

func (cm *CacheMiddleware) cacheResponse(ctx context.Context, cacheKey string, writer *responseWriter, ttl time.Duration) {
	// Prepare headers map
	headers := make(map[string]string)
	for key, values := range writer.Header() {
		if len(values) > 0 {
			// Skip headers that shouldn't be cached
			skip := false
			for _, skipHeader := range cm.config.SkipHeaders {
				if strings.ToLower(key) == strings.ToLower(skipHeader) {
					skip = true
					break
				}
			}
			if skip {
				continue
			}

			headers[key] = values[0]
		}
	}

	// Generate ETag if enabled
	var etag string
	if cm.config.EnableETag {
		hash := md5.Sum(writer.body.Bytes())
		etag = fmt.Sprintf(`"%s"`, hex.EncodeToString(hash[:]))
		headers["ETag"] = etag
	}

	// Create cached response
	cachedResp := CachedResponse{
		StatusCode: writer.status,
		Headers:    headers,
		Body:       writer.body.Bytes(),
		ETag:       etag,
		CachedAt:   time.Now(),
		TTL:        ttl,
	}

	// Cache the response
	if err := cm.cacheProvider.Set(ctx, cacheKey, cachedResp, ttl); err != nil {
		cm.logger.Error("Failed to cache response",
			zap.String("cache_key", cacheKey),
			zap.Error(err),
		)
	} else {
		cm.logger.Debug("Cached response",
			zap.String("cache_key", cacheKey),
			zap.Int("status", writer.status),
			zap.Int("size", len(cachedResp.Body)),
			zap.Duration("ttl", ttl),
		)
	}
}

// responseWriter implementation

func (rw *responseWriter) Write(data []byte) (int, error) {
	rw.body.Write(data)
	return rw.ResponseWriter.Write(data)
}

func (rw *responseWriter) WriteHeader(statusCode int) {
	rw.status = statusCode
	rw.ResponseWriter.WriteHeader(statusCode)
}

func (rw *responseWriter) WriteString(s string) (int, error) {
	rw.body.WriteString(s)
	return rw.ResponseWriter.WriteString(s)
}

// Utility functions for common caching patterns

// CacheProductsMiddleware returns middleware for caching product-related endpoints
func CacheProductsMiddleware(cacheProvider *cache.CacheProvider, logger *zap.Logger) gin.HandlerFunc {
	middleware := NewCacheMiddleware(cacheProvider, logger)
	return middleware.CacheAPI(10 * time.Minute) // Cache products for 10 minutes
}

// CacheCategoriesMiddleware returns middleware for caching category-related endpoints
func CacheCategoriesMiddleware(cacheProvider *cache.CacheProvider, logger *zap.Logger) gin.HandlerFunc {
	middleware := NewCacheMiddleware(cacheProvider, logger)
	return middleware.CacheAPI(30 * time.Minute) // Cache categories for 30 minutes
}

// CacheStaticMiddleware returns middleware for caching static content
func CacheStaticMiddleware(cacheProvider *cache.CacheProvider, logger *zap.Logger) gin.HandlerFunc {
	middleware := NewCacheMiddleware(cacheProvider, logger)
	return middleware.CacheStatic(24 * time.Hour) // Cache static content for 24 hours
}

// CacheUserDataMiddleware returns middleware for caching user-specific data
func CacheUserDataMiddleware(cacheProvider *cache.CacheProvider, logger *zap.Logger) gin.HandlerFunc {
	middleware := NewCacheMiddleware(cacheProvider, logger).WithConfig(CacheMiddlewareConfig{
		DefaultTTL:        2 * time.Minute,
		MaxResponseSize:   512 * 1024, // 512KB
		CacheableStatuses: []int{200},
		CacheableMethods:  []string{"GET"},
		VaryHeaders:       []string{"Authorization"}, // Cache per user
		EnableETag:        true,
		CacheKeyPrefix:    "user:",
	})
	return middleware.CacheResponse(2 * time.Minute)
}
