package middleware

import (
	"context"
	"fmt"
	"sync"
	"time"

	"go.uber.org/zap"
)

// Circuit Breaker for Phase 3: Error Handling and Resilience
//
// Features:
// - Prevent cascading failures to external services
// - Automatic recovery and health monitoring
// - Configurable failure thresholds and timeouts
// - Metrics collection and monitoring
// - Graceful degradation with fallback mechanisms

// CircuitState represents the current state of the circuit breaker
type CircuitState int

const (
	// StateClosed - Normal operation, requests are allowed
	StateClosed CircuitState = iota
	// StateOpen - Circuit is open, requests are blocked
	StateOpen
	// StateHalfOpen - Testing if service has recovered
	StateHalfOpen
)

func (s CircuitState) String() string {
	switch s {
	case StateClosed:
		return "CLOSED"
	case StateOpen:
		return "OPEN"
	case StateHalfOpen:
		return "HALF_OPEN"
	default:
		return "UNKNOWN"
	}
}

// CircuitBreakerConfig contains configuration for the circuit breaker
type CircuitBreakerConfig struct {
	// FailureThreshold is the number of failures before opening the circuit
	FailureThreshold int
	// RecoveryTimeout is the time to wait before attempting recovery
	RecoveryTimeout time.Duration
	// HalfOpenMaxCalls is the max calls allowed in half-open state
	HalfOpenMaxCalls int
	// Timeout is the maximum time to wait for a call
	Timeout time.Duration
	// EnableMetrics enables metrics collection
	EnableMetrics bool
}

// DefaultCircuitBreakerConfig returns a default configuration
func DefaultCircuitBreakerConfig() *CircuitBreakerConfig {
	return &CircuitBreakerConfig{
		FailureThreshold: 5,
		RecoveryTimeout:  time.Minute,
		HalfOpenMaxCalls: 3,
		Timeout:          time.Second * 30,
		EnableMetrics:    true,
	}
}

// CircuitBreakerMetrics tracks circuit breaker statistics
type CircuitBreakerMetrics struct {
	TotalRequests       int64
	SuccessfulCalls     int64
	FailedCalls         int64
	TimeoutCalls        int64
	CircuitOpenCalls    int64
	StateTransitions    int64
	LastStateChange     time.Time
	CurrentState        CircuitState
	ConsecutiveFailures int64
	SuccessRate         float64
}

// CircuitBreaker implements the circuit breaker pattern
type CircuitBreaker struct {
	name    string
	config  *CircuitBreakerConfig
	logger  *zap.Logger
	metrics *CircuitBreakerMetrics

	state           CircuitState
	failureCount    int64
	lastFailureTime time.Time
	halfOpenCalls   int64
	mutex           sync.RWMutex
}

// NewCircuitBreaker creates a new circuit breaker
func NewCircuitBreaker(name string, config *CircuitBreakerConfig, logger *zap.Logger) *CircuitBreaker {
	if config == nil {
		config = DefaultCircuitBreakerConfig()
	}

	if logger == nil {
		logger = zap.NewNop()
	}

	return &CircuitBreaker{
		name:   name,
		config: config,
		logger: logger,
		state:  StateClosed,
		metrics: &CircuitBreakerMetrics{
			CurrentState:    StateClosed,
			LastStateChange: time.Now(),
		},
	}
}

// Execute executes a function with circuit breaker protection
func (cb *CircuitBreaker) Execute(ctx context.Context, fn func() error) error {
	// Check if we can execute the request
	if !cb.canExecute() {
		cb.recordCall(false, true) // blocked call
		return &CircuitBreakerOpenError{
			CircuitName: cb.name,
			State:       cb.getState(),
		}
	}

	// Create a timeout context
	timeoutCtx, cancel := context.WithTimeout(ctx, cb.config.Timeout)
	defer cancel()

	// Execute the function with timeout
	resultChan := make(chan error, 1)
	go func() {
		resultChan <- fn()
	}()

	select {
	case err := <-resultChan:
		if err != nil {
			cb.recordCall(false, false) // failed call
			cb.recordFailure()
			return err
		}
		cb.recordCall(true, false) // successful call
		cb.recordSuccess()
		return nil

	case <-timeoutCtx.Done():
		cb.recordCall(false, false) // timeout call
		cb.recordFailure()
		cb.metrics.TimeoutCalls++
		return &CircuitBreakerTimeoutError{
			CircuitName: cb.name,
			Timeout:     cb.config.Timeout,
		}
	}
}

// ExecuteWithFallback executes a function with circuit breaker and fallback
func (cb *CircuitBreaker) ExecuteWithFallback(
	ctx context.Context,
	fn func() error,
	fallback func() error,
) error {
	err := cb.Execute(ctx, fn)
	if err != nil {
		// Check if we should use fallback
		if cb.shouldUseFallback(err) && fallback != nil {
			cb.logger.Info("Circuit breaker using fallback",
				zap.String("circuit", cb.name),
				zap.String("reason", err.Error()),
			)
			return fallback()
		}
	}
	return err
}

// canExecute determines if a request can be executed
func (cb *CircuitBreaker) canExecute() bool {
	cb.mutex.RLock()
	defer cb.mutex.RUnlock()

	switch cb.state {
	case StateClosed:
		return true
	case StateOpen:
		return cb.shouldAttemptReset()
	case StateHalfOpen:
		return cb.halfOpenCalls < int64(cb.config.HalfOpenMaxCalls)
	default:
		return false
	}
}

// shouldAttemptReset checks if we should attempt to reset from open state
func (cb *CircuitBreaker) shouldAttemptReset() bool {
	if cb.lastFailureTime.IsZero() {
		return false
	}
	return time.Since(cb.lastFailureTime) >= cb.config.RecoveryTimeout
}

// recordSuccess records a successful call
func (cb *CircuitBreaker) recordSuccess() {
	cb.mutex.Lock()
	defer cb.mutex.Unlock()

	cb.failureCount = 0
	cb.lastFailureTime = time.Time{}

	switch cb.state {
	case StateHalfOpen:
		// If we have enough successful calls in half-open, close the circuit
		if cb.halfOpenCalls >= int64(cb.config.HalfOpenMaxCalls-1) {
			cb.transitionTo(StateClosed)
		}
	case StateOpen:
		// Direct transition from open to half-open on first success
		cb.transitionTo(StateHalfOpen)
		cb.halfOpenCalls = 0
	}
}

// recordFailure records a failed call
func (cb *CircuitBreaker) recordFailure() {
	cb.mutex.Lock()
	defer cb.mutex.Unlock()

	cb.failureCount++
	cb.lastFailureTime = time.Now()
	cb.metrics.ConsecutiveFailures++

	switch cb.state {
	case StateClosed:
		if cb.failureCount >= int64(cb.config.FailureThreshold) {
			cb.transitionTo(StateOpen)
		}
	case StateHalfOpen:
		// Any failure in half-open state opens the circuit
		cb.transitionTo(StateOpen)
	}
}

// recordCall records metrics for a call
func (cb *CircuitBreaker) recordCall(success, blocked bool) {
	if !cb.config.EnableMetrics {
		return
	}

	cb.metrics.TotalRequests++

	if blocked {
		cb.metrics.CircuitOpenCalls++
		return
	}

	if success {
		cb.metrics.SuccessfulCalls++
		cb.metrics.ConsecutiveFailures = 0
	} else {
		cb.metrics.FailedCalls++
	}

	// Update success rate
	if cb.metrics.TotalRequests > 0 {
		cb.metrics.SuccessRate = float64(cb.metrics.SuccessfulCalls) / float64(cb.metrics.TotalRequests) * 100
	}
}

// transitionTo transitions the circuit breaker to a new state
func (cb *CircuitBreaker) transitionTo(newState CircuitState) {
	oldState := cb.state
	cb.state = newState
	cb.metrics.CurrentState = newState
	cb.metrics.StateTransitions++
	cb.metrics.LastStateChange = time.Now()

	if newState == StateHalfOpen {
		cb.halfOpenCalls = 0
	}

	cb.logger.Info("Circuit breaker state transition",
		zap.String("circuit", cb.name),
		zap.String("from", oldState.String()),
		zap.String("to", newState.String()),
		zap.Int64("failure_count", cb.failureCount),
	)
}

// shouldUseFallback determines if fallback should be used for the error
func (cb *CircuitBreaker) shouldUseFallback(err error) bool {
	switch err.(type) {
	case *CircuitBreakerOpenError, *CircuitBreakerTimeoutError:
		return true
	default:
		return false
	}
}

// GetState returns the current state of the circuit breaker
func (cb *CircuitBreaker) getState() CircuitState {
	cb.mutex.RLock()
	defer cb.mutex.RUnlock()
	return cb.state
}

// GetMetrics returns current metrics
func (cb *CircuitBreaker) GetMetrics() *CircuitBreakerMetrics {
	cb.mutex.RLock()
	defer cb.mutex.RUnlock()

	// Create a copy to avoid race conditions
	metrics := *cb.metrics
	metrics.CurrentState = cb.state
	return &metrics
}

// Reset manually resets the circuit breaker to closed state
func (cb *CircuitBreaker) Reset() {
	cb.mutex.Lock()
	defer cb.mutex.Unlock()

	cb.transitionTo(StateClosed)
	cb.failureCount = 0
	cb.lastFailureTime = time.Time{}
	cb.halfOpenCalls = 0

	cb.logger.Info("Circuit breaker manually reset",
		zap.String("circuit", cb.name),
	)
}

// IsOpen returns true if the circuit breaker is open
func (cb *CircuitBreaker) IsOpen() bool {
	return cb.getState() == StateOpen
}

// IsClosed returns true if the circuit breaker is closed
func (cb *CircuitBreaker) IsClosed() bool {
	return cb.getState() == StateClosed
}

// IsHalfOpen returns true if the circuit breaker is half-open
func (cb *CircuitBreaker) IsHalfOpen() bool {
	return cb.getState() == StateHalfOpen
}

// GetName returns the circuit breaker name
func (cb *CircuitBreaker) GetName() string {
	return cb.name
}

// Circuit Breaker Manager for managing multiple circuit breakers
type CircuitBreakerManager struct {
	breakers map[string]*CircuitBreaker
	mutex    sync.RWMutex
	logger   *zap.Logger
}

// NewCircuitBreakerManager creates a new circuit breaker manager
func NewCircuitBreakerManager(logger *zap.Logger) *CircuitBreakerManager {
	return &CircuitBreakerManager{
		breakers: make(map[string]*CircuitBreaker),
		logger:   logger,
	}
}

// GetCircuitBreaker gets or creates a circuit breaker for a service
func (cbm *CircuitBreakerManager) GetCircuitBreaker(name string, config *CircuitBreakerConfig) *CircuitBreaker {
	cbm.mutex.RLock()
	if breaker, exists := cbm.breakers[name]; exists {
		cbm.mutex.RUnlock()
		return breaker
	}
	cbm.mutex.RUnlock()

	cbm.mutex.Lock()
	defer cbm.mutex.Unlock()

	// Double-check after acquiring write lock
	if breaker, exists := cbm.breakers[name]; exists {
		return breaker
	}

	// Create new circuit breaker
	breaker := NewCircuitBreaker(name, config, cbm.logger)
	cbm.breakers[name] = breaker

	cbm.logger.Info("Created new circuit breaker",
		zap.String("name", name),
		zap.Int("failure_threshold", config.FailureThreshold),
		zap.Duration("recovery_timeout", config.RecoveryTimeout),
	)

	return breaker
}

// GetAllCircuitBreakers returns all circuit breakers
func (cbm *CircuitBreakerManager) GetAllCircuitBreakers() map[string]*CircuitBreaker {
	cbm.mutex.RLock()
	defer cbm.mutex.RUnlock()

	// Create a copy to avoid race conditions
	result := make(map[string]*CircuitBreaker)
	for name, breaker := range cbm.breakers {
		result[name] = breaker
	}
	return result
}

// ResetAll resets all circuit breakers
func (cbm *CircuitBreakerManager) ResetAll() {
	cbm.mutex.RLock()
	defer cbm.mutex.RUnlock()

	for _, breaker := range cbm.breakers {
		breaker.Reset()
	}

	cbm.logger.Info("Reset all circuit breakers")
}

// GetManagerMetrics returns metrics for all circuit breakers
func (cbm *CircuitBreakerManager) GetManagerMetrics() map[string]*CircuitBreakerMetrics {
	cbm.mutex.RLock()
	defer cbm.mutex.RUnlock()

	result := make(map[string]*CircuitBreakerMetrics)
	for name, breaker := range cbm.breakers {
		result[name] = breaker.GetMetrics()
	}
	return result
}

// Error types for circuit breaker
type CircuitBreakerOpenError struct {
	CircuitName string
	State       CircuitState
}

func (e *CircuitBreakerOpenError) Error() string {
	return fmt.Sprintf("circuit breaker '%s' is %s", e.CircuitName, e.State.String())
}

type CircuitBreakerTimeoutError struct {
	CircuitName string
	Timeout     time.Duration
}

func (e *CircuitBreakerTimeoutError) Error() string {
	return fmt.Sprintf("circuit breaker '%s' timeout after %v", e.CircuitName, e.Timeout)
}

// Helper function to create a circuit breaker for database operations
func NewDatabaseCircuitBreaker(logger *zap.Logger) *CircuitBreaker {
	config := &CircuitBreakerConfig{
		FailureThreshold: 3,
		RecoveryTimeout:  time.Second * 30,
		HalfOpenMaxCalls: 2,
		Timeout:          time.Second * 10,
		EnableMetrics:    true,
	}
	return NewCircuitBreaker("database", config, logger)
}

// Helper function to create a circuit breaker for external API calls
func NewExternalAPICircuitBreaker(name string, logger *zap.Logger) *CircuitBreaker {
	config := &CircuitBreakerConfig{
		FailureThreshold: 5,
		RecoveryTimeout:  time.Minute,
		HalfOpenMaxCalls: 3,
		Timeout:          time.Second * 30,
		EnableMetrics:    true,
	}
	return NewCircuitBreaker(fmt.Sprintf("external_api_%s", name), config, logger)
}


