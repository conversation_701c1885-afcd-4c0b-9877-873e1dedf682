package middleware

import (
	"context"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
)

// RedisRateLimiterConfig holds configuration for Redis-based rate limiting
type RedisRateLimiterConfig struct {
	RedisAddr     string        // Redis server address
	RedisPassword string        // Redis password
	RedisDB       int           // Redis database number
	KeyPrefix     string        // Prefix for Redis keys
	WindowSize    time.Duration // Time window for rate limiting
	MaxRequests   int           // Maximum requests per window
	BlockDuration time.Duration // How long to block after exceeding limits
	Logger        *zap.Logger   // Logger instance
}

// DefaultRedisRateLimiterConfig returns default configuration
func DefaultRedisRateLimiterConfig() *RedisRateLimiterConfig {
	logger, _ := zap.NewProduction()
	return &RedisRateLimiterConfig{
		RedisAddr:     "localhost:6379",
		RedisPassword: "",
		RedisDB:       0,
		KeyPrefix:     "carnow:ratelimit:",
		WindowSize:    time.Minute,
		MaxRequests:   60,
		BlockDuration: 15 * time.Minute,
		Logger:        logger,
	}
}

// RedisRateLimiter implements rate limiting using Redis
type RedisRateLimiter struct {
	client *redis.Client
	config *RedisRateLimiterConfig
}

// NewRedisRateLimiter creates a new Redis-based rate limiter
func NewRedisRateLimiter(config *RedisRateLimiterConfig) (*RedisRateLimiter, error) {
	if config == nil {
		config = DefaultRedisRateLimiterConfig()
	}

	// Create Redis client
	client := redis.NewClient(&redis.Options{
		Addr:     config.RedisAddr,
		Password: config.RedisPassword,
		DB:       config.RedisDB,
	})

	// Test connection
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := client.Ping(ctx).Err(); err != nil {
		return nil, fmt.Errorf("failed to connect to Redis: %w", err)
	}

	config.Logger.Info("Redis rate limiter initialized",
		zap.String("redis_addr", config.RedisAddr),
		zap.Int("redis_db", config.RedisDB),
	)

	return &RedisRateLimiter{
		client: client,
		config: config,
	}, nil
}

// RedisRateLimitMiddleware creates a Redis-based rate limiting middleware
func RedisRateLimitMiddleware(config *RedisRateLimiterConfig) gin.HandlerFunc {
	limiter, err := NewRedisRateLimiter(config)
	if err != nil {
		// Fallback to in-memory rate limiter if Redis is not available
		config.Logger.Error("Failed to initialize Redis rate limiter, falling back to in-memory",
			zap.Error(err),
		)
		return RateLimitMiddleware(DefaultRateLimitConfig())
	}

	return func(c *gin.Context) {
		clientIP := getClientIP(c)

		// Check rate limit
		allowed, remaining, resetTime, err := limiter.Allow(c.Request.Context(), clientIP)
		if err != nil {
			limiter.config.Logger.Error("Redis rate limiter error",
				zap.String("client_ip", clientIP),
				zap.Error(err),
			)
			// Continue without rate limiting on Redis errors
			c.Next()
			return
		}

		// Set rate limit headers
		c.Header("X-RateLimit-Limit", strconv.Itoa(limiter.config.MaxRequests))
		c.Header("X-RateLimit-Remaining", strconv.Itoa(remaining))
		c.Header("X-RateLimit-Reset", strconv.FormatInt(resetTime.Unix(), 10))

		if !allowed {
			limiter.config.Logger.Warn("Redis rate limit exceeded",
				zap.String("client_ip", clientIP),
				zap.String("path", c.Request.URL.Path),
				zap.String("method", c.Request.Method),
				zap.Time("reset_time", resetTime),
			)

			c.Header("Retry-After", strconv.FormatInt(int64(resetTime.Sub(time.Now()).Seconds()), 10))

			c.JSON(http.StatusTooManyRequests, gin.H{
				"success":     false,
				"error":       "Rate limit exceeded",
				"code":        "RATE_LIMIT_EXCEEDED",
				"message":     fmt.Sprintf("Too many requests. Try again after %v", resetTime.Sub(time.Now()).Round(time.Second)),
				"retry_after": resetTime.Unix(),
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// Allow checks if a request is allowed and updates the rate limit counter
func (rl *RedisRateLimiter) Allow(ctx context.Context, clientIP string) (allowed bool, remaining int, resetTime time.Time, err error) {
	now := time.Now()
	key := rl.config.KeyPrefix + clientIP
	blockKey := key + ":blocked"

	// Check if IP is currently blocked
	blocked, err := rl.client.Get(ctx, blockKey).Result()
	if err != nil && err != redis.Nil {
		return false, 0, now, fmt.Errorf("failed to check block status: %w", err)
	}

	if blocked != "" {
		// IP is blocked, get the block expiration time
		ttl, err := rl.client.TTL(ctx, blockKey).Result()
		if err != nil {
			return false, 0, now, fmt.Errorf("failed to get block TTL: %w", err)
		}
		return false, 0, now.Add(ttl), nil
	}

	// Use sliding window log algorithm with Redis sorted sets
	windowStart := now.Add(-rl.config.WindowSize)

	// Remove old entries
	_, err = rl.client.ZRemRangeByScore(ctx, key, "0", strconv.FormatInt(windowStart.UnixNano(), 10)).Result()
	if err != nil {
		return false, 0, now, fmt.Errorf("failed to clean old entries: %w", err)
	}

	// Count current requests in window
	count, err := rl.client.ZCard(ctx, key).Result()
	if err != nil {
		return false, 0, now, fmt.Errorf("failed to count requests: %w", err)
	}

	// Check if limit exceeded
	if int(count) >= rl.config.MaxRequests {
		// Block the IP
		err = rl.client.Set(ctx, blockKey, "1", rl.config.BlockDuration).Err()
		if err != nil {
			rl.config.Logger.Error("Failed to block IP in Redis", zap.Error(err))
		}

		rl.config.Logger.Warn("IP blocked for exceeding rate limit",
			zap.String("client_ip", clientIP),
			zap.Int64("request_count", count),
			zap.Int("limit", rl.config.MaxRequests),
		)

		return false, 0, now.Add(rl.config.BlockDuration), nil
	}

	// Add current request to the window
	score := now.UnixNano()
	_, err = rl.client.ZAdd(ctx, key, redis.Z{
		Score:  float64(score),
		Member: score,
	}).Result()
	if err != nil {
		return false, 0, now, fmt.Errorf("failed to add request: %w", err)
	}

	// Set expiration for the key
	_, err = rl.client.Expire(ctx, key, rl.config.WindowSize+time.Minute).Result()
	if err != nil {
		rl.config.Logger.Error("Failed to set key expiration", zap.Error(err))
	}

	remaining = rl.config.MaxRequests - int(count) - 1
	if remaining < 0 {
		remaining = 0
	}

	resetTime = now.Add(rl.config.WindowSize)
	return true, remaining, resetTime, nil
}

// GetStats returns statistics about the Redis rate limiter
func (rl *RedisRateLimiter) GetStats(ctx context.Context) (map[string]interface{}, error) {
	// Get total number of tracked IPs
	pattern := rl.config.KeyPrefix + "*"
	keys, err := rl.client.Keys(ctx, pattern).Result()
	if err != nil {
		return nil, fmt.Errorf("failed to get keys: %w", err)
	}

	// Filter out blocked keys
	activeKeys := 0
	blockedKeys := 0
	for _, key := range keys {
		if strings.HasSuffix(key, ":blocked") {
			blockedKeys++
		} else {
			activeKeys++
		}
	}

	// Get Redis info
	info, err := rl.client.Info(ctx, "memory").Result()
	if err != nil {
		rl.config.Logger.Error("Failed to get Redis info", zap.Error(err))
		info = "unavailable"
	}

	return map[string]interface{}{
		"total_keys":   len(keys),
		"active_keys":  activeKeys,
		"blocked_keys": blockedKeys,
		"redis_info":   info,
		"config": map[string]interface{}{
			"window_size":    rl.config.WindowSize.String(),
			"max_requests":   rl.config.MaxRequests,
			"block_duration": rl.config.BlockDuration.String(),
		},
	}, nil
}

// Reset resets the rate limit for a specific IP
func (rl *RedisRateLimiter) Reset(ctx context.Context, clientIP string) error {
	key := rl.config.KeyPrefix + clientIP
	blockKey := key + ":blocked"

	// Remove both the rate limit key and block key
	_, err := rl.client.Del(ctx, key, blockKey).Result()
	if err != nil {
		return fmt.Errorf("failed to reset rate limit: %w", err)
	}

	rl.config.Logger.Info("Rate limit reset for IP",
		zap.String("client_ip", clientIP),
	)

	return nil
}

// Block manually blocks an IP for a specified duration
func (rl *RedisRateLimiter) Block(ctx context.Context, clientIP string, duration time.Duration) error {
	blockKey := rl.config.KeyPrefix + clientIP + ":blocked"

	err := rl.client.Set(ctx, blockKey, "1", duration).Err()
	if err != nil {
		return fmt.Errorf("failed to block IP: %w", err)
	}

	rl.config.Logger.Info("IP manually blocked",
		zap.String("client_ip", clientIP),
		zap.Duration("duration", duration),
	)

	return nil
}

// Unblock manually unblocks an IP
func (rl *RedisRateLimiter) Unblock(ctx context.Context, clientIP string) error {
	key := rl.config.KeyPrefix + clientIP
	blockKey := key + ":blocked"

	// Remove both the rate limit key and block key
	_, err := rl.client.Del(ctx, key, blockKey).Result()
	if err != nil {
		return fmt.Errorf("failed to unblock IP: %w", err)
	}

	rl.config.Logger.Info("IP manually unblocked",
		zap.String("client_ip", clientIP),
	)

	return nil
}

// Close closes the Redis connection
func (rl *RedisRateLimiter) Close() error {
	return rl.client.Close()
}

// HealthCheck performs a health check on the Redis connection
func (rl *RedisRateLimiter) HealthCheck(ctx context.Context) error {
	return rl.client.Ping(ctx).Err()
}

// GetBlockedIPs returns a list of currently blocked IPs
func (rl *RedisRateLimiter) GetBlockedIPs(ctx context.Context) ([]string, error) {
	pattern := rl.config.KeyPrefix + "*:blocked"
	keys, err := rl.client.Keys(ctx, pattern).Result()
	if err != nil {
		return nil, fmt.Errorf("failed to get blocked IPs: %w", err)
	}

	var blockedIPs []string
	for _, key := range keys {
		// Extract IP from key (remove prefix and :blocked suffix)
		ip := strings.TrimPrefix(key, rl.config.KeyPrefix)
		ip = strings.TrimSuffix(ip, ":blocked")
		blockedIPs = append(blockedIPs, ip)
	}

	return blockedIPs, nil
}

// GetIPRequestCount returns the current request count for an IP
func (rl *RedisRateLimiter) GetIPRequestCount(ctx context.Context, clientIP string) (int, error) {
	key := rl.config.KeyPrefix + clientIP

	// Remove old entries first
	now := time.Now()
	windowStart := now.Add(-rl.config.WindowSize)

	_, err := rl.client.ZRemRangeByScore(ctx, key, "0", strconv.FormatInt(windowStart.UnixNano(), 10)).Result()
	if err != nil {
		return 0, fmt.Errorf("failed to clean old entries: %w", err)
	}

	// Count current requests
	count, err := rl.client.ZCard(ctx, key).Result()
	if err != nil {
		return 0, fmt.Errorf("failed to count requests: %w", err)
	}

	return int(count), nil
}

// FlushAll removes all rate limiting data (use with caution)
func (rl *RedisRateLimiter) FlushAll(ctx context.Context) error {
	pattern := rl.config.KeyPrefix + "*"
	keys, err := rl.client.Keys(ctx, pattern).Result()
	if err != nil {
		return fmt.Errorf("failed to get keys: %w", err)
	}

	if len(keys) > 0 {
		_, err = rl.client.Del(ctx, keys...).Result()
		if err != nil {
			return fmt.Errorf("failed to delete keys: %w", err)
		}
	}

	rl.config.Logger.Info("All rate limiting data flushed",
		zap.Int("keys_deleted", len(keys)),
	)

	return nil
}
