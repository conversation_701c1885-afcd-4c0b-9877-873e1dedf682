package middleware

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/zap"
)

func TestAdvancedSecurityMiddleware(t *testing.T) {
	gin.SetMode(gin.TestMode)
	logger, _ := zap.NewDevelopment()

	config := &AdvancedSecurityConfig{
		EnableIPWhitelist:      false,
		EnableIPBlacklist:      true,
		BlacklistedIPs:         []string{"*************"},
		EnableFingerprinting:   true,
		MaxFingerprintAge:      time.Hour,
		EnableAnomalyDetection: true,
		AnomalyThreshold:       5,
		AnomalyWindow:          time.Minute,
		EnableSecurityHeaders:  true,
		CSPPolicy:              "default-src 'self'",
		HSTSMaxAge:             31536000,
		EnableBotDetection:     true,
		BotUserAgents:          []string{"bot", "crawler"},
		Logger:                 logger,
	}

	middleware := AdvancedSecurityMiddleware(config)

	t.Run("Security Headers Applied", func(t *testing.T) {
		router := gin.New()
		router.Use(middleware)
		router.GET("/test", func(c *gin.Context) {
			c.JSON(http.StatusOK, gin.H{"message": "success"})
		})

		req := httptest.NewRequest("GET", "/test", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Equal(t, "default-src 'self'", w.Header().Get("Content-Security-Policy"))
		assert.Equal(t, "max-age=31536000; includeSubDomains", w.Header().Get("Strict-Transport-Security"))
		assert.Equal(t, "DENY", w.Header().Get("X-Frame-Options"))
		assert.Equal(t, "nosniff", w.Header().Get("X-Content-Type-Options"))
		assert.Equal(t, "1; mode=block", w.Header().Get("X-XSS-Protection"))
	})

	t.Run("IP Blacklist Blocking", func(t *testing.T) {
		router := gin.New()
		router.Use(middleware)
		router.GET("/test", func(c *gin.Context) {
			c.JSON(http.StatusOK, gin.H{"message": "success"})
		})

		req := httptest.NewRequest("GET", "/test", nil)
		req.Header.Set("X-Forwarded-For", "*************")
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusForbidden, w.Code)

		var response map[string]interface{}
		err := parseJSONResponse(w.Body.String(), &response)
		require.NoError(t, err)

		assert.Equal(t, false, response["success"])
		assert.Equal(t, "IP_BLACKLISTED", response["code"])
	})

	t.Run("Bot Detection", func(t *testing.T) {
		router := gin.New()
		router.Use(middleware)
		router.GET("/test", func(c *gin.Context) {
			c.JSON(http.StatusOK, gin.H{"message": "success"})
		})

		req := httptest.NewRequest("GET", "/test", nil)
		req.Header.Set("User-Agent", "GoogleBot/2.1")
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		// First request should be allowed
		assert.Equal(t, http.StatusOK, w.Code)
	})

	t.Run("Anomaly Detection", func(t *testing.T) {
		router := gin.New()
		router.Use(middleware)
		router.GET("/test", func(c *gin.Context) {
			c.JSON(http.StatusOK, gin.H{"message": "success"})
		})

		// Make multiple requests with same fingerprint to trigger anomaly detection
		userAgent := "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"

		for i := 0; i < 6; i++ { // Exceed threshold of 5
			req := httptest.NewRequest("GET", "/test", nil)
			req.Header.Set("User-Agent", userAgent)
			req.Header.Set("Accept", "text/html,application/xhtml+xml")
			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			if i < 5 {
				assert.Equal(t, http.StatusOK, w.Code, "Request %d should succeed", i+1)
			} else {
				assert.Equal(t, http.StatusTooManyRequests, w.Code, "Request %d should be blocked", i+1)
			}
		}
	})
}

func TestSecurityManager(t *testing.T) {
	logger, _ := zap.NewDevelopment()
	config := &AdvancedSecurityConfig{
		EnableFingerprinting:   true,
		MaxFingerprintAge:      time.Hour,
		EnableAnomalyDetection: true,
		AnomalyThreshold:       3,
		AnomalyWindow:          time.Minute,
		BotUserAgents:          []string{"bot", "crawler", "spider"},
		Logger:                 logger,
	}

	sm := NewSecurityManager(config)

	t.Run("Fingerprint Generation", func(t *testing.T) {
		router := gin.New()
		router.GET("/test", func(c *gin.Context) {
			fingerprint := sm.GenerateFingerprint(c)
			assert.NotEmpty(t, fingerprint)
			assert.Len(t, fingerprint, 64) // SHA256 hex string length
			c.JSON(http.StatusOK, gin.H{"fingerprint": fingerprint})
		})

		req := httptest.NewRequest("GET", "/test", nil)
		req.Header.Set("User-Agent", "Mozilla/5.0")
		req.Header.Set("Accept", "text/html")
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
	})

	t.Run("Bot Detection", func(t *testing.T) {
		testCases := []struct {
			userAgent string
			isBot     bool
		}{
			{"Mozilla/5.0 (Windows NT 10.0; Win64; x64)", false},
			{"GoogleBot/2.1", true},
			{"Mozilla/5.0 (compatible; bingbot/2.0)", true},
			{"Twitterbot/1.0", true},
			{"curl/7.68.0", false}, // curl is not in our bot list for this test
			{"Python-requests/2.25.1", false},
		}

		for _, tc := range testCases {
			t.Run(tc.userAgent, func(t *testing.T) {
				isBot := sm.IsBotRequest(tc.userAgent)
				assert.Equal(t, tc.isBot, isBot)
			})
		}
	})

	t.Run("IP Whitelist/Blacklist", func(t *testing.T) {
		config.WhitelistedIPs = []string{"127.0.0.1", "***********"}
		config.BlacklistedIPs = []string{"********", "**********"}

		testCases := []struct {
			ip            string
			isWhitelisted bool
			isBlacklisted bool
		}{
			{"127.0.0.1", true, false},
			{"***********", true, false},
			{"********", false, true},
			{"**********", false, true},
			{"*******", false, false},
		}

		for _, tc := range testCases {
			t.Run(tc.ip, func(t *testing.T) {
				assert.Equal(t, tc.isWhitelisted, sm.IsIPWhitelisted(tc.ip))
				assert.Equal(t, tc.isBlacklisted, sm.IsIPBlacklisted(tc.ip))
			})
		}
	})

	t.Run("Anomaly Detection Logic", func(t *testing.T) {
		fingerprint := "test-fingerprint-123"
		clientIP := "************"

		// First few requests should not trigger anomaly
		for i := 0; i < 3; i++ {
			anomaly := sm.DetectAnomaly(fingerprint, clientIP)
			assert.False(t, anomaly, "Request %d should not trigger anomaly", i+1)
		}

		// Next request should trigger anomaly (threshold is 3)
		anomaly := sm.DetectAnomaly(fingerprint, clientIP)
		assert.True(t, anomaly, "Request should trigger anomaly detection")
	})

	t.Run("Bot Rate Limiting", func(t *testing.T) {
		botIP := "************"

		// First 10 requests should be allowed
		for i := 0; i < 10; i++ {
			allowed := sm.AllowBotRequest(botIP)
			assert.True(t, allowed, "Bot request %d should be allowed", i+1)
		}

		// 11th request should be blocked
		allowed := sm.AllowBotRequest(botIP)
		assert.False(t, allowed, "11th bot request should be blocked")
	})

	t.Run("Security Stats", func(t *testing.T) {
		// Generate some activity
		sm.DetectAnomaly("fingerprint1", "************")
		sm.DetectAnomaly("fingerprint2", "************")
		sm.AllowBotRequest("************")

		stats := sm.GetSecurityStats()
		assert.Contains(t, stats, "total_fingerprints")
		assert.Contains(t, stats, "bot_requests")
		assert.Contains(t, stats, "config")

		config := stats["config"].(map[string]interface{})
		assert.Equal(t, 3, config["anomaly_threshold"])
		assert.Equal(t, time.Minute.String(), config["anomaly_window"])
	})

	t.Run("Manual IP Blocking", func(t *testing.T) {
		testIP := "************"

		// Initially not blacklisted
		assert.False(t, sm.IsIPBlacklisted(testIP))

		// Block IP
		sm.BlockIP(testIP)
		assert.True(t, sm.IsIPBlacklisted(testIP))

		// Unblock IP
		sm.UnblockIP(testIP)
		assert.False(t, sm.IsIPBlacklisted(testIP))
	})
}

func TestSecurityManagerCleanup(t *testing.T) {
	logger, _ := zap.NewDevelopment()
	config := &AdvancedSecurityConfig{
		MaxFingerprintAge: 100 * time.Millisecond, // Very short for testing
		Logger:            logger,
	}

	sm := NewSecurityManager(config)

	// Add some fingerprints
	sm.DetectAnomaly("old-fingerprint", "************")
	sm.AllowBotRequest("************")

	// Wait for cleanup
	time.Sleep(150 * time.Millisecond)

	// Trigger cleanup manually for testing
	sm.cleanup()

	// Check that old entries are cleaned up
	stats := sm.GetSecurityStats()
	// Note: In a real scenario, we'd need to check the internal state
	// For this test, we just verify the cleanup doesn't crash
	assert.NotNil(t, stats)
}

func TestAdvancedSecurityConfigDefaults(t *testing.T) {
	config := DefaultAdvancedSecurityConfig()

	assert.False(t, config.EnableRedisRateLimit)
	assert.False(t, config.EnableIPWhitelist)
	assert.True(t, config.EnableIPBlacklist)
	assert.False(t, config.EnableGeoBlocking)
	assert.True(t, config.EnableFingerprinting)
	assert.Equal(t, 24*time.Hour, config.MaxFingerprintAge)
	assert.True(t, config.EnableAnomalyDetection)
	assert.Equal(t, 100, config.AnomalyThreshold)
	assert.Equal(t, time.Minute, config.AnomalyWindow)
	assert.True(t, config.EnableSecurityHeaders)
	assert.NotEmpty(t, config.CSPPolicy)
	assert.Equal(t, 31536000, config.HSTSMaxAge)
	assert.True(t, config.EnableBotDetection)
	assert.NotEmpty(t, config.BotUserAgents)
	assert.NotNil(t, config.Logger)
}

func TestSecurityHealthCheck(t *testing.T) {
	logger, _ := zap.NewDevelopment()
	config := DefaultAdvancedSecurityConfig()
	config.Logger = logger

	sm := NewSecurityManager(config)

	// Test healthy state
	healthCheck := sm.SecurityHealthCheck(nil)
	assert.Equal(t, "healthy", healthCheck["status"])
	assert.Equal(t, 0, healthCheck["recent_threats"])
	assert.Equal(t, 0, healthCheck["active_blocks"])

	// Simulate some threats
	for i := 0; i < 12; i++ {
		fingerprint := fmt.Sprintf("threat-fingerprint-%d", i)
		sm.BlockFingerprint(fingerprint, 15*time.Minute)
	}

	// Test under attack state
	healthCheck = sm.SecurityHealthCheck(nil)
	assert.Equal(t, "under_attack", healthCheck["status"])
	assert.True(t, healthCheck["recent_threats"].(int) > 10)
}

// Helper function to parse JSON response
func parseJSONResponse(body string, v interface{}) error {
	return json.Unmarshal([]byte(body), v)
}

// Benchmark tests
func BenchmarkSecurityManagerFingerprint(b *testing.B) {
	logger, _ := zap.NewDevelopment()
	config := DefaultAdvancedSecurityConfig()
	config.Logger = logger
	sm := NewSecurityManager(config)

	router := gin.New()
	router.GET("/test", func(c *gin.Context) {
		c.Status(http.StatusOK)
	})

	req := httptest.NewRequest("GET", "/test", nil)
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64)")
	req.Header.Set("Accept", "text/html,application/xhtml+xml")
	req.Header.Set("Accept-Language", "en-US,en;q=0.9")

	b.ResetTimer()
	for range b.N {
		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)
		c.Request = req
		_ = sm.GenerateFingerprint(c)
	}
}

func BenchmarkSecurityManagerAnomalyDetection(b *testing.B) {
	logger, _ := zap.NewDevelopment()
	config := DefaultAdvancedSecurityConfig()
	config.Logger = logger
	sm := NewSecurityManager(config)

	b.ResetTimer()
	for i := range b.N {
		fingerprint := fmt.Sprintf("benchmark-fingerprint-%d", i%100)
		clientIP := fmt.Sprintf("192.168.1.%d", i%255)
		_ = sm.DetectAnomaly(fingerprint, clientIP)
	}
}
