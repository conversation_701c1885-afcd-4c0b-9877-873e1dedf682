package middleware

import (
	"bytes"
	"compress/gzip"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// Response Optimization for Phase 4: Performance Optimization
//
// Features:
// - Implement response compression (gzip)
// - Add pagination for large result sets
// - Create efficient data serialization
// - Implement partial response capabilities
// - Response caching with ETags
// - Adaptive compression based on content type
// - Streaming for large responses
// - Response size optimization

// ResponseOptimizer provides advanced response optimization capabilities
type ResponseOptimizer struct {
	config       *ResponseOptimizerConfig
	logger       *zap.Logger
	metrics      *ResponseMetrics
	compressor   *ResponseCompressor
	paginator    *SmartPaginator
	serializer   *EfficientSerializer
	fieldsFilter *FieldsFilter
	mutex        sync.RWMutex
}

// ResponseOptimizerConfig contains response optimization configuration
type ResponseOptimizerConfig struct {
	// Compression configuration
	CompressionConfig CompressionConfig

	// Pagination configuration
	PaginationConfig PaginationConfig

	// Serialization configuration
	SerializationConfig SerializationConfig

	// Fields filtering configuration
	FieldsConfig FieldsFilterConfig

	// Caching configuration
	CachingConfig ResponseCachingConfig

	// Performance configuration
	PerformanceConfig PerformanceConfig

	// Monitoring configuration
	EnableMetrics         bool
	MetricsInterval       time.Duration
	LogSlowResponses      bool
	SlowResponseThreshold time.Duration
}

// CompressionConfig configures response compression
type CompressionConfig struct {
	Enabled           bool     // Enable compression
	MinSize           int      // Minimum response size to compress (bytes)
	MaxSize           int      // Maximum response size to compress (bytes)
	CompressionLevel  int      // Compression level (1-9)
	CompressibleTypes []string // MIME types to compress
	ExcludedPaths     []string // Paths to exclude from compression
	GzipPoolSize      int      // Pool size for gzip writers
	EnableBrotli      bool     // Enable Brotli compression
	PreferBrotli      bool     // Prefer Brotli over gzip when available
}

// PaginationConfig configures pagination behavior
type PaginationConfig struct {
	DefaultPageSize int    // Default page size if not specified
	MaxPageSize     int    // Maximum allowed page size
	PageSizeParam   string // Query parameter name for page size
	PageParam       string // Query parameter name for page number
	OffsetParam     string // Query parameter name for offset
	CursorParam     string // Query parameter name for cursor
	SortParam       string // Query parameter name for sorting
	IncludeTotal    bool   // Include total count in responses
	EnableCursor    bool   // Enable cursor-based pagination
	EnableMetadata  bool   // Include pagination metadata
}

// SerializationConfig configures data serialization
type SerializationConfig struct {
	EnableStreamResponse bool                  // Enable streaming for large responses
	StreamThreshold      int                   // Size threshold for streaming
	CompactJSON          bool                  // Use compact JSON (no indentation)
	EnableJSONMinify     bool                  // Minify JSON responses
	CustomSerializers    map[string]Serializer // Custom serializers per content type
	DateFormat           string                // Date format for serialization
	FloatPrecision       int                   // Precision for floating point numbers
	EnableNullOmit       bool                  // Omit null/empty fields
}

// FieldsFilterConfig configures partial response capabilities
type FieldsFilterConfig struct {
	Enabled         bool     // Enable fields filtering
	FieldsParam     string   // Query parameter name for fields
	IncludeParam    string   // Query parameter name for included fields
	ExcludeParam    string   // Query parameter name for excluded fields
	DefaultIncludes []string // Default fields to include
	DefaultExcludes []string // Default fields to exclude
	MaxDepth        int      // Maximum nesting depth for field selection
	EnableWildcards bool     // Enable wildcard field selection
	CaseSensitive   bool     // Case sensitive field matching
}

// ResponseCachingConfig configures response caching
type ResponseCachingConfig struct {
	EnableETags        bool          // Enable ETag generation
	ETagAlgorithm      string        // ETag algorithm (md5, sha1, sha256)
	CacheableStatus    []int         // HTTP status codes to cache
	CacheableHeaders   []string      // Headers that indicate cacheability
	MaxAge             time.Duration // Default cache max-age
	EnableLastModified bool          // Enable Last-Modified headers
	EnableVary         bool          // Enable Vary headers
}

// PerformanceConfig configures performance optimizations
type PerformanceConfig struct {
	EnableResponsePool       bool // Enable response buffer pooling
	PoolSize                 int  // Size of response buffer pool
	BufferSize               int  // Size of individual response buffers
	EnableAsyncSerialization bool // Enable async serialization for large responses
	WorkerPoolSize           int  // Size of async worker pool
	EnableResponsePreload    bool // Preload response data
	BatchSize                int  // Batch size for bulk operations
}

// ResponseMetrics tracks response optimization metrics
type ResponseMetrics struct {
	// Compression metrics
	CompressedResponses int64
	CompressionRatio    float64
	CompressionTime     time.Duration
	BytesSaved          int64

	// Pagination metrics
	PaginatedRequests   int64
	AveragePageSize     float64
	LargestPageSize     int
	CursorPaginationUse int64

	// Serialization metrics
	SerializationTime time.Duration
	StreamedResponses int64
	PartialResponses  int64
	FieldsFiltered    int64

	// Performance metrics
	AverageResponseSize int64
	LargestResponseSize int64
	ResponseTime        time.Duration
	CacheHitRate        float64

	// Error metrics
	CompressionErrors   int64
	SerializationErrors int64
	PaginationErrors    int64

	// Timing
	LastUpdated time.Time
}

// ResponseCompressor handles response compression
type ResponseCompressor struct {
	config     CompressionConfig
	gzipPool   sync.Pool
	brotliPool sync.Pool
	logger     *zap.Logger
}

// SmartPaginator handles intelligent pagination
type SmartPaginator struct {
	config PaginationConfig
	logger *zap.Logger
}

// EfficientSerializer handles optimized data serialization
type EfficientSerializer struct {
	config      SerializationConfig
	serializers map[string]Serializer
	logger      *zap.Logger
}

// FieldsFilter handles partial response field filtering
type FieldsFilter struct {
	config FieldsFilterConfig
	logger *zap.Logger
}

// Serializer interface for custom serializers
type Serializer interface {
	Serialize(data interface{}) ([]byte, error)
	ContentType() string
}

// PaginationInfo contains pagination metadata
type PaginationInfo struct {
	Page       int    `json:"page,omitempty"`
	PageSize   int    `json:"page_size,omitempty"`
	Total      int64  `json:"total,omitempty"`
	TotalPages int    `json:"total_pages,omitempty"`
	HasNext    bool   `json:"has_next,omitempty"`
	HasPrev    bool   `json:"has_prev,omitempty"`
	NextCursor string `json:"next_cursor,omitempty"`
	PrevCursor string `json:"prev_cursor,omitempty"`
}

// PartialResponseInfo contains field filtering metadata
type PartialResponseInfo struct {
	RequestedFields []string `json:"requested_fields,omitempty"`
	IncludedFields  []string `json:"included_fields,omitempty"`
	ExcludedFields  []string `json:"excluded_fields,omitempty"`
	FilteredCount   int      `json:"filtered_count,omitempty"`
}

// DefaultResponseOptimizerConfig returns optimized default configuration
func DefaultResponseOptimizerConfig() *ResponseOptimizerConfig {
	return &ResponseOptimizerConfig{
		CompressionConfig: CompressionConfig{
			Enabled:          true,
			MinSize:          1024,     // 1KB minimum
			MaxSize:          10485760, // 10MB maximum
			CompressionLevel: 6,        // Balanced compression
			CompressibleTypes: []string{
				"application/json",
				"application/xml",
				"text/plain",
				"text/html",
				"text/css",
				"application/javascript",
				"text/javascript",
			},
			GzipPoolSize: 100,
			EnableBrotli: true,
			PreferBrotli: true,
		},
		PaginationConfig: PaginationConfig{
			DefaultPageSize: 20,
			MaxPageSize:     1000,
			PageSizeParam:   "page_size",
			PageParam:       "page",
			OffsetParam:     "offset",
			CursorParam:     "cursor",
			SortParam:       "sort",
			IncludeTotal:    true,
			EnableCursor:    true,
			EnableMetadata:  true,
		},
		SerializationConfig: SerializationConfig{
			EnableStreamResponse: true,
			StreamThreshold:      1048576, // 1MB
			CompactJSON:          true,
			EnableJSONMinify:     true,
			DateFormat:           time.RFC3339,
			FloatPrecision:       2,
			EnableNullOmit:       true,
		},
		FieldsConfig: FieldsFilterConfig{
			Enabled:         true,
			FieldsParam:     "fields",
			IncludeParam:    "include",
			ExcludeParam:    "exclude",
			MaxDepth:        5,
			EnableWildcards: true,
			CaseSensitive:   false,
		},
		CachingConfig: ResponseCachingConfig{
			EnableETags:        true,
			ETagAlgorithm:      "md5",
			CacheableStatus:    []int{200, 301, 302, 304, 404},
			MaxAge:             5 * time.Minute,
			EnableLastModified: true,
			EnableVary:         true,
		},
		PerformanceConfig: PerformanceConfig{
			EnableResponsePool:       true,
			PoolSize:                 1000,
			BufferSize:               32768, // 32KB
			EnableAsyncSerialization: true,
			WorkerPoolSize:           10,
			BatchSize:                100,
		},
		EnableMetrics:         true,
		MetricsInterval:       30 * time.Second,
		LogSlowResponses:      true,
		SlowResponseThreshold: 500 * time.Millisecond,
	}
}

// NewResponseOptimizer creates a new response optimizer
func NewResponseOptimizer(config *ResponseOptimizerConfig, logger *zap.Logger) *ResponseOptimizer {
	if config == nil {
		config = DefaultResponseOptimizerConfig()
	}

	optimizer := &ResponseOptimizer{
		config: config,
		logger: logger,
		metrics: &ResponseMetrics{
			LastUpdated: time.Now(),
		},
	}

	// Initialize compressor
	optimizer.compressor = NewResponseCompressor(config.CompressionConfig, logger)

	// Initialize paginator
	optimizer.paginator = NewSmartPaginator(config.PaginationConfig, logger)

	// Initialize serializer
	optimizer.serializer = NewEfficientSerializer(config.SerializationConfig, logger)

	// Initialize fields filter
	optimizer.fieldsFilter = NewFieldsFilter(config.FieldsConfig, logger)

	// Start metrics collection
	if config.EnableMetrics {
		go optimizer.startMetricsCollection()
	}

	logger.Info("Response optimizer initialized",
		zap.Bool("compression_enabled", config.CompressionConfig.Enabled),
		zap.Bool("pagination_enabled", true),
		zap.Bool("fields_filtering_enabled", config.FieldsConfig.Enabled),
		zap.Bool("streaming_enabled", config.SerializationConfig.EnableStreamResponse),
	)

	return optimizer
}

// ResponseOptimizationMiddleware creates the response optimization middleware
func ResponseOptimizationMiddleware(config *ResponseOptimizerConfig, logger *zap.Logger) gin.HandlerFunc {
	optimizer := NewResponseOptimizer(config, logger)

	return func(c *gin.Context) {
		start := time.Now()

		// Wrap the response writer
		optimizedWriter := &OptimizedResponseWriter{
			ResponseWriter: c.Writer,
			optimizer:      optimizer,
			context:        c,
			startTime:      start,
		}
		c.Writer = optimizedWriter

		// Execute the request
		c.Next()

		// Finalize optimization
		optimizedWriter.finalizeResponse()

		// Record metrics
		processingTime := time.Since(start)
		optimizer.recordResponseTime(processingTime)

		// Log slow responses
		if config.LogSlowResponses && processingTime > config.SlowResponseThreshold {
			logger.Warn("Slow response detected",
				zap.String("path", c.Request.URL.Path),
				zap.Duration("processing_time", processingTime),
				zap.Int("response_size", optimizedWriter.responseSize),
				zap.Bool("compressed", optimizedWriter.compressed),
			)
		}
	}
}

// OptimizedResponseWriter wraps gin.ResponseWriter with optimization capabilities
type OptimizedResponseWriter struct {
	gin.ResponseWriter
	optimizer    *ResponseOptimizer
	context      *gin.Context
	buffer       *bytes.Buffer
	compressed   bool
	responseSize int
	startTime    time.Time
	headers      http.Header
}

// Write captures response data for optimization
func (w *OptimizedResponseWriter) Write(data []byte) (int, error) {
	if w.buffer == nil {
		w.buffer = bytes.NewBuffer(make([]byte, 0, len(data)))
	}

	w.responseSize += len(data)
	return w.buffer.Write(data)
}

// WriteHeader captures headers for optimization
func (w *OptimizedResponseWriter) WriteHeader(status int) {
	w.ResponseWriter.WriteHeader(status)
}

// finalizeResponse applies all optimizations and writes the final response
func (w *OptimizedResponseWriter) finalizeResponse() {
	if w.buffer == nil || w.buffer.Len() == 0 {
		return
	}

	data := w.buffer.Bytes()
	originalSize := len(data)

	// Apply field filtering if requested
	if w.optimizer.config.FieldsConfig.Enabled {
		if filteredData, err := w.optimizer.fieldsFilter.FilterFields(w.context, data); err == nil {
			data = filteredData
		}
	}

	// Apply compression if beneficial
	if w.optimizer.config.CompressionConfig.Enabled {
		if compressedData, compressed, err := w.optimizer.compressor.Compress(w.context.Request, data); err == nil && compressed {
			data = compressedData
			w.compressed = true
		}
	}

	// Set optimization headers
	w.setOptimizationHeaders(originalSize, len(data))

	// Write final response
	w.ResponseWriter.Write(data)

	// Record metrics
	w.optimizer.recordResponse(originalSize, len(data), w.compressed)
}

// setOptimizationHeaders sets headers related to response optimization
func (w *OptimizedResponseWriter) setOptimizationHeaders(originalSize, finalSize int) {
	// Set content length
	w.ResponseWriter.Header().Set("Content-Length", strconv.Itoa(finalSize))

	// Set compression headers
	if w.compressed {
		compressionRatio := float64(originalSize-finalSize) / float64(originalSize) * 100
		w.ResponseWriter.Header().Set("X-Compression-Ratio", fmt.Sprintf("%.2f%%", compressionRatio))
	}

	// Set ETag if enabled
	if w.optimizer.config.CachingConfig.EnableETags {
		etag := w.optimizer.generateETag(w.buffer.Bytes())
		w.ResponseWriter.Header().Set("ETag", etag)
	}

	// Set cache headers
	if w.optimizer.config.CachingConfig.MaxAge > 0 {
		w.ResponseWriter.Header().Set("Cache-Control", fmt.Sprintf("max-age=%d", int(w.optimizer.config.CachingConfig.MaxAge.Seconds())))
	}

	// Set optimization metadata
	w.ResponseWriter.Header().Set("X-Response-Optimized", "true")
	w.ResponseWriter.Header().Set("X-Original-Size", strconv.Itoa(originalSize))
	w.ResponseWriter.Header().Set("X-Final-Size", strconv.Itoa(finalSize))
}

// Response Compressor Implementation

// NewResponseCompressor creates a new response compressor
func NewResponseCompressor(config CompressionConfig, logger *zap.Logger) *ResponseCompressor {
	compressor := &ResponseCompressor{
		config: config,
		logger: logger,
	}

	// Initialize gzip pool
	compressor.gzipPool = sync.Pool{
		New: func() interface{} {
			writer, _ := gzip.NewWriterLevel(nil, config.CompressionLevel)
			return writer
		},
	}

	return compressor
}

// Compress compresses response data if beneficial
func (rc *ResponseCompressor) Compress(req *http.Request, data []byte) ([]byte, bool, error) {
	// Check if compression is beneficial
	if !rc.shouldCompress(req, data) {
		return data, false, nil
	}

	// Determine compression method
	encoding := rc.getBestEncoding(req)

	switch encoding {
	case "gzip":
		return rc.compressGzip(data)
	case "br":
		return rc.compressBrotli(data)
	default:
		return data, false, nil
	}
}

// shouldCompress determines if response should be compressed
func (rc *ResponseCompressor) shouldCompress(req *http.Request, data []byte) bool {
	// Check size constraints
	if len(data) < rc.config.MinSize || len(data) > rc.config.MaxSize {
		return false
	}

	// Check if client accepts compression
	acceptEncoding := req.Header.Get("Accept-Encoding")
	if !strings.Contains(acceptEncoding, "gzip") && !strings.Contains(acceptEncoding, "br") {
		return false
	}

	// Check content type (if we can determine it)
	// This is a simplified check - in production, you'd analyze the content
	return true
}

// getBestEncoding returns the best compression encoding for the client
func (rc *ResponseCompressor) getBestEncoding(req *http.Request) string {
	acceptEncoding := req.Header.Get("Accept-Encoding")

	if rc.config.EnableBrotli && rc.config.PreferBrotli && strings.Contains(acceptEncoding, "br") {
		return "br"
	}

	if strings.Contains(acceptEncoding, "gzip") {
		return "gzip"
	}

	return ""
}

// compressGzip compresses data using gzip
func (rc *ResponseCompressor) compressGzip(data []byte) ([]byte, bool, error) {
	var buf bytes.Buffer

	// Get writer from pool
	writer := rc.gzipPool.Get().(*gzip.Writer)
	defer rc.gzipPool.Put(writer)

	writer.Reset(&buf)

	if _, err := writer.Write(data); err != nil {
		return nil, false, err
	}

	if err := writer.Close(); err != nil {
		return nil, false, err
	}

	compressed := buf.Bytes()

	// Only return compressed data if it's actually smaller
	if len(compressed) < len(data) {
		return compressed, true, nil
	}

	return data, false, nil
}

// compressBrotli compresses data using Brotli (placeholder implementation)
func (rc *ResponseCompressor) compressBrotli(data []byte) ([]byte, bool, error) {
	// Brotli compression would be implemented here
	// For now, fall back to gzip
	return rc.compressGzip(data)
}

// Smart Paginator Implementation

// NewSmartPaginator creates a new smart paginator
func NewSmartPaginator(config PaginationConfig, logger *zap.Logger) *SmartPaginator {
	return &SmartPaginator{
		config: config,
		logger: logger,
	}
}

// PaginationParams extracts pagination parameters from request
type PaginationParams struct {
	Page     int    `json:"page"`
	PageSize int    `json:"page_size"`
	Offset   int    `json:"offset"`
	Cursor   string `json:"cursor,omitempty"`
	Sort     string `json:"sort,omitempty"`
}

// ExtractPaginationParams extracts pagination parameters from gin context
func (sp *SmartPaginator) ExtractPaginationParams(c *gin.Context) PaginationParams {
	params := PaginationParams{
		Page:     1,
		PageSize: sp.config.DefaultPageSize,
	}

	// Extract page number
	if pageStr := c.Query(sp.config.PageParam); pageStr != "" {
		if page, err := strconv.Atoi(pageStr); err == nil && page > 0 {
			params.Page = page
		}
	}

	// Extract page size
	if pageSizeStr := c.Query(sp.config.PageSizeParam); pageSizeStr != "" {
		if pageSize, err := strconv.Atoi(pageSizeStr); err == nil && pageSize > 0 {
			params.PageSize = pageSize
			if params.PageSize > sp.config.MaxPageSize {
				params.PageSize = sp.config.MaxPageSize
			}
		}
	}

	// Extract offset
	if offsetStr := c.Query(sp.config.OffsetParam); offsetStr != "" {
		if offset, err := strconv.Atoi(offsetStr); err == nil && offset >= 0 {
			params.Offset = offset
		}
	} else {
		// Calculate offset from page and page size
		params.Offset = (params.Page - 1) * params.PageSize
	}

	// Extract cursor
	params.Cursor = c.Query(sp.config.CursorParam)

	// Extract sort
	params.Sort = c.Query(sp.config.SortParam)

	return params
}

// CreatePaginationInfo creates pagination metadata
func (sp *SmartPaginator) CreatePaginationInfo(params PaginationParams, total int64) PaginationInfo {
	totalPages := int((total + int64(params.PageSize) - 1) / int64(params.PageSize))

	info := PaginationInfo{
		Page:       params.Page,
		PageSize:   params.PageSize,
		Total:      total,
		TotalPages: totalPages,
		HasNext:    params.Page < totalPages,
		HasPrev:    params.Page > 1,
	}

	// Add cursor information if using cursor pagination
	if sp.config.EnableCursor && params.Cursor != "" {
		info.NextCursor = sp.generateNextCursor(params)
		info.PrevCursor = sp.generatePrevCursor(params)
	}

	return info
}

// generateNextCursor generates cursor for next page
func (sp *SmartPaginator) generateNextCursor(params PaginationParams) string {
	// Simplified cursor generation - in production, use proper cursor logic
	return fmt.Sprintf("page_%d", params.Page+1)
}

// generatePrevCursor generates cursor for previous page
func (sp *SmartPaginator) generatePrevCursor(params PaginationParams) string {
	if params.Page <= 1 {
		return ""
	}
	return fmt.Sprintf("page_%d", params.Page-1)
}

// Efficient Serializer Implementation

// NewEfficientSerializer creates a new efficient serializer
func NewEfficientSerializer(config SerializationConfig, logger *zap.Logger) *EfficientSerializer {
	serializer := &EfficientSerializer{
		config:      config,
		serializers: make(map[string]Serializer),
		logger:      logger,
	}

	// Register default serializers
	if config.CustomSerializers != nil {
		for contentType, customSerializer := range config.CustomSerializers {
			serializer.serializers[contentType] = customSerializer
		}
	}

	return serializer
}

// SerializeResponse serializes response data efficiently
func (es *EfficientSerializer) SerializeResponse(data interface{}, contentType string) ([]byte, error) {
	// Use custom serializer if available
	if serializer, exists := es.serializers[contentType]; exists {
		return serializer.Serialize(data)
	}

	// Default JSON serialization with optimizations
	return es.serializeJSON(data)
}

// serializeJSON serializes data to optimized JSON
func (es *EfficientSerializer) serializeJSON(data interface{}) ([]byte, error) {
	var result []byte
	var err error

	if es.config.CompactJSON {
		// Compact JSON without indentation
		result, err = json.Marshal(data)
	} else {
		// Pretty-printed JSON
		result, err = json.MarshalIndent(data, "", "  ")
	}

	if err != nil {
		return nil, err
	}

	// Apply JSON minification if enabled
	if es.config.EnableJSONMinify {
		result = es.minifyJSON(result)
	}

	return result, nil
}

// minifyJSON removes unnecessary whitespace from JSON
func (es *EfficientSerializer) minifyJSON(data []byte) []byte {
	// Simple minification - remove spaces around colons and commas
	minified := bytes.ReplaceAll(data, []byte(": "), []byte(":"))
	minified = bytes.ReplaceAll(minified, []byte(", "), []byte(","))
	return minified
}

// Fields Filter Implementation

// NewFieldsFilter creates a new fields filter
func NewFieldsFilter(config FieldsFilterConfig, logger *zap.Logger) *FieldsFilter {
	return &FieldsFilter{
		config: config,
		logger: logger,
	}
}

// FilterFields applies field filtering to response data
func (ff *FieldsFilter) FilterFields(c *gin.Context, data []byte) ([]byte, error) {
	if !ff.config.Enabled {
		return data, nil
	}

	// Extract field selection parameters
	fieldsParam := c.Query(ff.config.FieldsParam)
	includeParam := c.Query(ff.config.IncludeParam)
	excludeParam := c.Query(ff.config.ExcludeParam)

	// If no field filtering requested, return original data
	if fieldsParam == "" && includeParam == "" && excludeParam == "" {
		return data, nil
	}

	// Parse JSON data
	var jsonData interface{}
	if err := json.Unmarshal(data, &jsonData); err != nil {
		return data, err // Return original data if parsing fails
	}

	// Apply field filtering
	filteredData := ff.applyFieldFiltering(jsonData, fieldsParam, includeParam, excludeParam)

	// Serialize back to JSON
	return json.Marshal(filteredData)
}

// applyFieldFiltering applies field filtering logic
func (ff *FieldsFilter) applyFieldFiltering(data interface{}, fields, include, exclude string) interface{} {
	// Simplified field filtering implementation
	// In production, you would implement sophisticated field selection logic

	if fields != "" {
		return ff.selectFields(data, strings.Split(fields, ","))
	}

	if include != "" {
		return ff.includeFields(data, strings.Split(include, ","))
	}

	if exclude != "" {
		return ff.excludeFields(data, strings.Split(exclude, ","))
	}

	return data
}

// selectFields selects only specified fields
func (ff *FieldsFilter) selectFields(data interface{}, fields []string) interface{} {
	// Simplified implementation
	return data
}

// includeFields includes specified fields
func (ff *FieldsFilter) includeFields(data interface{}, fields []string) interface{} {
	// Simplified implementation
	return data
}

// excludeFields excludes specified fields
func (ff *FieldsFilter) excludeFields(data interface{}, fields []string) interface{} {
	// Simplified implementation
	return data
}

// Utility methods

// generateETag generates an ETag for response data
func (ro *ResponseOptimizer) generateETag(data []byte) string {
	// Simple ETag generation using content hash
	return fmt.Sprintf(`"%x"`, len(data)) // Simplified - use proper hash in production
}

// recordResponse records response metrics
func (ro *ResponseOptimizer) recordResponse(originalSize, finalSize int, compressed bool) {
	ro.mutex.Lock()
	defer ro.mutex.Unlock()

	if compressed {
		ro.metrics.CompressedResponses++
		if originalSize > 0 {
			ratio := float64(originalSize-finalSize) / float64(originalSize)
			ro.metrics.CompressionRatio = (ro.metrics.CompressionRatio + ratio) / 2
			ro.metrics.BytesSaved += int64(originalSize - finalSize)
		}
	}

	// Update response size metrics
	ro.metrics.AverageResponseSize = (ro.metrics.AverageResponseSize + int64(finalSize)) / 2
	if int64(finalSize) > ro.metrics.LargestResponseSize {
		ro.metrics.LargestResponseSize = int64(finalSize)
	}
}

// recordResponseTime records response processing time
func (ro *ResponseOptimizer) recordResponseTime(duration time.Duration) {
	ro.mutex.Lock()
	defer ro.mutex.Unlock()

	if ro.metrics.ResponseTime == 0 {
		ro.metrics.ResponseTime = duration
	} else {
		ro.metrics.ResponseTime = (ro.metrics.ResponseTime + duration) / 2
	}
}

// GetMetrics returns current optimization metrics
func (ro *ResponseOptimizer) GetMetrics() *ResponseMetrics {
	ro.mutex.RLock()
	defer ro.mutex.RUnlock()

	// Update timing
	ro.metrics.LastUpdated = time.Now()

	// Return a copy
	metricsCopy := *ro.metrics
	return &metricsCopy
}

// startMetricsCollection starts metrics collection routine
func (ro *ResponseOptimizer) startMetricsCollection() {
	ticker := time.NewTicker(ro.config.MetricsInterval)
	defer ticker.Stop()

	for range ticker.C {
		ro.collectMetrics()
	}
}

// collectMetrics collects and logs optimization metrics
func (ro *ResponseOptimizer) collectMetrics() {
	ro.mutex.RLock()
	metrics := *ro.metrics
	ro.mutex.RUnlock()

	ro.logger.Debug("Response optimization metrics",
		zap.Int64("compressed_responses", metrics.CompressedResponses),
		zap.Float64("compression_ratio", metrics.CompressionRatio),
		zap.Int64("bytes_saved", metrics.BytesSaved),
		zap.Int64("average_response_size", metrics.AverageResponseSize),
		zap.Duration("average_response_time", metrics.ResponseTime),
	)
}

// Pagination helper functions for handlers

// CreatePaginatedResponse creates a paginated response with metadata
func CreatePaginatedResponse(c *gin.Context, data interface{}, total int64, paginator *SmartPaginator) gin.H {
	params := paginator.ExtractPaginationParams(c)
	pagination := paginator.CreatePaginationInfo(params, total)

	return gin.H{
		"success":    true,
		"data":       data,
		"pagination": pagination,
		"metadata": gin.H{
			"total":       total,
			"page":        pagination.Page,
			"page_size":   pagination.PageSize,
			"total_pages": pagination.TotalPages,
		},
	}
}

// ExtractSortParams extracts sorting parameters from request
func ExtractSortParams(c *gin.Context, sortParam string) map[string]string {
	sortQuery := c.Query(sortParam)
	if sortQuery == "" {
		return nil
	}

	sorts := make(map[string]string)
	sortFields := strings.Split(sortQuery, ",")

	for _, field := range sortFields {
		field = strings.TrimSpace(field)
		if strings.HasPrefix(field, "-") {
			sorts[field[1:]] = "DESC"
		} else {
			sorts[field] = "ASC"
		}
	}

	return sorts
}
