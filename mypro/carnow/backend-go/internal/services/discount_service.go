package services

import (
	"context"
	"errors"
	"fmt"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"

	"carnow-backend/internal/domain"
)

// DiscountService handles discount-related operations
type DiscountService struct {
	db     *gorm.DB
	logger *zap.Logger
}

// NewDiscountService creates a new discount service
func NewDiscountService(db *gorm.DB, logger *zap.Logger) *DiscountService {
	return &DiscountService{
		db:     db,
		logger: logger,
	}
}

// GetActiveDiscounts retrieves all active discounts
func (s *DiscountService) GetActiveDiscounts(ctx context.Context) ([]domain.Discount, error) {
	var discounts []domain.Discount

	err := s.db.WithContext(ctx).
		Where("is_active = ? AND (end_date IS NULL OR end_date >= ?)", true, time.Now()).
		Find(&discounts).Error

	if err != nil {
		s.logger.Error("Failed to fetch active discounts", zap.Error(err))
		return nil, fmt.Errorf("failed to fetch discounts: %w", err)
	}

	return discounts, nil
}

// GetDiscountByCode retrieves a discount by its code
func (s *DiscountService) GetDiscountByCode(ctx context.Context, code string) (*domain.DiscountCode, error) {
	var discountCode domain.DiscountCode

	err := s.db.WithContext(ctx).
		Preload("Discount").
		Where("code = ? AND is_active = ?", code, true).
		First(&discountCode).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("discount code not found: %s", code)
		}
		s.logger.Error("Failed to fetch discount code", zap.Error(err), zap.String("code", code))
		return nil, fmt.Errorf("failed to fetch discount code: %w", err)
	}

	// Check if discount is still active
	if !discountCode.Discount.IsActive {
		return nil, fmt.Errorf("discount is not active")
	}

	// Check date validity
	now := time.Now()
	if discountCode.Discount.StartDate.After(now) {
		return nil, fmt.Errorf("discount has not started yet")
	}

	if discountCode.Discount.EndDate != nil && discountCode.Discount.EndDate.Before(now) {
		return nil, fmt.Errorf("discount has expired")
	}

	// Check usage limits
	if discountCode.Discount.UsageLimit != nil && discountCode.Discount.UsedCount >= *discountCode.Discount.UsageLimit {
		return nil, fmt.Errorf("discount usage limit exceeded")
	}

	if discountCode.UsageLimit != nil && discountCode.UsedCount >= *discountCode.UsageLimit {
		return nil, fmt.Errorf("discount code usage limit exceeded")
	}

	return &discountCode, nil
}

// CalculateDiscount calculates the discount for a given amount
func (s *DiscountService) CalculateDiscount(ctx context.Context, amount float64, discountCode *domain.DiscountCode) (*domain.DiscountCalculation, error) {
	if discountCode == nil {
		return &domain.DiscountCalculation{
			OriginalAmount:     amount,
			DiscountAmount:     0,
			FinalAmount:        amount,
			DiscountPercentage: 0,
		}, nil
	}

	discount := discountCode.Discount

	// Check minimum amount requirement
	if amount < discount.MinAmount {
		return nil, fmt.Errorf("minimum amount required: %.2f", discount.MinAmount)
	}

	var discountAmount float64

	switch discount.DiscountType {
	case "percentage":
		discountAmount = amount * (discount.DiscountValue / 100)
	case "fixed_amount":
		discountAmount = discount.DiscountValue
	default:
		return nil, fmt.Errorf("invalid discount type: %s", discount.DiscountType)
	}

	// Apply maximum discount limit if set
	if discount.MaxDiscount != nil && discountAmount > *discount.MaxDiscount {
		discountAmount = *discount.MaxDiscount
	}

	// Ensure discount doesn't exceed the original amount
	if discountAmount > amount {
		discountAmount = amount
	}

	finalAmount := amount - discountAmount
	discountPercentage := (discountAmount / amount) * 100

	return &domain.DiscountCalculation{
		OriginalAmount:     amount,
		DiscountAmount:     discountAmount,
		FinalAmount:        finalAmount,
		DiscountPercentage: discountPercentage,
		AppliedDiscount:    &discount,
		AppliedCode:        discountCode,
	}, nil
}

// ApplyDiscount applies a discount and records the usage
func (s *DiscountService) ApplyDiscount(ctx context.Context, userID string, amount float64, discountCode *domain.DiscountCode, subscriptionID *uint) (*domain.DiscountCalculation, error) {
	// Calculate discount
	calculation, err := s.CalculateDiscount(ctx, amount, discountCode)
	if err != nil {
		return nil, err
	}

	// If no discount applied, return calculation without recording usage
	if discountCode == nil {
		return calculation, nil
	}

	// Record discount usage
	usage := domain.DiscountUsage{
		DiscountID:           discountCode.DiscountID,
		DiscountCodeID:       &discountCode.ID,
		UserID:               userID,
		SubscriptionID:       subscriptionID,
		AmountBeforeDiscount: amount,
		DiscountAmount:       calculation.DiscountAmount,
		AmountAfterDiscount:  calculation.FinalAmount,
	}

	err = s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// Record usage
		if err := tx.Create(&usage).Error; err != nil {
			return fmt.Errorf("failed to record discount usage: %w", err)
		}

		// Update usage counts
		if err := tx.Model(&domain.Discount{}).
			Where("id = ?", discountCode.DiscountID).
			UpdateColumn("used_count", gorm.Expr("used_count + 1")).Error; err != nil {
			return fmt.Errorf("failed to update discount usage count: %w", err)
		}

		if err := tx.Model(&domain.DiscountCode{}).
			Where("id = ?", discountCode.ID).
			UpdateColumn("used_count", gorm.Expr("used_count + 1")).Error; err != nil {
			return fmt.Errorf("failed to update discount code usage count: %w", err)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	s.logger.Info("Discount applied successfully",
		zap.String("user_id", userID),
		zap.String("discount_code", discountCode.Code),
		zap.Float64("original_amount", amount),
		zap.Float64("discount_amount", calculation.DiscountAmount),
		zap.Float64("final_amount", calculation.FinalAmount),
	)

	return calculation, nil
}

// GetYearlySubscriptionDiscount retrieves the automatic yearly subscription discount
func (s *DiscountService) GetYearlySubscriptionDiscount(ctx context.Context) (*domain.Discount, error) {
	var discount domain.Discount

	err := s.db.WithContext(ctx).
		Where("is_active = ? AND name = ?", true, "Yearly Subscription Discount").
		First(&discount).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// Create default yearly discount if not exists
			discount = domain.Discount{
				Name:          "Yearly Subscription Discount",
				NameAr:        "خصم الاشتراك السنوي",
				Description:   "Automatic discount for yearly subscriptions",
				DescriptionAr: "خصم تلقائي للاشتراكات السنوية",
				DiscountType:  "percentage",
				DiscountValue: 15.00,
				ApplicableTo:  []string{"subscription"},
				IsActive:      true,
			}

			if err := s.db.WithContext(ctx).Create(&discount).Error; err != nil {
				s.logger.Error("Failed to create default yearly discount", zap.Error(err))
				return nil, fmt.Errorf("failed to create default yearly discount: %w", err)
			}
		} else {
			s.logger.Error("Failed to fetch yearly subscription discount", zap.Error(err))
			return nil, fmt.Errorf("failed to fetch yearly discount: %w", err)
		}
	}

	return &discount, nil
}

// ValidateDiscountCode validates a discount code without applying it
func (s *DiscountService) ValidateDiscountCode(ctx context.Context, code string) (*domain.DiscountCode, error) {
	return s.GetDiscountByCode(ctx, code)
}
