package services

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"sync"
	"time"

	"carnow-backend/internal/infrastructure/cache"
	"carnow-backend/internal/interfaces"

	"gorm.io/gorm"
)

// RealtimeService handles real-time data synchronization and updates
type RealtimeService struct {
	db              *gorm.DB
	wsHandler       interfaces.WebSocketBroadcaster
	cacheProvider   cache.CacheProvider
	pollingInterval time.Duration
	mutex           sync.RWMutex
	subscribers     map[string][]string  // Map of data types to user IDs
	lastUpdates     map[string]time.Time // Track last update times
}

// DataChangeEvent represents a data change event
type DataChangeEvent struct {
	Type       string                 `json:"type"`
	EntityID   string                 `json:"entity_id"`
	EntityType string                 `json:"entity_type"`
	Action     string                 `json:"action"` // create, update, delete
	Data       interface{}            `json:"data"`
	UserID     string                 `json:"user_id,omitempty"`
	Timestamp  time.Time              `json:"timestamp"`
	Metadata   map[string]interface{} `json:"metadata,omitempty"`
	Version    int64                  `json:"version,omitempty"`
}

// ConflictResolution represents conflict resolution data
type ConflictResolution struct {
	EntityID      string      `json:"entity_id"`
	EntityType    string      `json:"entity_type"`
	ClientVersion int64       `json:"client_version"`
	ServerVersion int64       `json:"server_version"`
	ConflictType  string      `json:"conflict_type"`
	Resolution    string      `json:"resolution"` // server_wins, client_wins, merge
	MergedData    interface{} `json:"merged_data,omitempty"`
	Timestamp     time.Time   `json:"timestamp"`
}

// NewRealtimeService creates a new real-time service
func NewRealtimeService(db *gorm.DB, wsHandler interfaces.WebSocketBroadcaster) *RealtimeService {
	cacheProvider := cache.GetCacheProvider()

	service := &RealtimeService{
		db:              db,
		wsHandler:       wsHandler,
		cacheProvider:   *cacheProvider,
		pollingInterval: 5 * time.Second, // Poll every 5 seconds
		subscribers:     make(map[string][]string),
		lastUpdates:     make(map[string]time.Time),
	}

	// Start polling for data changes
	go service.startPolling()

	return service
}

// startPolling starts the polling mechanism for frequently changing data
func (s *RealtimeService) startPolling() {
	ticker := time.NewTicker(s.pollingInterval)
	defer ticker.Stop()

	log.Printf("✅ Real-time polling started (interval: %v)", s.pollingInterval)

	for {
		select {
		case <-ticker.C:
			s.pollForChanges()
		}
	}
}

// pollForChanges polls the database for changes in frequently updated data
func (s *RealtimeService) pollForChanges() {
	ctx := context.Background()

	// Poll for product changes
	s.pollProductChanges(ctx)

	// Poll for order status changes
	s.pollOrderChanges(ctx)

	// Poll for wallet balance changes
	s.pollWalletChanges(ctx)

	// Poll for inventory changes
	s.pollInventoryChanges(ctx)
}

// pollProductChanges polls for product updates
func (s *RealtimeService) pollProductChanges(ctx context.Context) {
	lastUpdate := s.getLastUpdate("products")

	// Query for products updated since last check
	var products []struct {
		ID        string    `json:"id"`
		Name      string    `json:"name"`
		Price     float64   `json:"price"`
		UpdatedAt time.Time `json:"updated_at"`
		UserID    *string   `json:"user_id"`
	}

	query := `
		SELECT id, name, price, updated_at, user_id 
		FROM products 
		WHERE updated_at > ? AND is_deleted = false
		ORDER BY updated_at DESC
		LIMIT 100
	`

	if err := s.db.Raw(query, lastUpdate).Scan(&products).Error; err != nil {
		log.Printf("❌ Error polling product changes: %v", err)
		return
	}

	if len(products) > 0 {
		log.Printf("🔄 Found %d product updates since %v", len(products), lastUpdate)

		for _, product := range products {
			// Notify about product update
			message := interfaces.WebSocketMessage{
				Type:      "product",
				Event:     "updated",
				Data:      product,
				UserID:    *product.UserID,
				"timestamp": time.Now().Format(time.RFC3339),
			}

			_ = s.wsHandler.BroadcastToUser(*product.UserID, messageBytes)

			// Invalidate cache
			s.invalidateProductCache(product.ID)
		}

		// Update last check time
		s.setLastUpdate("products", time.Now())
	}
}

// pollOrderChanges polls for order status changes
func (s *RealtimeService) pollOrderChanges(ctx context.Context) {
	lastUpdate := s.getLastUpdate("orders")

	var orders []struct {
		ID        string    `json:"id"`
		Status    string    `json:"status"`
		UpdatedAt time.Time `json:"updated_at"`
		UserID    string    `json:"user_id"`
		Total     float64   `json:"total"`
	}

	query := `
		SELECT id, status, updated_at, user_id, total 
		FROM orders 
		WHERE updated_at > ? AND is_deleted = false
		ORDER BY updated_at DESC
		LIMIT 50
	`

	if err := s.db.Raw(query, lastUpdate).Scan(&orders).Error; err != nil {
		log.Printf("❌ Error polling order changes: %v", err)
		return
	}

	if len(orders) > 0 {
		log.Printf("🔄 Found %d order updates since %v", len(orders), lastUpdate)

		for _, order := range orders {
			// Notify user about order status change
			message := interfaces.WebSocketMessage{
				Type:      "order",
				"event":     "status_changed",
				"data":      order,
				"user_id":    order.UserID,
				"timestamp": time.Now().Format(time.RFC3339),
			}

			_ = s.wsHandler.BroadcastToUser(order.UserID, messageBytes)
		}

		s.setLastUpdate("orders", time.Now())
	}
}

// pollWalletChanges polls for wallet balance changes
func (s *RealtimeService) pollWalletChanges(ctx context.Context) {
	lastUpdate := s.getLastUpdate("wallets")

	var wallets []struct {
		ID        string    `json:"id"`
		UserID    string    `json:"user_id"`
		Balance   float64   `json:"balance"`
		UpdatedAt time.Time `json:"updated_at"`
	}

	query := `
		SELECT id, user_id, balance, updated_at 
		FROM wallets 
		WHERE updated_at > ? AND is_deleted = false
		ORDER BY updated_at DESC
		LIMIT 50
	`

	if err := s.db.Raw(query, lastUpdate).Scan(&wallets).Error; err != nil {
		log.Printf("❌ Error polling wallet changes: %v", err)
		return
	}

	if len(wallets) > 0 {
		log.Printf("🔄 Found %d wallet updates since %v", len(wallets), lastUpdate)

		for _, wallet := range wallets {
			// Notify user about balance change
			message := interfaces.WebSocketMessage{
				Type:      "wallet",
				Event:     "balance_updated",
				Data:      wallet,
				UserID:    wallet.UserID,
				Timestamp: time.Now().Format(time.RFC3339),
			}

			_ = s.wsHandler.BroadcastToUser(wallet.UserID, messageBytes)
		}

		s.setLastUpdate("wallets", time.Now())
	}
}

// pollInventoryChanges polls for inventory quantity changes
func (s *RealtimeService) pollInventoryChanges(ctx context.Context) {
	lastUpdate := s.getLastUpdate("inventory")

	var inventoryItems []struct {
		ProductID string    `json:"product_id"`
		Quantity  int       `json:"quantity"`
		UpdatedAt time.Time `json:"updated_at"`
		SellerID  string    `json:"seller_id"`
	}

	query := `
		SELECT p.id as product_id, p.quantity, p.updated_at, p.user_id as seller_id
		FROM products p
		WHERE p.updated_at > ? AND p.is_deleted = false
		AND p.quantity IS NOT NULL
		ORDER BY p.updated_at DESC
		LIMIT 100
	`

	if err := s.db.Raw(query, lastUpdate).Scan(&inventoryItems).Error; err != nil {
		log.Printf("❌ Error polling inventory changes: %v", err)
		return
	}

	if len(inventoryItems) > 0 {
		log.Printf("🔄 Found %d inventory updates since %v", len(inventoryItems), lastUpdate)

		for _, item := range inventoryItems {
			// Notify seller about inventory change
			message := interfaces.WebSocketMessage{
				Type:      "inventory",
				Event:     "quantity_updated",
				Data:      item,
				UserID:    item.SellerID,
				Timestamp: time.Now().Format(time.RFC3339),
			}

			_ = s.wsHandler.BroadcastToUser(item.SellerID, messageBytes)
		}

		s.setLastUpdate("inventory", time.Now())
	}
}

// getLastUpdate gets the last update time for a data type
func (s *RealtimeService) getLastUpdate(dataType string) time.Time {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	if lastUpdate, exists := s.lastUpdates[dataType]; exists {
		return lastUpdate
	}

	// Default to 1 minute ago for first run
	return time.Now().Add(-1 * time.Minute)
}

// setLastUpdate sets the last update time for a data type
func (s *RealtimeService) setLastUpdate(dataType string, timestamp time.Time) {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	s.lastUpdates[dataType] = timestamp
}

// invalidateProductCache invalidates product-related cache entries
func (s *RealtimeService) invalidateProductCache(productID string) {
	if !s.cacheProvider.IsEnabled() {
		return
	}

	ctx := context.Background()

	// Invalidate specific product cache
	productKey := fmt.Sprintf("product:%s", productID)
	s.cacheProvider.Delete(ctx, productKey)

	// Invalidate products list cache (if cache supports pattern deletion)
	// s.cacheProvider.DeletePattern(ctx, "products:*")

	log.Printf("🗑️ Invalidated cache for product: %s", productID)
}

// HandleOptimisticUpdate handles optimistic updates with conflict resolution
func (s *RealtimeService) HandleOptimisticUpdate(ctx context.Context, entityType, entityID string, clientVersion int64, updateData map[string]interface{}, userID string) (*ConflictResolution, error) {
	// Get current server version
	serverVersion, err := s.getEntityVersion(ctx, entityType, entityID)
	if err != nil {
		return nil, fmt.Errorf("failed to get server version: %w", err)
	}

	// Check for conflicts
	if clientVersion < serverVersion {
		// Conflict detected - client is behind server
		log.Printf("⚠️ Conflict detected for %s:%s (client: %d, server: %d)", entityType, entityID, clientVersion, serverVersion)

		// Get current server data
		serverData, err := s.getEntityData(ctx, entityType, entityID)
		if err != nil {
			return nil, fmt.Errorf("failed to get server data: %w", err)
		}

		// Resolve conflict based on entity type and conflict resolution strategy
		resolution := s.resolveConflict(entityType, serverData, updateData)

		return &ConflictResolution{
			EntityID:      entityID,
			Entity"type":    entityType,
			ClientVersion: clientVersion,
			ServerVersion: serverVersion,
			Conflict"type":  "version_mismatch",
			Resolution:    resolution.Resolution,
			Merged"data":    resolution.MergedData,
			"timestamp":     time.Now(),
		}, nil
	}

	// No conflict - apply update
	newVersion := serverVersion + 1
	if err := s.applyEntityUpdate(ctx, entityType, entityID, updateData, newVersion, userID); err != nil {
		return nil, fmt.Errorf("failed to apply update: %w", err)
	}

	// Broadcast update to other clients
	s.broadcastDataChange(DataChangeEvent{
		"type":       "update",
		EntityID:   entityID,
		Entity"type": entityType,
		Action:     "update",
		"data":       updateData,
		"user_id":     userID,
		"timestamp":  time.Now(),
		Version:    newVersion,
	})

	return nil, nil // No conflict
}

// getEntityVersion gets the current version of an entity
func (s *RealtimeService) getEntityVersion(ctx context.Context, entityType, entityID string) (int64, error) {
	var version int64

	switch entityType {
	case "product":
		query := "SELECT EXTRACT(EPOCH FROM updated_at)::bigint as version FROM products WHERE id = ? AND is_deleted = false"
		if err := s.db.Raw(query, entityID).Scan(&version).Error; err != nil {
			return 0, err
		}
	case "order":
		query := "SELECT EXTRACT(EPOCH FROM updated_at)::bigint as version FROM orders WHERE id = ? AND is_deleted = false"
		if err := s.db.Raw(query, entityID).Scan(&version).Error; err != nil {
			return 0, err
		}
	default:
		return 0, fmt.Errorf("unsupported entity type: %s", entityType)
	}

	return version, nil
}

// getEntityData gets the current data of an entity
func (s *RealtimeService) getEntityData(ctx context.Context, entityType, entityID string) (map[string]interface{}, error) {
	var data map[string]interface{}

	switch entityType {
	case "product":
		var product struct {
			ID          string  `json:"id"`
			Name        string  `json:"name"`
			Description *string `json:"description"`
			Price       float64 `json:"price"`
			Quantity    *int    `json:"quantity"`
		}

		query := "SELECT id, name, description, price, quantity FROM products WHERE id = ? AND is_deleted = false"
		if err := s.db.Raw(query, entityID).Scan(&product).Error; err != nil {
			return nil, err
		}

		// Convert to map
		jsonData, _ := json.Marshal(product)
		json.Unmarshal(jsonData, &data)

	case "order":
		var order struct {
			ID     string  `json:"id"`
			Status string  `json:"status"`
			Total  float64 `json:"total"`
		}

		query := "SELECT id, status, total FROM orders WHERE id = ? AND is_deleted = false"
		if err := s.db.Raw(query, entityID).Scan(&order).Error; err != nil {
			return nil, err
		}

		// Convert to map
		jsonData, _ := json.Marshal(order)
		json.Unmarshal(jsonData, &data)

	default:
		return nil, fmt.Errorf("unsupported entity type: %s", entityType)
	}

	return data, nil
}

// resolveConflict resolves conflicts between client and server data
func (s *RealtimeService) resolveConflict(entityType string, serverData, clientData map[string]interface{}) ConflictResolution {
	// Default resolution strategy: server wins for most cases
	resolution := ConflictResolution{
		Resolution: "server_wins",
		Merged"data": serverData,
	}

	switch entityType {
	case "product":
		// For products, merge non-conflicting fields
		merged := make(map[string]interface{})
		for k, v := range serverData {
			merged[k] = v
		}

		// Allow client to update certain fields if they don't conflict with critical server data
		if clientPrice, exists := clientData["price"]; exists {
			if serverPrice, ok := serverData["price"]; ok {
				// If prices are close (within 1%), use client price
				if clientPriceFloat, ok := clientPrice.(float64); ok {
					if serverPriceFloat, ok := serverPrice.(float64); ok {
						diff := (clientPriceFloat - serverPriceFloat) / serverPriceFloat
						if diff < 0.01 && diff > -0.01 { // Within 1%
							merged["price"] = clientPrice
							resolution.Resolution = "merge"
						}
					}
				}
			}
		}

		resolution.MergedData = merged

	case "order":
		// For orders, server always wins (critical business data)
		resolution.Resolution = "server_wins"
		resolution.MergedData = serverData
	}

	return resolution
}

// applyEntityUpdate applies an update to an entity
func (s *RealtimeService) applyEntityUpdate(ctx context.Context, entityType, entityID string, updateData map[string]interface{}, version int64, userID string) error {
	switch entityType {
	case "product":
		// Build dynamic update query
		setParts := []string{}
		values := []interface{}{}

		for field, value := range updateData {
			switch field {
			case "name", "description", "price", "quantity":
				setParts = append(setParts, fmt.Sprintf("%s = ?", field))
				values = append(values, value)
			}
		}

		if len(setParts) > 0 {
			setParts = append(setParts, "updated_at = NOW()")
			values = append(values, entityID)

			query := fmt.Sprintf("UPDATE products SET %s WHERE id = ?",
				fmt.Sprintf("%s", setParts))

			if err := s.db.Exec(query, values...).Error; err != nil {
				return err
			}
		}

	case "order":
		// Update order status
		if status, exists := updateData["status"]; exists {
			query := "UPDATE orders SET status = ?, updated_at = NOW() WHERE id = ?"
			if err := s.db.Exec(query, status, entityID).Error; err != nil {
				return err
			}
		}
	}

	return nil
}

// broadcastDataChange broadcasts a data change event to connected clients
func (s *RealtimeService) broadcastDataChange(event DataChangeEvent) {
	message := interfaces.WebSocketMessage{
		Type:      "data_change",
		Event:     event.Action,
		Data:      event,
		UserID:    event.UserID,
		Timestamp: time.Now().Format(time.RFC3339),
	}

	// Broadcast to all clients or specific user based on event type
	if event.UserID != "" {
		s.wsHandler.BroadcastToUser(event.UserID, message)
	} else {
		s.wsHandler.BroadcastToAll(message)
	}
}

// SubscribeToDataChanges subscribes a user to data change notifications
func (s *RealtimeService) SubscribeToDataChanges(userID, dataType string) {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if s.subscribers[dataType] == nil {
		s.subscribers[dataType] = make([]string, 0)
	}

	// Check if user is already subscribed
	for _, subscribedUserID := range s.subscribers[dataType] {
		if subscribedUserID == userID {
			return // Already subscribed
		}
	}

	s.subscribers[dataType] = append(s.subscribers[dataType], userID)
	log.Printf("✅ User %s subscribed to %s updates", userID, dataType)
}

// UnsubscribeFromDataChanges unsubscribes a user from data change notifications
func (s *RealtimeService) UnsubscribeFromDataChanges(userID, dataType string) {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if subscribers, exists := s.subscribers[dataType]; exists {
		for i, subscribedUserID := range subscribers {
			if subscribedUserID == userID {
				s.subscribers[dataType] = append(subscribers[:i], subscribers[i+1:]...)
				log.Printf("✅ User %s unsubscribed from %s updates", userID, dataType)
				break
			}
		}
	}
}

// GetRealtimeStats returns real-time service statistics
func (s *RealtimeService) GetRealtimeStats() map[string]interface{} {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	subscriberCounts := make(map[string]int)
	for dataType, subscribers := range s.subscribers {
		subscriberCounts[dataType] = len(subscribers)
	}

	return map[string]interface{}{
		"polling_interval":  s.pollingInterval.String(),
		"subscriber_counts": subscriberCounts,
		"last_updates":      s.lastUpdates,
		"websocket_stats":   "N/A", // Would need to be implemented in interface
		"timestamp":         time.Now(),
	}
}
