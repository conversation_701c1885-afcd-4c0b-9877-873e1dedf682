package config

import (
	"time"
)

// TimeoutConfig إعدادات المهلة الزمنية لجميع العمليات (Forever Plan - مبسط)
type TimeoutConfig struct {
	// HTTP Server Timeouts
	HTTPRead    time.Duration `mapstructure:"http_read" yaml:"http_read"`
	HTTPWrite   time.Duration `mapstructure:"http_write" yaml:"http_write"`
	HTTPIdle    time.Duration `mapstructure:"http_idle" yaml:"http_idle"`
	HTTPHandler time.Duration `mapstructure:"http_handler" yaml:"http_handler"`

	// Database Operation Timeouts
	DBConnection time.Duration `mapstructure:"db_connection" yaml:"db_connection"`
	DBQuery      time.Duration `mapstructure:"db_query" yaml:"db_query"`
	DBTx         time.Duration `mapstructure:"db_tx" yaml:"db_tx"`
	DBPing       time.Duration `mapstructure:"db_ping" yaml:"db_ping"`

	// External Service Timeouts
	SupabaseAPI time.Duration `mapstructure:"supabase_api" yaml:"supabase_api"`
	JWT         time.Duration `mapstructure:"jwt" yaml:"jwt"`

	// Circuit Breaker Timeouts
	CircuitBreakerTimeout    time.Duration `mapstructure:"circuit_breaker_timeout" yaml:"circuit_breaker_timeout"`
	CircuitBreakerRetryAfter time.Duration `mapstructure:"circuit_breaker_retry_after" yaml:"circuit_breaker_retry_after"`

	// Retry Configuration
	RetryMaxAttempts   int           `mapstructure:"retry_max_attempts" yaml:"retry_max_attempts"`
	RetryBaseDelay     time.Duration `mapstructure:"retry_base_delay" yaml:"retry_base_delay"`
	RetryMaxDelay      time.Duration `mapstructure:"retry_max_delay" yaml:"retry_max_delay"`
	RetryMultiplier    float64       `mapstructure:"retry_multiplier" yaml:"retry_multiplier"`
	RetryJitterEnabled bool          `mapstructure:"retry_jitter_enabled" yaml:"retry_jitter_enabled"`
	RetryJitterFactor  float64       `mapstructure:"retry_jitter_factor" yaml:"retry_jitter_factor"`

	// Dead Letter Queue
	DLQProcessingInterval time.Duration `mapstructure:"dlq_processing_interval" yaml:"dlq_processing_interval"`
	DLQRetryDelay         time.Duration `mapstructure:"dlq_retry_delay" yaml:"dlq_retry_delay"`

	// Health Check Timeouts
	HealthCheckInterval time.Duration `mapstructure:"health_check_interval" yaml:"health_check_interval"`
	HealthCheckTimeout  time.Duration `mapstructure:"health_check_timeout" yaml:"health_check_timeout"`

	// Graceful Shutdown
	GracefulShutdown time.Duration `mapstructure:"graceful_shutdown" yaml:"graceful_shutdown"`
}

// DefaultTimeoutConfig القيم الافتراضية للمهل الزمنية (Forever Plan - مبسط ومحسن)
func DefaultTimeoutConfig() TimeoutConfig {
	return TimeoutConfig{
		// HTTP Server Timeouts - محسنة للإنتاج
		HTTPRead:    30 * time.Second,
		HTTPWrite:   30 * time.Second,
		HTTPIdle:    120 * time.Second,
		HTTPHandler: 45 * time.Second,

		// Database Operation Timeouts - محسنة لـ Supabase
		DBConnection: 10 * time.Second,
		DBQuery:      30 * time.Second,
		DBTx:         60 * time.Second,
		DBPing:       5 * time.Second,

		// External Service Timeouts
		SupabaseAPI: 30 * time.Second,
		JWT:         5 * time.Second,

		// Circuit Breaker Timeouts
		CircuitBreakerTimeout:    30 * time.Second,
		CircuitBreakerRetryAfter: 60 * time.Second,

		// Retry Configuration - توازن بين الموثوقية والأداء
		RetryMaxAttempts:   3,
		RetryBaseDelay:     500 * time.Millisecond,
		RetryMaxDelay:      10 * time.Second,
		RetryMultiplier:    2.0,
		RetryJitterEnabled: true,
		RetryJitterFactor:  0.1,

		// Dead Letter Queue
		DLQProcessingInterval: 5 * time.Minute,
		DLQRetryDelay:         1 * time.Minute,

		// Health Check Timeouts
		HealthCheckInterval: 30 * time.Second,
		HealthCheckTimeout:  10 * time.Second,

		// Graceful Shutdown
		GracefulShutdown: 30 * time.Second,
	}
}

// CriticalTimeoutConfig إعدادات للعمليات الحرجة
func CriticalTimeoutConfig() TimeoutConfig {
	base := DefaultTimeoutConfig()
	return TimeoutConfig{
		HTTPRead:    base.HTTPRead,
		HTTPWrite:   base.HTTPWrite,
		HTTPIdle:    base.HTTPIdle,
		HTTPHandler: 60 * time.Second, // وقت أطول للعمليات الحرجة

		DBConnection: 15 * time.Second, // وقت أطول للاتصال
		DBQuery:      45 * time.Second, // وقت أطول للاستعلامات المعقدة
		DBTx:         90 * time.Second, // وقت أطول للمعاملات
		DBPing:       base.DBPing,

		SupabaseAPI: 45 * time.Second, // وقت أطول لـ Supabase
		JWT:         base.JWT,

		CircuitBreakerTimeout:    45 * time.Second,
		CircuitBreakerRetryAfter: 90 * time.Second,

		// إعدادات إعادة المحاولة الأكثر عدوانية
		RetryMaxAttempts:   5,
		RetryBaseDelay:     200 * time.Millisecond,
		RetryMaxDelay:      15 * time.Second,
		RetryMultiplier:    1.8,
		RetryJitterEnabled: true,
		RetryJitterFactor:  0.15,

		DLQProcessingInterval: base.DLQProcessingInterval,
		DLQRetryDelay:         base.DLQRetryDelay,

		HealthCheckInterval: base.HealthCheckInterval,
		HealthCheckTimeout:  15 * time.Second,

		GracefulShutdown: 45 * time.Second,
	}
}

// QuickTimeoutConfig إعدادات للعمليات السريعة
func QuickTimeoutConfig() TimeoutConfig {
	return TimeoutConfig{
		HTTPRead:    15 * time.Second,
		HTTPWrite:   15 * time.Second,
		HTTPIdle:    60 * time.Second,
		HTTPHandler: 20 * time.Second,

		DBConnection: 5 * time.Second,
		DBQuery:      10 * time.Second,
		DBTx:         20 * time.Second,
		DBPing:       3 * time.Second,

		SupabaseAPI: 15 * time.Second,
		JWT:         3 * time.Second,

		CircuitBreakerTimeout:    15 * time.Second,
		CircuitBreakerRetryAfter: 30 * time.Second,

		// إعادة محاولة أقل للعمليات السريعة
		RetryMaxAttempts:   2,
		RetryBaseDelay:     200 * time.Millisecond,
		RetryMaxDelay:      2 * time.Second,
		RetryMultiplier:    2.0,
		RetryJitterEnabled: false,
		RetryJitterFactor:  0.0,

		DLQProcessingInterval: 2 * time.Minute,
		DLQRetryDelay:         30 * time.Second,

		HealthCheckInterval: 15 * time.Second,
		HealthCheckTimeout:  5 * time.Second,

		GracefulShutdown: 15 * time.Second,
	}
}

// GetTimeoutForOperation الحصول على المهلة الزمنية المناسبة للعملية
func (tc *TimeoutConfig) GetTimeoutForOperation(operation string) time.Duration {
	switch operation {
	case "db_connection":
		return tc.DBConnection
	case "db_query":
		return tc.DBQuery
	case "db_tx":
		return tc.DBTx
	case "db_ping":
		return tc.DBPing
	case "supabase_api":
		return tc.SupabaseAPI
	case "jwt":
		return tc.JWT
	case "http_handler":
		return tc.HTTPHandler
	case "health_check":
		return tc.HealthCheckTimeout
	case "circuit_breaker":
		return tc.CircuitBreakerTimeout
	default:
		return tc.HTTPHandler // افتراضي
	}
}

// RetryConfig إعدادات إعادة المحاولة
type RetryConfig struct {
	MaxAttempts   int
	BaseDelay     time.Duration
	MaxDelay      time.Duration
	Multiplier    float64
	JitterEnabled bool
	JitterFactor  float64
}

// GetRetryConfig الحصول على إعدادات إعادة المحاولة
func (tc *TimeoutConfig) GetRetryConfig() RetryConfig {
	return RetryConfig{
		MaxAttempts:   tc.RetryMaxAttempts,
		BaseDelay:     tc.RetryBaseDelay,
		MaxDelay:      tc.RetryMaxDelay,
		Multiplier:    tc.RetryMultiplier,
		JitterEnabled: tc.RetryJitterEnabled,
		JitterFactor:  tc.RetryJitterFactor,
	}
}

// IsRetryableOperation تحديد ما إذا كانت العملية قابلة لإعادة المحاولة
func IsRetryableOperation(operation string) bool {
	retryableOps := map[string]bool{
		"db_connection":   true,
		"db_query":        true,
		"db_ping":         true,
		"supabase_api":    true,
		"health_check":    true,
		"circuit_breaker": false, // Circuit breaker يدير إعادة المحاولة بنفسه
	}

	if retryable, exists := retryableOps[operation]; exists {
		return retryable
	}

	return true // افتراضياً معظم العمليات قابلة لإعادة المحاولة
}
