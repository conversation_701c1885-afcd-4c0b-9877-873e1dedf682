package interfaces

// WebSocketMessage represents a real-time message (shared type)
type WebSocketMessage struct {
	Type      string                 `json:"type"`
	Event     string                 `json:"event"`
	Data      interface{}            `json:"data"`
	UserID    string                 `json:"user_id,omitempty"`
	Timestamp string                 `json:"timestamp"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
}

// WebSocketBroadcaster defines the interface for WebSocket broadcasting
// This interface breaks the import cycle between handlers and services
type WebSocketBroadcaster interface {
	BroadcastToUser(userID string, message interface{}) error
	BroadcastToAll(message interface{}) error
	GetConnectedUsers() []string
	IsUserConnected(userID string) bool
}

// MessageBroadcaster defines a more generic message broadcasting interface
type MessageBroadcaster interface {
	Broadcast(channel string, message interface{}) error
	BroadcastToUsers(userIDs []string, message interface{}) error
}
