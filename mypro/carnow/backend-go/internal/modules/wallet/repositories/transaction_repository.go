package repositories

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5"
	"go.uber.org/zap"

	"carnow-backend/internal/modules/wallet/models"
)

// TransactionRepository handles transaction data access
type TransactionRepository struct {
	db     *pgx.Conn
	logger *zap.Logger
}

// NewTransactionRepository creates a new transaction repository
func NewTransactionRepository(db *pgx.Conn, logger *zap.Logger) *TransactionRepository {
	return &TransactionRepository{
		db:     db,
		logger: logger,
	}
}

// Create creates a new transaction
func (r *TransactionRepository) Create(ctx context.Context, transaction *models.Transaction) (*models.Transaction, error) {
	transaction.ID = uuid.New()
	transaction.CreatedAt = time.Now()
	transaction.UpdatedAt = time.Now()

	query := `
		INSERT INTO wallet_transactions (id, wallet_id, type, amount, currency, status, description, 
		                                reference_id, reference_type, fee, balance_before, balance_after, 
		                                created_at, updated_at, is_deleted)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15)
		RETURNING id, wallet_id, type, amount, currency, status, description, reference_id, reference_type, 
		          fee, balance_before, balance_after, created_at, updated_at, is_deleted
	`

	err := r.db.QueryRow(ctx, query,
		transaction.ID,
		transaction.WalletID,
		transaction.Type,
		transaction.Amount,
		transaction.Currency,
		transaction.Status,
		transaction.Description,
		transaction.ReferenceID,
		transaction.ReferenceType,
		transaction.Fee,
		transaction.BalanceBefore,
		transaction.BalanceAfter,
		transaction.CreatedAt,
		transaction.UpdatedAt,
		transaction.IsDeleted,
	).Scan(
		&transaction.ID,
		&transaction.WalletID,
		&transaction.Type,
		&transaction.Amount,
		&transaction.Currency,
		&transaction.Status,
		&transaction.Description,
		&transaction.ReferenceID,
		&transaction.ReferenceType,
		&transaction.Fee,
		&transaction.BalanceBefore,
		&transaction.BalanceAfter,
		&transaction.CreatedAt,
		&transaction.UpdatedAt,
		&transaction.IsDeleted,
	)

	if err != nil {
		return nil, fmt.Errorf("failed to create transaction: %w", err)
	}

	return transaction, nil
}

// GetByWalletID retrieves transactions by wallet ID with filtering and pagination
func (r *TransactionRepository) GetByWalletID(
	ctx context.Context,
	walletID uuid.UUID,
	page, limit int,
	transactionType, status string,
	startTime, endTime *time.Time,
) ([]models.Transaction, int, error) {
	// Build the base query
	baseQuery := `
		FROM wallet_transactions
		WHERE wallet_id = $1 AND is_deleted = false
	`

	// Build the count query
	countQuery := `SELECT COUNT(*) ` + baseQuery

	// Build the main query
	mainQuery := `
		SELECT id, wallet_id, type, amount, currency, status, description, reference_id, reference_type,
		       fee, balance_before, balance_after, created_at, updated_at, is_deleted
		` + baseQuery

	// Build WHERE conditions
	conditions := []string{}
	args := []interface{}{walletID}
	argIndex := 2

	if transactionType != "" {
		conditions = append(conditions, fmt.Sprintf("type = $%d", argIndex))
		args = append(args, transactionType)
		argIndex++
	}

	if status != "" {
		conditions = append(conditions, fmt.Sprintf("status = $%d", argIndex))
		args = append(args, status)
		argIndex++
	}

	if startTime != nil {
		conditions = append(conditions, fmt.Sprintf("created_at >= $%d", argIndex))
		args = append(args, *startTime)
		argIndex++
	}

	if endTime != nil {
		conditions = append(conditions, fmt.Sprintf("created_at <= $%d", argIndex))
		args = append(args, *endTime)
		argIndex++
	}

	// Add conditions to queries
	if len(conditions) > 0 {
		conditionClause := " AND " + conditions[0]
		for i := 1; i < len(conditions); i++ {
			conditionClause += " AND " + conditions[i]
		}
		countQuery += conditionClause
		mainQuery += conditionClause
	}

	// Add ordering and pagination to main query
	mainQuery += " ORDER BY created_at DESC"
	mainQuery += fmt.Sprintf(" LIMIT $%d OFFSET $%d", argIndex, argIndex+1)
	args = append(args, limit, (page-1)*limit)

	// Get total count
	var total int
	err := r.db.QueryRow(ctx, countQuery, args[:len(args)-2]...).Scan(&total)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get transaction count: %w", err)
	}

	// Get transactions
	rows, err := r.db.Query(ctx, mainQuery, args...)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get transactions: %w", err)
	}
	defer rows.Close()

	var transactions []models.Transaction
	for rows.Next() {
		var transaction models.Transaction
		err := rows.Scan(
			&transaction.ID,
			&transaction.WalletID,
			&transaction.Type,
			&transaction.Amount,
			&transaction.Currency,
			&transaction.Status,
			&transaction.Description,
			&transaction.ReferenceID,
			&transaction.ReferenceType,
			&transaction.Fee,
			&transaction.BalanceBefore,
			&transaction.BalanceAfter,
			&transaction.CreatedAt,
			&transaction.UpdatedAt,
			&transaction.IsDeleted,
		)
		if err != nil {
			return nil, 0, fmt.Errorf("failed to scan transaction: %w", err)
		}
		transactions = append(transactions, transaction)
	}

	if err = rows.Err(); err != nil {
		return nil, 0, fmt.Errorf("error iterating transactions: %w", err)
	}

	return transactions, total, nil
}

// Update updates a transaction
func (r *TransactionRepository) Update(ctx context.Context, transaction *models.Transaction) error {
	transaction.UpdatedAt = time.Now()

	query := `
		UPDATE wallet_transactions
		SET type = $2, amount = $3, currency = $4, status = $5, description = $6,
		    reference_id = $7, reference_type = $8, fee = $9, balance_before = $10,
		    balance_after = $11, updated_at = $12, is_deleted = $13
		WHERE id = $1
	`

	result, err := r.db.Exec(ctx, query,
		transaction.ID,
		transaction.Type,
		transaction.Amount,
		transaction.Currency,
		transaction.Status,
		transaction.Description,
		transaction.ReferenceID,
		transaction.ReferenceType,
		transaction.Fee,
		transaction.BalanceBefore,
		transaction.BalanceAfter,
		transaction.UpdatedAt,
		transaction.IsDeleted,
	)

	if err != nil {
		return fmt.Errorf("failed to update transaction: %w", err)
	}

	if result.RowsAffected() == 0 {
		return fmt.Errorf("transaction not found")
	}

	return nil
}

// GetStatistics retrieves transaction statistics for a wallet
func (r *TransactionRepository) GetStatistics(ctx context.Context, walletID uuid.UUID, startDate, endDate time.Time) (*models.WalletStatistics, error) {
	query := `
		SELECT 
			COUNT(*) as total_transactions,
			COUNT(CASE WHEN type = 'deposit' THEN 1 END) as total_deposits,
			COUNT(CASE WHEN type = 'withdrawal' THEN 1 END) as total_withdrawals,
			COUNT(CASE WHEN type = 'transfer' THEN 1 END) as total_transfers,
			COUNT(CASE WHEN type = 'payment' THEN 1 END) as total_payments,
			COALESCE(SUM(CASE WHEN type = 'deposit' THEN amount ELSE 0 END), 0) as total_amount_deposited,
			COALESCE(SUM(CASE WHEN type = 'withdrawal' THEN amount ELSE 0 END), 0) as total_amount_withdrawn,
			COALESCE(SUM(CASE WHEN type = 'transfer' THEN amount ELSE 0 END), 0) as total_amount_transferred,
			COALESCE(SUM(CASE WHEN type = 'payment' THEN amount ELSE 0 END), 0) as total_amount_paid,
			COALESCE(AVG(amount), 0) as average_transaction_amount,
			COALESCE(MAX(amount), 0) as largest_transaction,
			COALESCE(MIN(amount), 0) as smallest_transaction,
			COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_transactions,
			COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_transactions
		FROM wallet_transactions
		WHERE wallet_id = $1 
		  AND created_at >= $2 
		  AND created_at <= $3 
		  AND is_deleted = false
	`

	var stats models.WalletStatistics
	err := r.db.QueryRow(ctx, query, walletID, startDate, endDate).Scan(
		&stats.TotalTransactions,
		&stats.TotalDeposits,
		&stats.TotalWithdrawals,
		&stats.TotalTransfers,
		&stats.TotalPayments,
		&stats.TotalAmountDeposited,
		&stats.TotalAmountWithdrawn,
		&stats.TotalAmountTransferred,
		&stats.TotalAmountPaid,
		&stats.AverageTransactionAmount,
		&stats.LargestTransaction,
		&stats.SmallestTransaction,
		&stats.PendingTransactions,
		&stats.FailedTransactions,
	)

	if err != nil {
		return nil, fmt.Errorf("failed to get transaction statistics: %w", err)
	}

	// Calculate success rate
	if stats.TotalTransactions > 0 {
		successfulTransactions := stats.TotalTransactions - stats.FailedTransactions
		stats.SuccessRate = float64(successfulTransactions) / float64(stats.TotalTransactions) * 100
	}

	return &stats, nil
}

// GetByID retrieves a transaction by ID
func (r *TransactionRepository) GetByID(ctx context.Context, id uuid.UUID) (*models.Transaction, error) {
	query := `
		SELECT id, wallet_id, type, amount, currency, status, description, reference_id, reference_type,
		       fee, balance_before, balance_after, created_at, updated_at, is_deleted
		FROM wallet_transactions
		WHERE id = $1 AND is_deleted = false
	`

	var transaction models.Transaction
	err := r.db.QueryRow(ctx, query, id).Scan(
		&transaction.ID,
		&transaction.WalletID,
		&transaction.Type,
		&transaction.Amount,
		&transaction.Currency,
		&transaction.Status,
		&transaction.Description,
		&transaction.ReferenceID,
		&transaction.ReferenceType,
		&transaction.Fee,
		&transaction.BalanceBefore,
		&transaction.BalanceAfter,
		&transaction.CreatedAt,
		&transaction.UpdatedAt,
		&transaction.IsDeleted,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, fmt.Errorf("transaction not found")
		}
		return nil, fmt.Errorf("failed to get transaction: %w", err)
	}

	return &transaction, nil
}

// GetByReference retrieves transactions by reference ID and type
func (r *TransactionRepository) GetByReference(ctx context.Context, referenceID, referenceType string) ([]models.Transaction, error) {
	query := `
		SELECT id, wallet_id, type, amount, currency, status, description, reference_id, reference_type,
		       fee, balance_before, balance_after, created_at, updated_at, is_deleted
		FROM wallet_transactions
		WHERE reference_id = $1 AND reference_type = $2 AND is_deleted = false
		ORDER BY created_at DESC
	`

	rows, err := r.db.Query(ctx, query, referenceID, referenceType)
	if err != nil {
		return nil, fmt.Errorf("failed to get transactions by reference: %w", err)
	}
	defer rows.Close()

	var transactions []models.Transaction
	for rows.Next() {
		var transaction models.Transaction
		err := rows.Scan(
			&transaction.ID,
			&transaction.WalletID,
			&transaction.Type,
			&transaction.Amount,
			&transaction.Currency,
			&transaction.Status,
			&transaction.Description,
			&transaction.ReferenceID,
			&transaction.ReferenceType,
			&transaction.Fee,
			&transaction.BalanceBefore,
			&transaction.BalanceAfter,
			&transaction.CreatedAt,
			&transaction.UpdatedAt,
			&transaction.IsDeleted,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan transaction: %w", err)
		}
		transactions = append(transactions, transaction)
	}

	if err = rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating transactions: %w", err)
	}

	return transactions, nil
}
