package models

import (
	"time"

	"github.com/google/uuid"
)

// Wallet represents a user's wallet
type Wallet struct {
	ID         uuid.UUID `json:"id" db:"id"`
	UserID     string    `json:"user_id" db:"user_id"`
	Balance    float64   `json:"balance" db:"balance"`
	Currency   string    `json:"currency" db:"currency"`
	Status     string    `json:"status" db:"status"`
	IsVerified bool      `json:"is_verified" db:"is_verified"`
	CreatedAt  time.Time `json:"created_at" db:"created_at"`
	UpdatedAt  time.Time `json:"updated_at" db:"updated_at"`
	IsDeleted  bool      `json:"is_deleted" db:"is_deleted"`
}

// Transaction represents a wallet transaction
type Transaction struct {
	ID            uuid.UUID `json:"id" db:"id"`
	WalletID      uuid.UUID `json:"wallet_id" db:"wallet_id"`
	Type          string    `json:"type" db:"type"` // deposit, withdrawal, transfer, payment
	Amount        float64   `json:"amount" db:"amount"`
	Currency      string    `json:"currency" db:"currency"`
	Status        string    `json:"status" db:"status"` // pending, completed, failed
	Description   string    `json:"description" db:"description"`
	ReferenceID   *string   `json:"reference_id" db:"reference_id"`
	ReferenceType *string   `json:"reference_type" db:"reference_type"`
	Fee           float64   `json:"fee" db:"fee"`
	BalanceBefore float64   `json:"balance_before" db:"balance_before"`
	BalanceAfter  float64   `json:"balance_after" db:"balance_after"`
	CreatedAt     time.Time `json:"created_at" db:"created_at"`
	UpdatedAt     time.Time `json:"updated_at" db:"updated_at"`
	IsDeleted     bool      `json:"is_deleted" db:"is_deleted"`
}

// WalletLimits represents wallet limits and restrictions
type WalletLimits struct {
	ID                     uuid.UUID `json:"id" db:"id"`
	WalletID               uuid.UUID `json:"wallet_id" db:"wallet_id"`
	DailyDepositLimit      float64   `json:"daily_deposit_limit" db:"daily_deposit_limit"`
	DailyWithdrawalLimit   float64   `json:"daily_withdrawal_limit" db:"daily_withdrawal_limit"`
	MonthlyDepositLimit    float64   `json:"monthly_deposit_limit" db:"monthly_deposit_limit"`
	MonthlyWithdrawalLimit float64   `json:"monthly_withdrawal_limit" db:"monthly_withdrawal_limit"`
	MaxBalance             float64   `json:"max_balance" db:"max_balance"`
	MinTransactionAmount   float64   `json:"min_transaction_amount" db:"min_transaction_amount"`
	MaxTransactionAmount   float64   `json:"max_transaction_amount" db:"max_transaction_amount"`
	CreatedAt              time.Time `json:"created_at" db:"created_at"`
	UpdatedAt              time.Time `json:"updated_at" db:"updated_at"`
}

// WalletSettings represents wallet settings and preferences
type WalletSettings struct {
	ID                    uuid.UUID `json:"id" db:"id"`
	WalletID              uuid.UUID `json:"wallet_id" db:"wallet_id"`
	EmailNotifications    bool      `json:"email_notifications" db:"email_notifications"`
	PushNotifications     bool      `json:"push_notifications" db:"push_notifications"`
	SMSNotifications      bool      `json:"sms_notifications" db:"sms_notifications"`
	TransactionAlerts     bool      `json:"transaction_alerts" db:"transaction_alerts"`
	BalanceAlerts         bool      `json:"balance_alerts" db:"balance_alerts"`
	SecurityAlerts        bool      `json:"security_alerts" db:"security_alerts"`
	AutoRecharge          bool      `json:"auto_recharge" db:"auto_recharge"`
	AutoRechargeAmount    float64   `json:"auto_recharge_amount" db:"auto_recharge_amount"`
	AutoRechargeThreshold float64   `json:"auto_recharge_threshold" db:"auto_recharge_threshold"`
	CreatedAt             time.Time `json:"created_at" db:"created_at"`
	UpdatedAt             time.Time `json:"updated_at" db:"updated_at"`
}

// WalletStatistics represents wallet statistics
type WalletStatistics struct {
	TotalTransactions        int     `json:"total_transactions"`
	TotalDeposits            int     `json:"total_deposits"`
	TotalWithdrawals         int     `json:"total_withdrawals"`
	TotalTransfers           int     `json:"total_transfers"`
	TotalPayments            int     `json:"total_payments"`
	TotalAmountDeposited     float64 `json:"total_amount_deposited"`
	TotalAmountWithdrawn     float64 `json:"total_amount_withdrawn"`
	TotalAmountTransferred   float64 `json:"total_amount_transferred"`
	TotalAmountPaid          float64 `json:"total_amount_paid"`
	AverageTransactionAmount float64 `json:"average_transaction_amount"`
	LargestTransaction       float64 `json:"largest_transaction"`
	SmallestTransaction      float64 `json:"smallest_transaction"`
	PendingTransactions      int     `json:"pending_transactions"`
	FailedTransactions       int     `json:"failed_transactions"`
	SuccessRate              float64 `json:"success_rate"`
	Period                   string  `json:"period"`
}

// API Request/Response Models

// WalletResponse represents the API response for wallet data
type WalletResponse struct {
	Wallet *Wallet `json:"wallet"`
}

// TransactionsResponse represents the API response for transactions
type TransactionsResponse struct {
	Transactions []Transaction `json:"transactions"`
	Pagination   Pagination    `json:"pagination"`
}

// TransactionResponse represents the API response for a single transaction
type TransactionResponse struct {
	Transaction *Transaction `json:"transaction"`
}

// CreateTransactionRequest represents the request to create a transaction
type CreateTransactionRequest struct {
	UserID        string  `json:"user_id" validate:"required"`
	Type          string  `json:"type" validate:"required,oneof=deposit withdrawal transfer payment"`
	Amount        float64 `json:"amount" validate:"required,gt=0"`
	Currency      string  `json:"currency" validate:"required,len=3"`
	Description   string  `json:"description" validate:"required,min=1,max=500"`
	ReferenceID   *string `json:"reference_id"`
	ReferenceType *string `json:"reference_type"`
}

// UpdateWalletSettingsRequest represents the request to update wallet settings
type UpdateWalletSettingsRequest struct {
	EmailNotifications    *bool    `json:"email_notifications"`
	PushNotifications     *bool    `json:"push_notifications"`
	SMSNotifications      *bool    `json:"sms_notifications"`
	TransactionAlerts     *bool    `json:"transaction_alerts"`
	BalanceAlerts         *bool    `json:"balance_alerts"`
	SecurityAlerts        *bool    `json:"security_alerts"`
	AutoRecharge          *bool    `json:"auto_recharge"`
	AutoRechargeAmount    *float64 `json:"auto_recharge_amount" validate:"omitempty,gt=0"`
	AutoRechargeThreshold *float64 `json:"auto_recharge_threshold" validate:"omitempty,gt=0"`
}

// WalletLimitsResponse represents the API response for wallet limits
type WalletLimitsResponse struct {
	Limits *WalletLimits `json:"limits"`
}

// WalletSettingsResponse represents the API response for wallet settings
type WalletSettingsResponse struct {
	Settings *WalletSettings `json:"settings"`
}

// WalletStatisticsResponse represents the API response for wallet statistics
type WalletStatisticsResponse struct {
	Statistics *WalletStatistics `json:"statistics"`
}

// Pagination represents pagination information
type Pagination struct {
	Page       int  `json:"page"`
	Limit      int  `json:"limit"`
	Total      int  `json:"total"`
	TotalPages int  `json:"total_pages"`
	HasNext    bool `json:"has_next"`
	HasPrev    bool `json:"has_prev"`
}

// Transaction types
const (
	TransactionTypeDeposit    = "deposit"
	TransactionTypeWithdrawal = "withdrawal"
	TransactionTypeTransfer   = "transfer"
	TransactionTypePayment    = "payment"
)

// Transaction statuses
const (
	TransactionStatusPending   = "pending"
	TransactionStatusCompleted = "completed"
	TransactionStatusFailed    = "failed"
)

// Wallet statuses
const (
	WalletStatusActive    = "active"
	WalletStatusSuspended = "suspended"
	WalletStatusClosed    = "closed"
)

// Currency codes
const (
	CurrencySAR = "SAR"
	CurrencyUSD = "USD"
	CurrencyEUR = "EUR"
)
