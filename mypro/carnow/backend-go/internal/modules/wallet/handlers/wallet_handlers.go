package handlers

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"carnow-backend/internal/modules/wallet/models"
	"carnow-backend/internal/modules/wallet/services"
	"carnow-backend/internal/shared/middleware"
	"carnow-backend/internal/shared/validation"
)

// WalletHandler handles wallet-related HTTP requests
type WalletHandler struct {
	walletService *services.WalletService
	logger        *zap.Logger
}

// NewWalletHandler creates a new wallet handler
func NewWalletHandler(walletService *services.WalletService, logger *zap.Logger) *WalletHandler {
	return &WalletHandler{
		walletService: walletService,
		logger:        logger,
	}
}

// GetUserWallet returns the current user's wallet information
// @Summary Get user wallet
// @Description Get current user's wallet balance and information
// @Tags Wallet
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} models.WalletResponse
// @Failure 401 {object} middleware.ErrorResponse
// @Failure 500 {object} middleware.ErrorResponse
// @Router /api/v1/wallet [get]
func (h *WalletHandler) GetUserWallet(c *gin.Context) {
	userID := middleware.GetUserIDFromContext(c)
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	wallet, err := h.walletService.GetUserWallet(c.Request.Context(), userID)
	if err != nil {
		h.logger.Error("Failed to get user wallet", zap.Error(err), zap.String("user_id", userID))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get wallet information"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"wallet":  wallet,
		"success": true,
	})
}

// GetWalletTransactions returns user's wallet transactions with pagination
// @Summary Get wallet transactions
// @Description Get user's wallet transactions with pagination and filtering
// @Tags Wallet
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Param type query string false "Transaction type (deposit, withdrawal, transfer, payment)"
// @Param status query string false "Transaction status (pending, completed, failed)"
// @Param start_date query string false "Start date (YYYY-MM-DD)"
// @Param end_date query string false "End date (YYYY-MM-DD)"
// @Success 200 {object} models.TransactionsResponse
// @Failure 401 {object} middleware.ErrorResponse
// @Failure 500 {object} middleware.ErrorResponse
// @Router /api/v1/wallet/transactions [get]
func (h *WalletHandler) GetWalletTransactions(c *gin.Context) {
	userID := middleware.GetUserIDFromContext(c)
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	// Parse query parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	transactionType := c.Query("type")
	status := c.Query("status")
	startDate := c.Query("start_date")
	endDate := c.Query("end_date")

	// Validate pagination
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 20
	}

	// Parse dates if provided
	var startTime, endTime *time.Time
	if startDate != "" {
		if t, err := time.Parse("2006-01-02", startDate); err == nil {
			startTime = &t
		}
	}
	if endDate != "" {
		if t, err := time.Parse("2006-01-02", endDate); err == nil {
			// Set to end of day
			t = t.Add(23*time.Hour + 59*time.Minute + 59*time.Second)
			endTime = &t
		}
	}

	transactions, total, err := h.walletService.GetUserTransactions(
		c.Request.Context(),
		userID,
		page,
		limit,
		transactionType,
		status,
		startTime,
		endTime,
	)
	if err != nil {
		h.logger.Error("Failed to get wallet transactions", zap.Error(err), zap.String("user_id", userID))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get transactions"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"transactions": transactions,
		"pagination": gin.H{
			"page":        page,
			"limit":       limit,
			"total":       total,
			"total_pages": (total + limit - 1) / limit,
			"has_next":    page*limit < total,
			"has_prev":    page > 1,
		},
		"success": true,
	})
}

// CreateTransaction creates a new wallet transaction
// @Summary Create wallet transaction
// @Description Create a new wallet transaction (deposit, withdrawal, transfer)
// @Tags Wallet
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param transaction body models.CreateTransactionRequest true "Transaction details"
// @Success 201 {object} models.TransactionResponse
// @Failure 400 {object} middleware.ErrorResponse
// @Failure 401 {object} middleware.ErrorResponse
// @Failure 500 {object} middleware.ErrorResponse
// @Router /api/v1/wallet/transactions [post]
func (h *WalletHandler) CreateTransaction(c *gin.Context) {
	userID := middleware.GetUserIDFromContext(c)
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	var req models.CreateTransactionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request data", "details": err.Error()})
		return
	}

	// Validate request
	if err := validation.NewCustomValidator().Validate(req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Validation failed", "details": err.Error()})
		return
	}

	// Set user ID from context
	req.UserID = userID

	transaction, err := h.walletService.CreateTransaction(c.Request.Context(), req)
	if err != nil {
		h.logger.Error("Failed to create transaction", zap.Error(err), zap.String("user_id", userID))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create transaction"})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"transaction": transaction,
		"success":     true,
	})
}

// GetWalletStatistics returns wallet statistics for the user
// @Summary Get wallet statistics
// @Description Get wallet statistics including balance history, transaction counts, etc.
// @Tags Wallet
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param period query string false "Statistics period (week, month, year)" default(month)
// @Success 200 {object} models.WalletStatisticsResponse
// @Failure 401 {object} middleware.ErrorResponse
// @Failure 500 {object} middleware.ErrorResponse
// @Router /api/v1/wallet/statistics [get]
func (h *WalletHandler) GetWalletStatistics(c *gin.Context) {
	userID := middleware.GetUserIDFromContext(c)
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	period := c.DefaultQuery("period", "month")

	statistics, err := h.walletService.GetWalletStatistics(c.Request.Context(), userID, period)
	if err != nil {
		h.logger.Error("Failed to get wallet statistics", zap.Error(err), zap.String("user_id", userID))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get statistics"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"statistics": statistics,
		"success":    true,
	})
}

// GetWalletLimits returns wallet limits and restrictions
// @Summary Get wallet limits
// @Description Get wallet limits, restrictions, and verification status
// @Tags Wallet
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} models.WalletLimitsResponse
// @Failure 401 {object} middleware.ErrorResponse
// @Failure 500 {object} middleware.ErrorResponse
// @Router /api/v1/wallet/limits [get]
func (h *WalletHandler) GetWalletLimits(c *gin.Context) {
	userID := middleware.GetUserIDFromContext(c)
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	limits, err := h.walletService.GetWalletLimits(c.Request.Context(), userID)
	if err != nil {
		h.logger.Error("Failed to get wallet limits", zap.Error(err), zap.String("user_id", userID))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get wallet limits"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"limits":  limits,
		"success": true,
	})
}

// UpdateWalletSettings updates wallet settings and preferences
// @Summary Update wallet settings
// @Description Update wallet settings, notifications, and preferences
// @Tags Wallet
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param settings body models.UpdateWalletSettingsRequest true "Wallet settings"
// @Success 200 {object} models.WalletSettingsResponse
// @Failure 400 {object} middleware.ErrorResponse
// @Failure 401 {object} middleware.ErrorResponse
// @Failure 500 {object} middleware.ErrorResponse
// @Router /api/v1/wallet/settings [put]
func (h *WalletHandler) UpdateWalletSettings(c *gin.Context) {
	userID := middleware.GetUserIDFromContext(c)
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	var req models.UpdateWalletSettingsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request data", "details": err.Error()})
		return
	}

	// Validate request
	if err := validation.NewCustomValidator().Validate(req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Validation failed", "details": err.Error()})
		return
	}

	settings, err := h.walletService.UpdateWalletSettings(c.Request.Context(), userID, req)
	if err != nil {
		h.logger.Error("Failed to update wallet settings", zap.Error(err), zap.String("user_id", userID))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update settings"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"settings": settings,
		"success":  true,
	})
}
