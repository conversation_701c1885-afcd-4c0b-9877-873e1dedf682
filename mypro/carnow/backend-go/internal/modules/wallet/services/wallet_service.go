package services

import (
	"context"
	"fmt"
	"time"

	"go.uber.org/zap"

	"carnow-backend/internal/modules/wallet/models"
	"carnow-backend/internal/modules/wallet/repositories"
	"carnow-backend/internal/shared/errors"

	"github.com/google/uuid"
)

// WalletService handles wallet business logic
type WalletService struct {
	walletRepo      *repositories.WalletRepository
	transactionRepo *repositories.TransactionRepository
	logger          *zap.Logger
}

// NewWalletService creates a new wallet service
func NewWalletService(
	walletRepo *repositories.WalletRepository,
	transactionRepo *repositories.TransactionRepository,
	logger *zap.Logger,
) *WalletService {
	return &WalletService{
		walletRepo:      walletRepo,
		transactionRepo: transactionRepo,
		logger:          logger,
	}
}

// GetUserWallet retrieves the user's wallet information
func (s *WalletService) GetUserWallet(ctx context.Context, userID string) (*models.Wallet, error) {
	wallet, err := s.walletRepo.GetByUserID(ctx, userID)
	if err != nil {
		if errors.IsNotFound(err) {
			// Create wallet if it doesn't exist
			wallet, err = s.createDefaultWallet(ctx, userID)
			if err != nil {
				return nil, fmt.Errorf("failed to create default wallet: %w", err)
			}
		} else {
			return nil, fmt.Errorf("failed to get wallet: %w", err)
		}
	}

	return wallet, nil
}

// GetUserTransactions retrieves user's wallet transactions with filtering and pagination
func (s *WalletService) GetUserTransactions(
	ctx context.Context,
	userID string,
	page, limit int,
	transactionType, status string,
	startTime, endTime *time.Time,
) ([]models.Transaction, int, error) {
	// Get user's wallet first
	wallet, err := s.GetUserWallet(ctx, userID)
	if err != nil {
		return nil, 0, err
	}

	// Get transactions
	transactions, total, err := s.transactionRepo.GetByWalletID(
		ctx,
		wallet.ID,
		page,
		limit,
		transactionType,
		status,
		startTime,
		endTime,
	)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get transactions: %w", err)
	}

	return transactions, total, nil
}

// CreateTransaction creates a new wallet transaction
func (s *WalletService) CreateTransaction(ctx context.Context, req models.CreateTransactionRequest) (*models.Transaction, error) {
	// Get user's wallet
	wallet, err := s.GetUserWallet(ctx, req.UserID)
	if err != nil {
		return nil, err
	}

	// Validate transaction based on type
	if err := s.validateTransaction(ctx, wallet, req); err != nil {
		return nil, err
	}

	// Calculate fees
	fee := s.calculateTransactionFee(req.Type, req.Amount)

	// Get current balance
	balanceBefore := wallet.Balance
	var balanceAfter float64

	// Calculate new balance based on transaction type
	switch req.Type {
	case models.TransactionTypeDeposit:
		balanceAfter = balanceBefore + req.Amount - fee
	case models.TransactionTypeWithdrawal:
		if balanceBefore < req.Amount+fee {
			return nil, errors.NewValidationError("insufficient balance")
		}
		balanceAfter = balanceBefore - req.Amount - fee
	case models.TransactionTypeTransfer:
		if balanceBefore < req.Amount+fee {
			return nil, errors.NewValidationError("insufficient balance")
		}
		balanceAfter = balanceBefore - req.Amount - fee
	case models.TransactionTypePayment:
		if balanceBefore < req.Amount+fee {
			return nil, errors.NewValidationError("insufficient balance")
		}
		balanceAfter = balanceBefore - req.Amount - fee
	default:
		return nil, errors.NewValidationError("invalid transaction type")
	}

	// Create transaction
	transaction := &models.Transaction{
		WalletID:      wallet.ID,
		Type:          req.Type,
		Amount:        req.Amount,
		Currency:      req.Currency,
		Status:        models.TransactionStatusPending,
		Description:   req.Description,
		ReferenceID:   req.ReferenceID,
		ReferenceType: req.ReferenceType,
		Fee:           fee,
		BalanceBefore: balanceBefore,
		BalanceAfter:  balanceAfter,
	}

	// Save transaction
	transaction, err = s.transactionRepo.Create(ctx, transaction)
	if err != nil {
		return nil, fmt.Errorf("failed to create transaction: %w", err)
	}

	// Update wallet balance
	wallet.Balance = balanceAfter
	wallet.UpdatedAt = time.Now()

	if err := s.walletRepo.Update(ctx, wallet); err != nil {
		s.logger.Error("Failed to update wallet balance after transaction",
			zap.Error(err),
			zap.String("wallet_id", wallet.ID.String()),
		)
		// Don't fail the transaction creation, but log the error
	}

	// Process transaction based on type
	if err := s.processTransaction(ctx, transaction); err != nil {
		s.logger.Error("Failed to process transaction",
			zap.Error(err),
			zap.String("transaction_id", transaction.ID.String()),
		)
		// Update transaction status to failed
		transaction.Status = models.TransactionStatusFailed
		s.transactionRepo.Update(ctx, transaction)
		return nil, fmt.Errorf("failed to process transaction: %w", err)
	}

	// Update transaction status to completed
	transaction.Status = models.TransactionStatusCompleted
	transaction.UpdatedAt = time.Now()

	if err := s.transactionRepo.Update(ctx, transaction); err != nil {
		s.logger.Error("Failed to update transaction status",
			zap.Error(err),
			zap.String("transaction_id", transaction.ID.String()),
		)
	}

	return transaction, nil
}

// GetWalletStatistics retrieves wallet statistics for the specified period
func (s *WalletService) GetWalletStatistics(ctx context.Context, userID, period string) (*models.WalletStatistics, error) {
	// Get user's wallet
	wallet, err := s.GetUserWallet(ctx, userID)
	if err != nil {
		return nil, err
	}

	// Calculate date range based on period
	var startDate time.Time
	switch period {
	case "week":
		startDate = time.Now().AddDate(0, 0, -7)
	case "month":
		startDate = time.Now().AddDate(0, -1, 0)
	case "year":
		startDate = time.Now().AddDate(-1, 0, 0)
	default:
		startDate = time.Now().AddDate(0, -1, 0) // Default to month
	}

	// Get statistics from repository
	statistics, err := s.transactionRepo.GetStatistics(ctx, wallet.ID, startDate, time.Now())
	if err != nil {
		return nil, fmt.Errorf("failed to get statistics: %w", err)
	}

	statistics.Period = period
	return statistics, nil
}

// GetWalletLimits retrieves wallet limits and restrictions
func (s *WalletService) GetWalletLimits(ctx context.Context, userID string) (*models.WalletLimits, error) {
	// Get user's wallet
	wallet, err := s.GetUserWallet(ctx, userID)
	if err != nil {
		return nil, err
	}

	// Get limits from repository
	limits, err := s.walletRepo.GetLimits(ctx, wallet.ID)
	if err != nil {
		if errors.IsNotFound(err) {
			// Create default limits if they don't exist
			limits, err = s.createDefaultLimits(ctx, wallet.ID)
			if err != nil {
				return nil, fmt.Errorf("failed to create default limits: %w", err)
			}
		} else {
			return nil, fmt.Errorf("failed to get wallet limits: %w", err)
		}
	}

	return limits, nil
}

// UpdateWalletSettings updates wallet settings and preferences
func (s *WalletService) UpdateWalletSettings(ctx context.Context, userID string, req models.UpdateWalletSettingsRequest) (*models.WalletSettings, error) {
	// Get user's wallet
	wallet, err := s.GetUserWallet(ctx, userID)
	if err != nil {
		return nil, err
	}

	// Get current settings
	settings, err := s.walletRepo.GetSettings(ctx, wallet.ID)
	if err != nil {
		if errors.IsNotFound(err) {
			// Create default settings if they don't exist
			settings, err = s.createDefaultSettings(ctx, wallet.ID)
			if err != nil {
				return nil, fmt.Errorf("failed to create default settings: %w", err)
			}
		} else {
			return nil, fmt.Errorf("failed to get wallet settings: %w", err)
		}
	}

	// Update settings with provided values
	if req.EmailNotifications != nil {
		settings.EmailNotifications = *req.EmailNotifications
	}
	if req.PushNotifications != nil {
		settings.PushNotifications = *req.PushNotifications
	}
	if req.SMSNotifications != nil {
		settings.SMSNotifications = *req.SMSNotifications
	}
	if req.TransactionAlerts != nil {
		settings.TransactionAlerts = *req.TransactionAlerts
	}
	if req.BalanceAlerts != nil {
		settings.BalanceAlerts = *req.BalanceAlerts
	}
	if req.SecurityAlerts != nil {
		settings.SecurityAlerts = *req.SecurityAlerts
	}
	if req.AutoRecharge != nil {
		settings.AutoRecharge = *req.AutoRecharge
	}
	if req.AutoRechargeAmount != nil {
		settings.AutoRechargeAmount = *req.AutoRechargeAmount
	}
	if req.AutoRechargeThreshold != nil {
		settings.AutoRechargeThreshold = *req.AutoRechargeThreshold
	}

	settings.UpdatedAt = time.Now()

	// Save updated settings
	settings, err = s.walletRepo.UpdateSettings(ctx, settings)
	if err != nil {
		return nil, fmt.Errorf("failed to update wallet settings: %w", err)
	}

	return settings, nil
}

// Helper methods

func (s *WalletService) createDefaultWallet(ctx context.Context, userID string) (*models.Wallet, error) {
	wallet := &models.Wallet{
		UserID:     userID,
		Balance:    0.0,
		Currency:   models.CurrencySAR,
		Status:     models.WalletStatusActive,
		IsVerified: false,
	}

	return s.walletRepo.Create(ctx, wallet)
}

func (s *WalletService) createDefaultLimits(ctx context.Context, walletID uuid.UUID) (*models.WalletLimits, error) {
	limits := &models.WalletLimits{
		WalletID:               walletID,
		DailyDepositLimit:      50000.0,
		DailyWithdrawalLimit:   50000.0,
		MonthlyDepositLimit:    500000.0,
		MonthlyWithdrawalLimit: 500000.0,
		MaxBalance:             1000000.0,
		MinTransactionAmount:   1.0,
		MaxTransactionAmount:   100000.0,
	}

	return s.walletRepo.CreateLimits(ctx, limits)
}

func (s *WalletService) createDefaultSettings(ctx context.Context, walletID uuid.UUID) (*models.WalletSettings, error) {
	settings := &models.WalletSettings{
		WalletID:              walletID,
		EmailNotifications:    true,
		PushNotifications:     true,
		SMSNotifications:      false,
		TransactionAlerts:     true,
		BalanceAlerts:         true,
		SecurityAlerts:        true,
		AutoRecharge:          false,
		AutoRechargeAmount:    1000.0,
		AutoRechargeThreshold: 100.0,
	}

	return s.walletRepo.CreateSettings(ctx, settings)
}

func (s *WalletService) validateTransaction(ctx context.Context, wallet *models.Wallet, req models.CreateTransactionRequest) error {
	// Check wallet status
	if wallet.Status != models.WalletStatusActive {
		return errors.NewValidationError("wallet is not active")
	}

	// Check currency match
	if req.Currency != wallet.Currency {
		return errors.NewValidationError("currency mismatch")
	}

	// Get limits
	limits, err := s.GetWalletLimits(ctx, wallet.UserID)
	if err != nil {
		s.logger.Warn("Failed to get wallet limits for validation", zap.Error(err))
		// Continue without limits validation
		return nil
	}

	// Validate amount limits
	if req.Amount < limits.MinTransactionAmount {
		return errors.NewValidationError(fmt.Sprintf("amount below minimum: %f", limits.MinTransactionAmount))
	}
	if req.Amount > limits.MaxTransactionAmount {
		return errors.NewValidationError(fmt.Sprintf("amount above maximum: %f", limits.MaxTransactionAmount))
	}

	// Validate daily/monthly limits based on transaction type
	if err := s.validateLimits(ctx, wallet, req, limits); err != nil {
		return err
	}

	return nil
}

func (s *WalletService) validateLimits(ctx context.Context, wallet *models.Wallet, req models.CreateTransactionRequest, limits *models.WalletLimits) error {
	// This is a simplified validation - in production, you'd check actual daily/monthly totals
	// For now, we'll just validate the basic limits

	switch req.Type {
	case models.TransactionTypeWithdrawal, models.TransactionTypeTransfer, models.TransactionTypePayment:
		totalAmount := req.Amount + s.calculateTransactionFee(req.Type, req.Amount)
		if wallet.Balance < totalAmount {
			return errors.NewValidationError("insufficient balance")
		}
	}

	return nil
}

func (s *WalletService) calculateTransactionFee(transactionType string, amount float64) float64 {
	// Simplified fee calculation - in production, this would be more complex
	switch transactionType {
	case models.TransactionTypeDeposit:
		return 0.0 // No fee for deposits
	case models.TransactionTypeWithdrawal:
		return amount * 0.01 // 1% fee for withdrawals
	case models.TransactionTypeTransfer:
		return amount * 0.005 // 0.5% fee for transfers
	case models.TransactionTypePayment:
		return amount * 0.02 // 2% fee for payments
	default:
		return 0.0
	}
}

func (s *WalletService) processTransaction(ctx context.Context, transaction *models.Transaction) error {
	// This is where you'd integrate with external payment processors
	// For now, we'll just simulate processing

	switch transaction.Type {
	case models.TransactionTypeDeposit:
		// Simulate external deposit processing
		return s.processDeposit(ctx, transaction)
	case models.TransactionTypeWithdrawal:
		// Simulate external withdrawal processing
		return s.processWithdrawal(ctx, transaction)
	case models.TransactionTypeTransfer:
		// Process internal transfer
		return s.processTransfer(ctx, transaction)
	case models.TransactionTypePayment:
		// Process payment
		return s.processPayment(ctx, transaction)
	default:
		return errors.NewValidationError("unknown transaction type")
	}
}

func (s *WalletService) processDeposit(ctx context.Context, transaction *models.Transaction) error {
	// Simulate external deposit processing
	// In production, this would call a payment gateway
	time.Sleep(100 * time.Millisecond) // Simulate processing time
	return nil
}

func (s *WalletService) processWithdrawal(ctx context.Context, transaction *models.Transaction) error {
	// Simulate external withdrawal processing
	// In production, this would call a payment gateway
	time.Sleep(200 * time.Millisecond) // Simulate processing time
	return nil
}

func (s *WalletService) processTransfer(ctx context.Context, transaction *models.Transaction) error {
	// Process internal transfer
	// This would involve updating another wallet's balance
	time.Sleep(50 * time.Millisecond) // Simulate processing time
	return nil
}

func (s *WalletService) processPayment(ctx context.Context, transaction *models.Transaction) error {
	// Process payment to external system
	// In production, this would call an order/payment system
	time.Sleep(150 * time.Millisecond) // Simulate processing time
	return nil
}
