package models

import (
	"time"

	"github.com/google/uuid"
)

// Order represents an order in the system
type Order struct {
	ID              string    `json:"id" db:"id"`
	UserID          string    `json:"user_id" db:"user_id"`
	ProductID       string    `json:"product_id" db:"product_id"`
	Quantity        int       `json:"quantity" db:"quantity"`
	TotalAmount     float64   `json:"total_amount" db:"total_amount"`
	Status          string    `json:"status" db:"status"`                 // pending, confirmed, shipped, delivered, cancelled
	PaymentStatus   string    `json:"payment_status" db:"payment_status"` // pending, paid, failed, refunded
	ShippingAddress string    `json:"shipping_address" db:"shipping_address"`
	BillingAddress  string    `json:"billing_address" db:"billing_address"`
	Notes           string    `json:"notes" db:"notes"`
	IsDeleted       bool      `json:"is_deleted" db:"is_deleted"`
	CreatedAt       time.Time `json:"created_at" db:"created_at"`
	UpdatedAt       time.Time `json:"updated_at" db:"updated_at"`
	Version         int       `json:"version" db:"version"`
}

// OrderFilter represents filtering options for orders
type OrderFilter struct {
	Status        string `json:"status"`
	Search        string `json:"search"`
	StartDate     string `json:"start_date"`
	EndDate       string `json:"end_date"`
	PaymentStatus string `json:"payment_status"`
}

// OrdersResponse represents the response for order queries
type OrdersResponse struct {
	Orders     []Order         `json:"orders"`
	Pagination OrderPagination `json:"pagination"`
}

// OrderPagination represents pagination information for orders
type OrderPagination struct {
	Page       int  `json:"page"`
	Limit      int  `json:"limit"`
	Total      int  `json:"total"`
	TotalPages int  `json:"total_pages"`
	HasNext    bool `json:"has_next"`
	HasPrev    bool `json:"has_prev"`
}

// OrderStatistics represents order statistics
type OrderStatistics struct {
	TotalOrders       int     `json:"total_orders"`
	PendingOrders     int     `json:"pending_orders"`
	ConfirmedOrders   int     `json:"confirmed_orders"`
	ShippedOrders     int     `json:"shipped_orders"`
	DeliveredOrders   int     `json:"delivered_orders"`
	CancelledOrders   int     `json:"cancelled_orders"`
	TotalRevenue      float64 `json:"total_revenue"`
	AverageOrderValue float64 `json:"average_order_value"`
	OrdersThisMonth   int     `json:"orders_this_month"`
	RevenueThisMonth  float64 `json:"revenue_this_month"`
}

// OrderItem represents an item within an order
type OrderItem struct {
	ID        string  `json:"id" db:"id"`
	OrderID   string  `json:"order_id" db:"order_id"`
	ProductID string  `json:"product_id" db:"product_id"`
	Quantity  int     `json:"quantity" db:"quantity"`
	Price     float64 `json:"price" db:"price"`
	Total     float64 `json:"total" db:"total"`
}

// OrderTracking represents order tracking information
type OrderTracking struct {
	ID          string    `json:"id" db:"id"`
	OrderID     string    `json:"order_id" db:"order_id"`
	Status      string    `json:"status" db:"status"`
	Location    string    `json:"location" db:"location"`
	Description string    `json:"description" db:"description"`
	Timestamp   time.Time `json:"timestamp" db:"timestamp"`
}

// NewOrder creates a new order with default values
func NewOrder(userID, productID string, quantity int, totalAmount float64) *Order {
	now := time.Now()
	return &Order{
		ID:            uuid.New().String(),
		UserID:        userID,
		ProductID:     productID,
		Quantity:      quantity,
		TotalAmount:   totalAmount,
		Status:        "pending",
		PaymentStatus: "pending",
		IsDeleted:     false,
		CreatedAt:     now,
		UpdatedAt:     now,
		Version:       1,
	}
}

// NewOrderItem creates a new order item
func NewOrderItem(orderID, productID string, quantity int, price float64) *OrderItem {
	return &OrderItem{
		ID:        uuid.New().String(),
		OrderID:   orderID,
		ProductID: productID,
		Quantity:  quantity,
		Price:     price,
		Total:     float64(quantity) * price,
	}
}

// NewOrderTracking creates a new order tracking entry
func NewOrderTracking(orderID, status, location, description string) *OrderTracking {
	return &OrderTracking{
		ID:          uuid.New().String(),
		OrderID:     orderID,
		Status:      status,
		Location:    location,
		Description: description,
		Timestamp:   time.Now(),
	}
}

// Request/Response DTOs for API

// CreateOrderRequest represents the request for creating a new order
type CreateOrderRequest struct {
	UserID          string  `json:"user_id" binding:"required"`
	ProductID       string  `json:"product_id" binding:"required"`
	Quantity        int     `json:"quantity" binding:"required,min=1"`
	TotalAmount     float64 `json:"total_amount" binding:"required,min=0"`
	ShippingAddress string  `json:"shipping_address"`
	BillingAddress  string  `json:"billing_address"`
	Notes           string  `json:"notes"`
}

// UpdateOrderStatusRequest represents the request for updating order status
type UpdateOrderStatusRequest struct {
	Status string `json:"status" binding:"required"`
	Notes  string `json:"notes"`
}

// CancelOrderRequest represents the request for cancelling an order
type CancelOrderRequest struct {
	Reason string `json:"reason" binding:"required"`
}

// OrderResponse represents the response for order operations
type OrderResponse struct {
	Order   *Order `json:"order"`
	Success bool   `json:"success"`
}

// OrderTrackingResponse represents the response for order tracking
type OrderTrackingResponse struct {
	Tracking []OrderTracking `json:"tracking"`
	Success  bool            `json:"success"`
}

// OrderStatisticsResponse represents the response for order statistics
type OrderStatisticsResponse struct {
	Statistics *OrderStatistics `json:"statistics"`
	Success    bool             `json:"success"`
}
