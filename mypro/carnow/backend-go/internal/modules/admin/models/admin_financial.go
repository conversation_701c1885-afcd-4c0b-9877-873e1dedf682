package models

import (
	"time"
)

// AdminDashboard represents the main admin dashboard data
type AdminDashboard struct {
	FinancialSummary FinancialSummary `json:"financial_summary"`
	Alerts           []Alert          `json:"alerts"`
	RecentActions    []RecentAction   `json:"recent_actions"`
	SystemHealth     SystemHealth     `json:"system_health"`
	WalletManagement WalletManagement `json:"wallet_management"`
}

// FinancialSummary represents financial summary data
type FinancialSummary struct {
	TotalRevenue   float64 `json:"total_revenue"`
	MonthlyRevenue float64 `json:"monthly_revenue"`
	TotalOrders    int     `json:"total_orders"`
	PendingOrders  int     `json:"pending_orders"`
	ActiveUsers    int     `json:"active_users"`
	TotalProducts  int     `json:"total_products"`
	RevenueGrowth  float64 `json:"revenue_growth"`
	OrderGrowth    float64 `json:"order_growth"`
	UserGrowth     float64 `json:"user_growth"`
}

// <PERSON><PERSON> represents a system alert
type Alert struct {
	ID         string     `json:"id"`
	Type       string     `json:"type"`  // financial, security, system, performance
	Level      string     `json:"level"` // low, medium, high, critical
	Title      string     `json:"title"`
	Message    string     `json:"message"`
	Status     string     `json:"status"` // active, resolved, dismissed
	CreatedAt  time.Time  `json:"created_at"`
	ResolvedAt *time.Time `json:"resolved_at,omitempty"`
	ResolvedBy *string    `json:"resolved_by,omitempty"`
}

// RecentAction represents a recent system action
type RecentAction struct {
	ID        string    `json:"id"`
	UserID    string    `json:"user_id"`
	Action    string    `json:"action"`
	Resource  string    `json:"resource"`
	Details   string    `json:"details"`
	IPAddress string    `json:"ip_address"`
	Timestamp time.Time `json:"timestamp"`
}

// SystemHealth represents system health metrics
type SystemHealth struct {
	Status            string  `json:"status"` // healthy, degraded, critical
	Uptime            float64 `json:"uptime"`
	ResponseTime      float64 `json:"response_time"`
	ErrorRate         float64 `json:"error_rate"`
	DatabaseStatus    string  `json:"database_status"`
	CacheStatus       string  `json:"cache_status"`
	MemoryUsage       float64 `json:"memory_usage"`
	CPUUsage          float64 `json:"cpu_usage"`
	DiskUsage         float64 `json:"disk_usage"`
	ActiveConnections int     `json:"active_connections"`
}

// WalletManagement represents wallet management data
type WalletManagement struct {
	TotalWallets        int     `json:"total_wallets"`
	ActiveWallets       int     `json:"active_wallets"`
	TotalBalance        float64 `json:"total_balance"`
	AverageBalance      float64 `json:"average_balance"`
	TransactionsToday   int     `json:"transactions_today"`
	PendingTransactions int     `json:"pending_transactions"`
	FailedTransactions  int     `json:"failed_transactions"`
}

// AdminDashboardResponse represents the response for admin dashboard
type AdminDashboardResponse struct {
	Dashboard AdminDashboard `json:"dashboard"`
	Period    string         `json:"period"`
	Generated time.Time      `json:"generated"`
}

// WalletManagementResponse represents wallet management response
type WalletManagementResponse struct {
	Wallets    []WalletSummary  `json:"wallets"`
	Pagination AdminPagination  `json:"pagination"`
	Summary    WalletManagement `json:"summary"`
}

// WalletSummary represents a wallet summary for admin
type WalletSummary struct {
	ID               string    `json:"id"`
	UserID           string    `json:"user_id"`
	Balance          float64   `json:"balance"`
	Status           string    `json:"status"`
	LastTransaction  time.Time `json:"last_transaction"`
	TransactionCount int       `json:"transaction_count"`
	CreatedAt        time.Time `json:"created_at"`
}

// AdminPagination represents pagination for admin responses
type AdminPagination struct {
	Page       int  `json:"page"`
	Limit      int  `json:"limit"`
	Total      int  `json:"total"`
	TotalPages int  `json:"total_pages"`
	HasNext    bool `json:"has_next"`
	HasPrev    bool `json:"has_prev"`
}

// FinancialReport represents a financial report
type FinancialReport struct {
	ID          string                 `json:"id"`
	Type        string                 `json:"type"` // daily, weekly, monthly, yearly
	Period      string                 `json:"period"`
	Revenue     float64                `json:"revenue"`
	Orders      int                    `json:"orders"`
	Users       int                    `json:"users"`
	Products    int                    `json:"products"`
	GeneratedAt time.Time              `json:"generated_at"`
	Data        map[string]interface{} `json:"data"`
}

// FinancialReportsResponse represents financial reports response
type FinancialReportsResponse struct {
	Reports    []FinancialReport `json:"reports"`
	Pagination AdminPagination   `json:"pagination"`
}

// NewAlert creates a new alert
func NewAlert(alertType, level, title, message string) *Alert {
	return &Alert{
		ID:        generateID(),
		Type:      alertType,
		Level:     level,
		Title:     title,
		Message:   message,
		Status:    "active",
		CreatedAt: time.Now(),
	}
}

// NewRecentAction creates a new recent action
func NewRecentAction(userID, action, resource, details, ipAddress string) *RecentAction {
	return &RecentAction{
		ID:        generateID(),
		UserID:    userID,
		Action:    action,
		Resource:  resource,
		Details:   details,
		IPAddress: ipAddress,
		Timestamp: time.Now(),
	}
}

// UpdateAlertStatusRequest represents the request to update alert status
type UpdateAlertStatusRequest struct {
	Status string `json:"status" binding:"required"` // active, resolved, dismissed
	Notes  string `json:"notes,omitempty"`           // Optional notes for the status change
}

// generateID generates a simple ID (in production, use UUID)
func generateID() string {
	return time.Now().Format("20060102150405")
}
