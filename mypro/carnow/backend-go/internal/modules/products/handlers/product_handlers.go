package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"carnow-backend/internal/modules/products/models"
	"carnow-backend/internal/modules/products/services"
	"carnow-backend/internal/shared/middleware"
)

// ProductHandler handles product-related HTTP requests
type ProductHandler struct {
	productService *services.ProductService
	logger         *zap.Logger
}

// NewProductHandler creates a new product handler
func NewProductHandler(productService *services.ProductService, logger *zap.Logger) *ProductHandler {
	return &ProductHandler{
		productService: productService,
		logger:         logger,
	}
}

// GetAllProducts returns all products with pagination and filtering
// @Summary Get all products
// @Description Get all products with pagination, filtering, and sorting
// @Tags Products
// @Accept json
// @Produce json
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Param category_id query string false "Filter by category ID"
// @Param search query string false "Search term"
// @Param sort_by query string false "Sort field (name, price, created_at)" default(created_at)
// @Param sort_order query string false "Sort order (asc, desc)" default(desc)
// @Param min_price query number false "Minimum price"
// @Param max_price query number false "Maximum price"
// @Param condition query string false "Product condition (new, used, refurbished)"
// @Success 200 {object} models.ProductsResponse
// @Failure 400 {object} middleware.ErrorResponse
// @Failure 500 {object} middleware.ErrorResponse
// @Router /api/v1/products [get]
func (h *ProductHandler) GetAllProducts(c *gin.Context) {
	// Parse query parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	categoryID := c.Query("category_id")
	search := c.Query("search")
	sortBy := c.DefaultQuery("sort_by", "created_at")
	sortOrder := c.DefaultQuery("sort_order", "desc")
	minPrice, _ := strconv.ParseFloat(c.Query("min_price"), 64)
	maxPrice, _ := strconv.ParseFloat(c.Query("max_price"), 64)
	condition := c.Query("condition")

	// Validate pagination
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 20
	}

	// Validate sort parameters
	if sortBy != "name" && sortBy != "price" && sortBy != "created_at" {
		sortBy = "created_at"
	}
	if sortOrder != "asc" && sortOrder != "desc" {
		sortOrder = "desc"
	}

	// Build filter
	filter := models.ProductFilter{
		CategoryID: categoryID,
		Search:     search,
		SortBy:     sortBy,
		SortOrder:  sortOrder,
		MinPrice:   minPrice,
		MaxPrice:   maxPrice,
		Condition:  condition,
	}

	products, total, err := h.productService.GetAllProducts(c.Request.Context(), page, limit, filter)
	if err != nil {
		h.logger.Error("Failed to get products", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get products"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"products": products,
		"pagination": gin.H{
			"page":        page,
			"limit":       limit,
			"total":       total,
			"total_pages": (total + limit - 1) / limit,
			"has_next":    page*limit < total,
			"has_prev":    page > 1,
		},
		"success": true,
	})
}

// GetProductByID returns a specific product by ID
// @Summary Get product by ID
// @Description Get detailed information about a specific product
// @Tags Products
// @Accept json
// @Produce json
// @Param id path string true "Product ID"
// @Success 200 {object} models.ProductResponse
// @Failure 404 {object} middleware.ErrorResponse
// @Failure 500 {object} middleware.ErrorResponse
// @Router /api/v1/products/{id} [get]
func (h *ProductHandler) GetProductByID(c *gin.Context) {
	productID := c.Param("id")
	if productID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Product ID is required"})
		return
	}

	product, err := h.productService.GetProductByID(c.Request.Context(), productID)
	if err != nil {
		h.logger.Error("Failed to get product", zap.Error(err), zap.String("product_id", productID))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get product"})
		return
	}

	if product == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Product not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"product": product,
		"success": true,
	})
}

// GetProductsByCategory returns products filtered by category
// @Summary Get products by category
// @Description Get products filtered by category ID
// @Tags Products
// @Accept json
// @Produce json
// @Param category_id path string true "Category ID"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Success 200 {object} models.ProductsResponse
// @Failure 400 {object} middleware.ErrorResponse
// @Failure 500 {object} middleware.ErrorResponse
// @Router /api/v1/categories/{category_id}/products [get]
func (h *ProductHandler) GetProductsByCategory(c *gin.Context) {
	categoryID := c.Param("category_id")
	if categoryID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Category ID is required"})
		return
	}

	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 20
	}

	products, total, err := h.productService.GetProductsByCategory(c.Request.Context(), categoryID, page, limit)
	if err != nil {
		h.logger.Error("Failed to get products by category", zap.Error(err), zap.String("category_id", categoryID))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get products"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"products": products,
		"pagination": gin.H{
			"page":        page,
			"limit":       limit,
			"total":       total,
			"total_pages": (total + limit - 1) / limit,
			"has_next":    page*limit < total,
			"has_prev":    page > 1,
		},
		"success": true,
	})
}

// SearchProducts searches products by various criteria
// @Summary Search products
// @Description Search products by name, description, or other criteria
// @Tags Products
// @Accept json
// @Produce json
// @Param q query string true "Search query"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Param category_id query string false "Filter by category ID"
// @Param min_price query number false "Minimum price"
// @Param max_price query number false "Maximum price"
// @Success 200 {object} models.ProductsResponse
// @Failure 400 {object} middleware.ErrorResponse
// @Failure 500 {object} middleware.ErrorResponse
// @Router /api/v1/products/search [get]
func (h *ProductHandler) SearchProducts(c *gin.Context) {
	// Parse query parameters
	query := c.Query("q")
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))

	// Validate pagination
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 20
	}

	// Validate search query
	if query == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Search query is required"})
		return
	}

	products, total, err := h.productService.SearchProducts(c.Request.Context(), query, page, limit)
	if err != nil {
		h.logger.Error("Failed to search products", zap.Error(err), zap.String("query", query))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to search products"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"products": products,
		"pagination": gin.H{
			"page":        page,
			"limit":       limit,
			"total":       total,
			"total_pages": (total + limit - 1) / limit,
			"has_next":    page*limit < total,
			"has_prev":    page > 1,
		},
		"success": true,
	})
}

// GetFeaturedProducts returns featured/popular products
// @Summary Get featured products
// @Description Get featured or popular products
// @Tags Products
// @Accept json
// @Produce json
// @Param limit query int false "Number of products" default(10)
// @Success 200 {object} models.ProductsResponse
// @Failure 500 {object} middleware.ErrorResponse
// @Router /api/v1/products/featured [get]
func (h *ProductHandler) GetFeaturedProducts(c *gin.Context) {
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	if limit < 1 || limit > 50 {
		limit = 10
	}

	products, err := h.productService.GetFeaturedProducts(c.Request.Context(), limit)
	if err != nil {
		h.logger.Error("Failed to get featured products", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get featured products"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"products": products,
		"success":  true,
	})
}

// GetProductCategories returns all product categories
// @Summary Get product categories
// @Description Get all product categories with optional parent category filtering
// @Tags Products
// @Accept json
// @Produce json
// @Param parent_id query string false "Parent category ID"
// @Success 200 {object} models.CategoriesResponse
// @Failure 500 {object} middleware.ErrorResponse
// @Router /api/v1/categories [get]
func (h *ProductHandler) GetProductCategories(c *gin.Context) {
	categories, err := h.productService.GetProductCategories(c.Request.Context())
	if err != nil {
		h.logger.Error("Failed to get categories", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get categories"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"categories": categories,
		"success":    true,
	})
}

// GetProductRecommendations returns product recommendations
// @Summary Get product recommendations
// @Description Get personalized product recommendations for the user
// @Tags Products
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param limit query int false "Number of recommendations" default(10)
// @Success 200 {object} models.ProductsResponse
// @Failure 401 {object} middleware.ErrorResponse
// @Failure 500 {object} middleware.ErrorResponse
// @Router /api/v1/products/recommendations [get]
func (h *ProductHandler) GetProductRecommendations(c *gin.Context) {
	userID := middleware.GetUserIDFromContext(c)
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	if limit < 1 || limit > 20 {
		limit = 10
	}

	// For now, we'll use a placeholder product ID for recommendations
	// In a real implementation, this would be based on user preferences or browsing history
	productID := c.Query("product_id")
	if productID == "" {
		// Use a default product ID or get from user's recent activity
		productID = "default"
	}

	products, err := h.productService.GetProductRecommendations(c.Request.Context(), productID, limit)
	if err != nil {
		h.logger.Error("Failed to get product recommendations", zap.Error(err), zap.String("user_id", userID))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get recommendations"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"products": products,
		"success":  true,
	})
}

// CreateProduct creates a new product (admin only)
// @Summary Create product
// @Description Create a new product (admin only)
// @Tags Products
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param product body models.CreateProductRequest true "Product details"
// @Success 201 {object} models.ProductResponse
// @Failure 400 {object} middleware.ErrorResponse
// @Failure 401 {object} middleware.ErrorResponse
// @Failure 500 {object} middleware.ErrorResponse
// @Router /api/v1/products [post]
func (h *ProductHandler) CreateProduct(c *gin.Context) {
	// TODO: Implement admin authorization check
	userID := middleware.GetUserIDFromContext(c)
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	var req models.CreateProductRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request data", "details": err.Error()})
		return
	}

	// TODO: Implement product creation logic
	c.JSON(http.StatusNotImplemented, gin.H{"error": "Product creation not implemented yet"})
}

// UpdateProduct updates an existing product (admin only)
// @Summary Update product
// @Description Update an existing product (admin only)
// @Tags Products
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "Product ID"
// @Param product body models.UpdateProductRequest true "Product details"
// @Success 200 {object} models.ProductResponse
// @Failure 400 {object} middleware.ErrorResponse
// @Failure 401 {object} middleware.ErrorResponse
// @Failure 404 {object} middleware.ErrorResponse
// @Failure 500 {object} middleware.ErrorResponse
// @Router /api/v1/products/{id} [put]
func (h *ProductHandler) UpdateProduct(c *gin.Context) {
	// TODO: Implement admin authorization check
	userID := middleware.GetUserIDFromContext(c)
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	productID := c.Param("id")
	if productID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Product ID is required"})
		return
	}

	var req models.UpdateProductRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request data", "details": err.Error()})
		return
	}

	// TODO: Implement product update logic
	c.JSON(http.StatusNotImplemented, gin.H{"error": "Product update not implemented yet"})
}

// DeleteProduct deletes a product (admin only)
// @Summary Delete product
// @Description Delete a product (admin only)
// @Tags Products
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "Product ID"
// @Success 200 {object} middleware.SuccessResponse
// @Failure 401 {object} middleware.ErrorResponse
// @Failure 404 {object} middleware.ErrorResponse
// @Failure 500 {object} middleware.ErrorResponse
// @Router /api/v1/products/{id} [delete]
func (h *ProductHandler) DeleteProduct(c *gin.Context) {
	// TODO: Implement admin authorization check
	userID := middleware.GetUserIDFromContext(c)
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	productID := c.Param("id")
	if productID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Product ID is required"})
		return
	}

	// TODO: Implement product deletion logic
	c.JSON(http.StatusNotImplemented, gin.H{"error": "Product deletion not implemented yet"})
}
