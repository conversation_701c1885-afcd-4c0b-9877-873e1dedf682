package handlers

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"carnow-backend/internal/infrastructure/database"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func setupTestCleanAPI() (*gin.Engine, *CleanAPI) {
	gin.SetMode(gin.TestMode)

	// Create mock database (we'll use nil since we're testing handlers in isolation)
	var mockDB *database.SimpleDB = nil

	// Create clean API instance
	api := NewCleanAPI(mockDB)

	// Create router
	router := gin.New()
	router.Use(gin.Recovery())

	return router, api
}

func setupProductRoutes(router *gin.Engine, api *CleanAPI) {
	v1 := router.Group("/api/v1")
	{
		v1.GET("/products", api.GetProducts)
		v1.GET("/search", api.SearchProducts)
		v1.POST("/products", api.CreateProduct)
		v1.PUT("/products/:id", api.UpdateProduct)
		v1.DELETE("/products/:id", api.DeleteProduct)
	}
}

func TestProductHandlers_GetProducts(t *testing.T) {
	router, api := setupTestCleanAPI()
	setupProductRoutes(router, api)

	tests := []struct {
		name           string
		queryParams    string
		expectedStatus int
		checkResponse  func(t *testing.T, response map[string]interface{})
	}{
		{
			name:           "get products without parameters",
			queryParams:    "",
			expectedStatus: http.StatusOK,
			checkResponse: func(t *testing.T, response map[string]interface{}) {
				assert.Contains(t, response, "products")
				assert.Contains(t, response, "pagination")
			},
		},
		{
			name:           "get products with pagination",
			queryParams:    "?page=1&limit=10",
			expectedStatus: http.StatusOK,
			checkResponse: func(t *testing.T, response map[string]interface{}) {
				assert.Contains(t, response, "products")
				assert.Contains(t, response, "pagination")
			},
		},
		{
			name:           "get products with category filter",
			queryParams:    "?category=electronics",
			expectedStatus: http.StatusOK,
			checkResponse: func(t *testing.T, response map[string]interface{}) {
				assert.Contains(t, response, "products")
			},
		},
		{
			name:           "get products with search",
			queryParams:    "?search=phone",
			expectedStatus: http.StatusOK,
			checkResponse: func(t *testing.T, response map[string]interface{}) {
				assert.Contains(t, response, "products")
			},
		},
		{
			name:           "get products with invalid page",
			queryParams:    "?page=-1",
			expectedStatus: http.StatusOK, // Should default to page 1
			checkResponse: func(t *testing.T, response map[string]interface{}) {
				assert.Contains(t, response, "products")
			},
		},
		{
			name:           "get products with large limit",
			queryParams:    "?limit=1000",
			expectedStatus: http.StatusOK, // Should cap to max limit
			checkResponse: func(t *testing.T, response map[string]interface{}) {
				assert.Contains(t, response, "products")
			},
		},
		{
			name:           "get products with SQL injection in search",
			queryParams:    "?search='; DROP TABLE products; --",
			expectedStatus: http.StatusBadRequest,
			checkResponse: func(t *testing.T, response map[string]interface{}) {
				assert.Contains(t, response, "error")
			},
		},
		{
			name:           "get products with XSS in search",
			queryParams:    "?search=<script>alert('xss')</script>",
			expectedStatus: http.StatusBadRequest,
			checkResponse: func(t *testing.T, response map[string]interface{}) {
				assert.Contains(t, response, "error")
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req := httptest.NewRequest("GET", "/api/v1/products"+tt.queryParams, nil)

			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)

			var response map[string]interface{}
			err := json.Unmarshal(w.Body.Bytes(), &response)
			require.NoError(t, err)

			if tt.checkResponse != nil {
				tt.checkResponse(t, response)
			}
		})
	}
}

func TestProductHandlers_SearchProducts(t *testing.T) {
	router, api := setupTestCleanAPI()
	setupProductRoutes(router, api)

	tests := []struct {
		name           string
		queryParams    string
		expectedStatus int
		checkResponse  func(t *testing.T, response map[string]interface{})
	}{
		{
			name:           "search products with valid query",
			queryParams:    "?q=phone",
			expectedStatus: http.StatusOK,
			checkResponse: func(t *testing.T, response map[string]interface{}) {
				assert.Contains(t, response, "products")
			},
		},
		{
			name:           "search products with empty query",
			queryParams:    "",
			expectedStatus: http.StatusOK,
			checkResponse: func(t *testing.T, response map[string]interface{}) {
				assert.Contains(t, response, "products")
			},
		},
		{
			name:           "search products with SQL injection",
			queryParams:    "?q='; DROP TABLE products; --",
			expectedStatus: http.StatusBadRequest,
			checkResponse: func(t *testing.T, response map[string]interface{}) {
				assert.Contains(t, response, "error")
			},
		},
		{
			name:           "search products with XSS",
			queryParams:    "?q=<script>alert('xss')</script>",
			expectedStatus: http.StatusBadRequest,
			checkResponse: func(t *testing.T, response map[string]interface{}) {
				assert.Contains(t, response, "error")
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req := httptest.NewRequest("GET", "/api/v1/search"+tt.queryParams, nil)

			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)

			var response map[string]interface{}
			err := json.Unmarshal(w.Body.Bytes(), &response)
			require.NoError(t, err)

			if tt.checkResponse != nil {
				tt.checkResponse(t, response)
			}
		})
	}
}

func TestProductHandlers_CreateProduct(t *testing.T) {
	router, api := setupTestCleanAPI()
	setupProductRoutes(router, api)

	tests := []struct {
		name           string
		requestBody    interface{}
		expectedStatus int
		checkResponse  func(t *testing.T, response map[string]interface{})
	}{
		{
			name: "create product with valid data",
			requestBody: map[string]interface{}{
				"name":        "Test Product",
				"description": "A test product",
				"price":       99.99,
				"category_id": "550e8400-e29b-41d4-a716-************",
			},
			expectedStatus: http.StatusCreated,
			checkResponse: func(t *testing.T, response map[string]interface{}) {
				assert.Contains(t, response, "message")
			},
		},
		{
			name: "create product with missing name",
			requestBody: map[string]interface{}{
				"description": "A test product",
				"price":       99.99,
				"category_id": "550e8400-e29b-41d4-a716-************",
			},
			expectedStatus: http.StatusBadRequest,
			checkResponse: func(t *testing.T, response map[string]interface{}) {
				assert.Contains(t, response, "error")
			},
		},
		{
			name: "create product with invalid price",
			requestBody: map[string]interface{}{
				"name":        "Test Product",
				"description": "A test product",
				"price":       -10.0,
				"category_id": "550e8400-e29b-41d4-a716-************",
			},
			expectedStatus: http.StatusBadRequest,
			checkResponse: func(t *testing.T, response map[string]interface{}) {
				assert.Contains(t, response, "error")
			},
		},
		{
			name: "create product with XSS in name",
			requestBody: map[string]interface{}{
				"name":        "<script>alert('xss')</script>",
				"description": "A test product",
				"price":       99.99,
				"category_id": "550e8400-e29b-41d4-a716-************",
			},
			expectedStatus: http.StatusBadRequest,
			checkResponse: func(t *testing.T, response map[string]interface{}) {
				assert.Contains(t, response, "error")
			},
		},
		{
			name: "create product with SQL injection in description",
			requestBody: map[string]interface{}{
				"name":        "Test Product",
				"description": "'; DROP TABLE products; --",
				"price":       99.99,
				"category_id": "550e8400-e29b-41d4-a716-************",
			},
			expectedStatus: http.StatusBadRequest,
			checkResponse: func(t *testing.T, response map[string]interface{}) {
				assert.Contains(t, response, "error")
			},
		},
		{
			name: "create product with oversized name",
			requestBody: map[string]interface{}{
				"name":        strings.Repeat("A", 1000),
				"description": "A test product",
				"price":       99.99,
				"category_id": "550e8400-e29b-41d4-a716-************",
			},
			expectedStatus: http.StatusBadRequest,
			checkResponse: func(t *testing.T, response map[string]interface{}) {
				assert.Contains(t, response, "error")
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			bodyBytes, _ := json.Marshal(tt.requestBody)
			req := httptest.NewRequest("POST", "/api/v1/products", bytes.NewBuffer(bodyBytes))
			req.Header.Set("Content-Type", "application/json")

			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)

			var response map[string]interface{}
			err := json.Unmarshal(w.Body.Bytes(), &response)
			require.NoError(t, err)

			if tt.checkResponse != nil {
				tt.checkResponse(t, response)
			}
		})
	}
}

func TestProductHandlers_UpdateProduct(t *testing.T) {
	router, api := setupTestCleanAPI()
	setupProductRoutes(router, api)

	productID := "550e8400-e29b-41d4-a716-************"

	tests := []struct {
		name           string
		productID      string
		requestBody    interface{}
		expectedStatus int
		checkResponse  func(t *testing.T, response map[string]interface{})
	}{
		{
			name:      "update product with valid data",
			productID: productID,
			requestBody: map[string]interface{}{
				"name":  "Updated Product",
				"price": 149.99,
			},
			expectedStatus: http.StatusOK,
			checkResponse: func(t *testing.T, response map[string]interface{}) {
				assert.Contains(t, response, "message")
			},
		},
		{
			name:      "update product with invalid ID",
			productID: "invalid-uuid",
			requestBody: map[string]interface{}{
				"name": "Updated Product",
			},
			expectedStatus: http.StatusBadRequest,
			checkResponse: func(t *testing.T, response map[string]interface{}) {
				assert.Contains(t, response, "error")
			},
		},
		{
			name:      "update product with negative price",
			productID: productID,
			requestBody: map[string]interface{}{
				"price": -50.0,
			},
			expectedStatus: http.StatusBadRequest,
			checkResponse: func(t *testing.T, response map[string]interface{}) {
				assert.Contains(t, response, "error")
			},
		},
		{
			name:      "update product with XSS in name",
			productID: productID,
			requestBody: map[string]interface{}{
				"name": "<script>alert('xss')</script>",
			},
			expectedStatus: http.StatusBadRequest,
			checkResponse: func(t *testing.T, response map[string]interface{}) {
				assert.Contains(t, response, "error")
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			bodyBytes, _ := json.Marshal(tt.requestBody)
			req := httptest.NewRequest("PUT", "/api/v1/products/"+tt.productID, bytes.NewBuffer(bodyBytes))
			req.Header.Set("Content-Type", "application/json")

			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)

			var response map[string]interface{}
			err := json.Unmarshal(w.Body.Bytes(), &response)
			require.NoError(t, err)

			if tt.checkResponse != nil {
				tt.checkResponse(t, response)
			}
		})
	}
}

func TestProductHandlers_DeleteProduct(t *testing.T) {
	router, api := setupTestCleanAPI()
	setupProductRoutes(router, api)

	tests := []struct {
		name           string
		productID      string
		expectedStatus int
		checkResponse  func(t *testing.T, response map[string]interface{})
	}{
		{
			name:           "delete product with valid ID",
			productID:      "550e8400-e29b-41d4-a716-************",
			expectedStatus: http.StatusOK,
			checkResponse: func(t *testing.T, response map[string]interface{}) {
				assert.Contains(t, response, "message")
			},
		},
		{
			name:           "delete product with invalid ID",
			productID:      "invalid-uuid",
			expectedStatus: http.StatusBadRequest,
			checkResponse: func(t *testing.T, response map[string]interface{}) {
				assert.Contains(t, response, "error")
			},
		},
		{
			name:           "delete product with SQL injection",
			productID:      "'; DROP TABLE products; --",
			expectedStatus: http.StatusBadRequest,
			checkResponse: func(t *testing.T, response map[string]interface{}) {
				assert.Contains(t, response, "error")
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req := httptest.NewRequest("DELETE", "/api/v1/products/"+tt.productID, nil)

			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)

			var response map[string]interface{}
			err := json.Unmarshal(w.Body.Bytes(), &response)
			require.NoError(t, err)

			if tt.checkResponse != nil {
				tt.checkResponse(t, response)
			}
		})
	}
}

func TestProductHandlers_ContentTypeValidation(t *testing.T) {
	router, api := setupTestCleanAPI()
	setupProductRoutes(router, api)

	tests := []struct {
		name           string
		endpoint       string
		method         string
		contentType    string
		body           string
		expectedStatus int
	}{
		{
			name:           "POST with missing content type",
			endpoint:       "/api/v1/products",
			method:         "POST",
			contentType:    "",
			body:           `{"name":"Test Product","price":99.99}`,
			expectedStatus: http.StatusUnsupportedMediaType,
		},
		{
			name:           "POST with wrong content type",
			endpoint:       "/api/v1/products",
			method:         "POST",
			contentType:    "text/plain",
			body:           `{"name":"Test Product","price":99.99}`,
			expectedStatus: http.StatusUnsupportedMediaType,
		},
		{
			name:           "PUT with correct content type",
			endpoint:       "/api/v1/products/550e8400-e29b-41d4-a716-************",
			method:         "PUT",
			contentType:    "application/json",
			body:           `{"name":"Updated Product"}`,
			expectedStatus: http.StatusOK, // Will pass content type validation
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req := httptest.NewRequest(tt.method, tt.endpoint, strings.NewReader(tt.body))
			if tt.contentType != "" {
				req.Header.Set("Content-Type", tt.contentType)
			}

			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)
		})
	}
}

// Benchmark tests
func BenchmarkProductHandlers_GetProducts(b *testing.B) {
	router, api := setupTestCleanAPI()
	setupProductRoutes(router, api)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		req := httptest.NewRequest("GET", "/api/v1/products?page=1&limit=10", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)
	}
}

func BenchmarkProductHandlers_CreateProduct(b *testing.B) {
	router, api := setupTestCleanAPI()
	setupProductRoutes(router, api)

	requestBody := map[string]interface{}{
		"name":        "Benchmark Product",
		"description": "A benchmark test product",
		"price":       99.99,
		"category_id": "550e8400-e29b-41d4-a716-************",
	}
	bodyBytes, _ := json.Marshal(requestBody)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		req := httptest.NewRequest("POST", "/api/v1/products", bytes.NewBuffer(bodyBytes))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)
	}
}
