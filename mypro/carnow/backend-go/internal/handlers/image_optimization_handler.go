package handlers

import (
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/h2non/bimg"
	"go.uber.org/zap"
)

// ImageOptimizationHandler handles image optimization requests
type ImageOptimizationHandler struct {
	logger *zap.Logger
	cache  map[string][]byte // Simple in-memory cache (use Redis in production)
}

// NewImageOptimizationHandler creates a new image optimization handler
func NewImageOptimizationHandler(logger *zap.Logger) *ImageOptimizationHandler {
	return &ImageOptimizationHandler{
		logger: logger,
		cache:  make(map[string][]byte),
	}
}

// OptimizeImage handles image optimization requests
func (h *ImageOptimizationHandler) OptimizeImage(c *gin.Context) {
	// Get parameters
	imageURL := c.Query("url")
	if imageURL == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "URL parameter is required"})
		return
	}

	// Decode URL
	decodedURL, err := url.QueryUnescape(imageURL)
	if err != nil {
		h.logger.Error("Failed to decode URL", zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid URL"})
		return
	}

	// Parse optimization parameters
	width := h.parseIntParam(c, "w", 0)
	height := h.parseIntParam(c, "h", 0)
	quality := h.parseIntParam(c, "q", 85)
	thumbnail := c.Query("thumbnail") == "true"

	// Validate parameters
	if quality < 1 || quality > 100 {
		quality = 85
	}
	if width > 2000 {
		width = 2000
	}
	if height > 2000 {
		height = 2000
	}

	// Generate cache key
	cacheKey := fmt.Sprintf("%s_w%d_h%d_q%d_t%v", decodedURL, width, height, quality, thumbnail)

	// Check cache first
	if cachedImage, exists := h.cache[cacheKey]; exists {
		h.serveImage(c, cachedImage, "image/jpeg")
		return
	}

	// Download original image
	originalImage, contentType, err := h.downloadImage(decodedURL)
	if err != nil {
		h.logger.Error("Failed to download image", zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{"error": "Failed to download image"})
		return
	}

	// Optimize image
	optimizedImage, err := h.optimizeImage(originalImage, width, height, quality, thumbnail)
	if err != nil {
		h.logger.Error("Failed to optimize image", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to optimize image"})
		return
	}

	// Cache the optimized image
	h.cache[cacheKey] = optimizedImage

	// Serve the optimized image
	h.serveImage(c, optimizedImage, contentType)
}

// ResizeImage handles image resizing requests
func (h *ImageOptimizationHandler) ResizeImage(c *gin.Context) {
	imageURL := c.Query("url")
	if imageURL == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "URL parameter is required"})
		return
	}

	width := h.parseIntParam(c, "w", 0)
	height := h.parseIntParam(c, "h", 0)

	if width == 0 && height == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Width or height parameter is required"})
		return
	}

	// Decode URL
	decodedURL, err := url.QueryUnescape(imageURL)
	if err != nil {
		h.logger.Error("Failed to decode URL", zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid URL"})
		return
	}

	// Generate cache key
	cacheKey := fmt.Sprintf("resize_%s_w%d_h%d", decodedURL, width, height)

	// Check cache
	if cachedImage, exists := h.cache[cacheKey]; exists {
		h.serveImage(c, cachedImage, "image/jpeg")
		return
	}

	// Download and resize image
	originalImage, _, err := h.downloadImage(decodedURL)
	if err != nil {
		h.logger.Error("Failed to download image", zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{"error": "Failed to download image"})
		return
	}

	resizedImage, err := h.resizeImage(originalImage, width, height)
	if err != nil {
		h.logger.Error("Failed to resize image", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to resize image"})
		return
	}

	// Cache and serve
	h.cache[cacheKey] = resizedImage
	h.serveImage(c, resizedImage, "image/jpeg")
}

// CompressImage handles image compression requests
func (h *ImageOptimizationHandler) CompressImage(c *gin.Context) {
	imageURL := c.Query("url")
	if imageURL == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "URL parameter is required"})
		return
	}

	quality := h.parseIntParam(c, "q", 85)
	if quality < 1 || quality > 100 {
		quality = 85
	}

	// Decode URL
	decodedURL, err := url.QueryUnescape(imageURL)
	if err != nil {
		h.logger.Error("Failed to decode URL", zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid URL"})
		return
	}

	// Generate cache key
	cacheKey := fmt.Sprintf("compress_%s_q%d", decodedURL, quality)

	// Check cache
	if cachedImage, exists := h.cache[cacheKey]; exists {
		h.serveImage(c, cachedImage, "image/jpeg")
		return
	}

	// Download and compress image
	originalImage, _, err := h.downloadImage(decodedURL)
	if err != nil {
		h.logger.Error("Failed to download image", zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{"error": "Failed to download image"})
		return
	}

	compressedImage, err := h.compressImage(originalImage, quality)
	if err != nil {
		h.logger.Error("Failed to compress image", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to compress image"})
		return
	}

	// Cache and serve
	h.cache[cacheKey] = compressedImage
	h.serveImage(c, compressedImage, "image/jpeg")
}

// GetImageInfo returns information about an image
func (h *ImageOptimizationHandler) GetImageInfo(c *gin.Context) {
	imageURL := c.Query("url")
	if imageURL == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "URL parameter is required"})
		return
	}

	// Decode URL
	decodedURL, err := url.QueryUnescape(imageURL)
	if err != nil {
		h.logger.Error("Failed to decode URL", zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid URL"})
		return
	}

	// Download image
	imageData, contentType, err := h.downloadImage(decodedURL)
	if err != nil {
		h.logger.Error("Failed to download image", zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{"error": "Failed to download image"})
		return
	}

	// Get image metadata using bimg
	image := bimg.NewImage(imageData)
	size, err := image.Size()
	if err != nil {
		h.logger.Error("Failed to get image size", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get image info"})
		return
	}

	// Get image type
	imageType := image.Type()
	var format string
	switch imageType {
	case bimg.JPEG:
		format = "jpeg"
	case bimg.PNG:
		format = "png"
	case bimg.WEBP:
		format = "webp"
	case bimg.GIF:
		format = "gif"
	case bimg.TIFF:
		format = "tiff"
	default:
		format = "unknown"
	}

	info := gin.H{
		"url":          decodedURL,
		"width":        size.Width,
		"height":       size.Height,
		"format":       format,
		"content_type": contentType,
		"size_bytes":   len(imageData),
		"has_alpha":    image.HasAlpha(),
		"has_profile":  image.HasProfile(),
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    info,
	})
}

// Helper methods

func (h *ImageOptimizationHandler) parseIntParam(c *gin.Context, param string, defaultValue int) int {
	value := c.Query(param)
	if value == "" {
		return defaultValue
	}

	intValue, err := strconv.Atoi(value)
	if err != nil {
		return defaultValue
	}

	return intValue
}

func (h *ImageOptimizationHandler) downloadImage(imageURL string) ([]byte, string, error) {
	// Create HTTP client with timeout
	client := &http.Client{
		Timeout: 30 * time.Second,
	}

	// Make request
	resp, err := client.Get(imageURL)
	if err != nil {
		return nil, "", fmt.Errorf("failed to download image: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, "", fmt.Errorf("failed to download image: status %d", resp.StatusCode)
	}

	// Read image data
	imageData, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, "", fmt.Errorf("failed to read image data: %w", err)
	}

	contentType := resp.Header.Get("Content-Type")
	if contentType == "" {
		contentType = "image/jpeg" // Default
	}

	return imageData, contentType, nil
}

func (h *ImageOptimizationHandler) optimizeImage(imageData []byte, width, height, quality int, thumbnail bool) ([]byte, error) {
	// Create bimg options
	options := bimg.Options{
		Quality: quality,
		Type:    bimg.JPEG,
	}

	// Set dimensions if specified
	if width > 0 || height > 0 {
		if thumbnail {
			// For thumbnails, use smaller dimensions and crop to square
			if width == 0 {
				width = height
			}
			if height == 0 {
				height = width
			}
			// Ensure thumbnail is not too large
			if width > 300 {
				width = 300
			}
			if height > 300 {
				height = 300
			}

			// Use smart crop for thumbnails
			options.Width = width
			options.Height = height
			options.Crop = true
			options.Gravity = bimg.GravitySmart
		} else {
			// Regular resize maintaining aspect ratio
			options.Width = width
			options.Height = height
			options.Force = false // Don't force exact dimensions
		}
	}

	// Process image with bimg
	processedImage, err := bimg.NewImage(imageData).Process(options)
	if err != nil {
		return nil, fmt.Errorf("failed to process image with bimg: %w", err)
	}

	return processedImage, nil
}

func (h *ImageOptimizationHandler) resizeImage(imageData []byte, width, height int) ([]byte, error) {
	// Create bimg options for resizing
	options := bimg.Options{
		Width:   width,
		Height:  height,
		Quality: 85,
		Type:    bimg.JPEG,
		Force:   false, // Maintain aspect ratio
	}

	// Process image with bimg
	resizedImage, err := bimg.NewImage(imageData).Process(options)
	if err != nil {
		return nil, fmt.Errorf("failed to resize image with bimg: %w", err)
	}

	return resizedImage, nil
}

func (h *ImageOptimizationHandler) compressImage(imageData []byte, quality int) ([]byte, error) {
	// Create bimg options for compression
	options := bimg.Options{
		Quality: quality,
		Type:    bimg.JPEG,
	}

	// Process image with bimg
	compressedImage, err := bimg.NewImage(imageData).Process(options)
	if err != nil {
		return nil, fmt.Errorf("failed to compress image with bimg: %w", err)
	}

	return compressedImage, nil
}

func (h *ImageOptimizationHandler) serveImage(c *gin.Context, imageData []byte, contentType string) {
	// Set appropriate headers
	c.Header("Content-Type", contentType)
	c.Header("Content-Length", strconv.Itoa(len(imageData)))
	c.Header("Cache-Control", "public, max-age=86400") // Cache for 24 hours
	c.Header("ETag", fmt.Sprintf(`"%x"`, len(imageData)))

	// Check if client has cached version
	if match := c.GetHeader("If-None-Match"); match != "" {
		etag := fmt.Sprintf(`"%x"`, len(imageData))
		if strings.Contains(match, etag) {
			c.Status(http.StatusNotModified)
			return
		}
	}

	// Serve image data
	c.Data(http.StatusOK, contentType, imageData)
}

// ClearCache clears the image cache
func (h *ImageOptimizationHandler) ClearCache(c *gin.Context) {
	h.cache = make(map[string][]byte)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Image cache cleared",
	})
}

// GetCacheStats returns cache statistics
func (h *ImageOptimizationHandler) GetCacheStats(c *gin.Context) {
	totalSize := 0
	for _, data := range h.cache {
		totalSize += len(data)
	}

	stats := gin.H{
		"cached_images":    len(h.cache),
		"total_size_bytes": totalSize,
		"total_size_mb":    float64(totalSize) / (1024 * 1024),
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    stats,
	})
}
