package handlers

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"sync"
	"time"

	"carnow-backend/internal/interfaces"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/gorilla/websocket"
	"gorm.io/gorm"
)

// WebSocketMessage represents a real-time message
type WebSocketMessage struct {
	Type      string                 `json:"type"`
	Event     string                 `json:"event"`
	Data      interface{}            `json:"data"`
	UserID    string                 `json:"user_id,omitempty"`
	Timestamp time.Time              `json:"timestamp"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
}

// WebSocketClient represents a connected client
type WebSocketClient struct {
	ID       string
	UserID   string
	Conn     *websocket.Conn
	Send     chan WebSocketMessage
	Hub      *WebSocketHub
	LastSeen time.Time
}

// WebSocketHub manages all connected clients
type WebSocketHub struct {
	clients     map[string]*WebSocketClient
	userClients map[string][]*WebSocketClient // Map user ID to their clients
	broadcast   chan WebSocketMessage
	register    chan *WebSocketClient
	unregister  chan *WebSocketClient
	mutex       sync.RWMutex
	db          *gorm.DB
}

// WebSocketHandler handles WebSocket connections and real-time updates
type WebSocketHandler struct {
	hub      *WebSocketHub
	upgrader websocket.Upgrader
	db       *gorm.DB
}

// NewWebSocketHandler creates a new WebSocket handler
func NewWebSocketHandler(db *gorm.DB) *WebSocketHandler {
	hub := &WebSocketHub{
		clients:     make(map[string]*WebSocketClient),
		userClients: make(map[string][]*WebSocketClient),
		broadcast:   make(chan WebSocketMessage, 256),
		register:    make(chan *WebSocketClient),
		unregister:  make(chan *WebSocketClient),
		db:          db,
	}

	handler := &WebSocketHandler{
		hub: hub,
		upgrader: websocket.Upgrader{
			CheckOrigin: func(r *http.Request) bool {
				// Allow all origins for development - restrict in production
				return true
			},
			ReadBufferSize:  1024,
			WriteBufferSize: 1024,
		},
		db: db,
	}

	// Start the hub
	go hub.run()

	return handler
}

// HandleWebSocket handles WebSocket connection upgrades
func (h *WebSocketHandler) HandleWebSocket(c *gin.Context) {
	// Get user ID from JWT context
	userIDStr, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	userID := userIDStr.(string)

	// Upgrade HTTP connection to WebSocket
	conn, err := h.upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		log.Printf("❌ WebSocket upgrade failed: %v", err)
		return
	}

	// Create new client
	client := &WebSocketClient{
		ID:       uuid.New().String(),
		UserID:   userID,
		Conn:     conn,
		Send:     make(chan WebSocketMessage, 256),
		Hub:      h.hub,
		LastSeen: time.Now(),
	}

	// Register client
	h.hub.register <- client

	// Start goroutines for reading and writing
	go client.writePump()
	go client.readPump()

	log.Printf("✅ WebSocket client connected: %s (User: %s)", client.ID, userID)
}

// run starts the WebSocket hub
func (h *WebSocketHub) run() {
	ticker := time.NewTicker(30 * time.Second) // Ping clients every 30 seconds
	defer ticker.Stop()

	for {
		select {
		case client := <-h.register:
			h.registerClient(client)

		case client := <-h.unregister:
			h.unregisterClient(client)

		case message := <-h.broadcast:
			h.broadcastMessage(message)

		case <-ticker.C:
			h.pingClients()
		}
	}
}

// registerClient registers a new client
func (h *WebSocketHub) registerClient(client *WebSocketClient) {
	h.mutex.Lock()
	defer h.mutex.Unlock()

	h.clients[client.ID] = client

	// Add to user clients map
	if h.userClients[client.UserID] == nil {
		h.userClients[client.UserID] = make([]*WebSocketClient, 0)
	}
	h.userClients[client.UserID] = append(h.userClients[client.UserID], client)

	log.Printf("✅ Client registered: %s (User: %s, Total clients: %d)",
		client.ID, client.UserID, len(h.clients))

	// Send welcome message
	welcomeMsg := WebSocketMessage{
		Type:      "system",
		Event:     "connected",
		Data:      gin.H{"message": "Connected to CarNow real-time updates"},
		Timestamp: time.Now(),
	}

	select {
	case client.Send <- welcomeMsg:
	default:
		close(client.Send)
		delete(h.clients, client.ID)
	}
}

// unregisterClient unregisters a client
func (h *WebSocketHub) unregisterClient(client *WebSocketClient) {
	h.mutex.Lock()
	defer h.mutex.Unlock()

	if _, ok := h.clients[client.ID]; ok {
		delete(h.clients, client.ID)
		close(client.Send)

		// Remove from user clients map
		if userClients, exists := h.userClients[client.UserID]; exists {
			for i, c := range userClients {
				if c.ID == client.ID {
					h.userClients[client.UserID] = append(userClients[:i], userClients[i+1:]...)
					break
				}
			}
			// Clean up empty user client list
			if len(h.userClients[client.UserID]) == 0 {
				delete(h.userClients, client.UserID)
			}
		}

		log.Printf("✅ Client unregistered: %s (User: %s, Total clients: %d)",
			client.ID, client.UserID, len(h.clients))
	}
}

// broadcastMessage broadcasts a message to relevant clients
func (h *WebSocketHub) broadcastMessage(message WebSocketMessage) {
	h.mutex.RLock()
	defer h.mutex.RUnlock()

	// If message has specific user ID, send only to that user's clients
	if message.UserID != "" {
		if userClients, exists := h.userClients[message.UserID]; exists {
			for _, client := range userClients {
				select {
				case client.Send <- message:
				default:
					// Client's send channel is full, close it
					close(client.Send)
					delete(h.clients, client.ID)
				}
			}
		}
		return
	}

	// Broadcast to all clients
	for clientID, client := range h.clients {
		select {
		case client.Send <- message:
		default:
			// Client's send channel is full, close it
			close(client.Send)
			delete(h.clients, clientID)
		}
	}
}

// pingClients sends ping messages to keep connections alive
func (h *WebSocketHub) pingClients() {
	h.mutex.RLock()
	defer h.mutex.RUnlock()

	pingMsg := WebSocketMessage{
		Type:      "system",
		Event:     "ping",
		Data:      gin.H{"timestamp": time.Now()},
		Timestamp: time.Now(),
	}

	for clientID, client := range h.clients {
		// Check if client is stale (no activity for 5 minutes)
		if time.Since(client.LastSeen) > 5*time.Minute {
			log.Printf("⚠️ Removing stale client: %s", clientID)
			h.unregister <- client
			continue
		}

		select {
		case client.Send <- pingMsg:
		default:
			// Client's send channel is full, close it
			close(client.Send)
			delete(h.clients, clientID)
		}
	}
}

// readPump handles reading messages from the WebSocket connection
func (c *WebSocketClient) readPump() {
	defer func() {
		c.Hub.unregister <- c
		c.Conn.Close()
	}()

	// Set read deadline and pong handler
	c.Conn.SetReadDeadline(time.Now().Add(60 * time.Second))
	c.Conn.SetPongHandler(func(string) error {
		c.Conn.SetReadDeadline(time.Now().Add(60 * time.Second))
		c.LastSeen = time.Now()
		return nil
	})

	for {
		var message WebSocketMessage
		err := c.Conn.ReadJSON(&message)
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				log.Printf("❌ WebSocket error: %v", err)
			}
			break
		}

		c.LastSeen = time.Now()

		// Handle client messages (e.g., subscription requests)
		c.handleClientMessage(message)
	}
}

// writePump handles writing messages to the WebSocket connection
func (c *WebSocketClient) writePump() {
	ticker := time.NewTicker(54 * time.Second) // Send ping every 54 seconds
	defer func() {
		ticker.Stop()
		c.Conn.Close()
	}()

	for {
		select {
		case message, ok := <-c.Send:
			c.Conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if !ok {
				// The hub closed the channel
				c.Conn.WriteMessage(websocket.CloseMessage, []byte{})
				return
			}

			if err := c.Conn.WriteJSON(message); err != nil {
				log.Printf("❌ WebSocket write error: %v", err)
				return
			}

		case <-ticker.C:
			c.Conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if err := c.Conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				return
			}
		}
	}
}

// handleClientMessage handles messages received from clients
func (c *WebSocketClient) handleClientMessage(message WebSocketMessage) {
	switch message.Event {
	case "subscribe":
		// Handle subscription requests
		c.handleSubscription(message)
	case "unsubscribe":
		// Handle unsubscription requests
		c.handleUnsubscription(message)
	case "pong":
		// Update last seen time
		c.LastSeen = time.Now()
	default:
		log.Printf("⚠️ Unknown message event: %s", message.Event)
	}
}

// handleSubscription handles subscription requests from clients
func (c *WebSocketClient) handleSubscription(message WebSocketMessage) {
	// Extract subscription type from message data
	if data, ok := message.Data.(map[string]interface{}); ok {
		if subscriptionType, exists := data["type"]; exists {
			log.Printf("✅ Client %s subscribed to: %s", c.ID, subscriptionType)

			// Send confirmation
			confirmMsg := WebSocketMessage{
				Type:      "system",
				Event:     "subscription_confirmed",
				Data:      gin.H{"type": subscriptionType, "status": "subscribed"},
				Timestamp: time.Now(),
			}

			select {
			case c.Send <- confirmMsg:
			default:
				// Channel is full, ignore
			}
		}
	}
}

// handleUnsubscription handles unsubscription requests from clients
func (c *WebSocketClient) handleUnsubscription(message WebSocketMessage) {
	// Extract subscription type from message data
	if data, ok := message.Data.(map[string]interface{}); ok {
		if subscriptionType, exists := data["type"]; exists {
			log.Printf("✅ Client %s unsubscribed from: %s", c.ID, subscriptionType)

			// Send confirmation
			confirmMsg := WebSocketMessage{
				Type:      "system",
				Event:     "unsubscription_confirmed",
				Data:      gin.H{"type": subscriptionType, "status": "unsubscribed"},
				Timestamp: time.Now(),
			}

			select {
			case c.Send <- confirmMsg:
			default:
				// Channel is full, ignore
			}
		}
	}
}

// BroadcastToUser sends a message to all clients of a specific user
func (h *WebSocketHandler) BroadcastToUser(userID string, messageType, event string, data interface{}) {
	message := WebSocketMessage{
		Type:      messageType,
		Event:     event,
		Data:      data,
		UserID:    userID,
		Timestamp: time.Now(),
	}

	select {
	case h.hub.broadcast <- message:
	default:
		log.Printf("⚠️ Broadcast channel full, message dropped")
	}
}

// BroadcastToAll sends a message to all connected clients
func (h *WebSocketHandler) BroadcastToAll(messageType, event string, data interface{}) {
	message := WebSocketMessage{
		Type:      messageType,
		Event:     event,
		Data:      data,
		Timestamp: time.Now(),
	}

	select {
	case h.hub.broadcast <- message:
	default:
		log.Printf("⚠️ Broadcast channel full, message dropped")
	}
}

// GetConnectedUsers returns a list of currently connected user IDs
func (h *WebSocketHandler) GetConnectedUsers() []string {
	h.hub.mutex.RLock()
	defer h.hub.mutex.RUnlock()

	users := make([]string, 0, len(h.hub.userClients))
	for userID := range h.hub.userClients {
		users = append(users, userID)
	}

	return users
}

// GetConnectionStats returns connection statistics
func (h *WebSocketHandler) GetConnectionStats() map[string]interface{} {
	h.hub.mutex.RLock()
	defer h.hub.mutex.RUnlock()

	return map[string]interface{}{
		"total_clients":   len(h.hub.clients),
		"connected_users": len(h.hub.userClients),
		"timestamp":       time.Now(),
	}
}

// Real-time notification methods for different events

// NotifyProductUpdate notifies about product updates
func (h *WebSocketHandler) NotifyProductUpdate(productID, userID string, action string, productData interface{}) {
	h.BroadcastToAll("product", "product_"+action, gin.H{
		"product_id": productID,
		"action":     action,
		"data":       productData,
		"user_id":    userID,
	})
}

// NotifyOrderUpdate notifies about order updates
func (h *WebSocketHandler) NotifyOrderUpdate(orderID, userID string, status string, orderData interface{}) {
	h.BroadcastToUser(userID, "order", "order_status_changed", gin.H{
		"order_id": orderID,
		"status":   status,
		"data":     orderData,
	})
}

// NotifyWalletUpdate notifies about wallet balance changes
func (h *WebSocketHandler) NotifyWalletUpdate(userID string, balance float64, transactionData interface{}) {
	h.BroadcastToUser(userID, "wallet", "balance_updated", gin.H{
		"balance":     balance,
		"transaction": transactionData,
	})
}

// NotifyInventoryUpdate notifies about inventory changes
func (h *WebSocketHandler) NotifyInventoryUpdate(productID string, quantity int, sellerID string) {
	h.BroadcastToUser(sellerID, "inventory", "quantity_changed", gin.H{
		"product_id": productID,
		"quantity":   quantity,
	})
}

// NotifySystemMessage sends system-wide notifications
func (h *WebSocketHandler) NotifySystemMessage(messageType, title, content string) {
	h.BroadcastToAll("system", "system_message", gin.H{
		"type":    messageType,
		"title":   title,
		"content": content,
	})
}

// Interface implementation methods for interfaces.WebSocketBroadcaster

// BroadcastToUser implements interfaces.WebSocketBroadcaster
func (h *WebSocketHandler) BroadcastToUser(userID string, message interface{}) error {
	h.BroadcastToUser(userID, "data_change", "updated", message)
	return nil
}

// BroadcastToAll implements interfaces.WebSocketBroadcaster
func (h *WebSocketHandler) BroadcastToAll(message interface{}) error {
	h.BroadcastToAll("data_change", "updated", message)
	return nil
}

// IsUserConnected implements interfaces.WebSocketBroadcaster
func (h *WebSocketHandler) IsUserConnected(userID string) bool {
	h.hub.mutex.RLock()
	defer h.hub.mutex.RUnlock()

	clients, exists := h.hub.userClients[userID]
	return exists && len(clients) > 0
}

// Ensure WebSocketHandler implements the interface
var _ interfaces.WebSocketBroadcaster = (*WebSocketHandler)(nil)

// Interface implementation methods

// BroadcastToUser implements interfaces.WebSocketBroadcaster
func (h *WebSocketHandler) BroadcastToUser(userID string, message []byte) error {
	var wsMessage interfaces.WebSocketMessage
	if err := json.Unmarshal(message, &wsMessage); err != nil {
		return err
	}

	h.BroadcastToUserTyped(wsMessage.Type, wsMessage.Event, wsMessage.Data, userID)
	return nil
}

// BroadcastToAll implements interfaces.WebSocketBroadcaster
func (h *WebSocketHandler) BroadcastToAll(message []byte) error {
	var wsMessage interfaces.WebSocketMessage
	if err := json.Unmarshal(message, &wsMessage); err != nil {
		return err
	}

	h.BroadcastToAllTyped(wsMessage.Type, wsMessage.Event, wsMessage.Data)
	return nil
}

// BroadcastMessage implements interfaces.WebSocketBroadcaster
func (h *WebSocketHandler) BroadcastMessage(messageType string, data interface{}) error {
	message := WebSocketMessage{
		Type:      messageType,
		Event:     "message",
		Data:      data,
		Timestamp: time.Now(),
	}

	select {
	case h.hub.broadcast <- message:
		return nil
	default:
		return fmt.Errorf("broadcast channel full")
	}
}

// Helper methods for backward compatibility
func (h *WebSocketHandler) BroadcastToUserTyped(messageType, event string, data interface{}, userID string) {
	message := WebSocketMessage{
		Type:      messageType,
		Event:     event,
		Data:      data,
		UserID:    userID,
		Timestamp: time.Now(),
	}

	select {
	case h.hub.broadcast <- message:
	default:
		log.Printf("⚠️ Broadcast channel full, message dropped")
	}
}

func (h *WebSocketHandler) BroadcastToAllTyped(messageType, event string, data interface{}) {
	message := WebSocketMessage{
		Type:      messageType,
		Event:     event,
		Data:      data,
		Timestamp: time.Now(),
	}

	select {
	case h.hub.broadcast <- message:
	default:
		log.Printf("⚠️ Broadcast channel full, message dropped")
	}
}
