package handlers

import (
	"context"
	"net/http"
	"time"

	"carnow-backend/internal/shared/errors"
	"carnow-backend/internal/shared/resilience"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// ResilientHandlers provides handlers with built-in resilience patterns
type ResilientHandlers struct {
	resilienceService *resilience.ResilienceService
	errorHandler      *errors.CentralizedErrorHandler
	logger            *zap.Logger
}

// NewResilientHandlers creates new resilient handlers
func NewResilientHandlers(
	resilienceService *resilience.ResilienceService,
	errorHandler *errors.CentralizedErrorHandler,
	logger *zap.Logger,
) *ResilientHandlers {
	return &ResilientHandlers{
		resilienceService: resilienceService,
		errorHandler:      errorHandler,
		logger:            logger,
	}
}

// WithResilience wraps a handler function with resilience patterns
func (rh *ResilientHandlers) WithResilience(
	operationName string,
	handler func(c *gin.Context) error,
	options ...resilience.ResilienceOption,
) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Create context with request timeout
		ctx, cancel := context.WithTimeout(c.Request.Context(), 30*time.Second)
		defer cancel()

		// Execute with resilience
		err := rh.resilienceService.ExecuteWithResilience(
			ctx,
			operationName,
			func() error {
				return handler(c)
			},
			options...,
		)

		// Handle any errors
		if err != nil {
			rh.errorHandler.HandleError(c, err)
		}
	}
}

// WithFallback wraps a handler with primary and fallback operations
func (rh *ResilientHandlers) WithFallback(
	operationName string,
	primaryHandler func(c *gin.Context) error,
	fallbackHandler func(c *gin.Context) error,
	options ...resilience.ResilienceOption,
) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Create context with request timeout
		ctx, cancel := context.WithTimeout(c.Request.Context(), 30*time.Second)
		defer cancel()

		// Execute with fallback
		err := rh.resilienceService.ExecuteWithFallback(
			ctx,
			operationName,
			func() error {
				return primaryHandler(c)
			},
			func() error {
				return fallbackHandler(c)
			},
			options...,
		)

		// Handle any errors
		if err != nil {
			rh.errorHandler.HandleError(c, err)
		}
	}
}

// Example resilient handlers

// GetProductsResilient demonstrates resilient product fetching
func (rh *ResilientHandlers) GetProductsResilient() gin.HandlerFunc {
	return rh.WithFallback(
		"get_products",
		// Primary operation: fetch from database
		func(c *gin.Context) error {
			// Simulate database operation that might fail
			rh.logger.Info("Fetching products from database")

			// This would be your actual database call
			// products, err := rh.productService.GetProducts(c.Request.Context())
			// if err != nil {
			//     return err
			// }

			// Simulate potential failure
			if time.Now().Unix()%3 == 0 {
				return errors.NewInternalError("Database connection failed", nil)
			}

			c.JSON(http.StatusOK, gin.H{
				"success": true,
				"data": []map[string]interface{}{
					{"id": "1", "name": "Product 1", "price": 100.0},
					{"id": "2", "name": "Product 2", "price": 200.0},
				},
				"source": "database",
			})
			return nil
		},
		// Fallback operation: return cached data
		func(c *gin.Context) error {
			rh.logger.Info("Returning cached products")

			c.JSON(http.StatusOK, gin.H{
				"success": true,
				"data": []map[string]interface{}{
					{"id": "1", "name": "Cached Product 1", "price": 100.0},
				},
				"source":  "cache",
				"message": "Data from cache due to service issues",
			})
			return nil
		},
		resilience.DatabaseResilienceOptions()...,
	)
}

// CreateProductResilient demonstrates resilient product creation
func (rh *ResilientHandlers) CreateProductResilient() gin.HandlerFunc {
	return rh.WithResilience(
		"create_product",
		func(c *gin.Context) error {
			var product map[string]interface{}
			if err := c.ShouldBindJSON(&product); err != nil {
				return errors.NewValidationError("Invalid product data", err.Error())
			}

			// Validate required fields
			if name, ok := product["name"].(string); !ok || name == "" {
				return errors.NewValidationError("Product name is required")
			}

			rh.logger.Info("Creating product", zap.Any("product", product))

			// Simulate database operation
			if time.Now().Unix()%4 == 0 {
				return errors.NewServiceUnavailableError("Product creation service")
			}

			// Add ID and timestamps
			product["id"] = "new_product_id"
			product["created_at"] = time.Now()
			product["updated_at"] = time.Now()

			c.JSON(http.StatusCreated, gin.H{
				"success": true,
				"data":    product,
				"message": "Product created successfully",
			})
			return nil
		},
		resilience.DatabaseResilienceOptions()...,
	)
}

// ExternalAPICallResilient demonstrates resilient external API calls
func (rh *ResilientHandlers) ExternalAPICallResilient() gin.HandlerFunc {
	return rh.WithResilience(
		"external_api_call",
		func(c *gin.Context) error {
			rh.logger.Info("Making external API call")

			// Simulate external API call that might fail
			if time.Now().Unix()%2 == 0 {
				return resilience.NewRetryableError(
					errors.NewInternalError("External API timeout", nil),
					true,
				)
			}

			c.JSON(http.StatusOK, gin.H{
				"success": true,
				"data":    map[string]interface{}{"external_data": "success"},
				"message": "External API call successful",
			})
			return nil
		},
		resilience.ExternalAPIResilienceOptions()...,
	)
}

// HealthCheckResilient provides health check with resilience metrics
func (rh *ResilientHandlers) HealthCheckResilient() gin.HandlerFunc {
	return func(c *gin.Context) {
		healthStatus := rh.resilienceService.GetHealthStatus()

		statusCode := http.StatusOK
		if healthStatus.Status == "unhealthy" {
			statusCode = http.StatusServiceUnavailable
		} else if healthStatus.Status == "degraded" {
			statusCode = http.StatusPartialContent
		}

		c.JSON(statusCode, gin.H{
			"success": healthStatus.Status == "healthy",
			"status":  healthStatus.Status,
			"data":    healthStatus,
		})
	}
}

// MetricsResilient provides resilience metrics endpoint
func (rh *ResilientHandlers) MetricsResilient() gin.HandlerFunc {
	return func(c *gin.Context) {
		cbStats := rh.resilienceService.GetCircuitBreakerStats()
		retryStats := rh.resilienceService.GetRetryStats()

		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"data": gin.H{
				"circuit_breakers": cbStats,
				"retry_stats":      retryStats,
				"timestamp":        time.Now(),
			},
		})
	}
}

// Middleware for adding resilience to existing handlers

// ResilienceMiddleware adds resilience patterns to any handler
func (rh *ResilientHandlers) ResilienceMiddleware(operationName string, options ...resilience.ResilienceOption) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Create context with request timeout
		ctx, cancel := context.WithTimeout(c.Request.Context(), 30*time.Second)
		defer cancel()

		// Store original context
		c.Request = c.Request.WithContext(ctx)

		// Execute next handler with resilience
		err := rh.resilienceService.ExecuteWithResilience(
			ctx,
			operationName,
			func() error {
				c.Next()
				// Check if there were any errors in the handler chain
				if len(c.Errors) > 0 {
					return c.Errors.Last()
				}
				return nil
			},
			options...,
		)

		// Handle any errors
		if err != nil {
			rh.errorHandler.HandleError(c, err)
		}
	}
}

// Example of how to use the resilience middleware
func (rh *ResilientHandlers) SetupResilientRoutes(router *gin.Engine) {
	// API routes with resilience
	api := router.Group("/api/v1")

	// Products with database resilience
	products := api.Group("/products")
	products.Use(rh.ResilienceMiddleware("products", resilience.DatabaseResilienceOptions()...))
	{
		products.GET("", rh.GetProductsResilient())
		products.POST("", rh.CreateProductResilient())
	}

	// External API calls with network resilience
	external := api.Group("/external")
	external.Use(rh.ResilienceMiddleware("external_api", resilience.ExternalAPIResilienceOptions()...))
	{
		external.GET("/data", rh.ExternalAPICallResilient())
	}

	// Health and metrics endpoints
	router.GET("/health", rh.HealthCheckResilient())
	router.GET("/metrics/resilience", rh.MetricsResilient())
}

// Helper function to create resilient handler wrapper
func CreateResilientHandler(
	resilienceService *resilience.ResilienceService,
	errorHandler *errors.CentralizedErrorHandler,
	logger *zap.Logger,
) *ResilientHandlers {
	return NewResilientHandlers(resilienceService, errorHandler, logger)
}
