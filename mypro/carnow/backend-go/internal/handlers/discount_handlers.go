package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"carnow-backend/internal/domain"
	"carnow-backend/internal/services"
)

// DiscountHandler handles discount-related HTTP requests
type DiscountHandler struct {
	discountService *services.DiscountService
	logger          *zap.Logger
}

// NewDiscountHandler creates a new discount handler
func NewDiscountHandler(discountService *services.DiscountService, logger *zap.Logger) *DiscountHandler {
	return &DiscountHandler{
		discountService: discountService,
		logger:          logger,
	}
}

// GetActiveDiscounts returns all active discounts
// @Summary Get active discounts
// @Description Get a list of all active discounts
// @Tags Discounts
// @Accept json
// @Produce json
// @Success 200 {array} domain.Discount
// @Router /api/v1/discounts [get]
func (h *DiscountHandler) GetActiveDiscounts(c *gin.Context) {
	discounts, err := h.discountService.GetActiveDiscounts(c.Request.Context())
	if err != nil {
		h.logger.Error("Failed to get active discounts", zap.Error(err))
		c.<PERSON>(http.StatusInternalServerError, gin.H{"error": "Failed to fetch discounts"})
		return
	}

	c.JSON(http.StatusOK, discounts)
}

// ValidateDiscountCode validates a discount code
// @Summary Validate discount code
// @Description Validate a discount code without applying it
// @Tags Discounts
// @Accept json
// @Produce json
// @Param code query string true "Discount code"
// @Success 200 {object} domain.DiscountCode
// @Failure 400 {object} gin.H
// @Router /api/v1/discounts/validate [get]
func (h *DiscountHandler) ValidateDiscountCode(c *gin.Context) {
	code := c.Query("code")
	if code == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Discount code is required"})
		return
	}

	discountCode, err := h.discountService.ValidateDiscountCode(c.Request.Context(), code)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, discountCode)
}

// CalculateDiscount calculates discount for a given amount
// @Summary Calculate discount
// @Description Calculate discount for a given amount and optional discount code
// @Tags Discounts
// @Accept json
// @Produce json
// @Param amount query number true "Original amount"
// @Param code query string false "Discount code"
// @Success 200 {object} domain.DiscountCalculation
// @Failure 400 {object} gin.H
// @Router /api/v1/discounts/calculate [get]
func (h *DiscountHandler) CalculateDiscount(c *gin.Context) {
	amountStr := c.Query("amount")
	if amountStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Amount is required"})
		return
	}

	amount, err := strconv.ParseFloat(amountStr, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid amount"})
		return
	}

	code := c.Query("code")
	var discountCode *domain.DiscountCode

	if code != "" {
		discountCode, err = h.discountService.GetDiscountByCode(c.Request.Context(), code)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}
	}

	calculation, err := h.discountService.CalculateDiscount(c.Request.Context(), amount, discountCode)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, calculation)
}

// ApplyDiscount applies a discount and records usage
// @Summary Apply discount
// @Description Apply a discount code and record its usage
// @Tags Discounts
// @Accept json
// @Produce json
// @Param request body ApplyDiscountRequest true "Discount application request"
// @Success 200 {object} domain.DiscountCalculation
// @Failure 400 {object} gin.H
// @Failure 401 {object} gin.H
// @Router /api/v1/discounts/apply [post]
func (h *DiscountHandler) ApplyDiscount(c *gin.Context) {
	// Get user ID from JWT context
	userIDStr, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	var req ApplyDiscountRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	if req.Amount <= 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Amount must be greater than 0"})
		return
	}

	var discountCode *domain.DiscountCode
	var err error

	if req.DiscountCode != "" {
		discountCode, err = h.discountService.GetDiscountByCode(c.Request.Context(), req.DiscountCode)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}
	}

	calculation, err := h.discountService.ApplyDiscount(
		c.Request.Context(),
		userIDStr.(string),
		req.Amount,
		discountCode,
		req.SubscriptionID,
	)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, calculation)
}

// GetYearlySubscriptionDiscount returns the automatic yearly subscription discount
// @Summary Get yearly subscription discount
// @Description Get the automatic discount applied to yearly subscriptions
// @Tags Discounts
// @Accept json
// @Produce json
// @Success 200 {object} domain.Discount
// @Router /api/v1/discounts/yearly-subscription [get]
func (h *DiscountHandler) GetYearlySubscriptionDiscount(c *gin.Context) {
	discount, err := h.discountService.GetYearlySubscriptionDiscount(c.Request.Context())
	if err != nil {
		h.logger.Error("Failed to get yearly subscription discount", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch yearly discount"})
		return
	}

	c.JSON(http.StatusOK, discount)
}

// ApplyDiscountRequest represents the request body for applying a discount
type ApplyDiscountRequest struct {
	Amount         float64 `json:"amount" binding:"required,gt=0"`
	DiscountCode   string  `json:"discount_code"`
	SubscriptionID *uint   `json:"subscription_id"`
}
