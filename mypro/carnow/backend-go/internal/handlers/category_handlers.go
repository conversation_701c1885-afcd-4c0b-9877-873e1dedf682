package handlers

import (
	"database/sql"
	"log"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// MainCategory represents a main category
type MainCategory struct {
	ID             string  `json:"id"`
	PartCategoryID string  `json:"part_category_id"`
	NameEn         string  `json:"name_en"`
	NameAr         string  `json:"name_ar"`
	DescriptionEn  *string `json:"description_en,omitempty"`
	DescriptionAr  *string `json:"description_ar,omitempty"`
	IsActive       bool    `json:"is_active"`
	SortOrder      int     `json:"sort_order"`
	CreatedAt      string  `json:"created_at"`
	UpdatedAt      string  `json:"updated_at"`
}

// CategoryAttribute represents a category attribute
type CategoryAttribute struct {
	ID              string                 `json:"id"`
	MainCategoryID  string                 `json:"main_category_id"`
	AttributeNameEn string                 `json:"attribute_name_en"`
	AttributeNameAr string                 `json:"attribute_name_ar"`
	AttributeType   string                 `json:"attribute_type"`
	IsRequired      bool                   `json:"is_required"`
	IsSearchable    bool                   `json:"is_searchable"`
	IsFilterable    bool                   `json:"is_filterable"`
	SortOrder       int                    `json:"sort_order"`
	ValidationRules map[string]interface{} `json:"validation_rules,omitempty"`
	DefaultValue    *string                `json:"default_value,omitempty"`
	CreatedAt       string                 `json:"created_at"`
	UpdatedAt       string                 `json:"updated_at"`
}

// GetMainCategories handles GET /api/v1/main-categories
func (api *CleanAPI) GetMainCategories(c *gin.Context) {
	ctx := c.Request.Context()

	partCategoryID := c.Query("part_category_id")

	query := `
		SELECT 
			id, part_category_id, name_en, name_ar, description_en, description_ar,
			is_active, sort_order, created_at, updated_at
		FROM main_categories 
		WHERE is_deleted = false
	`

	args := []interface{}{}

	if partCategoryID != "" {
		query += " AND part_category_id = $1"
		args = append(args, partCategoryID)
	}

	query += " ORDER BY sort_order, name_en"

	rows, err := api.DB.Query(ctx, query, args...)
	if err != nil {
		log.Printf("Failed to query main categories: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error"})
		return
	}
	defer rows.Close()

	var categories []MainCategory
	for rows.Next() {
		var category MainCategory

		err := rows.Scan(
			&category.ID, &category.PartCategoryID, &category.NameEn, &category.NameAr,
			&category.DescriptionEn, &category.DescriptionAr, &category.IsActive,
			&category.SortOrder, &category.CreatedAt, &category.UpdatedAt,
		)

		if err != nil {
			log.Printf("Failed to scan main category: %v", err)
			continue
		}

		categories = append(categories, category)
	}

	c.JSON(http.StatusOK, gin.H{"data": categories})
}

// GetCategoryAttributes handles GET /api/v1/category-attributes/:mainCategoryId
func (api *CleanAPI) GetCategoryAttributes(c *gin.Context) {
	ctx := c.Request.Context()
	mainCategoryID := c.Param("mainCategoryId")

	query := `
		SELECT 
			id, main_category_id, attribute_name_en, attribute_name_ar, attribute_type,
			is_required, is_searchable, is_filterable, sort_order, validation_rules,
			default_value, created_at, updated_at
		FROM category_attributes 
		WHERE main_category_id = $1 AND is_deleted = false
		ORDER BY sort_order
	`

	rows, err := api.DB.Query(ctx, query, mainCategoryID)
	if err != nil {
		log.Printf("Failed to query category attributes: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error"})
		return
	}
	defer rows.Close()

	var attributes []CategoryAttribute
	for rows.Next() {
		var attribute CategoryAttribute
		var validationRules sql.NullString

		err := rows.Scan(
			&attribute.ID, &attribute.MainCategoryID, &attribute.AttributeNameEn,
			&attribute.AttributeNameAr, &attribute.AttributeType, &attribute.IsRequired,
			&attribute.IsSearchable, &attribute.IsFilterable, &attribute.SortOrder,
			&validationRules, &attribute.DefaultValue, &attribute.CreatedAt, &attribute.UpdatedAt,
		)

		if err != nil {
			log.Printf("Failed to scan category attribute: %v", err)
			continue
		}

		// Parse validation rules if present
		if validationRules.Valid && validationRules.String != "" {
			// For now, we'll just store it as a string
			// In a real implementation, you might want to parse it as JSON
			attribute.ValidationRules = map[string]interface{}{
				"rules": validationRules.String,
			}
		}

		attributes = append(attributes, attribute)
	}

	c.JSON(http.StatusOK, gin.H{"data": attributes})
}

// CreateCategoryAttribute handles POST /api/v1/category-attributes
func (api *CleanAPI) CreateCategoryAttribute(c *gin.Context) {
	ctx := c.Request.Context()

	var request struct {
		MainCategoryID  string                 `json:"main_category_id" binding:"required"`
		AttributeNameEn string                 `json:"attribute_name_en" binding:"required"`
		AttributeNameAr string                 `json:"attribute_name_ar" binding:"required"`
		AttributeType   string                 `json:"attribute_type" binding:"required"`
		IsRequired      bool                   `json:"is_required"`
		IsSearchable    bool                   `json:"is_searchable"`
		IsFilterable    bool                   `json:"is_filterable"`
		SortOrder       int                    `json:"sort_order"`
		ValidationRules map[string]interface{} `json:"validation_rules"`
		DefaultValue    *string                `json:"default_value"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Validate attribute type
	validTypes := []string{"text", "number", "select", "boolean", "date", "email", "url"}
	isValidType := false
	for _, validType := range validTypes {
		if request.AttributeType == validType {
			isValidType = true
			break
		}
	}

	if !isValidType {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid attribute type"})
		return
	}

	// Check if main category exists
	var exists bool
	err := api.DB.QueryRow(ctx, "SELECT EXISTS(SELECT 1 FROM main_categories WHERE id = $1 AND is_deleted = false)", request.MainCategoryID).Scan(&exists)
	if err != nil {
		log.Printf("Failed to check main category existence: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error"})
		return
	}

	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Main category not found"})
		return
	}

	// Generate ID and timestamps
	attributeID := uuid.New().String()
	now := time.Now().UTC().Format(time.RFC3339)

	// Insert category attribute
	query := `
		INSERT INTO category_attributes (
			id, main_category_id, attribute_name_en, attribute_name_ar, attribute_type,
			is_required, is_searchable, is_filterable, sort_order, validation_rules,
			default_value, created_at, updated_at
		) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
		RETURNING id
	`

	validationRulesJSON := "{}"
	if request.ValidationRules != nil {
		// In a real implementation, you would marshal this to JSON
		validationRulesJSON = "{}"
	}

	err = api.DB.QueryRow(ctx, query,
		attributeID, request.MainCategoryID, request.AttributeNameEn, request.AttributeNameAr,
		request.AttributeType, request.IsRequired, request.IsSearchable, request.IsFilterable,
		request.SortOrder, validationRulesJSON, request.DefaultValue, now, now,
	).Scan(&attributeID)

	if err != nil {
		log.Printf("Failed to create category attribute: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error"})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"id":      attributeID,
		"message": "Category attribute created successfully",
	})
}

// UpdateCategoryAttribute handles PUT /api/v1/category-attributes/:id
func (api *CleanAPI) UpdateCategoryAttribute(c *gin.Context) {
	ctx := c.Request.Context()
	attributeID := c.Param("id")

	var request struct {
		AttributeNameEn string                 `json:"attribute_name_en"`
		AttributeNameAr string                 `json:"attribute_name_ar"`
		AttributeType   string                 `json:"attribute_type"`
		IsRequired      *bool                  `json:"is_required"`
		IsSearchable    *bool                  `json:"is_searchable"`
		IsFilterable    *bool                  `json:"is_filterable"`
		SortOrder       *int                   `json:"sort_order"`
		ValidationRules map[string]interface{} `json:"validation_rules"`
		DefaultValue    *string                `json:"default_value"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Check if attribute exists
	var exists bool
	err := api.DB.QueryRow(ctx, "SELECT EXISTS(SELECT 1 FROM category_attributes WHERE id = $1 AND is_deleted = false)", attributeID).Scan(&exists)
	if err != nil {
		log.Printf("Failed to check category attribute existence: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error"})
		return
	}

	if !exists {
		c.JSON(http.StatusNotFound, gin.H{"error": "Category attribute not found"})
		return
	}

	// Build update query dynamically
	query := "UPDATE category_attributes SET updated_at = $1"
	args := []interface{}{time.Now().UTC().Format(time.RFC3339)}
	argIndex := 2

	if request.AttributeNameEn != "" {
		query += ", attribute_name_en = $" + strconv.Itoa(argIndex)
		args = append(args, request.AttributeNameEn)
		argIndex++
	}

	if request.AttributeNameAr != "" {
		query += ", attribute_name_ar = $" + strconv.Itoa(argIndex)
		args = append(args, request.AttributeNameAr)
		argIndex++
	}

	if request.AttributeType != "" {
		// Validate attribute type
		validTypes := []string{"text", "number", "select", "boolean", "date", "email", "url"}
		isValidType := false
		for _, validType := range validTypes {
			if request.AttributeType == validType {
				isValidType = true
				break
			}
		}

		if !isValidType {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid attribute type"})
			return
		}

		query += ", attribute_type = $" + strconv.Itoa(argIndex)
		args = append(args, request.AttributeType)
		argIndex++
	}

	if request.IsRequired != nil {
		query += ", is_required = $" + strconv.Itoa(argIndex)
		args = append(args, *request.IsRequired)
		argIndex++
	}

	if request.IsSearchable != nil {
		query += ", is_searchable = $" + strconv.Itoa(argIndex)
		args = append(args, *request.IsSearchable)
		argIndex++
	}

	if request.IsFilterable != nil {
		query += ", is_filterable = $" + strconv.Itoa(argIndex)
		args = append(args, *request.IsFilterable)
		argIndex++
	}

	if request.SortOrder != nil {
		query += ", sort_order = $" + strconv.Itoa(argIndex)
		args = append(args, *request.SortOrder)
		argIndex++
	}

	if request.ValidationRules != nil {
		validationRulesJSON := "{}"
		query += ", validation_rules = $" + strconv.Itoa(argIndex)
		args = append(args, validationRulesJSON)
		argIndex++
	}

	if request.DefaultValue != nil {
		query += ", default_value = $" + strconv.Itoa(argIndex)
		args = append(args, *request.DefaultValue)
		argIndex++
	}

	query += " WHERE id = $" + strconv.Itoa(argIndex)
	args = append(args, attributeID)

	err = api.DB.Exec(ctx, query, args...)
	if err != nil {
		log.Printf("Failed to update category attribute: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Category attribute updated successfully",
	})
}

// DeleteCategoryAttribute handles DELETE /api/v1/category-attributes/:id
func (api *CleanAPI) DeleteCategoryAttribute(c *gin.Context) {
	ctx := c.Request.Context()
	attributeID := c.Param("id")

	// Check if attribute exists
	var exists bool
	err := api.DB.QueryRow(ctx, "SELECT EXISTS(SELECT 1 FROM category_attributes WHERE id = $1 AND is_deleted = false)", attributeID).Scan(&exists)
	if err != nil {
		log.Printf("Failed to check category attribute existence: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error"})
		return
	}

	if !exists {
		c.JSON(http.StatusNotFound, gin.H{"error": "Category attribute not found"})
		return
	}

	// Check if attribute is used by any products
	var usedByProducts bool
	err = api.DB.QueryRow(ctx, "SELECT EXISTS(SELECT 1 FROM product_attributes WHERE category_attribute_id = $1)", attributeID).Scan(&usedByProducts)
	if err != nil {
		log.Printf("Failed to check if attribute is used by products: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error"})
		return
	}

	if usedByProducts {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Cannot delete attribute that is used by products"})
		return
	}

	// Soft delete the attribute
	err = api.DB.Exec(ctx, "UPDATE category_attributes SET is_deleted = true, updated_at = $1 WHERE id = $2", time.Now().UTC().Format(time.RFC3339), attributeID)
	if err != nil {
		log.Printf("Failed to delete category attribute: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Category attribute deleted successfully",
	})
}

// GetCategoryHierarchy handles GET /api/v1/categories/:mainCategoryId/hierarchy
func (api *CleanAPI) GetCategoryHierarchy(c *gin.Context) {
	ctx := c.Request.Context()
	mainCategoryID := c.Param("mainCategoryId")

	// Get main category
	var mainCategory MainCategory
	err := api.DB.QueryRow(ctx, `
		SELECT 
			id, part_category_id, name_en, name_ar, description_en, description_ar,
			is_active, sort_order, created_at, updated_at
		FROM main_categories 
		WHERE id = $1 AND is_deleted = false
	`, mainCategoryID).Scan(
		&mainCategory.ID, &mainCategory.PartCategoryID, &mainCategory.NameEn, &mainCategory.NameAr,
		&mainCategory.DescriptionEn, &mainCategory.DescriptionAr, &mainCategory.IsActive,
		&mainCategory.SortOrder, &mainCategory.CreatedAt, &mainCategory.UpdatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			c.JSON(http.StatusNotFound, gin.H{"error": "Main category not found"})
			return
		}
		log.Printf("Failed to get main category: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error"})
		return
	}

	// Get part category
	var partCategory struct {
		ID       string `json:"id"`
		NameEn   string `json:"name_en"`
		NameAr   string `json:"name_ar"`
		SubCatID string `json:"sub_category_id"`
	}

	err = api.DB.QueryRow(ctx, `
		SELECT 
			id, name_en, name_ar, sub_category_id
		FROM global_part_categories 
		WHERE id = $1 AND is_deleted = false
	`, mainCategory.PartCategoryID).Scan(&partCategory.ID, &partCategory.NameEn, &partCategory.NameAr, &partCategory.SubCatID)

	if err != nil {
		log.Printf("Failed to get part category: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error"})
		return
	}

	// Get sub category
	var subCategory struct {
		ID     string `json:"id"`
		NameEn string `json:"name_en"`
		NameAr string `json:"name_ar"`
	}

	err = api.DB.QueryRow(ctx, `
		SELECT 
			id, name_en, name_ar
		FROM global_sub_categories 
		WHERE id = $1 AND is_deleted = false
	`, partCategory.SubCatID).Scan(&subCategory.ID, &subCategory.NameEn, &subCategory.NameAr)

	if err != nil {
		log.Printf("Failed to get sub category: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error"})
		return
	}

	hierarchy := map[string]interface{}{
		"main_category": mainCategory,
		"part_category": partCategory,
		"sub_category":  subCategory,
		"breadcrumb": []map[string]string{
			{"id": subCategory.ID, "name": subCategory.NameAr, "type": "sub_category"},
			{"id": partCategory.ID, "name": partCategory.NameAr, "type": "part_category"},
			{"id": mainCategory.ID, "name": mainCategory.NameAr, "type": "main_category"},
		},
	}

	c.JSON(http.StatusOK, hierarchy)
}
