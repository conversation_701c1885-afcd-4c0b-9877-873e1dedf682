package handlers

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"regexp"
	"strings"
	"time"
	"unicode"

	"carnow-backend/internal/config"
	"carnow-backend/internal/shared/services"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// =============================================================================
// TASK 7: Unified Authentication Handlers (Forever Plan Compliant)
// =============================================================================

// UnifiedAuthHandlers implements Task 7 requirements with proper dependency injection
// Following Forever Plan Architecture: Flutter (UI Only) → Go API → Supabase (Data Only)
type UnifiedAuthHandlers struct {
	jwtService    *services.JWTService
	googleService *services.GoogleOAuthService
	config        *config.Config
	httpClient    *http.Client
}

// NewUnifiedAuthHandlers creates a new unified auth handler with proper dependency injection
// Task 7 Requirement: Create auth handler structure with proper dependency injection
func NewUnifiedAuthHandlers(
	jwtService *services.JWTService,
	googleService *services.GoogleOAuthService,
	config *config.Config,
) *UnifiedAuthHandlers {
	return &UnifiedAuthHandlers{
		jwtService:    jwtService,
		googleService: googleService,
		config:        config,
		httpClient: &http.Client{
			Timeout: 15 * time.Second,
		},
	}
}

// =============================================================================
// Request/Response Models for Task 7 Endpoints
// =============================================================================

// UnifiedLoginRequest represents the login request payload for Task 7
type UnifiedLoginRequest struct {
	Email    string `json:"email" binding:"required,email" validate:"required,email,max=255"`
	Password string `json:"password" binding:"required,min=6" validate:"required,min=6,max=128"`
}

// UnifiedRegisterRequest represents the registration request payload for Task 7
type UnifiedRegisterRequest struct {
	Email           string `json:"email" binding:"required,email" validate:"required,email,max=255"`
	Password        string `json:"password" binding:"required,min=6" validate:"required,min=6,max=128"`
	FirstName       string `json:"first_name" binding:"required,min=2" validate:"required,min=2,max=50"`
	LastName        string `json:"last_name" binding:"required,min=2" validate:"required,min=2,max=50"`
	ConfirmPassword string `json:"confirm_password" binding:"required" validate:"required,eqfield=Password"`
}

// UnifiedGoogleAuthRequest represents the Google OAuth request payload for Task 7
type UnifiedGoogleAuthRequest struct {
	IDToken string `json:"id_token" binding:"required" validate:"required,min=10"`
}

// UnifiedRefreshTokenRequest represents the token refresh request payload for Task 7
type UnifiedRefreshTokenRequest struct {
	RefreshToken string `json:"refresh_token" binding:"required" validate:"required,min=10"`
}

// UnifiedAuthResponse represents the unified authentication response for Task 7
type UnifiedAuthResponse struct {
	Success      bool                   `json:"success"`
	AccessToken  string                 `json:"access_token,omitempty"`
	RefreshToken string                 `json:"refresh_token,omitempty"`
	ExpiresAt    int64                  `json:"expires_at,omitempty"`
	ExpiresIn    int                    `json:"expires_in,omitempty"`
	TokenType    string                 `json:"token_type,omitempty"`
	User         map[string]interface{} `json:"user,omitempty"`
	Message      string                 `json:"message"`
	Error        string                 `json:"error,omitempty"`
	Code         string                 `json:"code,omitempty"`
}

// =============================================================================
// Task 7.1: POST /auth/login - Email/Password Authentication
// =============================================================================

// Login handles POST /auth/login endpoint with email/password validation
// Task 7 Requirement: Implement POST /auth/login endpoint with email/password validation
// Forever Plan Compliance: Go API handles ALL business logic, Supabase for data only
func (h *UnifiedAuthHandlers) Login(c *gin.Context) {
	start := time.Now()
	requestID := h.getRequestID(c)
	clientIP := c.ClientIP()
	ctx, cancel := context.WithTimeout(context.Background(), 15*time.Second)
	defer cancel()
	
	// Check rate limiting first
	if !h.checkRateLimit(c, "login") {
		h.logSecurityEvent("RATE_LIMIT_EXCEEDED", clientIP, "login", 
			fmt.Sprintf("Rate limit exceeded for request %s", requestID), c)
		c.JSON(http.StatusTooManyRequests, UnifiedAuthResponse{
			Success: false,
			Message: "تم تجاوز الحد المسموح من المحاولات. يرجى المحاولة لاحقاً",
			Error:   "rate_limit_exceeded",
			Code:    "RATE_LIMIT_EXCEEDED",
		})
		return
	}
	
	var req UnifiedLoginRequest
	
	// Bind and validate JSON request
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logSecurityEvent("INVALID_REQUEST_FORMAT", clientIP, "login", fmt.Sprintf("JSON binding error: %v", err), c)
		c.JSON(http.StatusBadRequest, UnifiedAuthResponse{
			Success: false,
			Message: "تنسيق الطلب غير صحيح",
			Error:   "invalid_request_format",
			Code:    "INVALID_REQUEST_FORMAT",
		})
		return
	}
	
	// Enhanced input validation
	validationErrors := h.validateLoginRequest(&req)
	if len(validationErrors) > 0 {
		h.logSecurityEvent("VALIDATION_FAILED", clientIP, "login", 
			fmt.Sprintf("Validation errors: %d fields", len(validationErrors)), c)
		
		c.JSON(http.StatusBadRequest, UnifiedAuthResponse{
			Success: false,
			Message: "بيانات غير صحيحة",
			Error:   "validation_failed",
			Code:    "VALIDATION_FAILED",
			User: map[string]interface{}{
				"validation_errors": validationErrors,
			},
		})
		return
	}
	
	// Sanitize inputs
	req.Email = strings.TrimSpace(strings.ToLower(req.Email))
	req.Password = strings.TrimSpace(req.Password)

	log.Printf("🔐 Unified Auth Login: Attempting login for %s", req.Email)

	// Authenticate with Supabase (Data Only)
	authResp, err := h.authenticateWithSupabase(ctx, req.Email, req.Password)
	if err != nil {
		log.Printf("❌ Unified Auth Login: Authentication failed for %s: %v", req.Email, err)
		
		// Log the full error for debugging
		log.Printf("🔍 Detailed Supabase Auth Error for %s: %v", req.Email, err)
		
		// Determine error type for proper response
		var errorCode, errorMessage string
		if strings.Contains(err.Error(), "invalid_credentials") {
			errorCode = "INVALID_CREDENTIALS"
			errorMessage = "Invalid email or password"
		} else if strings.Contains(err.Error(), "email_not_confirmed") {
			errorCode = "EMAIL_NOT_VERIFIED"
			errorMessage = "Please verify your email address"
		} else if strings.Contains(err.Error(), "too_many_requests") {
			errorCode = "RATE_LIMIT_EXCEEDED"
			errorMessage = "Too many login attempts. Please try again later"
		} else {
			errorCode = "AUTH_FAILED"
			errorMessage = "Authentication failed. Please try again"
			// Include more details in the log for debugging
			log.Printf("🔍 Supabase Auth Error Details: %v", err.Error())
		}

		c.JSON(http.StatusUnauthorized, UnifiedAuthResponse{
			Success: false,
			Error:   errorMessage,
			Code:    errorCode,
			Message: "Login failed",
		})
		return
	}

	// Generate secure JWT tokens (Business Logic in Go API)
	tokenPair, err := h.jwtService.GenerateTokenPair(
		authResp.ID,
		authResp.Email,
		"user", // Default role
	)
	if err != nil {
		log.Printf("❌ Unified Auth Login: Failed to generate tokens for %s: %v", req.Email, err)
		c.JSON(http.StatusInternalServerError, UnifiedAuthResponse{
			Success: false,
			Error:   "Failed to generate authentication tokens",
			Code:    "TOKEN_GENERATION_FAILED",
			Message: "Internal server error",
		})
		return
	}

	// Log successful authentication
	h.logAuthAttempt("email", req.Email, "SUCCESS", c)
	h.logSuccessfulAuth("email", authResp.ID, req.Email, c)
	
	// Track performance
	duration := time.Since(start)
	log.Printf("✅ Unified Auth Login: Successful login for %s (duration: %v, request: %s)", req.Email, duration, requestID)

	// Return secure tokens and user info
	c.JSON(http.StatusOK, UnifiedAuthResponse{
		Success:      true,
		AccessToken:  tokenPair.AccessToken,
		RefreshToken: tokenPair.RefreshToken,
		ExpiresAt:    tokenPair.ExpiresAt.Unix(),
		ExpiresIn:    900, // 15 minutes
		TokenType:    "Bearer",
		User: map[string]interface{}{
			"id":            authResp.ID,
			"email":         authResp.Email,
			"email_verified": authResp.EmailConfirmedAt != nil,
			"created_at":    authResp.CreatedAt,
			"last_sign_in":  authResp.LastSignInAt,
		},
		Message: "تم تسجيل الدخول بنجاح",
	})
}

// =============================================================================
// Task 7.2: POST /auth/register - User Registration
// =============================================================================

// Register handles POST /auth/register endpoint with user creation logic
// Task 7 Requirement: Create POST /auth/register endpoint with user creation logic
// Forever Plan Compliance: Go API handles validation, Supabase for data storage only
func (h *UnifiedAuthHandlers) Register(c *gin.Context) {
	start := time.Now()
	requestID := h.getRequestID(c)
	clientIP := c.ClientIP()
	ctx, cancel := context.WithTimeout(context.Background(), 15*time.Second)
	defer cancel()
	
	// Check rate limiting first
	if !h.checkRateLimit(c, "register") {
		h.logSecurityEvent("RATE_LIMIT_EXCEEDED", clientIP, "register", 
			fmt.Sprintf("Rate limit exceeded for request %s", requestID), c)
		c.JSON(http.StatusTooManyRequests, UnifiedAuthResponse{
			Success: false,
			Message: "تم تجاوز الحد المسموح من المحاولات. يرجى المحاولة لاحقاً",
			Error:   "rate_limit_exceeded",
			Code:    "RATE_LIMIT_EXCEEDED",
		})
		return
	}

	var req UnifiedRegisterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logSecurityEvent("INVALID_REQUEST_FORMAT", clientIP, "register", fmt.Sprintf("JSON binding error: %v", err), c)
		c.JSON(http.StatusBadRequest, UnifiedAuthResponse{
			Success: false,
			Message: "تنسيق الطلب غير صحيح",
			Error:   "invalid_request_format",
			Code:    "INVALID_REQUEST_FORMAT",
		})
		return
	}
	
	// Enhanced input validation
	validationErrors := h.validateRegisterRequest(&req)
	if len(validationErrors) > 0 {
		h.logSecurityEvent("VALIDATION_FAILED", clientIP, "register", 
			fmt.Sprintf("Validation errors: %d fields", len(validationErrors)), c)
		
		c.JSON(http.StatusBadRequest, UnifiedAuthResponse{
			Success: false,
			Message: "بيانات غير صحيحة",
			Error:   "validation_failed",
			Code:    "VALIDATION_FAILED",
			User: map[string]interface{}{
				"validation_errors": validationErrors,
			},
		})
		return
	}

	// Sanitize inputs (Business Logic in Go API)
	req.Email = strings.TrimSpace(strings.ToLower(req.Email))
	req.FirstName = strings.TrimSpace(req.FirstName)
	req.LastName = strings.TrimSpace(req.LastName)
	req.Password = strings.TrimSpace(req.Password)
	req.ConfirmPassword = strings.TrimSpace(req.ConfirmPassword)

	// Validate password confirmation
	if req.Password != req.ConfirmPassword {
		h.logSecurityEvent("PASSWORD_MISMATCH", clientIP, "register", 
			fmt.Sprintf("Password confirmation mismatch for %s", req.Email), c)
		c.JSON(http.StatusBadRequest, UnifiedAuthResponse{
			Success: false,
			Message: "كلمات المرور غير متطابقة",
			Error:   "password_mismatch",
			Code:    "PASSWORD_MISMATCH",
		})
		return
	}

	// Log registration attempt
	h.logAuthAttempt("email", req.Email, "ATTEMPT", c)
	log.Printf("👤 Unified Auth Register: Attempting registration for %s (request: %s)", req.Email, requestID)

	// Prepare user metadata
	userMetadata := map[string]interface{}{
		"first_name": req.FirstName,
		"last_name":  req.LastName,
		"full_name":  fmt.Sprintf("%s %s", req.FirstName, req.LastName),
		"locale":     "ar", // Default to Arabic for CarNow
		"timezone":   "Asia/Riyadh",
	}

	// Create user with Supabase (Data Storage Only)
	authResp, err := h.registerWithSupabase(ctx, req.Email, req.Password, userMetadata)
	if err != nil {
		h.logAuthAttempt("email", req.Email, "FAILED", c)
		h.logSecurityEvent("REGISTRATION_FAILED", clientIP, "register", fmt.Sprintf("Registration failed for %s: %v", req.Email, err), c)
		
		// Determine error type for proper response
		var errorCode, errorMessage string
		if strings.Contains(err.Error(), "email_address_invalid") {
			errorCode = "INVALID_EMAIL"
			errorMessage = "تنسيق عنوان البريد الإلكتروني غير صحيح"
		} else if strings.Contains(err.Error(), "password_too_short") {
			errorCode = "WEAK_PASSWORD"
			errorMessage = "يجب أن تكون كلمة المرور 6 أحرف على الأقل"
		} else if strings.Contains(err.Error(), "user_already_exists") {
			errorCode = "EMAIL_ALREADY_EXISTS"
			errorMessage = "يوجد حساب بهذا البريد الإلكتروني بالفعل"
		} else if strings.Contains(err.Error(), "signup_disabled") {
			errorCode = "SIGNUP_DISABLED"
			errorMessage = "التسجيل غير متاح حالياً"
		} else {
			errorCode = "REGISTRATION_FAILED"
			errorMessage = "فشل في التسجيل. يرجى المحاولة مرة أخرى"
		}

		c.JSON(http.StatusBadRequest, UnifiedAuthResponse{
			Success: false,
			Message: errorMessage,
			Error:   "registration_failed",
			Code:    errorCode,
		})
		return
	}

	// Generate secure JWT tokens (Business Logic in Go API)
	tokenPair, err := h.jwtService.GenerateTokenPair(
		authResp.ID,
		authResp.Email,
		"user", // Default role
	)
	if err != nil {
		log.Printf("❌ Unified Auth Register: Failed to generate tokens for %s: %v", req.Email, err)
		c.JSON(http.StatusInternalServerError, UnifiedAuthResponse{
			Success: false,
			Error:   "Failed to generate authentication tokens",
			Code:    "TOKEN_GENERATION_FAILED",
			Message: "Registration completed but login failed",
		})
		return
	}

	// Log successful registration
	h.logAuthAttempt("email", req.Email, "SUCCESS", c)
	h.logSuccessfulAuth("email", authResp.ID, req.Email, c)
	
	// Track performance
	duration := time.Since(start)
	log.Printf("✅ Unified Auth Register: Successful registration for %s (duration: %v, request: %s)", req.Email, duration, requestID)

	// Return secure tokens and user info
	c.JSON(http.StatusCreated, UnifiedAuthResponse{
		Success:      true,
		AccessToken:  tokenPair.AccessToken,
		RefreshToken: tokenPair.RefreshToken,
		ExpiresAt:    tokenPair.ExpiresAt.Unix(),
		ExpiresIn:    900, // 15 minutes
		TokenType:    "Bearer",
		User: map[string]interface{}{
			"id":            authResp.ID,
			"email":         authResp.Email,
			"first_name":    authResp.FirstName,
			"last_name":     authResp.LastName,
			"display_name":  authResp.DisplayName,
			"role":          authResp.Role,
			"created_at":    authResp.CreatedAt,
			"updated_at":    authResp.UpdatedAt,
		},
		Message: "تم إنشاء الحساب بنجاح",
	})
}

// =============================================================================
// Task 7.3: POST /auth/google - Google OAuth Token Verification
// =============================================================================

// GoogleAuth handles POST /auth/google endpoint for Google OAuth token verification
// Task 7 Requirement: Add POST /auth/google endpoint for Google OAuth token verification
// Forever Plan Compliance: Go API verifies tokens, Supabase for user data only
func (h *UnifiedAuthHandlers) GoogleAuth(c *gin.Context) {
	ctx, cancel := context.WithTimeout(context.Background(), 15*time.Second)
	defer cancel()

	var req UnifiedGoogleAuthRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Printf("❌ Unified Auth Google: Invalid request format: %v", err)
		c.JSON(http.StatusBadRequest, UnifiedAuthResponse{
			Success: false,
			Error:   "Invalid request format",
			Code:    "INVALID_REQUEST",
			Message: "Please provide a valid Google ID token",
		})
		return
	}

	log.Printf("🔍 Unified Auth Google: Verifying Google ID token")

	// Verify Google ID token (Business Logic in Go API)
	googleUser, err := h.googleService.VerifyIDToken(ctx, req.IDToken)
	if err != nil {
		log.Printf("❌ Unified Auth Google: Token verification failed: %v", err)
		c.JSON(http.StatusUnauthorized, UnifiedAuthResponse{
			Success: false,
			Error:   "Invalid Google ID token",
			Code:    "INVALID_GOOGLE_TOKEN",
			Message: "Google authentication failed",
		})
		return
	}

	log.Printf("✅ Unified Auth Google: Token verified for %s", googleUser.Email)

	// Check if user exists in Supabase (Data Only)
	existingUser, err := h.getUserByEmail(ctx, googleUser.Email)
	if err != nil && !strings.Contains(err.Error(), "user_not_found") {
		log.Printf("❌ Unified Auth Google: Database error: %v", err)
		c.JSON(http.StatusInternalServerError, UnifiedAuthResponse{
			Success: false,
			Error:   "Database error",
			Code:    "DATABASE_ERROR",
			Message: "Internal server error",
		})
		return
	}

	var userID string
	var isNewUser bool

	if existingUser == nil {
		// Create new user with Google data (Data Storage in Supabase)
		userMetadata := map[string]interface{}{
			"first_name":    googleUser.GivenName,
			"last_name":     googleUser.FamilyName,
			"full_name":     googleUser.Name,
			"picture":       googleUser.Picture,
			"locale":        googleUser.Locale,
			"auth_provider": "google",
		}

		// Generate a secure random password for Google users
		randomPassword := uuid.New().String()
		
		authResp, err := h.registerWithSupabase(ctx, googleUser.Email, randomPassword, userMetadata)
		if err != nil {
			log.Printf("❌ Unified Auth Google: Failed to create user: %v", err)
			c.JSON(http.StatusInternalServerError, UnifiedAuthResponse{
				Success: false,
				Error:   "Failed to create user account",
				Code:    "USER_CREATION_FAILED",
				Message: "Google authentication failed",
			})
			return
		}

		userID = authResp.ID
		isNewUser = true
		log.Printf("👤 Unified Auth Google: Created new user %s", googleUser.Email)
	} else {
		userID = existingUser.ID
		isNewUser = false
		log.Printf("👤 Unified Auth Google: Found existing user %s", googleUser.Email)
	}

	// Generate secure JWT tokens (Business Logic in Go API)
	tokenPair, err := h.jwtService.GenerateTokenPair(userID, googleUser.Email, "user")
	if err != nil {
		log.Printf("❌ Unified Auth Google: Failed to generate tokens: %v", err)
		c.JSON(http.StatusInternalServerError, UnifiedAuthResponse{
			Success: false,
			Error:   "Failed to generate authentication tokens",
			Code:    "TOKEN_GENERATION_FAILED",
			Message: "Google authentication failed",
		})
		return
	}

	log.Printf("✅ Unified Auth Google: Successful authentication for %s", googleUser.Email)

	// Return secure tokens and user info
	c.JSON(http.StatusOK, UnifiedAuthResponse{
		Success:      true,
		AccessToken:  tokenPair.AccessToken,
		RefreshToken: tokenPair.RefreshToken,
		ExpiresAt:    tokenPair.ExpiresAt.Unix(),
		ExpiresIn:    900, // 15 minutes
		TokenType:    "Bearer",
		User: map[string]interface{}{
			"id":            userID,
			"email":         googleUser.Email,
			"first_name":    googleUser.GivenName,
			"last_name":     googleUser.FamilyName,
			"full_name":     googleUser.Name,
			"picture":       googleUser.Picture,
			"auth_provider": "google",
			"is_new_user":   isNewUser,
			"email_verified": googleUser.VerifiedEmail,
		},
		Message: "Google authentication successful",
	})
}

// =============================================================================
// Task 7.4: POST /auth/refresh - Token Renewal
// =============================================================================

// RefreshToken handles POST /auth/refresh endpoint for token renewal
// Task 7 Requirement: Implement POST /auth/refresh endpoint for token renewal
// Forever Plan Compliance: Go API handles token logic, secure token storage
func (h *UnifiedAuthHandlers) RefreshToken(c *gin.Context) {
	_ = context.Background() // Remove unused ctx variable

	var req UnifiedRefreshTokenRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Printf("❌ Unified Auth Refresh: Invalid request format: %v", err)
		c.JSON(http.StatusBadRequest, UnifiedAuthResponse{
			Success: false,
			Error:   "Invalid request format",
			Code:    "INVALID_REQUEST",
			Message: "Please provide a valid refresh token",
		})
		return
	}

	log.Printf("🔄 Unified Auth Refresh: Processing token refresh")

	// Validate and refresh token (Business Logic in Go API)
	tokenPair, err := h.jwtService.RefreshToken(req.RefreshToken)
	if err != nil {
		log.Printf("❌ Unified Auth Refresh: Token refresh failed: %v", err)
		
		// Determine error type for proper response
		var errorCode, errorMessage string
		if strings.Contains(err.Error(), "invalid_token") {
			errorCode = "INVALID_REFRESH_TOKEN"
			errorMessage = "Invalid or expired refresh token"
		} else if strings.Contains(err.Error(), "token_expired") {
			errorCode = "REFRESH_TOKEN_EXPIRED"
			errorMessage = "Refresh token has expired"
		} else {
			errorCode = "TOKEN_REFRESH_FAILED"
			errorMessage = "Failed to refresh token"
		}

		c.JSON(http.StatusUnauthorized, UnifiedAuthResponse{
			Success: false,
			Error:   errorMessage,
			Code:    errorCode,
			Message: "Token refresh failed",
		})
		return
	}

	log.Printf("✅ Unified Auth Refresh: Token refresh successful")

	// Return new tokens
	c.JSON(http.StatusOK, UnifiedAuthResponse{
		Success:      true,
		AccessToken:  tokenPair.AccessToken,
		RefreshToken: tokenPair.RefreshToken,
		ExpiresAt:    tokenPair.ExpiresAt.Unix(),
		ExpiresIn:    900, // 15 minutes
		TokenType:    "Bearer",
		Message:      "Token refresh successful",
	})
}

// =============================================================================
// Supabase HTTP API Integration Methods
// =============================================================================

// UnifiedSupabaseUser represents a user from Supabase Auth API for Task 7
type UnifiedSupabaseUser struct {
	ID               string     `json:"id"`
	Email            string     `json:"email"`
	EmailConfirmedAt *time.Time `json:"email_confirmed_at"`
	CreatedAt        time.Time  `json:"created_at"`
	UpdatedAt        time.Time  `json:"updated_at"`
	LastSignInAt     *time.Time `json:"last_sign_in_at"`
	Role             string     `json:"role"`
	FirstName        string     `json:"first_name,omitempty"`
	LastName         string     `json:"last_name,omitempty"`
	DisplayName      string     `json:"display_name,omitempty"`
}

// UnifiedSupabaseAuthResponse represents Supabase authentication response for the unified auth system
type UnifiedSupabaseAuthResponse struct {
	AccessToken  string               `json:"access_token"`
	RefreshToken string               `json:"refresh_token"`
	ExpiresIn    int                  `json:"expires_in"`
	TokenType    string               `json:"token_type"`
	User         UnifiedSupabaseUser  `json:"user"`
}

// Comment: Using the UnifiedSupabaseAuthResponse struct defined above

// authenticateWithSupabase authenticates user with Supabase Auth API
func (h *UnifiedAuthHandlers) authenticateWithSupabase(ctx context.Context, email, password string) (*UnifiedSupabaseUser, error) {
	url := fmt.Sprintf("%s/auth/v1/token?grant_type=password", h.config.Supabase.URL)
	
	reqBody := map[string]string{
		"email":    email,
		"password": password,
	}
	
	jsonBody, err := json.Marshal(reqBody)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request body: %w", err)
	}
	
	log.Printf("🔍 Supabase Auth Request: URL=%s, Email=%s", url, email)
	
	req, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(jsonBody))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}
	
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("apikey", h.config.Supabase.AnonKey)
	
	log.Printf("🔍 Supabase Auth Headers: Content-Type=%s, apikey=%s", req.Header.Get("Content-Type"), h.config.Supabase.AnonKey)
	
	resp, err := h.httpClient.Do(req)
	if err != nil {
		log.Printf("❌ Supabase Auth Request Failed: %v", err)
		return nil, fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()
	
	log.Printf("🔍 Supabase Auth Response: Status=%d", resp.StatusCode)
	
	// Read response body for logging
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Printf("❌ Failed to read response body: %v", err)
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}
	
	log.Printf("🔍 Supabase Auth Response Body: %s", string(respBody))
	
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("authentication failed with status: %d, body: %s", resp.StatusCode, string(respBody))
	}
	
	var authResp UnifiedSupabaseAuthResponse
	if err := json.Unmarshal(respBody, &authResp); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}
	
	return &authResp.User, nil
}

// registerWithSupabase creates a new user with Supabase Auth API
func (h *UnifiedAuthHandlers) registerWithSupabase(ctx context.Context, email, password string, metadata map[string]interface{}) (*UnifiedSupabaseUser, error) {
	url := fmt.Sprintf("%s/auth/v1/signup", h.config.Supabase.URL)
	
	reqBody := map[string]interface{}{
		"email":    email,
		"password": password,
		"data":     metadata,
	}
	
	jsonBody, err := json.Marshal(reqBody)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request body: %w", err)
	}
	
	req, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(jsonBody))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}
	
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("apikey", h.config.Supabase.AnonKey)
	
	resp, err := h.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()
	
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("registration failed with status: %d", resp.StatusCode)
	}
	
	var authResp UnifiedSupabaseAuthResponse
	if err := json.NewDecoder(resp.Body).Decode(&authResp); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}
	
	return &authResp.User, nil
}

// getUserByEmail retrieves user by email from Supabase
func (h *UnifiedAuthHandlers) getUserByEmail(ctx context.Context, email string) (*UnifiedSupabaseUser, error) {
	// This would typically query the users table directly
	// For now, we'll return nil to indicate user not found
	return nil, fmt.Errorf("user_not_found")
}

// =============================================================================
// Enhanced Input Validation and Security (Task 1.3)
// =============================================================================

// ValidationError represents a validation error with Arabic message
type ValidationError struct {
	Field   string `json:"field"`
	Message string `json:"message"`
	Code    string `json:"code"`
}

// validateEmailInput performs comprehensive email validation
func (h *UnifiedAuthHandlers) validateEmailInput(email string) *ValidationError {
	if email == "" {
		return &ValidationError{
			Field:   "email",
			Message: "البريد الإلكتروني مطلوب",
			Code:    "email_required",
		}
	}

	// Check email length
	if len(email) > 255 {
		return &ValidationError{
			Field:   "email",
			Message: "البريد الإلكتروني طويل جداً (الحد الأقصى 255 حرف)",
			Code:    "email_too_long",
		}
	}

	// Email format validation using regex
	emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
	if !emailRegex.MatchString(email) {
		return &ValidationError{
			Field:   "email",
			Message: "تنسيق البريد الإلكتروني غير صحيح",
			Code:    "email_invalid_format",
		}
	}

	// Check for suspicious patterns
	if strings.Contains(email, "..") || strings.HasPrefix(email, ".") || strings.HasSuffix(email, ".") {
		return &ValidationError{
			Field:   "email",
			Message: "البريد الإلكتروني يحتوي على أحرف غير مسموحة",
			Code:    "email_suspicious_pattern",
		}
	}

	return nil
}

// validatePasswordInput performs comprehensive password validation
func (h *UnifiedAuthHandlers) validatePasswordInput(password string) *ValidationError {
	if password == "" {
		return &ValidationError{
			Field:   "password",
			Message: "كلمة المرور مطلوبة",
			Code:    "password_required",
		}
	}

	// Check password length
	if len(password) < 6 {
		return &ValidationError{
			Field:   "password",
			Message: "كلمة المرور قصيرة جداً (الحد الأدنى 6 أحرف)",
			Code:    "password_too_short",
		}
	}

	if len(password) > 128 {
		return &ValidationError{
			Field:   "password",
			Message: "كلمة المرور طويلة جداً (الحد الأقصى 128 حرف)",
			Code:    "password_too_long",
		}
	}

	// Check for common weak passwords
	weakPasswords := []string{"123456", "password", "123456789", "qwerty", "abc123", "password123"}
	for _, weak := range weakPasswords {
		if strings.ToLower(password) == weak {
			return &ValidationError{
				Field:   "password",
				Message: "كلمة المرور ضعيفة جداً، يرجى اختيار كلمة مرور أقوى",
				Code:    "password_too_weak",
			}
		}
	}

	// Check for minimum complexity (at least one letter and one number)
	hasLetter := false
	hasNumber := false
	for _, char := range password {
		if unicode.IsLetter(char) {
			hasLetter = true
		}
		if unicode.IsNumber(char) {
			hasNumber = true
		}
	}

	if !hasLetter || !hasNumber {
		return &ValidationError{
			Field:   "password",
			Message: "كلمة المرور يجب أن تحتوي على حروف وأرقام على الأقل",
			Code:    "password_insufficient_complexity",
		}
	}

	return nil
}

// validateNameInput validates first and last names
func (h *UnifiedAuthHandlers) validateNameInput(name, fieldName string) *ValidationError {
	if name == "" {
		return &ValidationError{
			Field:   fieldName,
			Message: fmt.Sprintf("%s مطلوب", fieldName),
			Code:    fieldName + "_required",
		}
	}

	if len(name) < 2 {
		return &ValidationError{
			Field:   fieldName,
			Message: fmt.Sprintf("%s قصير جداً (الحد الأدنى حرفان)", fieldName),
			Code:    fieldName + "_too_short",
		}
	}

	if len(name) > 50 {
		return &ValidationError{
			Field:   fieldName,
			Message: fmt.Sprintf("%s طويل جداً (الحد الأقصى 50 حرف)", fieldName),
			Code:    fieldName + "_too_long",
		}
	}

	// Check for valid characters (letters, spaces, Arabic characters)
	for _, char := range name {
		if !unicode.IsLetter(char) && !unicode.IsSpace(char) {
			return &ValidationError{
				Field:   fieldName,
				Message: fmt.Sprintf("%s يحتوي على أحرف غير مسموحة", fieldName),
				Code:    fieldName + "_invalid_characters",
			}
		}
	}

	return nil
}

// validateGoogleTokenInput validates Google ID token format
func (h *UnifiedAuthHandlers) validateGoogleTokenInput(token string) *ValidationError {
	if token == "" {
		return &ValidationError{
			Field:   "id_token",
			Message: "رمز Google مطلوب",
			Code:    "google_token_required",
		}
	}

	if len(token) < 10 {
		return &ValidationError{
			Field:   "id_token",
			Message: "رمز Google غير صحيح",
			Code:    "google_token_invalid",
		}
	}

	// Basic JWT format check (3 parts separated by dots)
	parts := strings.Split(token, ".")
	if len(parts) != 3 {
		return &ValidationError{
			Field:   "id_token",
			Message: "تنسيق رمز Google غير صحيح",
			Code:    "google_token_invalid_format",
		}
	}

	return nil
}

// validateRefreshTokenInput validates refresh token format
func (h *UnifiedAuthHandlers) validateRefreshTokenInput(token string) *ValidationError {
	if token == "" {
		return &ValidationError{
			Field:   "refresh_token",
			Message: "رمز التحديث مطلوب",
			Code:    "refresh_token_required",
		}
	}

	if len(token) < 10 {
		return &ValidationError{
			Field:   "refresh_token",
			Message: "رمز التحديث غير صحيح",
			Code:    "refresh_token_invalid",
		}
	}

	// Basic JWT format check
	parts := strings.Split(token, ".")
	if len(parts) != 3 {
		return &ValidationError{
			Field:   "refresh_token",
			Message: "تنسيق رمز التحديث غير صحيح",
			Code:    "refresh_token_invalid_format",
		}
	}

	return nil
}

// validateLoginRequest performs comprehensive validation for login requests
func (h *UnifiedAuthHandlers) validateLoginRequest(req *UnifiedLoginRequest) []ValidationError {
	var errors []ValidationError

	if emailErr := h.validateEmailInput(req.Email); emailErr != nil {
		errors = append(errors, *emailErr)
	}

	if passwordErr := h.validatePasswordInput(req.Password); passwordErr != nil {
		errors = append(errors, *passwordErr)
	}

	return errors
}

// validateRegisterRequest performs comprehensive validation for registration requests
func (h *UnifiedAuthHandlers) validateRegisterRequest(req *UnifiedRegisterRequest) []ValidationError {
	var errors []ValidationError

	if emailErr := h.validateEmailInput(req.Email); emailErr != nil {
		errors = append(errors, *emailErr)
	}

	if passwordErr := h.validatePasswordInput(req.Password); passwordErr != nil {
		errors = append(errors, *passwordErr)
	}

	if firstNameErr := h.validateNameInput(req.FirstName, "الاسم الأول"); firstNameErr != nil {
		errors = append(errors, *firstNameErr)
	}

	if lastNameErr := h.validateNameInput(req.LastName, "الاسم الأخير"); lastNameErr != nil {
		errors = append(errors, *lastNameErr)
	}

	// Check password confirmation
	if req.Password != req.ConfirmPassword {
		errors = append(errors, ValidationError{
			Field:   "confirm_password",
			Message: "كلمات المرور غير متطابقة",
			Code:    "password_mismatch",
		})
	}

	return errors
}

// =============================================================================
// Helper Methods for Enhanced Security and Logging
// =============================================================================

// =============================================================================
// Rate Limiting and Security Middleware (Task 1.3)
// =============================================================================

// RateLimitInfo stores rate limiting information for an IP
type RateLimitInfo struct {
	Attempts    int       `json:"attempts"`
	LastAttempt time.Time `json:"last_attempt"`
	BlockedUntil time.Time `json:"blocked_until"`
}

// In-memory rate limiting store (in production, use Redis)
var rateLimitStore = make(map[string]*RateLimitInfo)

// checkRateLimit implements comprehensive rate limiting
func (h *UnifiedAuthHandlers) checkRateLimit(c *gin.Context, endpoint string) bool {
	clientIP := c.ClientIP()
	key := fmt.Sprintf("%s:%s", clientIP, endpoint)
	
	// Get current time
	now := time.Now()
	
	// Get or create rate limit info
	info, exists := rateLimitStore[key]
	if !exists {
		info = &RateLimitInfo{
			Attempts:    0,
			LastAttempt: now,
		}
		rateLimitStore[key] = info
	}
	
	// Check if currently blocked
	if now.Before(info.BlockedUntil) {
		h.logSecurityEvent("RATE_LIMIT_BLOCKED", clientIP, endpoint, fmt.Sprintf("IP blocked until %v", info.BlockedUntil), c)
		return false
	}
	
	// Reset attempts if enough time has passed (1 minute window)
	if now.Sub(info.LastAttempt) > time.Minute {
		info.Attempts = 0
	}
	
	// Increment attempts
	info.Attempts++
	info.LastAttempt = now
	
	// Define rate limits per endpoint
	var maxAttempts int
	var blockDuration time.Duration
	
	switch endpoint {
	case "login":
		maxAttempts = 5 // 5 login attempts per minute
		blockDuration = 15 * time.Minute
	case "register":
		maxAttempts = 3 // 3 registration attempts per minute
		blockDuration = 10 * time.Minute
	case "google":
		maxAttempts = 10 // 10 Google OAuth attempts per minute
		blockDuration = 5 * time.Minute
	case "refresh":
		maxAttempts = 20 // 20 refresh attempts per minute
		blockDuration = 2 * time.Minute
	default:
		maxAttempts = 10
		blockDuration = 5 * time.Minute
	}
	
	// Check if rate limit exceeded
	if info.Attempts > maxAttempts {
		info.BlockedUntil = now.Add(blockDuration)
		h.logSecurityEvent("RATE_LIMIT_EXCEEDED", clientIP, endpoint, 
			fmt.Sprintf("Exceeded %d attempts, blocked for %v", maxAttempts, blockDuration), c)
		return false
	}
	
	// Log warning if approaching limit
	if info.Attempts >= maxAttempts-1 {
		h.logSecurityEvent("RATE_LIMIT_WARNING", clientIP, endpoint, 
			fmt.Sprintf("Approaching rate limit: %d/%d attempts", info.Attempts, maxAttempts), c)
	}
	
	return true
}

// =============================================================================
// Comprehensive Audit Logging (Task 1.3)
// =============================================================================

// AuditLogEntry represents a comprehensive audit log entry
type AuditLogEntry struct {
	Timestamp    time.Time              `json:"timestamp"`
	EventType    string                 `json:"event_type"`
	UserID       string                 `json:"user_id,omitempty"`
	Email        string                 `json:"email,omitempty"`
	ClientIP     string                 `json:"client_ip"`
	UserAgent    string                 `json:"user_agent"`
	Endpoint     string                 `json:"endpoint"`
	Method       string                 `json:"method"`
	StatusCode   int                    `json:"status_code"`
	Result       string                 `json:"result"`
	ErrorCode    string                 `json:"error_code,omitempty"`
	ErrorMessage string                 `json:"error_message,omitempty"`
	RequestID    string                 `json:"request_id"`
	Duration     time.Duration          `json:"duration"`
	Metadata     map[string]interface{} `json:"metadata,omitempty"`
}

// logAuthAttempt logs authentication attempts for security monitoring
func (h *UnifiedAuthHandlers) logAuthAttempt(method, email, result string, c *gin.Context) {
	clientIP := c.ClientIP()
	userAgent := c.GetHeader("User-Agent")
	requestID := h.getRequestID(c)
	
	// Create audit log entry
	auditEntry := AuditLogEntry{
		Timestamp:  time.Now(),
		EventType:  "AUTH_ATTEMPT",
		Email:      email,
		ClientIP:   clientIP,
		UserAgent:  userAgent,
		Endpoint:   c.FullPath(),
		Method:     c.Request.Method,
		Result:     result,
		RequestID:  requestID,
		Metadata: map[string]interface{}{
			"auth_method": method,
			"timestamp":   time.Now().Unix(),
		},
	}
	
	// Log in structured format
	auditJSON, _ := json.Marshal(auditEntry)
	log.Printf("🔐 AUTH_AUDIT: %s", string(auditJSON))
	
	// Also log in human-readable format for development
	log.Printf("🔐 Auth Attempt: Method=%s, Email=%s, Result=%s, IP=%s, RequestID=%s", 
		method, email, result, clientIP, requestID)
}

// logSecurityEvent logs security-related events
func (h *UnifiedAuthHandlers) logSecurityEvent(eventType, clientIP, endpoint, details string, c *gin.Context) {
	requestID := h.getRequestID(c)
	userAgent := c.GetHeader("User-Agent")
	
	auditEntry := AuditLogEntry{
		Timestamp: time.Now(),
		EventType: eventType,
		ClientIP:  clientIP,
		UserAgent: userAgent,
		Endpoint:  endpoint,
		Method:    c.Request.Method,
		RequestID: requestID,
		Metadata: map[string]interface{}{
			"details":   details,
			"timestamp": time.Now().Unix(),
		},
	}
	
	auditJSON, _ := json.Marshal(auditEntry)
	log.Printf("🚨 SECURITY_AUDIT: %s", string(auditJSON))
	log.Printf("🚨 Security Event: Type=%s, IP=%s, Endpoint=%s, Details=%s, RequestID=%s", 
		eventType, clientIP, endpoint, details, requestID)
}

// logSuccessfulAuth logs successful authentication events
func (h *UnifiedAuthHandlers) logSuccessfulAuth(userID, email, authMethod string, c *gin.Context) {
	clientIP := c.ClientIP()
	userAgent := c.GetHeader("User-Agent")
	requestID := h.getRequestID(c)
	
	auditEntry := AuditLogEntry{
		Timestamp: time.Now(),
		EventType: "AUTH_SUCCESS",
		UserID:    userID,
		Email:     email,
		ClientIP:  clientIP,
		UserAgent: userAgent,
		Endpoint:  c.FullPath(),
		Method:    c.Request.Method,
		Result:    "SUCCESS",
		RequestID: requestID,
		Metadata: map[string]interface{}{
			"auth_method": authMethod,
			"timestamp":   time.Now().Unix(),
			"user_id":     userID,
		},
	}
	
	auditJSON, _ := json.Marshal(auditEntry)
	log.Printf("✅ AUTH_SUCCESS_AUDIT: %s", string(auditJSON))
	log.Printf("✅ Successful Auth: UserID=%s, Email=%s, Method=%s, IP=%s, RequestID=%s", 
		userID, email, authMethod, clientIP, requestID)
}

// getRequestID generates or retrieves request ID for tracing
func (h *UnifiedAuthHandlers) getRequestID(c *gin.Context) string {
	// Try to get existing request ID from header
	if requestID := c.GetHeader("X-Request-ID"); requestID != "" {
		return requestID
	}
	
	// Generate new request ID
	requestID := uuid.New().String()
	c.Header("X-Request-ID", requestID)
	return requestID
}

// validateRequestRate implements basic rate limiting check
func (h *UnifiedAuthHandlers) validateRequestRate(c *gin.Context) bool {
	// TODO: Implement Redis-based rate limiting in Task 9
	// For now, return true to allow all requests
	return true
}

// sanitizeUserData sanitizes user data before returning to client
func (h *UnifiedAuthHandlers) sanitizeUserData(userData map[string]interface{}) map[string]interface{} {
	// Remove sensitive fields that should not be exposed to client
	sanitized := make(map[string]interface{})
	
	allowedFields := []string{
		"id", "email", "first_name", "last_name", "full_name", 
		"picture", "auth_provider", "is_new_user", "email_verified",
		"created_at", "last_sign_in", "locale", "timezone",
	}
	
	for _, field := range allowedFields {
		if value, exists := userData[field]; exists {
			sanitized[field] = value
		}
	}
	
	return sanitized
}
