package handlers

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strings"

	"carnow-backend/internal/config"
	"carnow-backend/internal/core/domain"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// Note: SupabaseUser and SupabaseAuthResponse are defined in auth_handlers.go

// JWTMiddleware validates JWT tokens from Supabase and auto-creates users
func JWTMiddleware(cfg *config.Config) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get token from Authorization header
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Authorization header required"})
			c.Abort()
			return
		}

		// Extract Bearer token
		tokenParts := strings.Split(authHeader, " ")
		if len(tokenParts) != 2 || tokenParts[0] != "Bearer" {
			c.<PERSON>(http.StatusUnauthorized, gin.H{"error": "Invalid authorization header format"})
			c.Abort()
			return
		}

		tokenString := tokenParts[1]

		// Validate token with Supabase Auth API
		user, err := validateTokenWithSupabase(cfg, tokenString)
		if err != nil {
			fmt.Printf("Token validation error: %v\n", err)
			c.JSON(http.StatusUnauthorized, gin.H{"error": fmt.Sprintf("Invalid token: %v", err)})
			c.Abort()
			return
		}

		// Auto-create user if doesn't exist
		if err := ensureUserExists(c, user); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to ensure user exists"})
			c.Abort()
			return
		}

		// Set user information in context
		c.Set("user_id", user.ID)
		c.Set("user_email", user.Email)
		c.Set("user_role", user.Role)
		c.Next()
	}
}

// validateTokenWithSupabase validates JWT token by calling Supabase Auth API
func validateTokenWithSupabase(cfg *config.Config, token string) (*SupabaseUser, error) {
	// Create request to Supabase Auth API
	url := fmt.Sprintf("%s/auth/v1/user", cfg.Supabase.URL)

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %v", err)
	}

	// Set headers
	req.Header.Set("Authorization", "Bearer "+token)
	req.Header.Set("apikey", cfg.Supabase.AnonKey)

	// Make request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to validate token: %v", err)
	}
	defer resp.Body.Close()

	// Check response status
	if resp.StatusCode != 200 {
		return nil, fmt.Errorf("invalid token - status: %d", resp.StatusCode)
	}

	// Parse response
	var supabaseUser SupabaseUser
	if err := json.NewDecoder(resp.Body).Decode(&supabaseUser); err != nil {
		return nil, fmt.Errorf("failed to parse user data: %v", err)
	}

	// Set default role if not provided
	if supabaseUser.Role == "" {
		supabaseUser.Role = "authenticated"
	}

	fmt.Printf("✅ Token validated for user: %s (%s)\n", supabaseUser.Email, supabaseUser.ID)
	return &supabaseUser, nil
}

// ensureUserExists checks if user exists in Go backend, creates if not
func ensureUserExists(c *gin.Context, user *SupabaseUser) error {
	db, exists := c.Get("db")
	if !exists {
		return fmt.Errorf("database connection not found in context")
	}

	gormDB := db.(*gorm.DB)

	// Check if user exists
	var existingUser domain.User
	result := gormDB.Where("id = ?", user.ID).First(&existingUser)

	if result.Error == nil {
		// User exists, no need to create
		fmt.Printf("✅ User exists in Go backend: %s\n", user.Email)
		return nil
	}

	if result.Error != gorm.ErrRecordNotFound {
		return fmt.Errorf("database error: %v", result.Error)
	}

	// User doesn't exist, create new user
	userID, err := uuid.Parse(user.ID)
	if err != nil {
		return fmt.Errorf("invalid user ID format: %v", err)
	}

	// Extract username from email
	usernameStr := strings.Split(user.Email, "@")[0]

	newUser := domain.User{
		ID:       userID,
		Email:    user.Email,
		Username: &usernameStr, // Use email prefix as username
		// We don't have full_name from Supabase user endpoint, so we'll leave it empty for now
	}

	// Create user in transaction
	err = gormDB.Transaction(func(tx *gorm.DB) error {
		// Create user
		if err := tx.Create(&newUser).Error; err != nil {
			return err
		}

		// Create wallet for user
		wallet := domain.Wallet{
			UserID:  userID,
			Balance: 0.0,
		}

		if err := tx.Create(&wallet).Error; err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return fmt.Errorf("failed to create user and wallet: %v", err)
	}

	fmt.Printf("✅ Auto-created user in Go backend: %s (ID: %s)\n", user.Email, user.ID)
	fmt.Printf("✅ Created wallet for user: %s\n", user.ID)

	return nil
}

// OptionalJWTMiddleware allows both authenticated and non-authenticated requests
func OptionalJWTMiddleware(cfg *config.Config) gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.GetHeader("Authorization")

		// If no auth header, continue without setting user context
		if authHeader == "" {
			c.Next()
			return
		}

		// If auth header exists, validate it
		tokenParts := strings.Split(authHeader, " ")
		if len(tokenParts) != 2 || tokenParts[0] != "Bearer" {
			c.Next()
			return
		}

		tokenString := tokenParts[1]
		user, err := validateTokenWithSupabase(cfg, tokenString)

		// If valid token, set user context
		if err == nil {
			// Auto-create user if doesn't exist (optional middleware)
			_ = ensureUserExists(c, user)

			c.Set("user_id", user.ID)
			c.Set("user_email", user.Email)
			c.Set("user_role", user.Role)
		}

		c.Next()
	}
}
