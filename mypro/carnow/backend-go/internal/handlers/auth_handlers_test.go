package handlers

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"carnow-backend/internal/config"
	"carnow-backend/internal/infrastructure/database"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func setupTestAuthHandlers(t *testing.T) (*AuthHandlers, *gin.Engine) {
	// Create test config
	cfg := &config.Config{
		Google: config.GoogleConfig{
			ClientID:     "test-client-id.apps.googleusercontent.com",
			ClientSecret: "test-client-secret",
		},
		Supabase: config.SupabaseConfig{
			URL:            "https://test.supabase.co",
			Anon<PERSON><PERSON>:        "test-anon-key",
			ServiceRoleKey: "test-service-role-key",
			JWTSecret:      "test-jwt-secret",
		},
		JWT: config.JWTConfig{
			Secret:    "test-jwt-secret",
			Algorithm: "HS256",
			Issuer:    "test-issuer",
			Audience:  "test-audience",
		},
	}

	// Create test database (mock)
	db := &database.SimpleDB{} // This would be a mock in real tests

	// Create auth handlers
	authHandlers, err := NewAuthHandlers(cfg, db)
	require.NoError(t, err)

	// Setup Gin router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	return authHandlers, router
}

func TestAuthHandlers_GoogleOAuth_InvalidRequest(t *testing.T) {
	authHandlers, router := setupTestAuthHandlers(t)
	router.POST("/auth/google", authHandlers.GoogleOAuth)

	tests := []struct {
		name           string
		requestBody    interface{}
		expectedStatus int
		expectedError  string
	}{
		{
			name:           "Missing ID token",
			requestBody:    map[string]string{},
			expectedStatus: http.StatusBadRequest,
			expectedError:  "Invalid request format",
		},
		{
			name:           "Empty ID token",
			requestBody:    map[string]string{"id_token": ""},
			expectedStatus: http.StatusBadRequest,
			expectedError:  "Invalid request format",
		},
		{
			name:           "Invalid JSON",
			requestBody:    "invalid-json",
			expectedStatus: http.StatusBadRequest,
			expectedError:  "Invalid request format",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var body []byte
			var err error

			if str, ok := tt.requestBody.(string); ok {
				body = []byte(str)
			} else {
				body, err = json.Marshal(tt.requestBody)
				require.NoError(t, err)
			}

			req, err := http.NewRequest("POST", "/auth/google", bytes.NewBuffer(body))
			require.NoError(t, err)
			req.Header.Set("Content-Type", "application/json")

			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)

			var response map[string]interface{}
			err = json.Unmarshal(w.Body.Bytes(), &response)
			require.NoError(t, err)

			assert.Equal(t, false, response["success"])
			assert.Contains(t, response["error"], tt.expectedError)
		})
	}
}

func TestAuthHandlers_GoogleOAuth_InvalidToken(t *testing.T) {
	authHandlers, router := setupTestAuthHandlers(t)
	router.POST("/auth/google", authHandlers.GoogleOAuth)

	requestBody := map[string]string{
		"id_token": "invalid-google-id-token",
	}

	body, err := json.Marshal(requestBody)
	require.NoError(t, err)

	req, err := http.NewRequest("POST", "/auth/google", bytes.NewBuffer(body))
	require.NoError(t, err)
	req.Header.Set("Content-Type", "application/json")

	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Should return 401 for invalid token
	assert.Equal(t, http.StatusUnauthorized, w.Code)

	var response map[string]interface{}
	err = json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	assert.Equal(t, false, response["success"])
	assert.Equal(t, "GOOGLE_AUTH_FAILED", response["code"])
	assert.Contains(t, response["error"], "Google authentication failed")
}

func TestAuthHandlers_Login_InvalidRequest(t *testing.T) {
	authHandlers, router := setupTestAuthHandlers(t)
	router.POST("/auth/login", authHandlers.Login)

	tests := []struct {
		name           string
		requestBody    interface{}
		expectedStatus int
		expectedError  string
	}{
		{
			name:           "Missing email",
			requestBody:    map[string]string{"password": "password123"},
			expectedStatus: http.StatusBadRequest,
			expectedError:  "Invalid request format",
		},
		{
			name:           "Missing password",
			requestBody:    map[string]string{"email": "<EMAIL>"},
			expectedStatus: http.StatusBadRequest,
			expectedError:  "Invalid request format",
		},
		{
			name:           "Invalid email format",
			requestBody:    map[string]string{"email": "invalid-email", "password": "password123"},
			expectedStatus: http.StatusBadRequest,
			expectedError:  "Invalid request format",
		},
		{
			name:           "Short password",
			requestBody:    map[string]string{"email": "<EMAIL>", "password": "123"},
			expectedStatus: http.StatusBadRequest,
			expectedError:  "Invalid request format",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			body, err := json.Marshal(tt.requestBody)
			require.NoError(t, err)

			req, err := http.NewRequest("POST", "/auth/login", bytes.NewBuffer(body))
			require.NoError(t, err)
			req.Header.Set("Content-Type", "application/json")

			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)

			var response map[string]interface{}
			err = json.Unmarshal(w.Body.Bytes(), &response)
			require.NoError(t, err)

			assert.Equal(t, false, response["success"])
			assert.Contains(t, response["error"], tt.expectedError)
		})
	}
}

func TestAuthHandlers_RefreshToken_InvalidRequest(t *testing.T) {
	authHandlers, router := setupTestAuthHandlers(t)
	router.POST("/auth/refresh", authHandlers.RefreshToken)

	tests := []struct {
		name           string
		requestBody    interface{}
		expectedStatus int
		expectedError  string
	}{
		{
			name:           "Missing refresh token",
			requestBody:    map[string]string{},
			expectedStatus: http.StatusBadRequest,
			expectedError:  "Invalid request format",
		},
		{
			name:           "Empty refresh token",
			requestBody:    map[string]string{"refresh_token": ""},
			expectedStatus: http.StatusBadRequest,
			expectedError:  "Invalid request format",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			body, err := json.Marshal(tt.requestBody)
			require.NoError(t, err)

			req, err := http.NewRequest("POST", "/auth/refresh", bytes.NewBuffer(body))
			require.NoError(t, err)
			req.Header.Set("Content-Type", "application/json")

			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)

			var response map[string]interface{}
			err = json.Unmarshal(w.Body.Bytes(), &response)
			require.NoError(t, err)

			assert.Equal(t, false, response["success"])
			assert.Contains(t, response["error"], tt.expectedError)
		})
	}
}

func TestAuthHandlers_RefreshToken_InvalidToken(t *testing.T) {
	authHandlers, router := setupTestAuthHandlers(t)
	router.POST("/auth/refresh", authHandlers.RefreshToken)

	requestBody := map[string]string{
		"refresh_token": "invalid-refresh-token",
	}

	body, err := json.Marshal(requestBody)
	require.NoError(t, err)

	req, err := http.NewRequest("POST", "/auth/refresh", bytes.NewBuffer(body))
	require.NoError(t, err)
	req.Header.Set("Content-Type", "application/json")

	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Should return 401 for invalid refresh token
	assert.Equal(t, http.StatusUnauthorized, w.Code)

	var response map[string]interface{}
	err = json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	assert.Equal(t, false, response["success"])
	assert.Equal(t, "REFRESH_FAILED", response["code"])
	assert.Contains(t, response["error"], "Invalid refresh token")
}

func TestAuthHandlers_Logout_Success(t *testing.T) {
	authHandlers, router := setupTestAuthHandlers(t)
	router.POST("/auth/logout", authHandlers.Logout)

	req, err := http.NewRequest("POST", "/auth/logout", nil)
	require.NoError(t, err)

	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err = json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	assert.Equal(t, true, response["success"])
	assert.Equal(t, "Logout successful", response["message"])
}

func TestAuthHandlers_GetPublicKey_Success(t *testing.T) {
	authHandlers, router := setupTestAuthHandlers(t)
	router.GET("/auth/public-key", authHandlers.GetPublicKey)

	req, err := http.NewRequest("GET", "/auth/public-key", nil)
	require.NoError(t, err)

	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// This might fail if JWT service is not properly initialized
	// In a real test, we'd mock the JWT service
	var response map[string]interface{}
	err = json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	// Check that we get some response (success or error)
	assert.Contains(t, response, "success")
}

// Security-focused tests
func TestAuthHandlers_SecurityHeaders(t *testing.T) {
	authHandlers, router := setupTestAuthHandlers(t)
	router.POST("/auth/google", authHandlers.GoogleOAuth)

	requestBody := map[string]string{
		"id_token": "test-token",
	}

	body, err := json.Marshal(requestBody)
	require.NoError(t, err)

	req, err := http.NewRequest("POST", "/auth/google", bytes.NewBuffer(body))
	require.NoError(t, err)
	req.Header.Set("Content-Type", "application/json")

	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Verify that sensitive information is not leaked in error responses
	var response map[string]interface{}
	err = json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	// Should not contain internal error details
	if errorMsg, exists := response["error"]; exists {
		errorStr := errorMsg.(string)
		assert.NotContains(t, errorStr, "internal")
		assert.NotContains(t, errorStr, "database")
		assert.NotContains(t, errorStr, "secret")
		assert.NotContains(t, errorStr, "key")
	}
}

func TestAuthHandlers_RateLimitingHeaders(t *testing.T) {
	// This test would verify rate limiting headers if implemented
	// For now, we just test that the endpoint responds
	authHandlers, router := setupTestAuthHandlers(t)
	router.POST("/auth/google", authHandlers.GoogleOAuth)

	requestBody := map[string]string{
		"id_token": "test-token",
	}

	body, err := json.Marshal(requestBody)
	require.NoError(t, err)

	req, err := http.NewRequest("POST", "/auth/google", bytes.NewBuffer(body))
	require.NoError(t, err)
	req.Header.Set("Content-Type", "application/json")

	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Verify response is received (rate limiting would be tested separately)
	assert.NotEqual(t, 0, w.Code)
}

// Advanced Security Tests for Production Excellence
func TestAuthHandlers_SecurityInjectionAttempts(t *testing.T) {
	authHandlers, router := setupTestAuthHandlers(t)
	router.POST("/auth/google", authHandlers.GoogleOAuth)

	maliciousPayloads := []struct {
		name    string
		payload map[string]any
	}{
		{
			name: "SQL Injection in ID Token",
			payload: map[string]any{
				"id_token": "'; DROP TABLE users; --",
			},
		},
		{
			name: "XSS in ID Token",
			payload: map[string]any{
				"id_token": "<script>alert('xss')</script>",
			},
		},
		{
			name: "Command Injection",
			payload: map[string]any{
				"id_token": "; rm -rf /; echo 'pwned'",
			},
		},
		{
			name: "LDAP Injection",
			payload: map[string]any{
				"id_token": "*)(uid=*))(|(uid=*",
			},
		},
		{
			name: "NoSQL Injection",
			payload: map[string]any{
				"id_token": map[string]any{"$ne": ""},
			},
		},
	}

	for _, tt := range maliciousPayloads {
		t.Run(tt.name, func(t *testing.T) {
			body, err := json.Marshal(tt.payload)
			require.NoError(t, err)

			req, err := http.NewRequest("POST", "/auth/google", bytes.NewBuffer(body))
			require.NoError(t, err)
			req.Header.Set("Content-Type", "application/json")

			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			// Should handle malicious input gracefully
			assert.True(t, w.Code >= 400 && w.Code < 500, "Should return client error for malicious input")

			var response map[string]any
			err = json.Unmarshal(w.Body.Bytes(), &response)
			require.NoError(t, err)

			// Should not leak internal information
			if errorMsg, exists := response["error"]; exists {
				errorStr := fmt.Sprintf("%v", errorMsg)
				assert.NotContains(t, errorStr, "internal")
				assert.NotContains(t, errorStr, "database")
				assert.NotContains(t, errorStr, "secret")
			}
		})
	}
}

func TestAuthHandlers_ConcurrentRequests(t *testing.T) {
	authHandlers, router := setupTestAuthHandlers(t)
	router.POST("/auth/google", authHandlers.GoogleOAuth)

	const numGoroutines = 10
	const numRequestsPerGoroutine = 5

	results := make(chan int, numGoroutines*numRequestsPerGoroutine)

	for i := 0; i < numGoroutines; i++ {
		go func(goroutineID int) {
			for j := 0; j < numRequestsPerGoroutine; j++ {
				requestBody := map[string]string{
					"id_token": fmt.Sprintf("test-token-%d-%d", goroutineID, j),
				}

				body, err := json.Marshal(requestBody)
				if err != nil {
					results <- 500
					continue
				}

				req, err := http.NewRequest("POST", "/auth/google", bytes.NewBuffer(body))
				if err != nil {
					results <- 500
					continue
				}
				req.Header.Set("Content-Type", "application/json")

				w := httptest.NewRecorder()
				router.ServeHTTP(w, req)
				results <- w.Code
			}
		}(i)
	}

	// Collect all results
	for i := 0; i < numGoroutines*numRequestsPerGoroutine; i++ {
		statusCode := <-results
		// Should handle concurrent requests without crashing
		assert.True(t, statusCode > 0, "Should return valid HTTP status code")
	}
}

func TestAuthHandlers_LargePayloadHandling(t *testing.T) {
	authHandlers, router := setupTestAuthHandlers(t)
	router.POST("/auth/google", authHandlers.GoogleOAuth)

	// Test with very large payload (potential DoS)
	largeToken := string(make([]byte, 1024*1024)) // 1MB token
	requestBody := map[string]string{
		"id_token": largeToken,
	}

	body, err := json.Marshal(requestBody)
	require.NoError(t, err)

	req, err := http.NewRequest("POST", "/auth/google", bytes.NewBuffer(body))
	require.NoError(t, err)
	req.Header.Set("Content-Type", "application/json")

	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Should handle large payloads gracefully (either accept or reject cleanly)
	assert.True(t, w.Code >= 400 && w.Code < 600, "Should return valid error for large payload")
}

func TestAuthHandlers_InvalidContentType(t *testing.T) {
	authHandlers, router := setupTestAuthHandlers(t)
	router.POST("/auth/google", authHandlers.GoogleOAuth)

	requestBody := `{"id_token": "test-token"}`

	tests := []struct {
		name        string
		contentType string
	}{
		{"XML Content Type", "application/xml"},
		{"Plain Text", "text/plain"},
		{"Form Data", "application/x-www-form-urlencoded"},
		{"Empty Content Type", ""},
		{"Invalid Content Type", "invalid/type"},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req, err := http.NewRequest("POST", "/auth/google", bytes.NewBufferString(requestBody))
			require.NoError(t, err)

			if tt.contentType != "" {
				req.Header.Set("Content-Type", tt.contentType)
			}

			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			// Should handle different content types appropriately
			assert.True(t, w.Code >= 400 && w.Code < 500, "Should return client error for invalid content type")
		})
	}
}

func TestAuthHandlers_HTTPMethodSecurity(t *testing.T) {
	authHandlers, router := setupTestAuthHandlers(t)
	router.POST("/auth/google", authHandlers.GoogleOAuth)

	methods := []string{"GET", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS"}

	for _, method := range methods {
		t.Run(fmt.Sprintf("Method_%s", method), func(t *testing.T) {
			req, err := http.NewRequest(method, "/auth/google", nil)
			require.NoError(t, err)

			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			// Should only allow POST method
			assert.Equal(t, http.StatusMethodNotAllowed, w.Code,
				"Should not allow %s method", method)
		})
	}
}

func TestAuthHandlers_ResponseHeaderSecurity(t *testing.T) {
	authHandlers, router := setupTestAuthHandlers(t)
	router.POST("/auth/google", authHandlers.GoogleOAuth)

	requestBody := map[string]string{
		"id_token": "test-token",
	}

	body, err := json.Marshal(requestBody)
	require.NoError(t, err)

	req, err := http.NewRequest("POST", "/auth/google", bytes.NewBuffer(body))
	require.NoError(t, err)
	req.Header.Set("Content-Type", "application/json")

	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Check for security headers (these would be added by middleware in production)
	headers := w.Header()

	// Content-Type should be set
	assert.Contains(t, headers.Get("Content-Type"), "application/json")

	// Should not expose server information
	assert.Empty(t, headers.Get("Server"), "Should not expose server information")
	assert.Empty(t, headers.Get("X-Powered-By"), "Should not expose technology stack")
}
