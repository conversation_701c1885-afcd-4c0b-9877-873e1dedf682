package handlers

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"strings"
	"time"

	"carnow-backend/internal/config"
	"carnow-backend/internal/infrastructure/database"
	"carnow-backend/internal/shared/services"

	"github.com/gin-gonic/gin"
)

// AuthHandlers handles authentication-related endpoints
type AuthHandlers struct {
	config             *config.Config
	db                 *database.SimpleDB
	jwtService         *services.JWTService
	googleOAuthService *services.GoogleOAuthService
}

// NewAuthHandlers creates new authentication handlers
func NewAuthHandlers(cfg *config.Config, db *database.SimpleDB) (*AuthHandlers, error) {
	jwtService, err := services.NewJWTService(cfg)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize JWT service: %w", err)
	}

	// Initialize Google OAuth service with client ID and secret from config
	googleOAuthService, err := services.NewGoogleOAuthService(cfg.Google.ClientID, cfg.Google.ClientSecret)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize Google OAuth service: %w", err)
	}

	return &AuthHandlers{
		config:             cfg,
		db:                 db,
		jwtService:         jwtService,
		googleOAuthService: googleOAuthService,
	}, nil
}

// LoginRequest represents the login request payload
type LoginRequest struct {
	Email    string `json:"email" binding:"required,email"`
	Password string `json:"password" binding:"required,min=6"`
}

// LoginResponse represents the login response
type LoginResponse struct {
	Success      bool                   `json:"success"`
	AccessToken  string                 `json:"access_token"`
	RefreshToken string                 `json:"refresh_token"`
	ExpiresAt    time.Time              `json:"expires_at"`
	TokenType    string                 `json:"token_type"`
	User         map[string]interface{} `json:"user"`
	Message      string                 `json:"message"`
}

// RefreshRequest represents the token refresh request
type RefreshRequest struct {
	RefreshToken string `json:"refresh_token" binding:"required"`
}

// Login handles user authentication and returns secure JWT tokens
func (a *AuthHandlers) Login(c *gin.Context) {
	var req LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Printf("❌ Auth Login: Invalid request format: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Invalid request format",
			"code":    "INVALID_REQUEST",
			"message": err.Error(),
		})
		return
	}

	log.Printf("🔐 Auth Login: Attempting login for %s", req.Email)

	// Authenticate with Supabase
	user, err := a.authenticateWithSupabase(req.Email, req.Password)
	if err != nil {
		log.Printf("❌ Auth Login: Authentication failed for %s: %v", req.Email, err)
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"error":   "Authentication failed",
			"code":    "AUTH_FAILED",
			"message": "Invalid email or password",
		})
		return
	}

	// Generate secure JWT token pair
	tokenPair, err := a.jwtService.GenerateTokenPair(user.ID, user.Email, user.Role)
	if err != nil {
		log.Printf("❌ Auth Login: Failed to generate tokens for %s: %v", req.Email, err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to generate authentication tokens",
			"code":    "TOKEN_GENERATION_FAILED",
			"message": "Internal server error",
		})
		return
	}

	log.Printf("✅ Auth Login: Successful login for %s", req.Email)

	// Return secure tokens
	c.JSON(http.StatusOK, LoginResponse{
		Success:      true,
		AccessToken:  tokenPair.AccessToken,
		RefreshToken: tokenPair.RefreshToken,
		ExpiresAt:    tokenPair.ExpiresAt,
		TokenType:    tokenPair.TokenType,
		User: map[string]interface{}{
			"id":    user.ID,
			"email": user.Email,
			"role":  user.Role,
		},
		Message: "Login successful",
	})
}

// GoogleOAuth handles Google OAuth authentication
func (a *AuthHandlers) GoogleOAuth(c *gin.Context) {
	var req struct {
		IDToken string `json:"id_token" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		log.Printf("❌ Auth GoogleOAuth: Invalid request format: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Invalid request format",
			"code":    "INVALID_REQUEST",
			"message": err.Error(),
		})
		return
	}

	log.Printf("🔍 Auth GoogleOAuth: Processing Google OAuth request")

	// Verify Google ID token with Google's servers using fallback mechanism
	ctx, cancel := context.WithTimeout(c.Request.Context(), 10*time.Second)
	defer cancel()

	googleUser, err := a.googleOAuthService.VerifyIDTokenWithFallback(ctx, req.IDToken)
	if err != nil {
		log.Printf("❌ Auth GoogleOAuth: Google token verification failed: %v", err)
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"error":   "Google authentication failed",
			"code":    "GOOGLE_AUTH_FAILED",
			"message": "Invalid Google ID token",
		})
		return
	}

	// Check if user exists in our system or create new user
	user, err := a.findOrCreateGoogleUser(googleUser)
	if err != nil {
		log.Printf("❌ Auth GoogleOAuth: Failed to find/create user: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to process user account",
			"code":    "USER_PROCESSING_FAILED",
			"message": "Internal server error",
		})
		return
	}

	// Generate secure JWT token pair
	tokenPair, err := a.jwtService.GenerateTokenPair(user.ID, user.Email, user.Role)
	if err != nil {
		log.Printf("❌ Auth GoogleOAuth: Failed to generate tokens for %s: %v", user.Email, err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to generate authentication tokens",
			"code":    "TOKEN_GENERATION_FAILED",
			"message": "Internal server error",
		})
		return
	}

	log.Printf("✅ Auth GoogleOAuth: Successful Google OAuth for user: %s", user.Email)

	// Return response in expected format
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"access_token":  tokenPair.AccessToken,
			"refresh_token": tokenPair.RefreshToken,
			"user": gin.H{
				"id":    user.ID,
				"email": user.Email,
				"name":  user.Name,
				"role":  user.Role,
			},
		},
		"message": "Google authentication successful",
	})
}

// RefreshToken handles token refresh requests
func (a *AuthHandlers) RefreshToken(c *gin.Context) {
	var req RefreshRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Printf("❌ Auth Refresh: Invalid request format: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Invalid request format",
			"code":    "INVALID_REQUEST",
			"message": err.Error(),
		})
		return
	}

	log.Printf("🔄 Auth Refresh: Attempting token refresh")

	// Refresh tokens using JWT service
	tokenPair, err := a.jwtService.RefreshToken(req.RefreshToken)
	if err != nil {
		log.Printf("❌ Auth Refresh: Token refresh failed: %v", err)
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"error":   "Invalid refresh token",
			"code":    "REFRESH_FAILED",
			"message": "Please login again",
		})
		return
	}

	log.Printf("✅ Auth Refresh: Token refresh successful")

	c.JSON(http.StatusOK, LoginResponse{
		Success:      true,
		AccessToken:  tokenPair.AccessToken,
		RefreshToken: tokenPair.RefreshToken,
		ExpiresAt:    tokenPair.ExpiresAt,
		TokenType:    tokenPair.TokenType,
		Message:      "Token refresh successful",
	})
}

// Logout handles user logout and token revocation
func (a *AuthHandlers) Logout(c *gin.Context) {
	// Get token ID from context (set by middleware)
	tokenID, exists := c.Get("token_id")
	if exists && tokenID != "" {
		// Revoke the token
		if err := a.jwtService.RevokeToken(tokenID.(string)); err != nil {
			log.Printf("⚠️ Auth Logout: Failed to revoke token: %v", err)
		}
	}

	log.Printf("✅ Auth Logout: User logged out successfully")

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Logout successful",
	})
}

// GetPublicKey returns the JWT public key for external validation
func (a *AuthHandlers) GetPublicKey(c *gin.Context) {
	publicKeyPEM, err := a.jwtService.GetPublicKeyPEM()
	if err != nil {
		log.Printf("❌ Auth GetPublicKey: Failed to get public key: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to retrieve public key",
			"code":    "PUBLIC_KEY_ERROR",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success":    true,
		"public_key": publicKeyPEM,
		"algorithm":  "RS256",
		"key_id":     "carnow-jwt-key-1",
	})
}

// SupabaseAuthResponse represents Supabase authentication response
type SupabaseAuthResponse struct {
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
	ExpiresIn    int    `json:"expires_in"`
	TokenType    string `json:"token_type"`
	User         struct {
		ID    string `json:"id"`
		Email string `json:"email"`
		Role  string `json:"role"`
	} `json:"user"`
}

// SupabaseUser represents user data from Supabase
type SupabaseUser struct {
	ID    string `json:"id"`
	Email string `json:"email"`
	Name  string `json:"name"`
	Role  string `json:"role"`
}

// authenticateWithSupabase authenticates user credentials with Supabase
func (a *AuthHandlers) authenticateWithSupabase(email, password string) (*SupabaseUser, error) {
	// Create authentication request to Supabase
	url := fmt.Sprintf("%s/auth/v1/token?grant_type=password", a.config.Supabase.URL)

	payload := map[string]string{
		"email":    email,
		"password": password,
	}

	payloadBytes, err := json.Marshal(payload)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	// Create HTTP request
	req, err := http.NewRequest("POST", url, strings.NewReader(string(payloadBytes)))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("apikey", a.config.Supabase.AnonKey)

	// Make request with timeout
	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("authentication request failed: %w", err)
	}
	defer resp.Body.Close()

	// Check response status
	if resp.StatusCode != 200 {
		return nil, fmt.Errorf("authentication failed - status: %d", resp.StatusCode)
	}

	// Parse response
	var authResp SupabaseAuthResponse
	if err := json.NewDecoder(resp.Body).Decode(&authResp); err != nil {
		return nil, fmt.Errorf("failed to parse authentication response: %w", err)
	}

	// Validate response
	if authResp.User.ID == "" || authResp.User.Email == "" {
		return nil, fmt.Errorf("invalid user data in response")
	}

	// Set default role if not provided
	role := authResp.User.Role
	if role == "" {
		role = "authenticated"
	}

	return &SupabaseUser{
		ID:    authResp.User.ID,
		Email: authResp.User.Email,
		Role:  role,
	}, nil
}

// findOrCreateGoogleUser finds existing user or creates new user from Google OAuth data
func (a *AuthHandlers) findOrCreateGoogleUser(googleUser *services.GoogleUserInfo) (*SupabaseUser, error) {
	log.Printf("🔍 Auth: Finding or creating user for Google account: %s", googleUser.Email)

	// First, try to find existing user in Supabase by email
	existingUser, err := a.findUserByEmail(googleUser.Email)
	if err == nil && existingUser != nil {
		log.Printf("✅ Auth: Found existing user: %s", existingUser.Email)
		return existingUser, nil
	}

	// User doesn't exist, create new user in Supabase
	log.Printf("🔍 Auth: Creating new user for Google account: %s", googleUser.Email)

	newUser, err := a.createGoogleUser(googleUser)
	if err != nil {
		return nil, fmt.Errorf("failed to create Google user: %w", err)
	}

	log.Printf("✅ Auth: Successfully created new user: %s", newUser.Email)
	return newUser, nil
}

// findUserByEmail finds a user by email in Supabase
func (a *AuthHandlers) findUserByEmail(email string) (*SupabaseUser, error) {
	// Query Supabase to find user by email
	url := fmt.Sprintf("%s/rest/v1/auth.users?email=eq.%s&select=id,email,raw_user_meta_data",
		a.config.Supabase.URL, email)

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Set headers for Supabase API
	req.Header.Set("apikey", a.config.Supabase.ServiceRoleKey)
	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", a.config.Supabase.ServiceRoleKey))
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to query user: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode == 404 {
		return nil, fmt.Errorf("user not found")
	}

	if resp.StatusCode != 200 {
		return nil, fmt.Errorf("failed to query user - status: %d", resp.StatusCode)
	}

	var users []struct {
		ID              string                 `json:"id"`
		Email           string                 `json:"email"`
		RawUserMetaData map[string]interface{} `json:"raw_user_meta_data"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&users); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	if len(users) == 0 {
		return nil, fmt.Errorf("user not found")
	}

	user := users[0]
	name := email // Default to email
	if user.RawUserMetaData != nil {
		if fullName, ok := user.RawUserMetaData["full_name"].(string); ok && fullName != "" {
			name = fullName
		}
	}

	return &SupabaseUser{
		ID:    user.ID,
		Email: user.Email,
		Name:  name,
		Role:  "authenticated",
	}, nil
}

// createGoogleUser creates a new user in Supabase from Google OAuth data
func (a *AuthHandlers) createGoogleUser(googleUser *services.GoogleUserInfo) (*SupabaseUser, error) {
	// Create user in Supabase Auth
	url := fmt.Sprintf("%s/auth/v1/admin/users", a.config.Supabase.URL)

	userData := map[string]interface{}{
		"email":         googleUser.Email,
		"email_confirm": true,
		"user_metadata": map[string]interface{}{
			"full_name":   googleUser.Name,
			"avatar_url":  googleUser.Picture,
			"provider":    "google",
			"provider_id": googleUser.ID,
		},
	}

	payloadBytes, err := json.Marshal(userData)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal user data: %w", err)
	}

	req, err := http.NewRequest("POST", url, strings.NewReader(string(payloadBytes)))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Set headers for Supabase Admin API
	req.Header.Set("apikey", a.config.Supabase.ServiceRoleKey)
	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", a.config.Supabase.ServiceRoleKey))
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to create user: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 && resp.StatusCode != 201 {
		return nil, fmt.Errorf("failed to create user - status: %d", resp.StatusCode)
	}

	var createdUser struct {
		ID           string                 `json:"id"`
		Email        string                 `json:"email"`
		UserMetadata map[string]interface{} `json:"user_metadata"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&createdUser); err != nil {
		return nil, fmt.Errorf("failed to decode created user: %w", err)
	}

	name := googleUser.Name
	if name == "" {
		name = googleUser.Email
	}

	return &SupabaseUser{
		ID:    createdUser.ID,
		Email: createdUser.Email,
		Name:  name,
		Role:  "authenticated",
	}, nil
}
