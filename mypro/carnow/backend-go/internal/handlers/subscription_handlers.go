package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"carnow-backend/internal/services"
)

// SubscriptionHandler handles subscription-related HTTP requests
type SubscriptionHandler struct {
	subscriptionService *services.SubscriptionService
	logger              *zap.Logger
}

// NewSubscriptionHandler creates a new subscription handler
func NewSubscriptionHandler(subscriptionService *services.SubscriptionService, logger *zap.Logger) *SubscriptionHandler {
	return &SubscriptionHandler{
		subscriptionService: subscriptionService,
		logger:              logger,
	}
}

// GetSubscriptionPlans returns all active subscription plans
// @Summary Get subscription plans
// @Description Get a list of all active subscription plans
// @Tags Subscriptions
// @Accept json
// @Produce json
// @Success 200 {array} domain.SubscriptionPlan
// @Router /api/v1/subscription-plans [get]
func (h *SubscriptionHandler) GetSubscriptionPlans(c *gin.Context) {
	plans, err := h.subscriptionService.GetActiveSubscriptionPlans(c.Request.Context())
	if err != nil {
		h.logger.Error("Failed to get subscription plans", zap.Error(err))
		c.<PERSON>(http.StatusInternalServerError, gin.H{"error": "Failed to fetch subscription plans"})
		return
	}

	c.JSON(http.StatusOK, plans)
}

// GetSubscriptionPlan returns a specific subscription plan
// @Summary Get subscription plan
// @Description Get a specific subscription plan by ID
// @Tags Subscriptions
// @Accept json
// @Produce json
// @Param id path int true "Plan ID"
// @Success 200 {object} domain.SubscriptionPlan
// @Failure 404 {object} gin.H
// @Router /api/v1/subscription-plans/{id} [get]
func (h *SubscriptionHandler) GetSubscriptionPlan(c *gin.Context) {
	planIDStr := c.Param("id")
	planID, err := strconv.ParseUint(planIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid plan ID"})
		return
	}

	plan, err := h.subscriptionService.GetSubscriptionPlanByID(c.Request.Context(), uint(planID))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, plan)
}

// CalculateSubscriptionPrice calculates the price for a subscription
// @Summary Calculate subscription price
// @Description Calculate the price for a subscription with optional discount
// @Tags Subscriptions
// @Accept json
// @Produce json
// @Param plan_id query int true "Plan ID"
// @Param billing_cycle query string true "Billing cycle (monthly/yearly)"
// @Param discount_code query string false "Discount code"
// @Success 200 {object} domain.DiscountCalculation
// @Failure 400 {object} gin.H
// @Router /api/v1/subscriptions/calculate-price [get]
func (h *SubscriptionHandler) CalculateSubscriptionPrice(c *gin.Context) {
	planIDStr := c.Query("plan_id")
	if planIDStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Plan ID is required"})
		return
	}

	planID, err := strconv.ParseUint(planIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid plan ID"})
		return
	}

	billingCycle := c.Query("billing_cycle")
	if billingCycle == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Billing cycle is required"})
		return
	}

	if billingCycle != "monthly" && billingCycle != "yearly" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Billing cycle must be 'monthly' or 'yearly'"})
		return
	}

	discountCode := c.Query("discount_code")

	calculation, err := h.subscriptionService.CalculateSubscriptionPrice(
		c.Request.Context(),
		uint(planID),
		billingCycle,
		discountCode,
	)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, calculation)
}

// CreateSubscription creates a new subscription
// @Summary Create subscription
// @Description Create a new subscription for the authenticated user
// @Tags Subscriptions
// @Accept json
// @Produce json
// @Param request body CreateSubscriptionRequest true "Subscription creation request"
// @Success 201 {object} domain.Subscription
// @Failure 400 {object} gin.H
// @Failure 401 {object} gin.H
// @Router /api/v1/subscriptions [post]
func (h *SubscriptionHandler) CreateSubscription(c *gin.Context) {
	// Get user ID from JWT context
	userIDStr, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	var req CreateSubscriptionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	if req.PlanID == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Plan ID is required"})
		return
	}

	if req.BillingCycle == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Billing cycle is required"})
		return
	}

	if req.BillingCycle != "monthly" && req.BillingCycle != "yearly" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Billing cycle must be 'monthly' or 'yearly'"})
		return
	}

	subscription, err := h.subscriptionService.CreateSubscription(
		c.Request.Context(),
		userIDStr.(string),
		req.PlanID,
		req.BillingCycle,
		req.DiscountCode,
	)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, subscription)
}

// GetUserSubscription returns the active subscription for the authenticated user
// @Summary Get user subscription
// @Description Get the active subscription for the authenticated user
// @Tags Subscriptions
// @Accept json
// @Produce json
// @Success 200 {object} domain.Subscription
// @Failure 401 {object} gin.H
// @Router /api/v1/subscriptions/my [get]
func (h *SubscriptionHandler) GetUserSubscription(c *gin.Context) {
	// Get user ID from JWT context
	userIDStr, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	subscription, err := h.subscriptionService.GetUserActiveSubscription(c.Request.Context(), userIDStr.(string))
	if err != nil {
		h.logger.Error("Failed to get user subscription", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch subscription"})
		return
	}

	if subscription == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "No active subscription found"})
		return
	}

	c.JSON(http.StatusOK, subscription)
}

// CancelSubscription cancels the user's active subscription
// @Summary Cancel subscription
// @Description Cancel the active subscription for the authenticated user
// @Tags Subscriptions
// @Accept json
// @Produce json
// @Success 200 {object} gin.H
// @Failure 401 {object} gin.H
// @Router /api/v1/subscriptions/cancel [post]
func (h *SubscriptionHandler) CancelSubscription(c *gin.Context) {
	// Get user ID from JWT context
	userIDStr, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	err := h.subscriptionService.CancelSubscription(c.Request.Context(), userIDStr.(string))
	if err != nil {
		h.logger.Error("Failed to cancel subscription", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to cancel subscription"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Subscription cancelled successfully"})
}

// CreateSubscriptionRequest represents the request body for creating a subscription
type CreateSubscriptionRequest struct {
	PlanID       uint   `json:"plan_id" binding:"required"`
	BillingCycle string `json:"billing_cycle" binding:"required"`
	DiscountCode string `json:"discount_code"`
}
