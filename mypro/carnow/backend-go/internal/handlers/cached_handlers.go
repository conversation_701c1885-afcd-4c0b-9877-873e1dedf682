package handlers

import (
	"context"
	"database/sql"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"carnow-backend/internal/infrastructure/cache"
	"carnow-backend/internal/infrastructure/database"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// CachedAPI provides handlers with caching capabilities
type CachedAPI struct {
	DB           *database.SimpleDB
	CacheService *cache.CacheService
	Logger       *zap.Logger
	FallbackMode bool
}

// NewCachedAPI creates a new cached API handler
func NewCachedAPI(db *database.SimpleDB, logger *zap.Logger) *CachedAPI {
	cacheProvider := cache.GetCacheProvider()

	return &CachedAPI{
		DB:           db,
		CacheService: cacheProvider.GetService(),
		Logger:       logger,
		FallbackMode: db == nil,
	}
}

// GetProductsWithCache demonstrates cache-aside pattern for products
func (api *CachedAPI) GetProductsWithCache(c *gin.Context) {
	// Parse pagination parameters
	page, _ := strconv.Atoi(c.<PERSON><PERSON>("page", "1"))
	limit, _ := strconv.Atoi(c.<PERSON>("limit", "20"))
	category := c.Query("category")

	// Generate cache key
	cacheKey := fmt.Sprintf("products:page:%d:limit:%d:category:%s", page, limit, category)

	// Try to get from cache first
	var cachedProducts interface{}
	if api.CacheService.IsEnabled() {
		if err := api.CacheService.Get(c.Request.Context(), cacheKey, &cachedProducts); err == nil {
			// Cache hit
			api.Logger.Debug("Cache hit for products", zap.String("key", cacheKey))
			c.Header("X-Cache", "HIT")
			c.JSON(http.StatusOK, gin.H{
				"success": true,
				"data":    cachedProducts,
				"cached":  true,
			})
			return
		}
	}

	// Cache miss - fetch from database
	api.Logger.Debug("Cache miss for products", zap.String("key", cacheKey))

	// Simulate database fetch (in real implementation, this would query the database)
	products := api.fetchProductsFromDB(c.Request.Context(), page, limit, category)

	// Cache the result for future requests
	if api.CacheService.IsEnabled() {
		ttl := 10 * time.Minute // Cache products for 10 minutes
		if err := api.CacheService.Set(c.Request.Context(), cacheKey, products, ttl); err != nil {
			api.Logger.Error("Failed to cache products", zap.Error(err))
		}
	}

	c.Header("X-Cache", "MISS")
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    products,
		"cached":  false,
	})
}

// GetCategoriesWithCache demonstrates static data caching for categories
func (api *CachedAPI) GetCategoriesWithCache(c *gin.Context) {
	cacheKey := "categories:all"

	// Use GetOrSet pattern for cleaner code
	var categories interface{}

	if api.CacheService.IsEnabled() {
		err := api.CacheService.GetOrSet(
			c.Request.Context(),
			cacheKey,
			&categories,
			func() (interface{}, error) {
				// This function is called only on cache miss
				api.Logger.Debug("Fetching categories from database")
				return api.fetchCategoriesFromDB(c.Request.Context()), nil
			},
			30*time.Minute, // Cache categories for 30 minutes
		)

		if err != nil {
			api.Logger.Error("Cache operation failed", zap.Error(err))
			// Fallback to direct database fetch
			categories = api.fetchCategoriesFromDB(c.Request.Context())
		}
	} else {
		// Cache disabled - fetch directly
		categories = api.fetchCategoriesFromDB(c.Request.Context())
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    categories,
	})
}

// GetUserProfileWithCache demonstrates user-specific caching
func (api *CachedAPI) GetUserProfileWithCache(c *gin.Context) {
	userID := c.GetString("user_id") // Assuming this is set by auth middleware
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"error":   "User not authenticated",
		})
		return
	}

	cacheKey := cache.UserKey(userID)

	var userProfile interface{}
	if api.CacheService.IsEnabled() {
		err := api.CacheService.GetOrSet(
			c.Request.Context(),
			cacheKey,
			&userProfile,
			func() (interface{}, error) {
				api.Logger.Debug("Fetching user profile from database", zap.String("user_id", userID))
				return api.fetchUserProfileFromDB(c.Request.Context(), userID), nil
			},
			5*time.Minute, // Cache user profile for 5 minutes
		)

		if err != nil {
			api.Logger.Error("Cache operation failed for user profile", zap.Error(err))
			userProfile = api.fetchUserProfileFromDB(c.Request.Context(), userID)
		}
	} else {
		userProfile = api.fetchUserProfileFromDB(c.Request.Context(), userID)
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    userProfile,
	})
}

// InvalidateProductCache demonstrates cache invalidation
func (api *CachedAPI) InvalidateProductCache(c *gin.Context) {
	if !api.CacheService.IsEnabled() {
		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"message": "Cache is disabled",
		})
		return
	}

	// Invalidate all product-related cache entries
	pattern := "products:*"
	if err := api.CacheService.InvalidatePattern(c.Request.Context(), pattern); err != nil {
		api.Logger.Error("Failed to invalidate product cache", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to invalidate cache",
		})
		return
	}

	api.Logger.Info("Product cache invalidated", zap.String("pattern", pattern))
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Product cache invalidated successfully",
	})
}

// GetCacheMetrics returns cache performance metrics
func (api *CachedAPI) GetCacheMetrics(c *gin.Context) {
	if !api.CacheService.IsEnabled() {
		c.JSON(http.StatusOK, gin.H{
			"enabled": false,
			"message": "Cache is disabled",
		})
		return
	}

	metrics := api.CacheService.GetMetrics()
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    metrics,
	})
}

// WarmCache manually triggers cache warming
func (api *CachedAPI) WarmCache(c *gin.Context) {
	if !api.CacheService.IsEnabled() {
		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"message": "Cache is disabled",
		})
		return
	}

	// Trigger cache warming in background
	go func() {
		if err := api.CacheService.WarmCache(context.Background()); err != nil {
			api.Logger.Error("Cache warming failed", zap.Error(err))
		} else {
			api.Logger.Info("Cache warming completed successfully")
		}
	}()

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Cache warming started",
	})
}

// Helper methods to simulate database operations

func (api *CachedAPI) fetchProductsFromDB(ctx context.Context, page, limit int, category string) interface{} {
	// Forever Plan Architecture Compliance: Use real database when available
	if api.FallbackMode || api.DB == nil {
		return map[string]interface{}{
			"error": "Service temporarily unavailable. Database connection required.",
			"products": []map[string]interface{}{},
			"pagination": map[string]interface{}{
				"page":     page,
				"limit":    limit,
				"total":    0,
				"has_more": false,
			},
		}
	}

	// Real database query for products
	query := `
		SELECT 
			id, main_category_id, name_en, name_ar, description_en, description_ar,
			manufacturer_part_number, oem_part_numbers, brand, condition_type, fitment_type,
			core_charge, manufacturer_warranty, price, sale_price, currency, stock_quantity,
			min_order_quantity, weight_kg, dimensions_cm, country_of_origin, surface_finish,
			is_active, is_featured, is_bestseller, sort_order, created_at, updated_at,
			created_by, updated_by
		FROM products 
		WHERE is_active = true AND is_deleted = false
	`

	args := []interface{}{}
	argIndex := 1

	if category != "" && category != "all" {
		query += fmt.Sprintf(" AND main_category_id = $%d", argIndex)
		args = append(args, category)
		argIndex++
	}

	// Add pagination
	query += fmt.Sprintf(" ORDER BY sort_order, created_at DESC LIMIT $%d OFFSET $%d", argIndex, argIndex+1)
	args = append(args, limit, (page-1)*limit)

	rows, err := api.DB.Query(ctx, query, args...)
	if err != nil {
		return map[string]interface{}{
			"error": "Failed to fetch products from database",
			"products": []map[string]interface{}{},
			"pagination": map[string]interface{}{
				"page":     page,
				"limit":    limit,
				"total":    0,
				"has_more": false,
			},
		}
	}
	defer rows.Close()

	var products []map[string]interface{}
	for rows.Next() {
		var (
			id                     string
			mainCategoryID         string
			nameEn                 string
			nameAr                 string
			descriptionEn          sql.NullString
			descriptionAr          sql.NullString
			manufacturerPartNumber sql.NullString
			oemPartNumbers         sql.NullString
			brand                  sql.NullString
			conditionType          string
			fitmentType            sql.NullString
			coreCharge             float64
			manufacturerWarranty   sql.NullString
			price                  sql.NullFloat64
			salePrice              sql.NullFloat64
			currency               string
			stockQuantity          int
			minOrderQuantity       int
			weightKg               sql.NullFloat64
			dimensionsCm           sql.NullString
			countryOfOrigin        sql.NullString
			surfaceFinish          sql.NullString
			isActive               bool
			isFeatured             bool
			isBestseller           bool
			sortOrder              int
			createdAt              string
			updatedAt              string
			createdBy              sql.NullString
			updatedBy              sql.NullString
		)

		err := rows.Scan(
			&id, &mainCategoryID, &nameEn, &nameAr,
			&descriptionEn, &descriptionAr, &manufacturerPartNumber,
			&oemPartNumbers, &brand, &conditionType, &fitmentType,
			&coreCharge, &manufacturerWarranty, &price,
			&salePrice, &currency, &stockQuantity,
			&minOrderQuantity, &weightKg, &dimensionsCm,
			&countryOfOrigin, &surfaceFinish, &isActive,
			&isFeatured, &isBestseller, &sortOrder,
			&createdAt, &updatedAt, &createdBy, &updatedBy,
		)

		if err != nil {
			continue // Skip problematic rows
		}

		product := map[string]interface{}{
			"id":                      id,
			"main_category_id":        mainCategoryID,
			"name_en":                 nameEn,
			"name_ar":                 nameAr,
			"description_en":          descriptionEn.String,
			"description_ar":          descriptionAr.String,
			"manufacturer_part_number": manufacturerPartNumber.String,
			"brand":                   brand.String,
			"condition_type":          conditionType,
			"fitment_type":            fitmentType.String,
			"core_charge":             coreCharge,
			"manufacturer_warranty":   manufacturerWarranty.String,
			"price":                   price.Float64,
			"sale_price":              salePrice.Float64,
			"currency":                currency,
			"stock_quantity":          stockQuantity,
			"min_order_quantity":      minOrderQuantity,
			"weight_kg":               weightKg.Float64,
			"dimensions_cm":           dimensionsCm.String,
			"country_of_origin":       countryOfOrigin.String,
			"surface_finish":          surfaceFinish.String,
			"is_active":               isActive,
			"is_featured":             isFeatured,
			"is_bestseller":           isBestseller,
			"sort_order":              sortOrder,
			"created_at":              createdAt,
			"updated_at":              updatedAt,
			"created_by":              createdBy.String,
			"updated_by":              updatedBy.String,
		}

		// Handle OEM part numbers as array
		if oemPartNumbers.Valid && oemPartNumbers.String != "" {
			product["oem_part_numbers"] = strings.Split(oemPartNumbers.String, ",")
		} else {
			product["oem_part_numbers"] = []string{}
		}

		products = append(products, product)
	}

	// Get total count for pagination
	countQuery := `SELECT COUNT(*) FROM products WHERE is_active = true AND is_deleted = false`
	countArgs := []interface{}{}
	countArgIndex := 1

	if category != "" && category != "all" {
		countQuery += fmt.Sprintf(" AND main_category_id = $%d", countArgIndex)
		countArgs = append(countArgs, category)
	}

	var total int
	if err := api.DB.QueryRow(ctx, countQuery, countArgs...).Scan(&total); err != nil {
		total = len(products) // Fallback to what we have
	}

	return map[string]interface{}{
		"products": products,
		"pagination": map[string]interface{}{
			"page":     page,
			"limit":    limit,
			"total":    total,
			"has_more": page*limit < total,
		},
	}
}

func (api *CachedAPI) fetchCategoriesFromDB(ctx context.Context) interface{} {
	// Forever Plan Architecture Compliance: Use real database when available
	if api.FallbackMode || api.DB == nil {
		return []map[string]interface{}{}
	}

	// Real database query for categories
	query := `
		SELECT 
			id, part_category_id, name_en, name_ar, description_en, description_ar,
			is_active, sort_order, created_at, updated_at
		FROM main_categories 
		WHERE is_deleted = false
		ORDER BY sort_order, name_en
	`

	rows, err := api.DB.Query(ctx, query)
	if err != nil {
		return []map[string]interface{}{}
	}
	defer rows.Close()

	var categories []map[string]interface{}
	for rows.Next() {
		var (
			id             string
			partCategoryID string
			nameEn         string
			nameAr         string
			descriptionEn  sql.NullString
			descriptionAr  sql.NullString
			isActive       bool
			sortOrder      int
			createdAt      string
			updatedAt      string
		)

		err := rows.Scan(
			&id, &partCategoryID, &nameEn, &nameAr,
			&descriptionEn, &descriptionAr, &isActive,
			&sortOrder, &createdAt, &updatedAt,
		)

		if err != nil {
			continue // Skip problematic rows
		}

		category := map[string]interface{}{
			"id":              id,
			"part_category_id": partCategoryID,
			"name_en":          nameEn,
			"name_ar":          nameAr,
			"description_en":   descriptionEn.String,
			"description_ar":   descriptionAr.String,
			"is_active":        isActive,
			"sort_order":       sortOrder,
			"created_at":       createdAt,
			"updated_at":       updatedAt,
		}

		categories = append(categories, category)
	}

	return categories
}

func (api *CachedAPI) fetchUserProfileFromDB(ctx context.Context, userID string) interface{} {
	// Forever Plan Architecture Compliance: Use real database when available
	if api.FallbackMode || api.DB == nil || userID == "" {
		return map[string]interface{}{}
	}

	// Real database query for user profile
	query := `
		SELECT 
			id, auth_id, email, first_name, last_name, phone_number,
			profile_image_url, is_seller, is_verified_seller, is_buyer,
			created_at, updated_at
		FROM user_profiles 
		WHERE auth_id = $1 AND is_deleted = false
		LIMIT 1
	`

	row := api.DB.QueryRow(ctx, query, userID)

	var (
		id              string
		authID          string
		email           string
		firstName       sql.NullString
		lastName        sql.NullString
		phoneNumber     sql.NullString
		profileImageURL sql.NullString
		isSeller        bool
		isVerifiedSeller bool
		isBuyer         bool
		createdAt       string
		updatedAt       string
	)

	err := row.Scan(
		&id, &authID, &email, &firstName, &lastName,
		&phoneNumber, &profileImageURL, &isSeller,
		&isVerifiedSeller, &isBuyer, &createdAt, &updatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			// User profile not found, return empty map
			return map[string]interface{}{}
		}
		// For other errors, also return empty map
		return map[string]interface{}{}
	}

	profile := map[string]interface{}{
		"id":                id,
		"auth_id":           authID,
		"email":             email,
		"first_name":        firstName.String,
		"last_name":         lastName.String,
		"phone_number":      phoneNumber.String,
		"profile_image_url": profileImageURL.String,
		"is_seller":         isSeller,
		"is_verified_seller": isVerifiedSeller,
		"is_buyer":          isBuyer,
		"created_at":        createdAt,
		"updated_at":        updatedAt,
	}

	return profile
}
