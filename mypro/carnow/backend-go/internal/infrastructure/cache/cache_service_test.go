package cache

import (
	"context"
	"fmt"
	"testing"
	"time"

	"carnow-backend/internal/config"

	"github.com/stretchr/testify/assert"
	"go.uber.org/zap"
)

func TestCacheService(t *testing.T) {
	// Create test configuration
	cfg := &config.Config{
		Redis: config.RedisConfig{
			Enabled:      true,
			Addrs:        []string{"localhost:6379"},
			Password:     "",
			DB:           1, // Use different DB for testing
			PoolSize:     5,
			MinIdleConns: 2,
			MaxRetries:   3,
			DialTimeout:  5 * time.Second,
			ReadTimeout:  3 * time.Second,
			WriteTimeout: 3 * time.Second,
			IdleTimeout:  5 * time.Minute,
			DefaultTTL:   time.Hour,
			KeyPrefix:    "test:",
		},
		App: config.AppConfig{
			Environment: "test",
		},
	}

	logger, _ := zap.NewDevelopment()

	t.Run("Initialize Cache Service", func(t *testing.T) {
		service, err := NewCacheService(cfg, logger)
		if err != nil {
			t.Skipf("Redis not available: %v", err)
			return
		}

		assert.NotNil(t, service)
		assert.True(t, service.IsEnabled())

		// Clean up
		defer service.Close()
	})

	t.Run("Cache Operations", func(t *testing.T) {
		service, err := NewCacheService(cfg, logger)
		if err != nil {
			t.Skipf("Redis not available: %v", err)
			return
		}
		defer service.Close()

		ctx := context.Background()
		key := "test:key:1"
		value := map[string]interface{}{
			"id":    "123",
			"name":  "Test Product",
			"price": 99.99,
		}

		// Test Set
		err = service.Set(ctx, key, value, time.Minute)
		assert.NoError(t, err)

		// Test Get
		var retrieved map[string]interface{}
		err = service.Get(ctx, key, &retrieved)
		assert.NoError(t, err)
		assert.Equal(t, value["id"], retrieved["id"])
		assert.Equal(t, value["name"], retrieved["name"])

		// Test Delete
		err = service.Delete(ctx, key)
		assert.NoError(t, err)

		// Verify deletion
		err = service.Get(ctx, key, &retrieved)
		assert.Error(t, err)
		assert.Equal(t, ErrCacheMiss, err)
	})

	t.Run("GetOrSet Pattern", func(t *testing.T) {
		service, err := NewCacheService(cfg, logger)
		if err != nil {
			t.Skipf("Redis not available: %v", err)
			return
		}
		defer service.Close()

		ctx := context.Background()
		key := "test:getorset:1"

		fetchCalled := false
		fetchFunc := func() (interface{}, error) {
			fetchCalled = true
			return map[string]interface{}{
				"id":   "456",
				"name": "Fetched Product",
			}, nil
		}

		// First call should fetch
		var result map[string]interface{}
		err = service.GetOrSet(ctx, key, &result, fetchFunc, time.Minute)
		assert.NoError(t, err)
		assert.True(t, fetchCalled)
		assert.Equal(t, "456", result["id"])

		// Second call should use cache
		fetchCalled = false
		var result2 map[string]interface{}
		err = service.GetOrSet(ctx, key, &result2, fetchFunc, time.Minute)
		assert.NoError(t, err)
		assert.False(t, fetchCalled) // Should not call fetch function
		assert.Equal(t, "456", result2["id"])
	})

	t.Run("Cache Invalidation", func(t *testing.T) {
		service, err := NewCacheService(cfg, logger)
		if err != nil {
			t.Skipf("Redis not available: %v", err)
			return
		}
		defer service.Close()

		ctx := context.Background()

		// Set multiple keys with same pattern
		keys := []string{
			"test:products:1",
			"test:products:2",
			"test:products:3",
			"test:categories:1",
		}

		for _, key := range keys {
			err = service.Set(ctx, key, map[string]string{"data": key}, time.Minute)
			assert.NoError(t, err)
		}

		// Invalidate products pattern
		err = service.InvalidatePattern(ctx, "test:products:*")
		assert.NoError(t, err)

		// Check that product keys are gone
		var result map[string]string
		for i := 1; i <= 3; i++ {
			key := fmt.Sprintf("test:products:%d", i)
			err = service.Get(ctx, key, &result)
			assert.Error(t, err)
		}

		// Check that category key still exists
		err = service.Get(ctx, "test:categories:1", &result)
		assert.NoError(t, err)
	})

	t.Run("Cache Metrics", func(t *testing.T) {
		service, err := NewCacheService(cfg, logger)
		if err != nil {
			t.Skipf("Redis not available: %v", err)
			return
		}
		defer service.Close()

		ctx := context.Background()
		key := "test:metrics:1"
		value := "test value"

		// Perform some operations
		service.Set(ctx, key, value, time.Minute)

		var retrieved string
		service.Get(ctx, key, &retrieved)           // Hit
		service.Get(ctx, "nonexistent", &retrieved) // Miss

		// Get metrics
		metrics := service.GetMetrics()
		assert.NotNil(t, metrics)

		// Metrics should be a map with enabled=true
		metricsMap, ok := metrics.(map[string]interface{})
		assert.True(t, ok)
		assert.True(t, metricsMap["enabled"].(bool))
	})

	t.Run("Health Check", func(t *testing.T) {
		service, err := NewCacheService(cfg, logger)
		if err != nil {
			t.Skipf("Redis not available: %v", err)
			return
		}
		defer service.Close()

		ctx := context.Background()
		err = service.Health(ctx)
		assert.NoError(t, err)
	})

	t.Run("Cache Warming", func(t *testing.T) {
		service, err := NewCacheService(cfg, logger)
		if err != nil {
			t.Skipf("Redis not available: %v", err)
			return
		}
		defer service.Close()

		ctx := context.Background()
		err = service.WarmCache(ctx)
		assert.NoError(t, err)

		// Check that some warmed data exists
		var categories []map[string]interface{}
		err = service.Get(ctx, "categories:all", &categories)
		// This might be a miss if warming is async, so we don't assert NoError
		if err == nil {
			assert.NotEmpty(t, categories)
		}
	})
}

func TestCacheProvider(t *testing.T) {
	cfg := &config.Config{
		Redis: config.RedisConfig{
			Enabled:      true,
			Addrs:        []string{"localhost:6379"},
			Password:     "",
			DB:           2, // Different DB for provider tests
			PoolSize:     5,
			MinIdleConns: 2,
			MaxRetries:   3,
			DialTimeout:  5 * time.Second,
			ReadTimeout:  3 * time.Second,
			WriteTimeout: 3 * time.Second,
			IdleTimeout:  5 * time.Minute,
			DefaultTTL:   time.Hour,
			KeyPrefix:    "provider_test:",
		},
		App: config.AppConfig{
			Environment: "test",
		},
	}

	logger, _ := zap.NewDevelopment()

	t.Run("Initialize Provider", func(t *testing.T) {
		err := InitializeCacheProvider(cfg, logger)
		if err != nil {
			t.Skipf("Redis not available: %v", err)
			return
		}

		provider := GetCacheProvider()
		assert.NotNil(t, provider)
		assert.True(t, provider.IsEnabled())

		// Clean up
		defer provider.Close()
	})

	t.Run("Global Functions", func(t *testing.T) {
		err := InitializeCacheProvider(cfg, logger)
		if err != nil {
			t.Skipf("Redis not available: %v", err)
			return
		}
		defer GetCacheProvider().Close()

		ctx := context.Background()
		key := "global:test:1"
		value := "global test value"

		// Test global Set
		err = Set(ctx, key, value, time.Minute)
		assert.NoError(t, err)

		// Test global Get
		var retrieved string
		err = Get(ctx, key, &retrieved)
		assert.NoError(t, err)
		assert.Equal(t, value, retrieved)

		// Test global Delete
		err = Delete(ctx, key)
		assert.NoError(t, err)

		// Verify deletion
		err = Get(ctx, key, &retrieved)
		assert.Error(t, err)
	})

	t.Run("Key Helpers", func(t *testing.T) {
		productKey := ProductKey("123")
		assert.Contains(t, productKey, "product:123")

		categoryKey := CategoryKey("automotive")
		assert.Contains(t, categoryKey, "category:automotive")

		userKey := UserKey("user456")
		assert.Contains(t, userKey, "user:user456")

		sessionKey := SessionKey("session789")
		assert.Contains(t, sessionKey, "session:session789")

		searchKey := SearchKey("car parts", map[string]string{
			"category": "automotive",
			"price":    "100",
		})
		assert.Contains(t, searchKey, "search:car parts")
		assert.Contains(t, searchKey, "category=automotive")
		assert.Contains(t, searchKey, "price=100")
	})
}

func TestCacheDisabled(t *testing.T) {
	// Test with cache disabled
	cfg := &config.Config{
		Redis: config.RedisConfig{
			Enabled: false,
		},
		App: config.AppConfig{
			Environment: "test",
		},
	}

	logger, _ := zap.NewDevelopment()

	t.Run("Disabled Cache Service", func(t *testing.T) {
		service, err := NewCacheService(cfg, logger)
		assert.NoError(t, err)
		assert.NotNil(t, service)
		assert.False(t, service.IsEnabled())

		ctx := context.Background()

		// Operations should not fail but should be no-ops
		err = service.Set(ctx, "key", "value", time.Minute)
		assert.NoError(t, err)

		var result string
		err = service.Get(ctx, "key", &result)
		assert.Error(t, err) // Should return cache miss

		err = service.Delete(ctx, "key")
		assert.NoError(t, err)

		err = service.Health(ctx)
		assert.NoError(t, err)

		metrics := service.GetMetrics()
		metricsMap := metrics.(map[string]interface{})
		assert.False(t, metricsMap["enabled"].(bool))
	})
}

// Benchmark tests
func BenchmarkCacheOperations(b *testing.B) {
	cfg := &config.Config{
		Redis: config.RedisConfig{
			Enabled:      true,
			Addrs:        []string{"localhost:6379"},
			Password:     "",
			DB:           3,
			PoolSize:     10,
			MinIdleConns: 5,
			MaxRetries:   3,
			DialTimeout:  5 * time.Second,
			ReadTimeout:  3 * time.Second,
			WriteTimeout: 3 * time.Second,
			IdleTimeout:  5 * time.Minute,
			DefaultTTL:   time.Hour,
			KeyPrefix:    "bench:",
		},
		App: config.AppConfig{
			Environment: "test",
		},
	}

	logger, _ := zap.NewDevelopment()
	service, err := NewCacheService(cfg, logger)
	if err != nil {
		b.Skipf("Redis not available: %v", err)
		return
	}
	defer service.Close()

	ctx := context.Background()
	value := map[string]interface{}{
		"id":          "123",
		"name":        "Benchmark Product",
		"price":       99.99,
		"description": "This is a benchmark product for testing cache performance",
		"category":    "automotive",
		"in_stock":    true,
	}

	b.Run("Set", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			key := fmt.Sprintf("bench:set:%d", i)
			service.Set(ctx, key, value, time.Minute)
		}
	})

	// Pre-populate for Get benchmark
	for i := 0; i < 1000; i++ {
		key := fmt.Sprintf("bench:get:%d", i)
		service.Set(ctx, key, value, time.Minute)
	}

	b.Run("Get", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			key := fmt.Sprintf("bench:get:%d", i%1000)
			var result map[string]interface{}
			service.Get(ctx, key, &result)
		}
	})

	b.Run("GetOrSet", func(b *testing.B) {
		fetchFunc := func() (interface{}, error) {
			return value, nil
		}

		for i := 0; i < b.N; i++ {
			key := fmt.Sprintf("bench:getorset:%d", i%100) // Some cache hits, some misses
			var result map[string]interface{}
			service.GetOrSet(ctx, key, &result, fetchFunc, time.Minute)
		}
	})
}
