package cache

import (
	"context"
	"fmt"
	"sync"
	"time"

	"carnow-backend/internal/config"

	"go.uber.org/zap"
)

// CacheProvider provides a global cache instance
type CacheProvider struct {
	service *CacheService
	mutex   sync.RWMutex
}

var (
	globalCacheProvider *CacheProvider
	providerOnce        sync.Once
)

// InitializeCacheProvider initializes the global cache provider
func InitializeCacheProvider(cfg *config.Config, logger *zap.Logger) error {
	var initErr error

	providerOnce.Do(func() {
		service, err := NewCacheService(cfg, logger)
		if err != nil {
			// Create a disabled cache service as fallback
			logger.Warn("Failed to initialize Redis cache, creating disabled cache service", zap.Error(err))

			// Create a new config with cache disabled for fallback
			fallbackCfg := *cfg
			fallbackCfg.Redis.Enabled = false

			fallbackService, fallbackErr := NewCacheService(&fallbackCfg, logger)
			if fallbackErr != nil {
				initErr = fmt.Errorf("failed to create fallback cache service: %w", fallbackErr)
				return
			}
			service = fallbackService

			initErr = err // Keep the original error for logging, but don't fail
		}

		globalCacheProvider = &CacheProvider{
			service: service,
		}

		if service.IsEnabled() {
			logger.Info("Global cache provider initialized with Redis")
		} else {
			logger.Info("Global cache provider initialized in disabled mode (fallback)")
		}
	})

	return initErr
}

// GetCacheProvider returns the global cache provider
func GetCacheProvider() *CacheProvider {
	if globalCacheProvider == nil {
		panic("Cache provider not initialized. Call InitializeCacheProvider first.")
	}
	return globalCacheProvider
}

// GetService returns the cache service
func (cp *CacheProvider) GetService() *CacheService {
	cp.mutex.RLock()
	defer cp.mutex.RUnlock()
	return cp.service
}

// Convenience methods that delegate to the cache service

// Get retrieves a value from the cache
func (cp *CacheProvider) Get(ctx context.Context, key string, dest interface{}) error {
	return cp.service.Get(ctx, key, dest)
}

// Set stores a value in the cache
func (cp *CacheProvider) Set(ctx context.Context, key string, value interface{}, ttl time.Duration) error {
	return cp.service.Set(ctx, key, value, ttl)
}

// GetOrSet implements cache-aside pattern
func (cp *CacheProvider) GetOrSet(ctx context.Context, key string, dest interface{}, fetchFunc func() (interface{}, error), ttl time.Duration) error {
	return cp.service.GetOrSet(ctx, key, dest, fetchFunc, ttl)
}

// Delete removes a value from the cache
func (cp *CacheProvider) Delete(ctx context.Context, key string) error {
	return cp.service.Delete(ctx, key)
}

// InvalidatePattern invalidates all keys matching a pattern
func (cp *CacheProvider) InvalidatePattern(ctx context.Context, pattern string) error {
	return cp.service.InvalidatePattern(ctx, pattern)
}

// InvalidateCategory invalidates all items in a category
func (cp *CacheProvider) InvalidateCategory(category string) error {
	return cp.service.InvalidateCategory(category)
}

// WarmCache manually triggers cache warming
func (cp *CacheProvider) WarmCache(ctx context.Context) error {
	return cp.service.WarmCache(ctx)
}

// GetMetrics returns cache metrics
func (cp *CacheProvider) GetMetrics() interface{} {
	return cp.service.GetMetrics()
}

// Health checks the cache health
func (cp *CacheProvider) Health(ctx context.Context) error {
	return cp.service.Health(ctx)
}

// IsEnabled returns whether caching is enabled
func (cp *CacheProvider) IsEnabled() bool {
	return cp.service.IsEnabled()
}

// Close closes the cache provider
func (cp *CacheProvider) Close() error {
	if cp.service != nil {
		return cp.service.Close()
	}
	return nil
}

// Global convenience functions

// Get retrieves a value from the global cache
func Get(ctx context.Context, key string, dest interface{}) error {
	return GetCacheProvider().Get(ctx, key, dest)
}

// Set stores a value in the global cache
func Set(ctx context.Context, key string, value interface{}, ttl time.Duration) error {
	return GetCacheProvider().Set(ctx, key, value, ttl)
}

// GetOrSet implements cache-aside pattern with the global cache
func GetOrSet(ctx context.Context, key string, dest interface{}, fetchFunc func() (interface{}, error), ttl time.Duration) error {
	return GetCacheProvider().GetOrSet(ctx, key, dest, fetchFunc, ttl)
}

// Delete removes a value from the global cache
func Delete(ctx context.Context, key string) error {
	return GetCacheProvider().Delete(ctx, key)
}

// InvalidatePattern invalidates all keys matching a pattern in the global cache
func InvalidatePattern(ctx context.Context, pattern string) error {
	return GetCacheProvider().InvalidatePattern(ctx, pattern)
}

// InvalidateCategory invalidates all items in a category in the global cache
func InvalidateCategory(category string) error {
	return GetCacheProvider().InvalidateCategory(category)
}

// WarmCache manually triggers cache warming for the global cache
func WarmCache(ctx context.Context) error {
	return GetCacheProvider().WarmCache(ctx)
}

// GetMetrics returns metrics from the global cache
func GetMetrics() interface{} {
	return GetCacheProvider().GetMetrics()
}

// Health checks the global cache health
func Health(ctx context.Context) error {
	return GetCacheProvider().Health(ctx)
}

// IsEnabled returns whether the global cache is enabled
func IsEnabled() bool {
	return GetCacheProvider().IsEnabled()
}

// Cache key generation helpers

// ProductKey generates a cache key for a product
func ProductKey(productID string) string {
	return GetCacheProvider().service.ProductKey(productID)
}

// CategoryKey generates a cache key for a category
func CategoryKey(categoryID string) string {
	return GetCacheProvider().service.CategoryKey(categoryID)
}

// UserKey generates a cache key for user data
func UserKey(userID string) string {
	return GetCacheProvider().service.UserKey(userID)
}

// SessionKey generates a cache key for user session
func SessionKey(sessionID string) string {
	return GetCacheProvider().service.SessionKey(sessionID)
}

// SearchKey generates a cache key for search results
func SearchKey(query string, filters map[string]string) string {
	return GetCacheProvider().service.SearchKey(query, filters)
}
