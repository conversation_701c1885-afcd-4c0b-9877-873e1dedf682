package database

import (
	"log"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestPerformanceMonitor_RecordQuery(t *testing.T) {
	// Create a mock pool for testing (we'll skip actual database operations)
	pm := &PerformanceMonitor{
		logger:             log.New(log.Writer(), "[Test] ", log.LstdFlags),
		metrics:            &PerformanceMetrics{},
		slowQueryThreshold: 100 * time.Millisecond,
	}

	// Test recording normal query
	pm.RecordQuery("SELECT * FROM users", 50*time.Millisecond, nil)

	metrics := pm.GetMetrics()
	assert.Equal(t, int64(1), metrics.TotalQueries)
	assert.Equal(t, int64(0), metrics.SlowQueries)
	assert.Equal(t, 50*time.Millisecond, metrics.AverageLatency)
	assert.Equal(t, 50*time.Millisecond, metrics.MaxLatency)
	assert.Equal(t, 50*time.Millisecond, metrics.MinLatency)

	// Test recording slow query
	pm.RecordQuery("SELECT * FROM products", 200*time.Millisecond, nil)

	metrics = pm.GetMetrics()
	assert.Equal(t, int64(2), metrics.TotalQueries)
	assert.Equal(t, int64(1), metrics.SlowQueries)
	assert.Equal(t, 125*time.Millisecond, metrics.AverageLatency) // (50 + 200) / 2
	assert.Equal(t, 200*time.Millisecond, metrics.MaxLatency)
	assert.Equal(t, 50*time.Millisecond, metrics.MinLatency)
}

func TestPerformanceMonitor_GetMetrics(t *testing.T) {
	pm := &PerformanceMonitor{
		logger:             log.New(log.Writer(), "[Test] ", log.LstdFlags),
		metrics:            &PerformanceMetrics{},
		slowQueryThreshold: 100 * time.Millisecond,
	}

	// Record some queries
	pm.RecordQuery("SELECT 1", 10*time.Millisecond, nil)
	pm.RecordQuery("SELECT 2", 20*time.Millisecond, nil)
	pm.RecordQuery("SELECT 3", 150*time.Millisecond, nil) // Slow query

	metrics := pm.GetMetrics()

	assert.Equal(t, int64(3), metrics.TotalQueries)
	assert.Equal(t, int64(1), metrics.SlowQueries)
	assert.Equal(t, 60*time.Millisecond, metrics.AverageLatency) // (10 + 20 + 150) / 3
	assert.Equal(t, 150*time.Millisecond, metrics.MaxLatency)
	assert.Equal(t, 10*time.Millisecond, metrics.MinLatency)
}

func TestPerformanceMonitor_SlowQueryThreshold(t *testing.T) {
	pm := &PerformanceMonitor{
		logger:             log.New(log.Writer(), "[Test] ", log.LstdFlags),
		metrics:            &PerformanceMetrics{},
		slowQueryThreshold: 100 * time.Millisecond,
	}

	// Test getting threshold
	assert.Equal(t, 100*time.Millisecond, pm.GetSlowQueryThreshold())

	// Test setting threshold
	pm.SetSlowQueryThreshold(200 * time.Millisecond)
	assert.Equal(t, 200*time.Millisecond, pm.GetSlowQueryThreshold())

	// Test that new threshold affects slow query detection
	pm.RecordQuery("SELECT * FROM users", 150*time.Millisecond, nil)
	metrics := pm.GetMetrics()
	assert.Equal(t, int64(0), metrics.SlowQueries) // Should not be considered slow with 200ms threshold
}

func TestPerformanceMonitor_Reset(t *testing.T) {
	pm := &PerformanceMonitor{
		logger:             log.New(log.Writer(), "[Test] ", log.LstdFlags),
		metrics:            &PerformanceMetrics{},
		slowQueryThreshold: 100 * time.Millisecond,
	}

	// Record some queries
	pm.RecordQuery("SELECT 1", 10*time.Millisecond, nil)
	pm.RecordQuery("SELECT 2", 200*time.Millisecond, nil)

	// Verify metrics are recorded
	metrics := pm.GetMetrics()
	assert.Equal(t, int64(2), metrics.TotalQueries)
	assert.Equal(t, int64(1), metrics.SlowQueries)

	// Reset metrics
	pm.Reset()

	// Verify metrics are reset
	metrics = pm.GetMetrics()
	assert.Equal(t, int64(0), metrics.TotalQueries)
	assert.Equal(t, int64(0), metrics.SlowQueries)
	assert.Equal(t, time.Duration(0), metrics.AverageLatency)
	assert.Equal(t, time.Duration(0), metrics.MaxLatency)
	assert.Equal(t, time.Duration(0), metrics.MinLatency)
}

func TestPerformanceMonitor_ConcurrentAccess(t *testing.T) {
	pm := &PerformanceMonitor{
		logger:             log.New(log.Writer(), "[Test] ", log.LstdFlags),
		metrics:            &PerformanceMetrics{},
		slowQueryThreshold: 100 * time.Millisecond,
	}

	// Test concurrent access to metrics
	done := make(chan bool, 10)

	// Start multiple goroutines recording queries
	for i := 0; i < 10; i++ {
		go func(i int) {
			for j := 0; j < 100; j++ {
				pm.RecordQuery("SELECT 1", time.Duration(i+j)*time.Millisecond, nil)
			}
			done <- true
		}(i)
	}

	// Start multiple goroutines reading metrics
	for i := 0; i < 5; i++ {
		go func() {
			for j := 0; j < 100; j++ {
				_ = pm.GetMetrics()
			}
			done <- true
		}()
	}

	// Wait for all goroutines to complete
	for i := 0; i < 15; i++ {
		<-done
	}

	// Verify final metrics
	metrics := pm.GetMetrics()
	assert.Equal(t, int64(1000), metrics.TotalQueries) // 10 goroutines * 100 queries each
	assert.True(t, metrics.SlowQueries > 0)            // Some queries should be slow
}

func BenchmarkPerformanceMonitor_RecordQuery(b *testing.B) {
	pm := &PerformanceMonitor{
		logger:             log.New(log.Writer(), "[Test] ", log.LstdFlags),
		metrics:            &PerformanceMetrics{},
		slowQueryThreshold: 100 * time.Millisecond,
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		pm.RecordQuery("SELECT 1", 50*time.Millisecond, nil)
	}
}

func BenchmarkPerformanceMonitor_GetMetrics(b *testing.B) {
	pm := &PerformanceMonitor{
		logger:             log.New(log.Writer(), "[Test] ", log.LstdFlags),
		metrics:            &PerformanceMetrics{},
		slowQueryThreshold: 100 * time.Millisecond,
	}

	// Record some queries first
	for i := 0; i < 1000; i++ {
		pm.RecordQuery("SELECT 1", 50*time.Millisecond, nil)
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = pm.GetMetrics()
	}
}
