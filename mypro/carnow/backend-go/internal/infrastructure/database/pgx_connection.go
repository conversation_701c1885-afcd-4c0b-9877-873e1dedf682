package database

import (
	"context"
	"fmt"
	"log"
	"time"

	"carnow-backend/internal/config"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
)

// SimpleDB represents a simple database connection using pgx
type SimpleDB struct {
	Pool   *pgxpool.Pool
	Config *config.Config
}

// NewSimpleDB creates a new simple database connection pool
func NewSimpleDB(cfg *config.Config) (*SimpleDB, error) {
	log.Println("🔄 Creating simple pgx database connection...")
	log.Printf("Host: %s", cfg.Database.Host)
	log.Printf("Port: %d", cfg.Database.Port)
	log.Printf("Database: %s", cfg.Database.Database)

	// Build connection string with enhanced security settings
	connString := fmt.Sprintf(
		"host=%s port=%d user=%s password=%s dbname=%s sslmode=%s application_name=carnow-backend connect_timeout=10",
		cfg.Database.Host,
		cfg.Database.Port,
		cfg.Database.Username,
		cfg.Database.Password,
		cfg.Database.Database,
		cfg.Database.SSLMode,
	)

	// Parse config for pgxpool
	poolConfig, err := pgxpool.ParseConfig(connString)
	if err != nil {
		log.Printf("❌ Failed to parse connection string: %v", err)
		return nil, fmt.Errorf("failed to parse connection string: %w", err)
	}

	// Production-ready pool configuration following security requirements
	poolConfig.MaxConns = int32(cfg.Database.MaxOpenConns)
	poolConfig.MinConns = int32(cfg.Database.MaxIdleConns)
	poolConfig.MaxConnLifetime = cfg.Database.ConnMaxLifetime
	poolConfig.MaxConnIdleTime = cfg.Database.ConnMaxIdleTime
	poolConfig.HealthCheckPeriod = time.Minute

	// Fix prepared statement cache issues
	poolConfig.ConnConfig.DefaultQueryExecMode = pgx.QueryExecModeSimpleProtocol

	// Enhanced security: Enable connection validation
	poolConfig.BeforeConnect = func(ctx context.Context, connConfig *pgx.ConnConfig) error {
		log.Printf("🔐 Establishing secure connection to %s:%d", connConfig.Host, connConfig.Port)
		return nil
	}

	// Create connection pool with timeout
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	pool, err := pgxpool.NewWithConfig(ctx, poolConfig)
	if err != nil {
		log.Printf("❌ Failed to create connection pool: %v", err)
		return nil, fmt.Errorf("failed to create connection pool: %w", err)
	}

	// Test connection with retry mechanism
	log.Println("🔄 Testing database connection...")
	for attempt := 1; attempt <= 3; attempt++ {
		if err := pool.Ping(ctx); err != nil {
			log.Printf("❌ Connection attempt %d failed: %v", attempt, err)
			if attempt == 3 {
				pool.Close()
				return nil, fmt.Errorf("failed to ping database after 3 attempts: %w", err)
			}
			time.Sleep(time.Duration(attempt) * time.Second)
			continue
		}
		break
	}

	log.Println("✅ Simple pgx database connection established successfully")

	return &SimpleDB{
		Pool:   pool,
		Config: cfg,
	}, nil
}

// Health checks the database connection health
func (db *SimpleDB) Health() error {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	return db.Pool.Ping(ctx)
}

// Close closes the database connection pool
func (db *SimpleDB) Close() {
	log.Println("🔄 Closing database connection pool...")
	db.Pool.Close()
	log.Println("✅ Database connection pool closed")
}

// Stats returns database connection pool statistics
func (db *SimpleDB) Stats() *pgxpool.Stat {
	return db.Pool.Stat()
}

// Query executes a query that returns rows
func (db *SimpleDB) Query(ctx context.Context, query string, args ...interface{}) (pgx.Rows, error) {
	return db.Pool.Query(ctx, query, args...)
}

// QueryRow executes a query that returns a single row
func (db *SimpleDB) QueryRow(ctx context.Context, query string, args ...interface{}) pgx.Row {
	return db.Pool.QueryRow(ctx, query, args...)
}

// Exec executes a query that doesn't return rows
func (db *SimpleDB) Exec(ctx context.Context, query string, args ...interface{}) error {
	_, err := db.Pool.Exec(ctx, query, args...)
	return err
}

// Begin starts a new transaction
func (db *SimpleDB) Begin(ctx context.Context) (pgx.Tx, error) {
	return db.Pool.Begin(ctx)
}

// WithTransaction executes a function within a database transaction
func (db *SimpleDB) WithTransaction(ctx context.Context, fn func(tx pgx.Tx) error) error {
	tx, err := db.Begin(ctx)
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback(ctx)

	if err := fn(tx); err != nil {
		return err
	}

	if err := tx.Commit(ctx); err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	return nil
}
