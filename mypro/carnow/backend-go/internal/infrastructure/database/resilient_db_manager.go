package database

import (
	"context"
	"fmt"
	"sync"
	"time"

	"carnow-backend/internal/config"
	"carnow-backend/internal/shared/resilience"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
	"go.uber.org/zap"
)

// ResilientDBManager provides database operations with comprehensive resilience patterns
type ResilientDBManager struct {
	primaryDB         *SecureDB
	readReplicaDB     *SecureDB
	resilienceService *resilience.ResilienceService
	logger            *zap.Logger
	config            *config.Config
	healthChecker     *SimpleHealthChecker
	connectionPool    *ConnectionPoolManager
	mu                sync.RWMutex
	isHealthy         bool
	lastHealthCheck   time.Time
}

// SimpleHealthChecker monitors database health with simple implementation
type SimpleHealthChecker struct {
	primaryHealthy      bool
	readReplicaHealthy  bool
	lastPrimaryCheck    time.Time
	lastReplicaCheck    time.Time
	healthCheckInterval time.Duration
	logger              *zap.Logger
	mu                  sync.RWMutex
}

// ConnectionPoolManager manages connection pool health and recovery
type ConnectionPoolManager struct {
	primaryPool     *pgxpool.Pool
	readReplicaPool *pgxpool.Pool
	poolStats       *PoolStatistics
	logger          *zap.Logger
	mu              sync.RWMutex
}

// PoolStatistics tracks connection pool statistics
type PoolStatistics struct {
	TotalConnections     int32         `json:"total_connections"`
	ActiveConnections    int32         `json:"active_connections"`
	IdleConnections      int32         `json:"idle_connections"`
	ConnectionsCreated   int64         `json:"connections_created"`
	ConnectionsDestroyed int64         `json:"connections_destroyed"`
	ConnectionFailures   int64         `json:"connection_failures"`
	LastFailureTime      time.Time     `json:"last_failure_time"`
	AverageQueryTime     time.Duration `json:"average_query_time"`
	SlowQueries          int64         `json:"slow_queries"`
}

// NewResilientDBManager creates a new resilient database manager
func NewResilientDBManager(
	cfg *config.Config,
	resilienceService *resilience.ResilienceService,
	logger *zap.Logger,
) (*ResilientDBManager, error) {
	if logger == nil {
		logger, _ = zap.NewProduction()
	}

	logger.Info("🔄 Initializing resilient database manager...")

	// Create primary database connection
	primaryDB, err := NewSecureDB(cfg)
	if err != nil {
		return nil, fmt.Errorf("failed to create primary database connection: %w", err)
	}

	// For Forever Plan: No read replica support to keep things simple
	// This aligns with the rule of "NO dual database configurations"

	// Initialize simple health checker
	healthChecker := &SimpleHealthChecker{
		primaryHealthy:      true,
		readReplicaHealthy:  false, // No replica in Forever Plan
		healthCheckInterval: 30 * time.Second,
		logger:              logger,
	}

	// Initialize connection pool manager
	poolManager := &ConnectionPoolManager{
		primaryPool:     primaryDB.Pool,
		readReplicaPool: nil, // No replica pool
		poolStats:       &PoolStatistics{},
		logger:          logger,
	}

	manager := &ResilientDBManager{
		primaryDB:         primaryDB,
		readReplicaDB:     nil, // Forever Plan: single database only
		resilienceService: resilienceService,
		logger:            logger,
		config:            cfg,
		healthChecker:     healthChecker,
		connectionPool:    poolManager,
		isHealthy:         true,
		lastHealthCheck:   time.Now(),
	}

	// Start health monitoring
	go manager.startHealthMonitoring()

	logger.Info("✅ Resilient database manager initialized successfully")
	return manager, nil
}

// ExecuteWithResilience executes a database operation with full resilience patterns
func (rdm *ResilientDBManager) ExecuteWithResilience(
	ctx context.Context,
	operationName string,
	operation func(db *SecureDB) error,
	options ...resilience.ResilienceOption,
) error {
	// Add database-specific resilience options
	dbOptions := append(options, resilience.DatabaseResilienceOptions()...)

	return rdm.resilienceService.ExecuteWithResilience(
		ctx,
		operationName,
		func() error {
			db := rdm.getHealthyDatabase(false) // false = prefer primary for writes
			if db == nil {
				return fmt.Errorf("no healthy database connection available")
			}
			return operation(db)
		},
		dbOptions...,
	)
}

// ExecuteReadWithResilience executes a read operation with resilience
// Forever Plan: No read replica, so this uses primary database only
func (rdm *ResilientDBManager) ExecuteReadWithResilience(
	ctx context.Context,
	operationName string,
	operation func(db *SecureDB) error,
	options ...resilience.ResilienceOption,
) error {
	// Add database-specific resilience options
	dbOptions := append(options, resilience.DatabaseResilienceOptions()...)

	return rdm.resilienceService.ExecuteWithResilience(
		ctx,
		operationName,
		func() error {
			db := rdm.getHealthyDatabase(true) // Will return primary database
			if db == nil {
				return fmt.Errorf("no healthy database connection available")
			}
			return operation(db)
		},
		dbOptions...,
	)
}

// QueryWithResilience executes a query with resilience patterns
func (rdm *ResilientDBManager) QueryWithResilience(
	ctx context.Context,
	query string,
	args ...interface{},
) (pgx.Rows, error) {
	var rows pgx.Rows
	var err error

	err = rdm.ExecuteReadWithResilience(
		ctx,
		"database_query",
		func(db *SecureDB) error {
			rows, err = db.Query(ctx, query, args...)
			return err
		},
	)

	return rows, err
}

// QueryRowWithResilience executes a single-row query with resilience patterns
func (rdm *ResilientDBManager) QueryRowWithResilience(
	ctx context.Context,
	query string,
	args ...interface{},
) pgx.Row {
	var row pgx.Row

	_ = rdm.ExecuteReadWithResilience(
		ctx,
		"database_query_row",
		func(db *SecureDB) error {
			row = db.QueryRow(ctx, query, args...)
			return nil
		},
	)

	return row
}

// ExecWithResilience executes a command with resilience patterns
func (rdm *ResilientDBManager) ExecWithResilience(
	ctx context.Context,
	query string,
	args ...interface{},
) error {
	return rdm.ExecuteWithResilience(
		ctx,
		"database_exec",
		func(db *SecureDB) error {
			return db.Exec(ctx, query, args...)
		},
	)
}

// WithTransactionResilience executes a transaction with resilience patterns
func (rdm *ResilientDBManager) WithTransactionResilience(
	ctx context.Context,
	fn func(tx pgx.Tx) error,
) error {
	return rdm.ExecuteWithResilience(
		ctx,
		"database_transaction",
		func(db *SecureDB) error {
			return db.WithTransaction(ctx, fn)
		},
		resilience.CriticalOperationResilienceOptions()...,
	)
}

// getHealthyDatabase returns a healthy database connection
// Forever Plan: Always returns primary database (no read replica)
func (rdm *ResilientDBManager) getHealthyDatabase(preferReadReplica bool) *SecureDB {
	rdm.mu.RLock()
	defer rdm.mu.RUnlock()

	// Forever Plan: Only primary database exists
	if rdm.primaryDB != nil && rdm.healthChecker.primaryHealthy {
		return rdm.primaryDB
	}

	if rdm.primaryDB != nil {
		// Return primary even if unhealthy as last resort
		rdm.logger.Warn("Returning potentially unhealthy primary database connection")
		return rdm.primaryDB
	}

	return nil
}

// startHealthMonitoring starts the health monitoring goroutine
func (rdm *ResilientDBManager) startHealthMonitoring() {
	ticker := time.NewTicker(rdm.healthChecker.healthCheckInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			rdm.performHealthCheck()
		}
	}
}

// performHealthCheck checks the health of all database connections
func (rdm *ResilientDBManager) performHealthCheck() {
	rdm.healthChecker.mu.Lock()
	defer rdm.healthChecker.mu.Unlock()

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// Check primary database health
	if rdm.primaryDB != nil {
		err := rdm.primaryDB.Health()
		rdm.healthChecker.primaryHealthy = err == nil
		rdm.healthChecker.lastPrimaryCheck = time.Now()

		if err != nil {
			rdm.logger.Error("Primary database health check failed", zap.Error(err))
		} else {
			rdm.logger.Debug("Primary database health check passed")
		}
	}

	// Forever Plan: No read replica to check
	rdm.healthChecker.readReplicaHealthy = false
	rdm.healthChecker.lastReplicaCheck = time.Now()

	// Update overall health status
	rdm.mu.Lock()
	rdm.isHealthy = rdm.healthChecker.primaryHealthy
	rdm.lastHealthCheck = time.Now()
	rdm.mu.Unlock()

	// Update connection pool statistics
	rdm.updatePoolStatistics(ctx)
}

// updatePoolStatistics updates connection pool statistics
func (rdm *ResilientDBManager) updatePoolStatistics(ctx context.Context) {
	rdm.connectionPool.mu.Lock()
	defer rdm.connectionPool.mu.Unlock()

	if rdm.primaryDB != nil && rdm.primaryDB.Pool != nil {
		stats := rdm.primaryDB.Stats()
		rdm.connectionPool.poolStats.TotalConnections = stats.TotalConns()
		rdm.connectionPool.poolStats.ActiveConnections = stats.AcquiredConns()
		rdm.connectionPool.poolStats.IdleConnections = stats.IdleConns()
		rdm.connectionPool.poolStats.ConnectionsCreated = int64(stats.NewConnsCount())
	}
}

// GetHealthStatus returns the current health status
func (rdm *ResilientDBManager) GetHealthStatus() *DatabaseHealthStatus {
	rdm.mu.RLock()
	rdm.healthChecker.mu.RLock()
	defer rdm.mu.RUnlock()
	defer rdm.healthChecker.mu.RUnlock()

	return &DatabaseHealthStatus{
		IsHealthy:           rdm.isHealthy,
		PrimaryHealthy:      rdm.healthChecker.primaryHealthy,
		ReadReplicaHealthy:  rdm.healthChecker.readReplicaHealthy,
		LastHealthCheck:     rdm.lastHealthCheck,
		LastPrimaryCheck:    rdm.healthChecker.lastPrimaryCheck,
		LastReplicaCheck:    rdm.healthChecker.lastReplicaCheck,
		ConnectionPoolStats: *rdm.connectionPool.poolStats,
		HasReadReplica:      false, // Forever Plan: no read replica
	}
}

// GetConnectionStats returns detailed connection statistics
func (rdm *ResilientDBManager) GetConnectionStats() *ConnectionStatistics {
	rdm.connectionPool.mu.RLock()
	defer rdm.connectionPool.mu.RUnlock()

	stats := &ConnectionStatistics{
		PoolStats: *rdm.connectionPool.poolStats,
	}

	if rdm.primaryDB != nil && rdm.primaryDB.Pool != nil {
		pgxStats := rdm.primaryDB.Stats()
		stats.PrimaryPoolStats = &PgxPoolStats{
			TotalConns:              pgxStats.TotalConns(),
			AcquiredConns:           pgxStats.AcquiredConns(),
			IdleConns:               pgxStats.IdleConns(),
			MaxConns:                pgxStats.MaxConns(),
			NewConnsCount:           uint64(pgxStats.NewConnsCount()),
			MaxLifetimeDestroyCount: uint64(pgxStats.MaxLifetimeDestroyCount()),
			MaxIdleDestroyCount:     uint64(pgxStats.MaxIdleDestroyCount()),
		}
	}

	// Forever Plan: No replica stats
	stats.ReplicaPoolStats = nil

	return stats
}

// Close closes all database connections
func (rdm *ResilientDBManager) Close() {
	rdm.logger.Info("🔄 Closing resilient database manager...")

	if rdm.primaryDB != nil {
		rdm.primaryDB.Close()
	}

	// Forever Plan: No read replica to close

	rdm.logger.Info("✅ Resilient database manager closed")
}

// DatabaseHealthStatus represents the health status of database connections
type DatabaseHealthStatus struct {
	IsHealthy           bool           `json:"is_healthy"`
	PrimaryHealthy      bool           `json:"primary_healthy"`
	ReadReplicaHealthy  bool           `json:"read_replica_healthy"`
	LastHealthCheck     time.Time      `json:"last_health_check"`
	LastPrimaryCheck    time.Time      `json:"last_primary_check"`
	LastReplicaCheck    time.Time      `json:"last_replica_check"`
	ConnectionPoolStats PoolStatistics `json:"connection_pool_stats"`
	HasReadReplica      bool           `json:"has_read_replica"`
}

// ConnectionStatistics provides detailed connection statistics
type ConnectionStatistics struct {
	PoolStats        PoolStatistics `json:"pool_stats"`
	PrimaryPoolStats *PgxPoolStats  `json:"primary_pool_stats,omitempty"`
	ReplicaPoolStats *PgxPoolStats  `json:"replica_pool_stats,omitempty"`
}

// PgxPoolStats represents pgx pool statistics
type PgxPoolStats struct {
	TotalConns              int32  `json:"total_conns"`
	AcquiredConns           int32  `json:"acquired_conns"`
	IdleConns               int32  `json:"idle_conns"`
	MaxConns                int32  `json:"max_conns"`
	NewConnsCount           uint64 `json:"new_conns_count"`
	MaxLifetimeDestroyCount uint64 `json:"max_lifetime_destroy_count"`
	MaxIdleDestroyCount     uint64 `json:"max_idle_destroy_count"`
}
