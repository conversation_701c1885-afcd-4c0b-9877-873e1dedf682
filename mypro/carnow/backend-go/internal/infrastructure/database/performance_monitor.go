package database

import (
	"context"
	"fmt"
	"log"
	"sync"
	"time"

	"github.com/jackc/pgx/v5/pgxpool"
)

// PerformanceMonitor monitors database performance metrics
type PerformanceMonitor struct {
	pool               *pgxpool.Pool
	logger             *log.Logger
	metrics            *PerformanceMetrics
	slowQueryThreshold time.Duration
	mu                 sync.RWMutex
}

// PerformanceMetrics holds database performance metrics
type PerformanceMetrics struct {
	TotalQueries      int64         `json:"total_queries"`
	SlowQueries       int64         `json:"slow_queries"`
	AverageLatency    time.Duration `json:"average_latency"`
	MaxLatency        time.Duration `json:"max_latency"`
	MinLatency        time.Duration `json:"min_latency"`
	ConnectionsActive int32         `json:"connections_active"`
	ConnectionsIdle   int32         `json:"connections_idle"`
	ConnectionsTotal  int32         `json:"connections_total"`
	CacheHitRatio     float64       `json:"cache_hit_ratio"`
	LastUpdated       time.Time     `json:"last_updated"`
}

// QueryMetrics holds metrics for individual queries
type QueryMetrics struct {
	Query         string        `json:"query"`
	ExecutionTime time.Duration `json:"execution_time"`
	Timestamp     time.Time     `json:"timestamp"`
	Success       bool          `json:"success"`
	Error         string        `json:"error,omitempty"`
}

// NewPerformanceMonitor creates a new database performance monitor
func NewPerformanceMonitor(pool *pgxpool.Pool, slowQueryThreshold time.Duration) *PerformanceMonitor {
	return &PerformanceMonitor{
		pool:               pool,
		logger:             log.New(log.Writer(), "[DBPerformance] ", log.LstdFlags),
		metrics:            &PerformanceMetrics{},
		slowQueryThreshold: slowQueryThreshold,
	}
}

// RecordQuery records metrics for a database query
func (pm *PerformanceMonitor) RecordQuery(query string, duration time.Duration, err error) {
	pm.mu.Lock()
	defer pm.mu.Unlock()

	pm.metrics.TotalQueries++

	// Update latency metrics
	if pm.metrics.TotalQueries == 1 {
		pm.metrics.AverageLatency = duration
		pm.metrics.MaxLatency = duration
		pm.metrics.MinLatency = duration
	} else {
		// Calculate running average
		pm.metrics.AverageLatency = time.Duration(
			(int64(pm.metrics.AverageLatency)*(pm.metrics.TotalQueries-1) + int64(duration)) / pm.metrics.TotalQueries,
		)

		if duration > pm.metrics.MaxLatency {
			pm.metrics.MaxLatency = duration
		}
		if duration < pm.metrics.MinLatency {
			pm.metrics.MinLatency = duration
		}
	}

	// Check for slow queries
	if duration > pm.slowQueryThreshold {
		pm.metrics.SlowQueries++
		pm.logger.Printf("🐌 Slow query detected (%.2fms): %s",
			float64(duration.Nanoseconds())/1e6, truncateQuery(query))
	}

	pm.metrics.LastUpdated = time.Now()

	// Log query metrics periodically
	if pm.metrics.TotalQueries%100 == 0 {
		pm.logMetricsSummary()
	}
}

// UpdateConnectionMetrics updates connection pool metrics
func (pm *PerformanceMonitor) UpdateConnectionMetrics() {
	pm.mu.Lock()
	defer pm.mu.Unlock()

	stats := pm.pool.Stat()
	pm.metrics.ConnectionsActive = stats.AcquiredConns()
	pm.metrics.ConnectionsIdle = stats.IdleConns()
	pm.metrics.ConnectionsTotal = stats.TotalConns()
	pm.metrics.LastUpdated = time.Now()
}

// GetMetrics returns current database metrics
func (pm *PerformanceMonitor) GetMetrics() *PerformanceMetrics {
	pm.mu.RLock()
	defer pm.mu.RUnlock()

	// Create a copy to avoid race conditions
	metricsCopy := *pm.metrics
	return &metricsCopy
}

// GetDatabaseStats retrieves comprehensive database statistics
func (pm *PerformanceMonitor) GetDatabaseStats(ctx context.Context) (map[string]interface{}, error) {
	pm.logger.Printf("📊 Retrieving database statistics")

	stats := make(map[string]interface{})

	// Get connection pool stats
	poolStats := pm.pool.Stat()
	stats["connection_pool"] = map[string]interface{}{
		"acquired_conns":             poolStats.AcquiredConns(),
		"canceled_acquire_count":     poolStats.CanceledAcquireCount(),
		"constructing_conns":         poolStats.ConstructingConns(),
		"empty_acquire_count":        poolStats.EmptyAcquireCount(),
		"idle_conns":                 poolStats.IdleConns(),
		"max_conns":                  poolStats.MaxConns(),
		"total_conns":                poolStats.TotalConns(),
		"new_conns_count":            poolStats.NewConnsCount(),
		"max_lifetime_destroy_count": poolStats.MaxLifetimeDestroyCount(),
		"max_idle_destroy_count":     poolStats.MaxIdleDestroyCount(),
	}

	// Get database size information
	var dbSize int64
	err := pm.pool.QueryRow(ctx, "SELECT pg_database_size(current_database())").Scan(&dbSize)
	if err != nil {
		pm.logger.Printf("⚠️ Failed to get database size: %v", err)
	} else {
		stats["database_size_bytes"] = dbSize
		stats["database_size_mb"] = float64(dbSize) / (1024 * 1024)
	}

	// Get table statistics
	tableStats, err := pm.getTableStatistics(ctx)
	if err != nil {
		pm.logger.Printf("⚠️ Failed to get table statistics: %v", err)
	} else {
		stats["table_statistics"] = tableStats
	}

	// Get index usage statistics
	indexStats, err := pm.getIndexStatistics(ctx)
	if err != nil {
		pm.logger.Printf("⚠️ Failed to get index statistics: %v", err)
	} else {
		stats["index_statistics"] = indexStats
	}

	// Get cache hit ratio
	cacheHitRatio, err := pm.getCacheHitRatio(ctx)
	if err != nil {
		pm.logger.Printf("⚠️ Failed to get cache hit ratio: %v", err)
	} else {
		stats["cache_hit_ratio"] = cacheHitRatio
		pm.mu.Lock()
		pm.metrics.CacheHitRatio = cacheHitRatio
		pm.mu.Unlock()
	}

	// Add performance metrics
	stats["performance_metrics"] = pm.GetMetrics()

	pm.logger.Printf("✅ Database statistics retrieved successfully")
	return stats, nil
}

// getTableStatistics retrieves table-level statistics
func (pm *PerformanceMonitor) getTableStatistics(ctx context.Context) ([]map[string]interface{}, error) {
	query := `
		SELECT 
			schemaname,
			tablename,
			n_tup_ins as inserts,
			n_tup_upd as updates,
			n_tup_del as deletes,
			n_live_tup as live_tuples,
			n_dead_tup as dead_tuples,
			last_vacuum,
			last_autovacuum,
			last_analyze,
			last_autoanalyze
		FROM pg_stat_user_tables 
		ORDER BY n_live_tup DESC
		LIMIT 20`

	rows, err := pm.pool.Query(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("failed to query table statistics: %w", err)
	}
	defer rows.Close()

	var tableStats []map[string]interface{}
	for rows.Next() {
		var schemaName, tableName string
		var inserts, updates, deletes, liveTuples, deadTuples int64
		var lastVacuum, lastAutovacuum, lastAnalyze, lastAutoanalyze *time.Time

		err := rows.Scan(&schemaName, &tableName, &inserts, &updates, &deletes,
			&liveTuples, &deadTuples, &lastVacuum, &lastAutovacuum, &lastAnalyze, &lastAutoanalyze)
		if err != nil {
			pm.logger.Printf("⚠️ Failed to scan table stats row: %v", err)
			continue
		}

		stat := map[string]interface{}{
			"schema_name":      schemaName,
			"table_name":       tableName,
			"inserts":          inserts,
			"updates":          updates,
			"deletes":          deletes,
			"live_tuples":      liveTuples,
			"dead_tuples":      deadTuples,
			"last_vacuum":      lastVacuum,
			"last_autovacuum":  lastAutovacuum,
			"last_analyze":     lastAnalyze,
			"last_autoanalyze": lastAutoanalyze,
		}
		tableStats = append(tableStats, stat)
	}

	return tableStats, nil
}

// getIndexStatistics retrieves index usage statistics
func (pm *PerformanceMonitor) getIndexStatistics(ctx context.Context) ([]map[string]interface{}, error) {
	query := `
		SELECT 
			schemaname,
			tablename,
			indexname,
			idx_tup_read,
			idx_tup_fetch,
			idx_scan
		FROM pg_stat_user_indexes 
		ORDER BY idx_scan DESC
		LIMIT 20`

	rows, err := pm.pool.Query(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("failed to query index statistics: %w", err)
	}
	defer rows.Close()

	var indexStats []map[string]interface{}
	for rows.Next() {
		var schemaName, tableName, indexName string
		var tupRead, tupFetch, idxScan int64

		err := rows.Scan(&schemaName, &tableName, &indexName, &tupRead, &tupFetch, &idxScan)
		if err != nil {
			pm.logger.Printf("⚠️ Failed to scan index stats row: %v", err)
			continue
		}

		stat := map[string]interface{}{
			"schema_name":  schemaName,
			"table_name":   tableName,
			"index_name":   indexName,
			"tuples_read":  tupRead,
			"tuples_fetch": tupFetch,
			"index_scans":  idxScan,
		}
		indexStats = append(indexStats, stat)
	}

	return indexStats, nil
}

// getCacheHitRatio calculates the database cache hit ratio
func (pm *PerformanceMonitor) getCacheHitRatio(ctx context.Context) (float64, error) {
	query := `
		SELECT 
			sum(heap_blks_hit) / (sum(heap_blks_hit) + sum(heap_blks_read)) as cache_hit_ratio
		FROM pg_statio_user_tables`

	var cacheHitRatio *float64
	err := pm.pool.QueryRow(ctx, query).Scan(&cacheHitRatio)
	if err != nil {
		return 0, fmt.Errorf("failed to get cache hit ratio: %w", err)
	}

	if cacheHitRatio == nil {
		return 0, nil
	}

	return *cacheHitRatio, nil
}

// logMetricsSummary logs a summary of current metrics
func (pm *PerformanceMonitor) logMetricsSummary() {
	pm.logger.Printf("📊 Metrics Summary - Queries: %d, Slow: %d, Avg Latency: %.2fms, Max: %.2fms",
		pm.metrics.TotalQueries,
		pm.metrics.SlowQueries,
		float64(pm.metrics.AverageLatency.Nanoseconds())/1e6,
		float64(pm.metrics.MaxLatency.Nanoseconds())/1e6,
	)
}

// StartPeriodicMetricsCollection starts periodic collection of database metrics
func (pm *PerformanceMonitor) StartPeriodicMetricsCollection(ctx context.Context, interval time.Duration) {
	pm.logger.Printf("🔄 Starting periodic metrics collection (interval: %v)", interval)

	ticker := time.NewTicker(interval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			pm.logger.Printf("🛑 Stopping periodic metrics collection")
			return
		case <-ticker.C:
			pm.UpdateConnectionMetrics()

			// Get and log database stats periodically
			stats, err := pm.GetDatabaseStats(ctx)
			if err != nil {
				pm.logger.Printf("⚠️ Failed to get database stats: %v", err)
				continue
			}

			// Log key metrics
			if cacheHitRatio, ok := stats["cache_hit_ratio"].(float64); ok {
				pm.logger.Printf("📊 Cache Hit Ratio: %.2f%%", cacheHitRatio*100)
			}

			if poolStats, ok := stats["connection_pool"].(map[string]interface{}); ok {
				if activeConns, ok := poolStats["acquired_conns"].(int32); ok {
					if totalConns, ok := poolStats["total_conns"].(int32); ok {
						pm.logger.Printf("🔗 Connections: %d/%d active", activeConns, totalConns)
					}
				}
			}
		}
	}
}

// GetSlowQueryThreshold returns the current slow query threshold
func (pm *PerformanceMonitor) GetSlowQueryThreshold() time.Duration {
	return pm.slowQueryThreshold
}

// SetSlowQueryThreshold sets the slow query threshold
func (pm *PerformanceMonitor) SetSlowQueryThreshold(threshold time.Duration) {
	pm.mu.Lock()
	defer pm.mu.Unlock()
	pm.slowQueryThreshold = threshold
	pm.logger.Printf("🔧 Slow query threshold updated to: %v", threshold)
}

// Reset resets all metrics
func (pm *PerformanceMonitor) Reset() {
	pm.mu.Lock()
	defer pm.mu.Unlock()

	pm.metrics = &PerformanceMetrics{
		LastUpdated: time.Now(),
	}
	pm.logger.Printf("🔄 Performance metrics reset")
}
