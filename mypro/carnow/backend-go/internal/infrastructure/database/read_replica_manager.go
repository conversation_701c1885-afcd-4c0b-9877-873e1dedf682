package database

import (
	"context"
	"fmt"
	"log"
	"math/rand"
	"sync"
	"time"

	"github.com/jackc/pgx/v5/pgxpool"
)

// ReadReplicaManager manages read replica connections and load balancing
type ReadReplicaManager struct {
	primaryPool   *pgxpool.Pool
	replicaPools  []*pgxpool.Pool
	replicaConfig []ReplicaConfig
	logger        *log.Logger
	mu            sync.RWMutex
	healthStatus  map[int]bool // Track health status of each replica
	loadBalancer  LoadBalancer
}

// ReplicaConfig holds configuration for a read replica
type ReplicaConfig struct {
	Host     string `json:"host"`
	Port     int    `json:"port"`
	Database string `json:"database"`
	Username string `json:"username"`
	Password string `json:"password"`
	SSLMode  string `json:"ssl_mode"`
	Weight   int    `json:"weight"` // For weighted load balancing
}

// LoadBalancer defines the interface for load balancing strategies
type LoadBalancer interface {
	SelectReplica(replicas []*pgxpool.Pool, healthStatus map[int]bool) (*pgxpool.Pool, error)
}

// RoundRobinBalancer implements round-robin load balancing
type RoundRobinBalancer struct {
	counter int64
	mu      sync.Mutex
}

// WeightedBalancer implements weighted load balancing
type WeightedBalancer struct {
	weights []int
	mu      sync.Mutex
}

// RandomBalancer implements random load balancing
type RandomBalancer struct{}

// NewReadReplicaManager creates a new read replica manager
func NewReadReplicaManager(primaryPool *pgxpool.Pool, replicaConfigs []ReplicaConfig) *ReadReplicaManager {
	return &ReadReplicaManager{
		primaryPool:   primaryPool,
		replicaConfig: replicaConfigs,
		logger:        log.New(log.Writer(), "[ReadReplica] ", log.LstdFlags),
		healthStatus:  make(map[int]bool),
		loadBalancer:  &RoundRobinBalancer{}, // Default to round-robin
	}
}

// Initialize initializes all read replica connections
func (rrm *ReadReplicaManager) Initialize(ctx context.Context) error {
	rrm.logger.Printf("🔄 Initializing %d read replicas", len(rrm.replicaConfig))

	rrm.mu.Lock()
	defer rrm.mu.Unlock()

	for i, replicaConfig := range rrm.replicaConfig {
		pool, err := rrm.createReplicaPool(ctx, replicaConfig)
		if err != nil {
			rrm.logger.Printf("⚠️ Failed to create replica pool %d: %v", i, err)
			rrm.healthStatus[i] = false
			continue
		}

		// Test the connection
		if err := pool.Ping(ctx); err != nil {
			rrm.logger.Printf("⚠️ Replica %d health check failed: %v", i, err)
			pool.Close()
			rrm.healthStatus[i] = false
			continue
		}

		rrm.replicaPools = append(rrm.replicaPools, pool)
		rrm.healthStatus[i] = true
		rrm.logger.Printf("✅ Read replica %d initialized successfully", i)
	}

	if len(rrm.replicaPools) == 0 {
		return fmt.Errorf("no healthy read replicas available")
	}

	rrm.logger.Printf("✅ Read replica manager initialized with %d healthy replicas", len(rrm.replicaPools))
	return nil
}

// createReplicaPool creates a connection pool for a read replica
func (rrm *ReadReplicaManager) createReplicaPool(ctx context.Context, replicaConfig ReplicaConfig) (*pgxpool.Pool, error) {
	// Build connection string for replica
	connString := fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=%s",
		replicaConfig.Host, replicaConfig.Port, replicaConfig.Username,
		replicaConfig.Password, replicaConfig.Database, replicaConfig.SSLMode)

	// Parse config for pgxpool
	poolConfig, err := pgxpool.ParseConfig(connString)
	if err != nil {
		return nil, fmt.Errorf("failed to parse replica connection string: %w", err)
	}

	// Configure pool settings for read replicas
	poolConfig.MaxConns = 5 // Fewer connections for read replicas
	poolConfig.MinConns = 1
	poolConfig.MaxConnLifetime = time.Hour
	poolConfig.MaxConnIdleTime = 30 * time.Minute
	poolConfig.HealthCheckPeriod = time.Minute

	// Create connection pool
	pool, err := pgxpool.NewWithConfig(ctx, poolConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to create replica connection pool: %w", err)
	}

	return pool, nil
}

// GetReadPool returns a read replica pool using load balancing
func (rrm *ReadReplicaManager) GetReadPool() (*pgxpool.Pool, error) {
	rrm.mu.RLock()
	defer rrm.mu.RUnlock()

	if len(rrm.replicaPools) == 0 {
		rrm.logger.Printf("⚠️ No read replicas available, falling back to primary")
		return rrm.primaryPool, nil
	}

	// Use load balancer to select a replica
	selectedPool, err := rrm.loadBalancer.SelectReplica(rrm.replicaPools, rrm.healthStatus)
	if err != nil {
		rrm.logger.Printf("⚠️ Load balancer failed: %v, falling back to primary", err)
		return rrm.primaryPool, nil
	}

	return selectedPool, nil
}

// GetWritePool returns the primary pool for write operations
func (rrm *ReadReplicaManager) GetWritePool() *pgxpool.Pool {
	return rrm.primaryPool
}

// SetLoadBalancer sets the load balancing strategy
func (rrm *ReadReplicaManager) SetLoadBalancer(lb LoadBalancer) {
	rrm.mu.Lock()
	defer rrm.mu.Unlock()
	rrm.loadBalancer = lb
	rrm.logger.Printf("🔧 Load balancer strategy updated")
}

// StartHealthChecks starts periodic health checks for all replicas
func (rrm *ReadReplicaManager) StartHealthChecks(ctx context.Context, interval time.Duration) {
	rrm.logger.Printf("🔄 Starting health checks (interval: %v)", interval)

	ticker := time.NewTicker(interval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			rrm.logger.Printf("🛑 Stopping health checks")
			return
		case <-ticker.C:
			rrm.performHealthChecks(ctx)
		}
	}
}

// performHealthChecks checks the health of all read replicas
func (rrm *ReadReplicaManager) performHealthChecks(ctx context.Context) {
	rrm.mu.Lock()
	defer rrm.mu.Unlock()

	healthyCount := 0
	for i, pool := range rrm.replicaPools {
		if pool == nil {
			rrm.healthStatus[i] = false
			continue
		}

		// Perform health check with timeout
		healthCtx, cancel := context.WithTimeout(ctx, 5*time.Second)
		err := pool.Ping(healthCtx)
		cancel()

		if err != nil {
			if rrm.healthStatus[i] {
				rrm.logger.Printf("⚠️ Replica %d became unhealthy: %v", i, err)
			}
			rrm.healthStatus[i] = false
		} else {
			if !rrm.healthStatus[i] {
				rrm.logger.Printf("✅ Replica %d recovered", i)
			}
			rrm.healthStatus[i] = true
			healthyCount++
		}
	}

	if healthyCount == 0 {
		rrm.logger.Printf("⚠️ No healthy read replicas available")
	}
}

// GetHealthStatus returns the health status of all replicas
func (rrm *ReadReplicaManager) GetHealthStatus() map[int]bool {
	rrm.mu.RLock()
	defer rrm.mu.RUnlock()

	status := make(map[int]bool)
	for i, healthy := range rrm.healthStatus {
		status[i] = healthy
	}
	return status
}

// GetStats returns statistics about the read replica manager
func (rrm *ReadReplicaManager) GetStats() map[string]interface{} {
	rrm.mu.RLock()
	defer rrm.mu.RUnlock()

	healthyCount := 0
	for _, healthy := range rrm.healthStatus {
		if healthy {
			healthyCount++
		}
	}

	stats := map[string]interface{}{
		"total_replicas":     len(rrm.replicaPools),
		"healthy_replicas":   healthyCount,
		"unhealthy_replicas": len(rrm.replicaPools) - healthyCount,
		"health_status":      rrm.healthStatus,
	}

	// Add connection pool stats for each replica
	replicaStats := make([]map[string]interface{}, len(rrm.replicaPools))
	for i, pool := range rrm.replicaPools {
		if pool != nil {
			poolStats := pool.Stat()
			replicaStats[i] = map[string]interface{}{
				"acquired_conns": poolStats.AcquiredConns(),
				"idle_conns":     poolStats.IdleConns(),
				"total_conns":    poolStats.TotalConns(),
				"healthy":        rrm.healthStatus[i],
			}
		}
	}
	stats["replica_stats"] = replicaStats

	return stats
}

// Close closes all replica connections
func (rrm *ReadReplicaManager) Close() {
	rrm.mu.Lock()
	defer rrm.mu.Unlock()

	rrm.logger.Printf("🔄 Closing %d read replica connections", len(rrm.replicaPools))

	for i, pool := range rrm.replicaPools {
		if pool != nil {
			pool.Close()
			rrm.logger.Printf("✅ Read replica %d closed", i)
		}
	}

	rrm.replicaPools = nil
	rrm.healthStatus = make(map[int]bool)
	rrm.logger.Printf("✅ All read replica connections closed")
}

// Load Balancer Implementations

// SelectReplica implements round-robin load balancing
func (rb *RoundRobinBalancer) SelectReplica(replicas []*pgxpool.Pool, healthStatus map[int]bool) (*pgxpool.Pool, error) {
	rb.mu.Lock()
	defer rb.mu.Unlock()

	if len(replicas) == 0 {
		return nil, fmt.Errorf("no replicas available")
	}

	// Find healthy replicas
	healthyReplicas := []int{}
	for i := 0; i < len(replicas); i++ {
		if healthStatus[i] {
			healthyReplicas = append(healthyReplicas, i)
		}
	}

	if len(healthyReplicas) == 0 {
		return nil, fmt.Errorf("no healthy replicas available")
	}

	// Select next replica in round-robin fashion
	selectedIndex := healthyReplicas[rb.counter%int64(len(healthyReplicas))]
	rb.counter++

	return replicas[selectedIndex], nil
}

// SelectReplica implements weighted load balancing
func (wb *WeightedBalancer) SelectReplica(replicas []*pgxpool.Pool, healthStatus map[int]bool) (*pgxpool.Pool, error) {
	wb.mu.Lock()
	defer wb.mu.Unlock()

	if len(replicas) == 0 {
		return nil, fmt.Errorf("no replicas available")
	}

	// For now, fall back to round-robin (weighted implementation would be more complex)
	healthyReplicas := []int{}
	for i := 0; i < len(replicas); i++ {
		if healthStatus[i] {
			healthyReplicas = append(healthyReplicas, i)
		}
	}

	if len(healthyReplicas) == 0 {
		return nil, fmt.Errorf("no healthy replicas available")
	}

	// Simple random selection for now
	selectedIndex := healthyReplicas[rand.Intn(len(healthyReplicas))]
	return replicas[selectedIndex], nil
}

// SelectReplica implements random load balancing
func (rb *RandomBalancer) SelectReplica(replicas []*pgxpool.Pool, healthStatus map[int]bool) (*pgxpool.Pool, error) {
	if len(replicas) == 0 {
		return nil, fmt.Errorf("no replicas available")
	}

	// Find healthy replicas
	healthyReplicas := []int{}
	for i := 0; i < len(replicas); i++ {
		if healthStatus[i] {
			healthyReplicas = append(healthyReplicas, i)
		}
	}

	if len(healthyReplicas) == 0 {
		return nil, fmt.Errorf("no healthy replicas available")
	}

	// Select random healthy replica
	selectedIndex := healthyReplicas[rand.Intn(len(healthyReplicas))]
	return replicas[selectedIndex], nil
}
