package database

import (
	"context"
	"fmt"
	"sync"
	"time"

	"carnow-backend/internal/config"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgconn"
	"github.com/jackc/pgx/v5/pgxpool"
	"go.uber.org/zap"
)

// CircuitBreakerConfig defines circuit breaker configuration
// Moved here to avoid import cycle with middleware package
type CircuitBreakerConfig struct {
	MaxFailures int           `json:"max_failures"`
	Timeout     time.Duration `json:"timeout"`
	RetryDelay  time.Duration `json:"retry_delay"`
}

// Resilient Database for Phase 3: Error Handling and Resilience
//
// Features:
// - Enhanced connection pooling with proper limits
// - Automatic connection retry logic with exponential backoff
// - Database health monitoring and alerting
// - Read replica failover support
// - Connection leak detection
// - Performance metrics and monitoring
// - Graceful degradation and circuit breaker integration

// DatabaseConfig contains configuration for resilient database connections
type DatabaseConfig struct {
	// Primary database configuration
	Primary DatabaseConnectionConfig `json:"primary"`
	// Read replica configuration (optional)
	ReadReplica *DatabaseConnectionConfig `json:"read_replica,omitempty"`
	// Connection pooling configuration
	Pool DatabasePoolConfig `json:"pool"`
	// Health check configuration
	HealthCheck DatabaseHealthConfig `json:"health_check"`
	// Retry configuration
	Retry DatabaseRetryConfig `json:"retry"`
	// Circuit breaker configuration
	CircuitBreaker *CircuitBreakerConfig `json:"circuit_breaker,omitempty"`
}

// DatabaseConnectionConfig contains individual database connection settings
type DatabaseConnectionConfig struct {
	Host     string `json:"host"`
	Port     int    `json:"port"`
	Database string `json:"database"`
	Username string `json:"username"`
	Password string `json:"password"`
	SSLMode  string `json:"ssl_mode"`
}

// DatabasePoolConfig contains connection pool settings
type DatabasePoolConfig struct {
	MaxConnections        int           `json:"max_connections"`
	MinConnections        int           `json:"min_connections"`
	MaxConnectionLifetime time.Duration `json:"max_connection_lifetime"`
	MaxConnectionIdleTime time.Duration `json:"max_connection_idle_time"`
	HealthCheckPeriod     time.Duration `json:"health_check_period"`
	AcquireTimeout        time.Duration `json:"acquire_timeout"`
}

// DatabaseHealthConfig contains health monitoring settings
type DatabaseHealthConfig struct {
	Enabled          bool          `json:"enabled"`
	CheckInterval    time.Duration `json:"check_interval"`
	Timeout          time.Duration `json:"timeout"`
	FailureThreshold int           `json:"failure_threshold"`
	RetryInterval    time.Duration `json:"retry_interval"`
}

// DatabaseRetryConfig contains retry logic settings
type DatabaseRetryConfig struct {
	MaxRetries   int           `json:"max_retries"`
	InitialDelay time.Duration `json:"initial_delay"`
	MaxDelay     time.Duration `json:"max_delay"`
	Multiplier   float64       `json:"multiplier"`
	JitterFactor float64       `json:"jitter_factor"`
}

// DatabaseMetrics tracks database performance and health metrics
type DatabaseMetrics struct {
	// Connection metrics
	TotalConnections     int64         `json:"total_connections"`
	ActiveConnections    int64         `json:"active_connections"`
	IdleConnections      int64         `json:"idle_connections"`
	ConnectionsCreated   int64         `json:"connections_created"`
	ConnectionsDestroyed int64         `json:"connections_destroyed"`
	ConnectionErrors     int64         `json:"connection_errors"`
	AverageAcquireTime   time.Duration `json:"average_acquire_time"`

	// Query metrics
	TotalQueries      int64         `json:"total_queries"`
	SuccessfulQueries int64         `json:"successful_queries"`
	FailedQueries     int64         `json:"failed_queries"`
	AverageQueryTime  time.Duration `json:"average_query_time"`
	SlowQueries       int64         `json:"slow_queries"`

	// Health metrics
	HealthChecksPassed int64     `json:"health_checks_passed"`
	HealthChecksFailed int64     `json:"health_checks_failed"`
	LastHealthCheck    time.Time `json:"last_health_check"`
	IsHealthy          bool      `json:"is_healthy"`

	// Failover metrics
	FailoverEvents     int64     `json:"failover_events"`
	ReplicaConnections int64     `json:"replica_connections"`
	LastFailover       time.Time `json:"last_failover"`
}

// ResilientDatabase provides enhanced database connectivity with resilience features
type ResilientDatabase struct {
	config         *DatabaseConfig
	logger         *zap.Logger
	circuitBreaker *CircuitBreakerConfig // Changed type to CircuitBreakerConfig

	// Connection pools
	primaryPool *pgxpool.Pool
	replicaPool *pgxpool.Pool

	// Health monitoring
	healthChecker *DatabaseHealthChecker
	metrics       *DatabaseMetrics

	// State management
	isHealthy    bool
	usingReplica bool
	mutex        sync.RWMutex

	// Shutdown
	shutdownChan chan struct{}
	shutdownOnce sync.Once
}

// NewResilientDatabase creates a new resilient database instance
func NewResilientDatabase(cfg *config.Config, logger *zap.Logger) (*ResilientDatabase, error) {
	dbConfig := buildDatabaseConfig(cfg)

	rd := &ResilientDatabase{
		config:       dbConfig,
		logger:       logger,
		metrics:      &DatabaseMetrics{},
		isHealthy:    true,
		shutdownChan: make(chan struct{}),
	}

	// Initialize circuit breaker
	if dbConfig.CircuitBreaker != nil {
		rd.circuitBreaker = dbConfig.CircuitBreaker
	}

	// Initialize primary connection pool
	if err := rd.initializePrimaryPool(); err != nil {
		return nil, fmt.Errorf("failed to initialize primary database pool: %w", err)
	}

	// Initialize read replica if configured
	if dbConfig.ReadReplica != nil {
		if err := rd.initializeReplicaPool(); err != nil {
			rd.logger.Warn("Failed to initialize read replica, continuing with primary only", zap.Error(err))
		}
	}

	// Initialize health checker
	rd.healthChecker = NewDatabaseHealthChecker(rd, dbConfig.HealthCheck, logger)
	rd.healthChecker.Start()

	rd.logger.Info("Resilient database initialized successfully",
		zap.String("primary_host", dbConfig.Primary.Host),
		zap.Int("max_connections", dbConfig.Pool.MaxConnections),
		zap.Bool("read_replica_enabled", dbConfig.ReadReplica != nil),
	)

	return rd, nil
}

// initializePrimaryPool initializes the primary database connection pool
func (rd *ResilientDatabase) initializePrimaryPool() error {
	connString := rd.buildConnectionString(&rd.config.Primary)
	poolConfig, err := pgxpool.ParseConfig(connString)
	if err != nil {
		return fmt.Errorf("failed to parse connection string: %w", err)
	}

	rd.configurePool(poolConfig)

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	pool, err := pgxpool.NewWithConfig(ctx, poolConfig)
	if err != nil {
		return fmt.Errorf("failed to create primary connection pool: %w", err)
	}

	// Test the connection
	if err := pool.Ping(ctx); err != nil {
		pool.Close()
		return fmt.Errorf("failed to ping primary database: %w", err)
	}

	rd.primaryPool = pool
	return nil
}

// initializeReplicaPool initializes the read replica connection pool
func (rd *ResilientDatabase) initializeReplicaPool() error {
	if rd.config.ReadReplica == nil {
		return nil
	}

	connString := rd.buildConnectionString(rd.config.ReadReplica)
	poolConfig, err := pgxpool.ParseConfig(connString)
	if err != nil {
		return fmt.Errorf("failed to parse replica connection string: %w", err)
	}

	rd.configurePool(poolConfig)

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	pool, err := pgxpool.NewWithConfig(ctx, poolConfig)
	if err != nil {
		return fmt.Errorf("failed to create replica connection pool: %w", err)
	}

	// Test the connection
	if err := pool.Ping(ctx); err != nil {
		pool.Close()
		return fmt.Errorf("failed to ping replica database: %w", err)
	}

	rd.replicaPool = pool
	return nil
}

// configurePool applies common pool configuration
func (rd *ResilientDatabase) configurePool(config *pgxpool.Config) {
	poolConfig := rd.config.Pool

	config.MaxConns = int32(poolConfig.MaxConnections)
	config.MinConns = int32(poolConfig.MinConnections)
	config.MaxConnLifetime = poolConfig.MaxConnectionLifetime
	config.MaxConnIdleTime = poolConfig.MaxConnectionIdleTime
	config.HealthCheckPeriod = poolConfig.HealthCheckPeriod

	// Set connection callbacks for metrics
	config.BeforeConnect = func(ctx context.Context, config *pgx.ConnConfig) error {
		rd.metrics.ConnectionsCreated++
		return nil
	}

	config.AfterConnect = func(ctx context.Context, conn *pgx.Conn) error {
		rd.metrics.TotalConnections++
		return nil
	}

	config.BeforeClose = func(conn *pgx.Conn) {
		rd.metrics.ConnectionsDestroyed++
		rd.metrics.TotalConnections--
	}
}

// buildConnectionString builds a PostgreSQL connection string
func (rd *ResilientDatabase) buildConnectionString(config *DatabaseConnectionConfig) string {
	return fmt.Sprintf(
		"postgres://%s:%s@%s:%d/%s?sslmode=%s",
		config.Username,
		config.Password,
		config.Host,
		config.Port,
		config.Database,
		config.SSLMode,
	)
}

// calculateBackoffDelay calculates the delay for exponential backoff with jitter
func (rd *ResilientDatabase) calculateBackoffDelay(attempt int) time.Duration {
	retryConfig := rd.config.Retry

	// Exponential backoff
	delay := time.Duration(float64(retryConfig.InitialDelay) *
		(float64(attempt) * retryConfig.Multiplier))

	// Cap at max delay
	if delay > retryConfig.MaxDelay {
		delay = retryConfig.MaxDelay
	}

	// Add jitter to avoid thundering herd
	if retryConfig.JitterFactor > 0 {
		jitter := time.Duration(float64(delay) * retryConfig.JitterFactor *
			(0.5 - (float64(attempt % 2))))
		delay += jitter
	}

	return delay
}

// executeWithRetry executes a function with retry logic
func (rd *ResilientDatabase) executeWithRetry(ctx context.Context, fn func() error) error {
	var lastErr error

	for attempt := 0; attempt < rd.config.Retry.MaxRetries; attempt++ {
		if attempt > 0 {
			// Calculate backoff delay
			delay := rd.calculateBackoffDelay(attempt)

			rd.logger.Info("Retrying database operation",
				zap.Int("attempt", attempt),
				zap.Duration("delay", delay),
				zap.Error(lastErr))

			select {
			case <-time.After(delay):
				// Continue with retry
			case <-ctx.Done():
				return ctx.Err()
			}
		}

		err := fn()
		if err == nil {
			// Success
			if attempt > 0 {
				rd.logger.Info("Database operation succeeded after retry",
					zap.Int("attempts", attempt+1))
			}
			return nil
		}

		lastErr = err

		// Check if error is retryable
		if !rd.isRetryableError(err) {
			rd.logger.Error("Non-retryable database error",
				zap.Error(err),
				zap.Int("attempt", attempt+1))
			return err
		}

		rd.logger.Warn("Retryable database error",
			zap.Error(err),
			zap.Int("attempt", attempt+1),
			zap.Int("max_retries", rd.config.Retry.MaxRetries))
	}

	rd.logger.Error("Database operation failed after all retries",
		zap.Error(lastErr),
		zap.Int("max_retries", rd.config.Retry.MaxRetries))

	return fmt.Errorf("database operation failed after %d attempts: %w",
		rd.config.Retry.MaxRetries, lastErr)
}

// isRetryableError determines if an error should trigger a retry
func (rd *ResilientDatabase) isRetryableError(err error) bool {
	if err == nil {
		return false
	}

	// Network-related errors
	retryablePatterns := []string{
		"connection refused",
		"connection reset",
		"timeout",
		"network is unreachable",
		"connection broken",
		"connection lost",
		"server closed the connection",
		"connection pool exhausted",
	}

	for _, pattern := range retryablePatterns {
		if fmt.Sprintf("%v", err) == pattern {
			return true
		}
	}

	// PostgreSQL-specific retryable errors
	if pgErr, ok := err.(*pgconn.PgError); ok {
		switch pgErr.Code {
		case "53300", // too_many_connections
			"53400", // configuration_limit_exceeded
			"08006", // connection_failure
			"08001": // sqlclient_unable_to_establish_sqlconnection
			return true
		}
	}

	return false
}

// Query executes a query with resilience features
func (rd *ResilientDatabase) Query(ctx context.Context, sql string, args ...interface{}) (pgx.Rows, error) {
	start := time.Now()
	defer func() {
		rd.updateQueryMetrics(time.Since(start))
	}()

	if rd.circuitBreaker != nil {
		return rd.queryWithCircuitBreaker(ctx, sql, args...)
	}

	return rd.queryWithRetry(ctx, sql, args...)
}

// queryWithCircuitBreaker executes query with circuit breaker protection
func (rd *ResilientDatabase) queryWithCircuitBreaker(ctx context.Context, sql string, args ...interface{}) (pgx.Rows, error) {
	var result pgx.Rows
	var err error

	err = rd.executeWithRetry(ctx, func() error {
		result, err = rd.queryWithRetry(ctx, sql, args...)
		return err
	})

	return result, err
}

// queryWithRetry executes query with retry logic
func (rd *ResilientDatabase) queryWithRetry(ctx context.Context, sql string, args ...interface{}) (pgx.Rows, error) {
	var result pgx.Rows
	var err error

	err = rd.executeWithRetry(ctx, func() error {
		pool := rd.getReadPool()
		result, err = pool.Query(ctx, sql, args...)
		return err
	})

	return result, err
}

// QueryRow executes a single row query with resilience features
func (rd *ResilientDatabase) QueryRow(ctx context.Context, sql string, args ...interface{}) pgx.Row {
	start := time.Now()
	defer func() {
		rd.updateQueryMetrics(time.Since(start))
	}()

	pool := rd.getReadPool()
	return pool.QueryRow(ctx, sql, args...)
}

// Exec executes a command with resilience features
func (rd *ResilientDatabase) Exec(ctx context.Context, sql string, args ...interface{}) error {
	start := time.Now()
	defer func() {
		rd.updateQueryMetrics(time.Since(start))
	}()

	return rd.executeWithRetry(ctx, func() error {
		_, err := rd.primaryPool.Exec(ctx, sql, args...)
		return err
	})
}

// getReadPool returns the appropriate pool for read operations
func (rd *ResilientDatabase) getReadPool() *pgxpool.Pool {
	rd.mutex.RLock()
	defer rd.mutex.RUnlock()

	// Use replica if available and healthy
	if rd.replicaPool != nil && !rd.usingReplica {
		rd.metrics.ReplicaConnections++
		return rd.replicaPool
	}

	return rd.primaryPool
}

// updateQueryMetrics updates query performance metrics
func (rd *ResilientDatabase) updateQueryMetrics(duration time.Duration) {
	rd.metrics.TotalQueries++

	// Update average query time (simple moving average)
	if rd.metrics.TotalQueries == 1 {
		rd.metrics.AverageQueryTime = duration
	} else {
		rd.metrics.AverageQueryTime = (rd.metrics.AverageQueryTime + duration) / 2
	}

	// Track slow queries (>1 second)
	if duration > time.Second {
		rd.metrics.SlowQueries++
	}
}

// GetMetrics returns current database metrics
func (rd *ResilientDatabase) GetMetrics() *DatabaseMetrics {
	rd.mutex.RLock()
	defer rd.mutex.RUnlock()

	// Update connection metrics from pools
	if rd.primaryPool != nil {
		stat := rd.primaryPool.Stat()
		rd.metrics.ActiveConnections = int64(stat.AcquiredConns())
		rd.metrics.IdleConnections = int64(stat.IdleConns())
		rd.metrics.AverageAcquireTime = stat.AcquireDuration()
	}

	// Create a copy to avoid race conditions
	metrics := *rd.metrics
	metrics.IsHealthy = rd.isHealthy

	return &metrics
}

// Health returns the current health status
func (rd *ResilientDatabase) Health() error {
	rd.mutex.RLock()
	defer rd.mutex.RUnlock()

	if !rd.isHealthy {
		return fmt.Errorf("database is unhealthy")
	}

	return nil
}

// Close gracefully shuts down the database connections
func (rd *ResilientDatabase) Close() {
	rd.shutdownOnce.Do(func() {
		rd.logger.Info("Shutting down resilient database...")

		// Stop health checker
		if rd.healthChecker != nil {
			rd.healthChecker.Stop()
		}

		// Close connection pools
		if rd.primaryPool != nil {
			rd.primaryPool.Close()
		}

		if rd.replicaPool != nil {
			rd.replicaPool.Close()
		}

		close(rd.shutdownChan)
		rd.logger.Info("Resilient database shutdown completed")
	})
}

// buildDatabaseConfig builds database configuration from app config
func buildDatabaseConfig(cfg *config.Config) *DatabaseConfig {
	return &DatabaseConfig{
		Primary: DatabaseConnectionConfig{
			Host:     cfg.Database.Host,
			Port:     cfg.Database.Port,
			Database: cfg.Database.Database,
			Username: cfg.Database.Username,
			Password: cfg.Database.Password,
			SSLMode:  cfg.Database.SSLMode,
		},
		Pool: DatabasePoolConfig{
			MaxConnections:        cfg.Database.MaxOpenConns,
			MinConnections:        cfg.Database.MaxIdleConns,
			MaxConnectionLifetime: cfg.Database.ConnMaxLifetime,
			MaxConnectionIdleTime: cfg.Database.ConnMaxIdleTime,
			HealthCheckPeriod:     time.Minute,
			AcquireTimeout:        time.Second * 30,
		},
		HealthCheck: DatabaseHealthConfig{
			Enabled:          true,
			CheckInterval:    time.Second * 30,
			Timeout:          time.Second * 5,
			FailureThreshold: 3,
			RetryInterval:    time.Second * 10,
		},
		Retry: DatabaseRetryConfig{
			MaxRetries:   3,
			InitialDelay: time.Millisecond * 100,
			MaxDelay:     time.Second * 5,
			Multiplier:   2.0,
			JitterFactor: 0.1,
		},
		CircuitBreaker: &CircuitBreakerConfig{
			MaxFailures: 5,
			Timeout:     time.Minute,
			RetryDelay:  time.Second * 10,
		},
	}
}
