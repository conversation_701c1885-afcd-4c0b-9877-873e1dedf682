package database

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/jackc/pgx/v5/pgxpool"
	"go.uber.org/zap"
)

// Connection Pool Optimizer for Phase 4: Performance Optimization
//
// Features:
// - Configure optimal connection pool sizes
// - Implement connection lifecycle management
// - Add connection pool monitoring and alerting
// - Create connection leak detection
// - Dynamic pool sizing based on load
// - Connection health monitoring
// - Pool performance metrics

// ConnectionPoolOptimizer provides optimized connection pool management
type ConnectionPoolOptimizer struct {
	pool         *pgxpool.Pool
	config       *PoolOptimizerConfig
	logger       *zap.Logger
	metrics      *PoolMetrics
	alerts       *PoolAlerts
	lifecycle    *ConnectionLifecycle
	leakDetector *ConnectionLeakDetector
	mutex        sync.RWMutex
	isRunning    bool
	stopChannel  chan struct{}
}

// PoolOptimizerConfig contains configuration for connection pool optimization
type PoolOptimizerConfig struct {
	// Pool sizing configuration
	MinConnections    int           // Minimum connections to maintain
	MaxConnections    int           // Maximum connections allowed
	TargetUtilization float64       // Target pool utilization (0.0-1.0)
	ScalingThreshold  float64       // Threshold to trigger scaling
	ScalingInterval   time.Duration // How often to check for scaling

	// Connection lifecycle configuration
	MaxConnectionLifetime time.Duration // Maximum lifetime of a connection
	MaxConnectionIdleTime time.Duration // Maximum idle time before closing
	HealthCheckInterval   time.Duration // How often to check connection health
	AcquireTimeout        time.Duration // Timeout for acquiring connections

	// Monitoring configuration
	MetricsInterval       time.Duration // How often to collect metrics
	AlertThresholds       AlertThresholds
	EnableLeakDetection   bool
	LeakDetectionInterval time.Duration

	// Performance configuration
	StatementTimeout         time.Duration
	IdleInTransactionTimeout time.Duration
}

// AlertThresholds defines thresholds for various alerts
type AlertThresholds struct {
	HighUtilization    float64 // Alert when utilization exceeds this
	LowUtilization     float64 // Alert when utilization is below this
	AcquireTimeoutRate float64 // Alert when timeout rate exceeds this
	ConnectionErrors   int64   // Alert when error count exceeds this
	LeakDetectionLimit int     // Alert when potential leaks exceed this
}

// DefaultPoolOptimizerConfig returns optimized default configuration
func DefaultPoolOptimizerConfig() *PoolOptimizerConfig {
	return &PoolOptimizerConfig{
		MinConnections:    5,
		MaxConnections:    25,
		TargetUtilization: 0.7,
		ScalingThreshold:  0.8,
		ScalingInterval:   30 * time.Second,

		MaxConnectionLifetime: time.Hour,
		MaxConnectionIdleTime: 30 * time.Minute,
		HealthCheckInterval:   time.Minute,
		AcquireTimeout:        30 * time.Second,

		MetricsInterval:       30 * time.Second,
		EnableLeakDetection:   true,
		LeakDetectionInterval: 5 * time.Minute,

		StatementTimeout:         30 * time.Second,
		IdleInTransactionTimeout: 60 * time.Second,

		AlertThresholds: AlertThresholds{
			HighUtilization:    0.9,
			LowUtilization:     0.1,
			AcquireTimeoutRate: 0.05, // 5%
			ConnectionErrors:   100,
			LeakDetectionLimit: 5,
		},
	}
}

// PoolMetrics tracks connection pool performance metrics
type PoolMetrics struct {
	// Current state metrics
	TotalConnections    int32
	AcquiredConnections int32
	IdleConnections     int32
	MaxConnections      int32

	// Utilization metrics
	CurrentUtilization float64
	AverageUtilization float64
	PeakUtilization    float64

	// Performance metrics
	AcquireCount       int64
	AcquireTimeouts    int64
	AcquireDuration    time.Duration
	AverageAcquireTime time.Duration
	MaxAcquireTime     time.Duration

	// Connection lifecycle metrics
	ConnectionsCreated   int64
	ConnectionsDestroyed int64
	ConnectionErrors     int64
	HealthCheckFailures  int64

	// Leak detection metrics
	PotentialLeaks    int
	LeakDetectionRuns int64

	// Timing metrics
	LastMetricsUpdate time.Time
	UptimeSeconds     int64
}

// PoolAlerts manages alerts for connection pool issues
type PoolAlerts struct {
	alerts []PoolAlert
	logger *zap.Logger
	mutex  sync.Mutex
}

// PoolAlert represents a connection pool alert
type PoolAlert struct {
	Type         AlertType
	Severity     AlertSeverity
	Message      string
	Timestamp    time.Time
	Acknowledged bool
	Count        int64
}

// AlertType represents the type of alert
type AlertType string

const (
	AlertHighUtilization     AlertType = "HIGH_UTILIZATION"
	AlertLowUtilization      AlertType = "LOW_UTILIZATION"
	AlertAcquireTimeouts     AlertType = "ACQUIRE_TIMEOUTS"
	AlertConnectionErrors    AlertType = "CONNECTION_ERRORS"
	AlertHealthCheckFailures AlertType = "HEALTH_CHECK_FAILURES"
	AlertPotentialLeaks      AlertType = "POTENTIAL_LEAKS"
	AlertPoolExhaustion      AlertType = "POOL_EXHAUSTION"
)

// AlertSeverity represents the severity of an alert
type AlertSeverity string

const (
	SeverityInfo     AlertSeverity = "INFO"
	SeverityWarning  AlertSeverity = "WARNING"
	SeverityCritical AlertSeverity = "CRITICAL"
)

// ConnectionLifecycle manages connection lifecycle events
type ConnectionLifecycle struct {
	createdConnections map[uintptr]*ConnectionInfo
	logger             *zap.Logger
	mutex              sync.RWMutex
}

// ConnectionInfo tracks information about individual connections
type ConnectionInfo struct {
	ID         uintptr
	CreatedAt  time.Time
	LastUsedAt time.Time
	UsageCount int64
	ErrorCount int64
	IsHealthy  bool
	StackTrace string // For leak detection
}

// ConnectionLeakDetector detects potential connection leaks
type ConnectionLeakDetector struct {
	suspiciousConnections map[uintptr]*SuspiciousConnection
	config                *PoolOptimizerConfig
	logger                *zap.Logger
	mutex                 sync.RWMutex
}

// SuspiciousConnection represents a potentially leaked connection
type SuspiciousConnection struct {
	ID           uintptr
	DetectedAt   time.Time
	LastUsedAt   time.Time
	IdleDuration time.Duration
	StackTrace   string
	Warnings     int
}

// NewConnectionPoolOptimizer creates a new connection pool optimizer
func NewConnectionPoolOptimizer(pool *pgxpool.Pool, config *PoolOptimizerConfig, logger *zap.Logger) *ConnectionPoolOptimizer {
	if config == nil {
		config = DefaultPoolOptimizerConfig()
	}

	optimizer := &ConnectionPoolOptimizer{
		pool:   pool,
		config: config,
		logger: logger,
		metrics: &PoolMetrics{
			LastMetricsUpdate: time.Now(),
		},
		alerts: &PoolAlerts{
			logger: logger,
		},
		lifecycle: &ConnectionLifecycle{
			createdConnections: make(map[uintptr]*ConnectionInfo),
			logger:             logger,
		},
		leakDetector: &ConnectionLeakDetector{
			suspiciousConnections: make(map[uintptr]*SuspiciousConnection),
			config:                config,
			logger:                logger,
		},
		stopChannel: make(chan struct{}),
	}

	return optimizer
}

// Start begins the connection pool optimization monitoring
func (cpo *ConnectionPoolOptimizer) Start() error {
	cpo.mutex.Lock()
	defer cpo.mutex.Unlock()

	if cpo.isRunning {
		return fmt.Errorf("connection pool optimizer is already running")
	}

	cpo.isRunning = true

	// Start monitoring goroutines
	go cpo.startMetricsCollection()
	go cpo.startPoolScaling()
	go cpo.startHealthMonitoring()

	if cpo.config.EnableLeakDetection {
		go cpo.startLeakDetection()
	}

	cpo.logger.Info("Connection pool optimizer started",
		zap.Int("min_connections", cpo.config.MinConnections),
		zap.Int("max_connections", cpo.config.MaxConnections),
		zap.Float64("target_utilization", cpo.config.TargetUtilization),
	)

	return nil
}

// Stop stops the connection pool optimization monitoring
func (cpo *ConnectionPoolOptimizer) Stop() {
	cpo.mutex.Lock()
	defer cpo.mutex.Unlock()

	if !cpo.isRunning {
		return
	}

	cpo.isRunning = false
	close(cpo.stopChannel)

	cpo.logger.Info("Connection pool optimizer stopped")
}

// startMetricsCollection starts the metrics collection routine
func (cpo *ConnectionPoolOptimizer) startMetricsCollection() {
	ticker := time.NewTicker(cpo.config.MetricsInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			cpo.collectMetrics()
		case <-cpo.stopChannel:
			return
		}
	}
}

// collectMetrics collects current pool metrics
func (cpo *ConnectionPoolOptimizer) collectMetrics() {
	stat := cpo.pool.Stat()

	cpo.mutex.Lock()
	defer cpo.mutex.Unlock()

	// Update current state metrics
	cpo.metrics.TotalConnections = stat.TotalConns()
	cpo.metrics.AcquiredConnections = stat.AcquiredConns()
	cpo.metrics.IdleConnections = stat.IdleConns()
	cpo.metrics.MaxConnections = stat.MaxConns()

	// Calculate utilization
	if cpo.metrics.MaxConnections > 0 {
		cpo.metrics.CurrentUtilization = float64(cpo.metrics.AcquiredConnections) / float64(cpo.metrics.MaxConnections)
	}

	// Update peak utilization
	if cpo.metrics.CurrentUtilization > cpo.metrics.PeakUtilization {
		cpo.metrics.PeakUtilization = cpo.metrics.CurrentUtilization
	}

	// Update average utilization (simple moving average)
	if cpo.metrics.AverageUtilization == 0 {
		cpo.metrics.AverageUtilization = cpo.metrics.CurrentUtilization
	} else {
		cpo.metrics.AverageUtilization = (cpo.metrics.AverageUtilization + cpo.metrics.CurrentUtilization) / 2
	}

	// Update acquire metrics
	cpo.metrics.AcquireCount = stat.AcquireCount()
	cpo.metrics.AcquireDuration = stat.AcquireDuration()

	// Calculate average acquire time
	if cpo.metrics.AcquireCount > 0 {
		cpo.metrics.AverageAcquireTime = cpo.metrics.AcquireDuration / time.Duration(cpo.metrics.AcquireCount)
	}

	// Update timing
	cpo.metrics.LastMetricsUpdate = time.Now()
	cpo.metrics.UptimeSeconds = int64(time.Since(cpo.metrics.LastMetricsUpdate).Seconds())

	// Check for alerts
	cpo.checkAlerts()

	// Log metrics periodically
	cpo.logger.Debug("Pool metrics collected",
		zap.Int32("total_connections", cpo.metrics.TotalConnections),
		zap.Int32("acquired_connections", cpo.metrics.AcquiredConnections),
		zap.Int32("idle_connections", cpo.metrics.IdleConnections),
		zap.Float64("utilization", cpo.metrics.CurrentUtilization),
		zap.Duration("avg_acquire_time", cpo.metrics.AverageAcquireTime),
	)
}

// checkAlerts checks for alert conditions and triggers alerts
func (cpo *ConnectionPoolOptimizer) checkAlerts() {
	thresholds := cpo.config.AlertThresholds

	// Check high utilization
	if cpo.metrics.CurrentUtilization > thresholds.HighUtilization {
		cpo.triggerAlert(AlertHighUtilization, SeverityWarning,
			fmt.Sprintf("High pool utilization: %.2f%%", cpo.metrics.CurrentUtilization*100))
	}

	// Check low utilization
	if cpo.metrics.CurrentUtilization < thresholds.LowUtilization && cpo.metrics.TotalConnections > int32(cpo.config.MinConnections) {
		cpo.triggerAlert(AlertLowUtilization, SeverityInfo,
			fmt.Sprintf("Low pool utilization: %.2f%%", cpo.metrics.CurrentUtilization*100))
	}

	// Check acquire timeouts
	if cpo.metrics.AcquireCount > 0 {
		timeoutRate := float64(cpo.metrics.AcquireTimeouts) / float64(cpo.metrics.AcquireCount)
		if timeoutRate > thresholds.AcquireTimeoutRate {
			cpo.triggerAlert(AlertAcquireTimeouts, SeverityCritical,
				fmt.Sprintf("High acquire timeout rate: %.2f%%", timeoutRate*100))
		}
	}

	// Check connection errors
	if cpo.metrics.ConnectionErrors > thresholds.ConnectionErrors {
		cpo.triggerAlert(AlertConnectionErrors, SeverityCritical,
			fmt.Sprintf("High connection error count: %d", cpo.metrics.ConnectionErrors))
	}

	// Check potential leaks
	if cpo.metrics.PotentialLeaks > thresholds.LeakDetectionLimit {
		cpo.triggerAlert(AlertPotentialLeaks, SeverityWarning,
			fmt.Sprintf("Potential connection leaks detected: %d", cpo.metrics.PotentialLeaks))
	}

	// Check pool exhaustion
	if cpo.metrics.AcquiredConnections >= cpo.metrics.MaxConnections {
		cpo.triggerAlert(AlertPoolExhaustion, SeverityCritical,
			"Connection pool exhausted - all connections in use")
	}
}

// triggerAlert triggers a new alert
func (cpo *ConnectionPoolOptimizer) triggerAlert(alertType AlertType, severity AlertSeverity, message string) {
	cpo.alerts.mutex.Lock()
	defer cpo.alerts.mutex.Unlock()

	// Check if this alert already exists and increment count
	for i := range cpo.alerts.alerts {
		if cpo.alerts.alerts[i].Type == alertType && !cpo.alerts.alerts[i].Acknowledged {
			cpo.alerts.alerts[i].Count++
			cpo.alerts.alerts[i].Timestamp = time.Now()
			return
		}
	}

	// Create new alert
	alert := PoolAlert{
		Type:      alertType,
		Severity:  severity,
		Message:   message,
		Timestamp: time.Now(),
		Count:     1,
	}

	cpo.alerts.alerts = append(cpo.alerts.alerts, alert)

	// Log the alert
	logLevel := zap.InfoLevel
	if severity == SeverityWarning {
		logLevel = zap.WarnLevel
	} else if severity == SeverityCritical {
		logLevel = zap.ErrorLevel
	}

	cpo.logger.Log(logLevel, "Connection pool alert triggered",
		zap.String("type", string(alertType)),
		zap.String("severity", string(severity)),
		zap.String("message", message),
	)
}

// startPoolScaling starts the automatic pool scaling routine
func (cpo *ConnectionPoolOptimizer) startPoolScaling() {
	ticker := time.NewTicker(cpo.config.ScalingInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			cpo.performPoolScaling()
		case <-cpo.stopChannel:
			return
		}
	}
}

// performPoolScaling adjusts pool size based on utilization
func (cpo *ConnectionPoolOptimizer) performPoolScaling() {
	// Note: pgxpool doesn't support dynamic max connection changes
	// This is a placeholder for monitoring and alerting
	// In practice, you would need to recreate the pool or use a custom pool implementation

	cpo.mutex.RLock()
	utilization := cpo.metrics.CurrentUtilization
	totalConns := cpo.metrics.TotalConnections
	maxConns := cpo.metrics.MaxConnections
	cpo.mutex.RUnlock()

	// Log scaling decision
	if utilization > cpo.config.ScalingThreshold {
		cpo.logger.Info("Pool scaling recommended - high utilization",
			zap.Float64("utilization", utilization),
			zap.Int32("current_max", maxConns),
			zap.String("recommendation", "Consider increasing max_connections"),
		)
	} else if utilization < cpo.config.AlertThresholds.LowUtilization && totalConns > int32(cpo.config.MinConnections) {
		cpo.logger.Info("Pool scaling recommended - low utilization",
			zap.Float64("utilization", utilization),
			zap.Int32("current_total", totalConns),
			zap.String("recommendation", "Consider decreasing max_connections"),
		)
	}
}

// startHealthMonitoring starts connection health monitoring
func (cpo *ConnectionPoolOptimizer) startHealthMonitoring() {
	ticker := time.NewTicker(cpo.config.HealthCheckInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			cpo.performHealthCheck()
		case <-cpo.stopChannel:
			return
		}
	}
}

// performHealthCheck performs a health check on the connection pool
func (cpo *ConnectionPoolOptimizer) performHealthCheck() {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// Simple ping test
	if err := cpo.pool.Ping(ctx); err != nil {
		cpo.mutex.Lock()
		cpo.metrics.HealthCheckFailures++
		cpo.mutex.Unlock()

		cpo.logger.Error("Connection pool health check failed", zap.Error(err))
		cpo.triggerAlert(AlertHealthCheckFailures, SeverityCritical,
			fmt.Sprintf("Health check failed: %v", err))
	}
}

// startLeakDetection starts connection leak detection
func (cpo *ConnectionPoolOptimizer) startLeakDetection() {
	ticker := time.NewTicker(cpo.config.LeakDetectionInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			cpo.performLeakDetection()
		case <-cpo.stopChannel:
			return
		}
	}
}

// performLeakDetection performs connection leak detection
func (cpo *ConnectionPoolOptimizer) performLeakDetection() {
	cpo.leakDetector.mutex.Lock()
	defer cpo.leakDetector.mutex.Unlock()

	cpo.leakDetector.logger.Debug("Performing connection leak detection")

	// Simple leak detection: connections idle for too long
	cutoff := time.Now().Add(-cpo.config.MaxConnectionIdleTime * 2)
	leakCount := 0

	for id, suspiciousConn := range cpo.leakDetector.suspiciousConnections {
		if suspiciousConn.LastUsedAt.Before(cutoff) {
			leakCount++
			suspiciousConn.Warnings++

			if suspiciousConn.Warnings > 3 {
				cpo.logger.Warn("Potential connection leak detected",
					zap.Uintptr("connection_id", id),
					zap.Duration("idle_duration", time.Since(suspiciousConn.LastUsedAt)),
					zap.Int("warnings", suspiciousConn.Warnings),
				)
			}
		}
	}

	// Update metrics
	cpo.mutex.Lock()
	cpo.metrics.PotentialLeaks = leakCount
	cpo.metrics.LeakDetectionRuns++
	cpo.mutex.Unlock()

	cpo.leakDetector.logger.Debug("Leak detection completed",
		zap.Int("potential_leaks", leakCount),
		zap.Int("monitored_connections", len(cpo.leakDetector.suspiciousConnections)),
	)
}

// GetMetrics returns current pool metrics
func (cpo *ConnectionPoolOptimizer) GetMetrics() *PoolMetrics {
	cpo.mutex.RLock()
	defer cpo.mutex.RUnlock()

	// Return a copy to avoid race conditions
	metricsCopy := *cpo.metrics
	return &metricsCopy
}

// GetAlerts returns current active alerts
func (cpo *ConnectionPoolOptimizer) GetAlerts() []PoolAlert {
	cpo.alerts.mutex.Lock()
	defer cpo.alerts.mutex.Unlock()

	// Return only unacknowledged alerts
	var activeAlerts []PoolAlert
	for _, alert := range cpo.alerts.alerts {
		if !alert.Acknowledged {
			activeAlerts = append(activeAlerts, alert)
		}
	}

	return activeAlerts
}

// AcknowledgeAlert acknowledges an alert
func (cpo *ConnectionPoolOptimizer) AcknowledgeAlert(alertType AlertType) {
	cpo.alerts.mutex.Lock()
	defer cpo.alerts.mutex.Unlock()

	for i := range cpo.alerts.alerts {
		if cpo.alerts.alerts[i].Type == alertType && !cpo.alerts.alerts[i].Acknowledged {
			cpo.alerts.alerts[i].Acknowledged = true
			cpo.logger.Info("Alert acknowledged", zap.String("type", string(alertType)))
			break
		}
	}
}

// OptimizePool provides recommendations for pool optimization
func (cpo *ConnectionPoolOptimizer) OptimizePool() PoolOptimizationRecommendations {
	cpo.mutex.RLock()
	metrics := *cpo.metrics
	cpo.mutex.RUnlock()

	recommendations := PoolOptimizationRecommendations{
		CurrentConfig: PoolConfigSummary{
			MinConnections: cpo.config.MinConnections,
			MaxConnections: cpo.config.MaxConnections,
			CurrentTotal:   int(metrics.TotalConnections),
			CurrentActive:  int(metrics.AcquiredConnections),
		},
		Utilization: UtilizationAnalysis{
			Current: metrics.CurrentUtilization,
			Average: metrics.AverageUtilization,
			Peak:    metrics.PeakUtilization,
		},
	}

	// Generate recommendations based on metrics
	if metrics.AverageUtilization > 0.8 {
		recommendations.Recommendations = append(recommendations.Recommendations,
			"Consider increasing max_connections - high average utilization detected")
	}

	if metrics.AverageUtilization < 0.2 && metrics.TotalConnections > int32(cpo.config.MinConnections) {
		recommendations.Recommendations = append(recommendations.Recommendations,
			"Consider decreasing max_connections - low average utilization detected")
	}

	if metrics.AverageAcquireTime > 100*time.Millisecond {
		recommendations.Recommendations = append(recommendations.Recommendations,
			"Consider increasing max_connections - high acquire times detected")
	}

	return recommendations
}

// PoolOptimizationRecommendations contains pool optimization recommendations
type PoolOptimizationRecommendations struct {
	CurrentConfig   PoolConfigSummary
	Utilization     UtilizationAnalysis
	Recommendations []string
}

// PoolConfigSummary summarizes current pool configuration
type PoolConfigSummary struct {
	MinConnections int
	MaxConnections int
	CurrentTotal   int
	CurrentActive  int
}

// UtilizationAnalysis provides utilization analysis
type UtilizationAnalysis struct {
	Current float64
	Average float64
	Peak    float64
}
