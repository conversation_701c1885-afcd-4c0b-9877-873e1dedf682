package database

import (
	"encoding/json"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"strings"
	"time"

	"carnow-backend/internal/config"
)

// DatabaseAuditEvent represents a database audit event
type DatabaseAuditEvent struct {
	Timestamp   time.Time `json:"timestamp"`
	EventType   string    `json:"event_type"`
	Description string    `json:"description"`
	Query       string    `json:"query,omitempty"`
	ArgCount    int       `json:"arg_count,omitempty"`
	UserID      string    `json:"user_id,omitempty"`
	IPAddress   string    `json:"ip_address,omitempty"`
	SessionID   string    `json:"session_id,omitempty"`
	Duration    int64     `json:"duration_ms,omitempty"`
	Success     bool      `json:"success"`
	Error       string    `json:"error,omitempty"`
}

// DatabaseAuditLogger handles database access audit logging
type DatabaseAuditLogger struct {
	enabled   bool
	logFile   *os.File
	config    *config.Config
	logPath   string
	maxSize   int64
	retention int
}

// NewDatabaseAuditLogger creates a new database audit logger
func NewDatabaseAuditLogger(cfg *config.Config) (*DatabaseAuditLogger, error) {
	// Check if audit logging is enabled
	enabled := cfg.App.Environment == "production" ||
		os.Getenv("CARNOW_DB_AUDIT_ENABLED") == "true"

	if !enabled {
		log.Println("📝 Database audit logging disabled")
		return &DatabaseAuditLogger{enabled: false}, nil
	}

	// Create audit log directory
	logDir := "logs/database"
	if customDir := os.Getenv("CARNOW_DB_AUDIT_LOG_DIR"); customDir != "" {
		logDir = customDir
	}

	if err := os.MkdirAll(logDir, 0755); err != nil {
		return nil, fmt.Errorf("failed to create audit log directory: %w", err)
	}

	// Create audit log file
	logPath := filepath.Join(logDir, fmt.Sprintf("db_audit_%s.log",
		time.Now().Format("2006-01-02")))

	logFile, err := os.OpenFile(logPath, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
	if err != nil {
		return nil, fmt.Errorf("failed to create audit log file: %w", err)
	}

	logger := &DatabaseAuditLogger{
		enabled:   true,
		logFile:   logFile,
		config:    cfg,
		logPath:   logPath,
		maxSize:   100 * 1024 * 1024, // 100MB default
		retention: 30,                // 30 days default
	}

	// Configure from environment
	logger.configureFromEnv()

	// Log initialization
	logger.LogEvent("AUDIT_LOGGER_INITIALIZED", "Database audit logger initialized")

	log.Printf("📝 Database audit logging enabled: %s", logPath)
	return logger, nil
}

// configureFromEnv configures the audit logger from environment variables
func (dal *DatabaseAuditLogger) configureFromEnv() {
	if maxSizeStr := os.Getenv("CARNOW_DB_AUDIT_MAX_SIZE_MB"); maxSizeStr != "" {
		if maxSize := parseInt64(maxSizeStr); maxSize > 0 {
			dal.maxSize = maxSize * 1024 * 1024 // Convert MB to bytes
		}
	}

	if retentionStr := os.Getenv("CARNOW_DB_AUDIT_RETENTION_DAYS"); retentionStr != "" {
		if retention := parseInt(retentionStr); retention > 0 {
			dal.retention = retention
		}
	}
}

// LogConnection logs database connection events
func (dal *DatabaseAuditLogger) LogConnection(eventType, description string) {
	if !dal.enabled {
		return
	}

	event := DatabaseAuditEvent{
		Timestamp:   time.Now().UTC(),
		EventType:   eventType,
		Description: description,
		Success:     true,
	}

	dal.writeEvent(event)
}

// LogQuery logs database query execution
func (dal *DatabaseAuditLogger) LogQuery(eventType, query string, argCount int) {
	if !dal.enabled {
		return
	}

	// Sanitize query for logging (remove sensitive data)
	sanitizedQuery := dal.sanitizeQuery(query)

	event := DatabaseAuditEvent{
		Timestamp:   time.Now().UTC(),
		EventType:   eventType,
		Description: "Database query executed",
		Query:       sanitizedQuery,
		ArgCount:    argCount,
		Success:     true,
	}

	dal.writeEvent(event)
}

// LogEvent logs general database events
func (dal *DatabaseAuditLogger) LogEvent(eventType, description string) {
	if !dal.enabled {
		return
	}

	event := DatabaseAuditEvent{
		Timestamp:   time.Now().UTC(),
		EventType:   eventType,
		Description: description,
		Success:     true,
	}

	dal.writeEvent(event)
}

// LogError logs database errors
func (dal *DatabaseAuditLogger) LogError(eventType, description, errorMsg string) {
	if !dal.enabled {
		return
	}

	event := DatabaseAuditEvent{
		Timestamp:   time.Now().UTC(),
		EventType:   eventType,
		Description: description,
		Success:     false,
		Error:       errorMsg,
	}

	dal.writeEvent(event)
}

// LogQueryWithContext logs database query with additional context
func (dal *DatabaseAuditLogger) LogQueryWithContext(eventType, query string, argCount int, userID, ipAddress, sessionID string, duration time.Duration, err error) {
	if !dal.enabled {
		return
	}

	// Sanitize query for logging
	sanitizedQuery := dal.sanitizeQuery(query)

	event := DatabaseAuditEvent{
		Timestamp:   time.Now().UTC(),
		EventType:   eventType,
		Description: "Database query executed with context",
		Query:       sanitizedQuery,
		ArgCount:    argCount,
		UserID:      userID,
		IPAddress:   ipAddress,
		SessionID:   sessionID,
		Duration:    duration.Milliseconds(),
		Success:     err == nil,
	}

	if err != nil {
		event.Error = err.Error()
	}

	dal.writeEvent(event)
}

// sanitizeQuery removes sensitive information from queries for logging
func (dal *DatabaseAuditLogger) sanitizeQuery(query string) string {
	// Convert to lowercase for pattern matching
	lowerQuery := strings.ToLower(query)

	// List of sensitive patterns to sanitize
	sensitivePatterns := []string{
		"password",
		"secret",
		"token",
		"key",
		"auth",
		"credential",
	}

	// Check if query contains sensitive patterns
	containsSensitive := false
	for _, pattern := range sensitivePatterns {
		if strings.Contains(lowerQuery, pattern) {
			containsSensitive = true
			break
		}
	}

	// If sensitive, return sanitized version
	if containsSensitive {
		// Extract query type and table name
		words := strings.Fields(query)
		if len(words) >= 2 {
			return fmt.Sprintf("%s %s [SANITIZED - contains sensitive data]",
				strings.ToUpper(words[0]), words[1])
		}
		return "[SANITIZED - contains sensitive data]"
	}

	// Limit query length for logging
	maxLength := 500
	if len(query) > maxLength {
		return query[:maxLength] + "... [TRUNCATED]"
	}

	return query
}

// writeEvent writes an audit event to the log file
func (dal *DatabaseAuditLogger) writeEvent(event DatabaseAuditEvent) {
	if dal.logFile == nil {
		return
	}

	// Convert event to JSON
	eventJSON, err := json.Marshal(event)
	if err != nil {
		log.Printf("❌ Failed to marshal audit event: %v", err)
		return
	}

	// Write to log file
	if _, err := dal.logFile.WriteString(string(eventJSON) + "\n"); err != nil {
		log.Printf("❌ Failed to write audit event: %v", err)
		return
	}

	// Flush to ensure data is written
	dal.logFile.Sync()

	// Check if log rotation is needed
	dal.checkLogRotation()
}

// checkLogRotation checks if log rotation is needed based on file size
func (dal *DatabaseAuditLogger) checkLogRotation() {
	if dal.logFile == nil {
		return
	}

	// Get current file size
	fileInfo, err := dal.logFile.Stat()
	if err != nil {
		return
	}

	// Rotate if file size exceeds maximum
	if fileInfo.Size() > dal.maxSize {
		dal.rotateLog()
	}
}

// rotateLog rotates the current log file
func (dal *DatabaseAuditLogger) rotateLog() {
	if dal.logFile == nil {
		return
	}

	// Close current log file
	dal.logFile.Close()

	// Rename current log file with timestamp
	timestamp := time.Now().Format("2006-01-02_15-04-05")
	rotatedPath := strings.Replace(dal.logPath, ".log", fmt.Sprintf("_%s.log", timestamp), 1)

	if err := os.Rename(dal.logPath, rotatedPath); err != nil {
		log.Printf("❌ Failed to rotate audit log: %v", err)
		return
	}

	// Create new log file
	logFile, err := os.OpenFile(dal.logPath, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
	if err != nil {
		log.Printf("❌ Failed to create new audit log file: %v", err)
		return
	}

	dal.logFile = logFile

	// Log rotation event
	dal.LogEvent("LOG_ROTATED", fmt.Sprintf("Audit log rotated to: %s", rotatedPath))

	// Clean up old log files
	dal.cleanupOldLogs()
}

// cleanupOldLogs removes old audit log files based on retention policy
func (dal *DatabaseAuditLogger) cleanupOldLogs() {
	logDir := filepath.Dir(dal.logPath)

	// Read directory contents
	files, err := os.ReadDir(logDir)
	if err != nil {
		log.Printf("❌ Failed to read audit log directory: %v", err)
		return
	}

	// Calculate cutoff date
	cutoffDate := time.Now().AddDate(0, 0, -dal.retention)

	// Remove old files
	for _, file := range files {
		if !strings.HasPrefix(file.Name(), "db_audit_") || !strings.HasSuffix(file.Name(), ".log") {
			continue
		}

		filePath := filepath.Join(logDir, file.Name())
		fileInfo, err := file.Info()
		if err != nil {
			continue
		}

		if fileInfo.ModTime().Before(cutoffDate) {
			if err := os.Remove(filePath); err != nil {
				log.Printf("❌ Failed to remove old audit log: %v", err)
			} else {
				log.Printf("🧹 Removed old audit log: %s", file.Name())
			}
		}
	}
}

// Close closes the audit logger
func (dal *DatabaseAuditLogger) Close() {
	if !dal.enabled || dal.logFile == nil {
		return
	}

	// Log closure event
	dal.LogEvent("AUDIT_LOGGER_CLOSED", "Database audit logger closed")

	// Close log file
	dal.logFile.Close()
	dal.logFile = nil

	log.Println("📝 Database audit logger closed")
}

// GetAuditStats returns statistics about the audit logging
func (dal *DatabaseAuditLogger) GetAuditStats() map[string]interface{} {
	stats := map[string]interface{}{
		"enabled":   dal.enabled,
		"log_path":  dal.logPath,
		"max_size":  dal.maxSize,
		"retention": dal.retention,
	}

	if dal.enabled && dal.logFile != nil {
		if fileInfo, err := dal.logFile.Stat(); err == nil {
			stats["current_size"] = fileInfo.Size()
			stats["last_modified"] = fileInfo.ModTime()
		}
	}

	return stats
}

// Helper functions
func parseInt(s string) int {
	var result int
	fmt.Sscanf(s, "%d", &result)
	return result
}

func parseInt64(s string) int64 {
	var result int64
	fmt.Sscanf(s, "%d", &result)
	return result
}
