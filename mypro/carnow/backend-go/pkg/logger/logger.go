package logger

import (
	"fmt"
	"os"
	"strings"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

// Logger يحتوي على واجهة نظام التسجيل
type Logger struct {
	*zap.Logger
}

// NewLogger ينشئ نظام تسجيل جديد
func NewLogger(level, format string) (*Logger, error) {
	zapLevel, err := parseLevel(level)
	if err != nil {
		return nil, fmt.Errorf("invalid log level: %w", err)
	}

	var config zap.Config
	if strings.ToLower(format) == "json" {
		config = zap.NewProductionConfig()
	} else {
		config = zap.NewDevelopmentConfig()
		config.EncoderConfig.EncodeLevel = zapcore.CapitalColorLevelEncoder
	}

	config.Level = zap.NewAtomicLevelAt(zapLevel)
	config.OutputPaths = []string{"stdout"}
	config.ErrorOutputPaths = []string{"stderr"}

	// إعداد الحقول الإضافية
	config.InitialFields = map[string]interface{}{
		"service": "carnow-backend",
		"version": "1.0.0",
	}

	// إعداد التشفير للرسائل العربية
	config.EncoderConfig.MessageKey = "message"
	config.EncoderConfig.TimeKey = "timestamp"
	config.EncoderConfig.LevelKey = "level"
	config.EncoderConfig.CallerKey = "caller"
	config.EncoderConfig.StacktraceKey = "stacktrace"
	config.EncoderConfig.EncodeTime = zapcore.ISO8601TimeEncoder
	config.EncoderConfig.EncodeCaller = zapcore.ShortCallerEncoder

	logger, err := config.Build()
	if err != nil {
		return nil, fmt.Errorf("failed to build logger: %w", err)
	}

	return &Logger{Logger: logger}, nil
}

// parseLevel يحول مستوى التسجيل من نص إلى zapcore.Level
func parseLevel(level string) (zapcore.Level, error) {
	switch strings.ToLower(level) {
	case "debug":
		return zapcore.DebugLevel, nil
	case "info":
		return zapcore.InfoLevel, nil
	case "warn", "warning":
		return zapcore.WarnLevel, nil
	case "error":
		return zapcore.ErrorLevel, nil
	case "fatal":
		return zapcore.FatalLevel, nil
	case "panic":
		return zapcore.PanicLevel, nil
	default:
		return zapcore.InfoLevel, fmt.Errorf("unknown level: %s", level)
	}
}

// WithFields يضيف حقول إضافية إلى السجل
func (l *Logger) WithFields(fields map[string]interface{}) *Logger {
	zapFields := make([]zap.Field, 0, len(fields))
	for key, value := range fields {
		zapFields = append(zapFields, zap.Any(key, value))
	}
	return &Logger{Logger: l.Logger.With(zapFields...)}
}

// WithError يضيف خطأ إلى السجل
func (l *Logger) WithError(err error) *Logger {
	return &Logger{Logger: l.Logger.With(zap.Error(err))}
}

// WithRequestID يضيف معرف الطلب إلى السجل
func (l *Logger) WithRequestID(requestID string) *Logger {
	return &Logger{Logger: l.Logger.With(zap.String("request_id", requestID))}
}

// WithUserID يضيف معرف المستخدم إلى السجل
func (l *Logger) WithUserID(userID string) *Logger {
	return &Logger{Logger: l.Logger.With(zap.String("user_id", userID))}
}

// WithOperation يضيف اسم العملية إلى السجل
func (l *Logger) WithOperation(operation string) *Logger {
	return &Logger{Logger: l.Logger.With(zap.String("operation", operation))}
}

// LogLevel يحدد مستوى التسجيل
type LogLevel string

const (
	DebugLevel LogLevel = "debug"
	InfoLevel  LogLevel = "info"
	WarnLevel  LogLevel = "warn"
	ErrorLevel LogLevel = "error"
	FatalLevel LogLevel = "fatal"
	PanicLevel LogLevel = "panic"
)

// GetLoggerFromEnv ينشئ نظام تسجيل من متغيرات البيئة
func GetLoggerFromEnv() (*Logger, error) {
	level := getEnvOrDefault("LOG_LEVEL", "info")
	format := getEnvOrDefault("LOG_FORMAT", "json")

	return NewLogger(level, format)
}

// getEnvOrDefault يحصل على متغير البيئة أو القيمة الافتراضية
func getEnvOrDefault(key, defaultValue string) string {
	if value, exists := os.LookupEnv(key); exists {
		return value
	}
	return defaultValue
}

// StructuredLogger يوفر واجهة منظمة للتسجيل
type StructuredLogger interface {
	Debug(msg string, fields ...zap.Field)
	Info(msg string, fields ...zap.Field)
	Warn(msg string, fields ...zap.Field)
	Error(msg string, fields ...zap.Field)
	Fatal(msg string, fields ...zap.Field)
	Panic(msg string, fields ...zap.Field)
	With(fields ...zap.Field) StructuredLogger
	Sync() error
}

// NewStructuredLogger ينشئ مسجل منظم جديد
func NewStructuredLogger(level, format string) (StructuredLogger, error) {
	logger, err := NewLogger(level, format)
	if err != nil {
		return nil, err
	}
	return &structuredLoggerWrapper{logger}, nil
}

// structuredLoggerWrapper يلف Logger لتطبيق StructuredLogger
type structuredLoggerWrapper struct {
	*Logger
}

// With يضيف حقول ويعيد StructuredLogger
func (s *structuredLoggerWrapper) With(fields ...zap.Field) StructuredLogger {
	return &structuredLoggerWrapper{&Logger{Logger: s.Logger.Logger.With(fields...)}}
}

// HTTPLogger يوفر واجهة خاصة لتسجيل طلبات HTTP
type HTTPLogger struct {
	*Logger
}

// NewHTTPLogger ينشئ مسجل HTTP جديد
func NewHTTPLogger(logger *Logger) *HTTPLogger {
	return &HTTPLogger{Logger: logger}
}

// LogRequest يسجل طلب HTTP
func (h *HTTPLogger) LogRequest(method, path, userAgent, remoteAddr string, statusCode int, duration float64) {
	h.Logger.Info("HTTP Request",
		zap.String("method", method),
		zap.String("path", path),
		zap.String("user_agent", userAgent),
		zap.String("remote_addr", remoteAddr),
		zap.Int("status_code", statusCode),
		zap.Float64("duration_ms", duration),
	)
}

// LogError يسجل خطأ HTTP
func (h *HTTPLogger) LogError(method, path string, err error, statusCode int) {
	h.Logger.Error("HTTP Error",
		zap.String("method", method),
		zap.String("path", path),
		zap.Error(err),
		zap.Int("status_code", statusCode),
	)
}

// DatabaseLogger يوفر واجهة خاصة لتسجيل عمليات قاعدة البيانات
type DatabaseLogger struct {
	*Logger
}

// NewDatabaseLogger ينشئ مسجل قاعدة بيانات جديد
func NewDatabaseLogger(logger *Logger) *DatabaseLogger {
	return &DatabaseLogger{Logger: logger}
}

// LogQuery يسجل استعلام قاعدة البيانات
func (d *DatabaseLogger) LogQuery(query string, args []interface{}, duration float64) {
	d.Logger.Debug("Database Query",
		zap.String("query", query),
		zap.Any("args", args),
		zap.Float64("duration_ms", duration),
	)
}

// LogQueryError يسجل خطأ استعلام قاعدة البيانات
func (d *DatabaseLogger) LogQueryError(query string, args []interface{}, err error) {
	d.Logger.Error("Database Query Error",
		zap.String("query", query),
		zap.Any("args", args),
		zap.Error(err),
	)
}

// LogConnection يسجل اتصال قاعدة البيانات
func (d *DatabaseLogger) LogConnection(event string, details map[string]interface{}) {
	fields := make([]zap.Field, 0, len(details)+1)
	fields = append(fields, zap.String("event", event))

	for key, value := range details {
		fields = append(fields, zap.Any(key, value))
	}

	d.Logger.Info("Database Connection", fields...)
}
